import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:reorderable_grid_view/reorderable_grid_view.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/extensions.dart';
import '../../data/models/product_image.dart';
import '../../providers/product_image_provider.dart';

class ProductImageManager extends ConsumerStatefulWidget {
  final String productId;
  final List<ProductImage>? initialImages;
  final Function(List<ProductImage>)? onImagesChanged;
  final bool readOnly;
  final int maxImages;

  const ProductImageManager({
    super.key,
    required this.productId,
    this.initialImages,
    this.onImagesChanged,
    this.readOnly = false,
    this.maxImages = 10,
  });

  @override
  ConsumerState<ProductImageManager> createState() =>
      _ProductImageManagerState();
}

class _ProductImageManagerState extends ConsumerState<ProductImageManager> {
  final ImagePicker _imagePicker = ImagePicker();
  List<ProductImage> _images = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _images = widget.initialImages ?? [];
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        _buildHeader(),
        SizedBox(height: 16.h),

        // Images grid
        _buildImagesGrid(),

        // Add image button
        if (!widget.readOnly && _images.length < widget.maxImages)
          _buildAddImageButton(),
      ],
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Text(
          'صور المنتج',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(width: 8.w),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Text(
            '${_images.length}/${widget.maxImages}',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const Spacer(),
        if (!widget.readOnly && _images.isNotEmpty) ...[
          IconButton(
            onPressed: _showBulkActions,
            icon: const Icon(Icons.more_vert),
            tooltip: 'إجراءات متعددة',
          ),
        ],
      ],
    );
  }

  Widget _buildImagesGrid() {
    if (_images.isEmpty) {
      return _buildEmptyState();
    }

    return Container(
      height: 200.h,
      child: ReorderableGridView.count(
        crossAxisCount: 3,
        crossAxisSpacing: 8.w,
        mainAxisSpacing: 8.h,
        childAspectRatio: 1,
        onReorder: (oldIndex, newIndex) {
          if (!widget.readOnly) {
            _onReorder(oldIndex, newIndex);
          }
        },
        children: _images.asMap().entries.map((entry) {
          final index = entry.key;
          final image = entry.value;
          return _buildImageItem(image, index);
        }).toList(),
      ),
    );
  }

  Widget _buildImageItem(ProductImage image, int index) {
    return Card(
      key: ValueKey(image.id),
      clipBehavior: Clip.antiAlias,
      child: Stack(
        fit: StackFit.expand,
        children: [
          // Image
          _buildImageWidget(image),

          // Primary indicator
          if (image.isPrimary)
            Positioned(
              top: 4.r,
              left: 4.r,
              child: Container(
                padding: EdgeInsets.all(4.r),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Icon(
                  Icons.star,
                  color: Colors.white,
                  size: 12.r,
                ),
              ),
            ),

          // Actions overlay
          if (!widget.readOnly)
            Positioned(
              top: 4.r,
              right: 4.r,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    InkWell(
                      onTap: () => _setPrimaryImage(image),
                      child: Padding(
                        padding: EdgeInsets.all(4.r),
                        child: Icon(
                          image.isPrimary ? Icons.star : Icons.star_border,
                          color: image.isPrimary ? Colors.yellow : Colors.white,
                          size: 16.r,
                        ),
                      ),
                    ),
                    InkWell(
                      onTap: () => _showImageOptions(image, index),
                      child: Padding(
                        padding: EdgeInsets.all(4.r),
                        child: Icon(
                          Icons.more_vert,
                          color: Colors.white,
                          size: 16.r,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // Loading overlay
          if (_isLoading)
            Container(
              color: Colors.black.withValues(alpha: 0.5),
              child: const Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildImageWidget(ProductImage image) {
    if (image.imagePath != null && File(image.imagePath!).existsSync()) {
      // Local image
      return Image.file(
        File(image.imagePath!),
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
      );
    } else if (image.imageUrl.isNotEmpty) {
      // Network image
      return CachedNetworkImage(
        imageUrl: image.imageUrl,
        fit: BoxFit.cover,
        placeholder: (context, url) => _buildLoadingWidget(),
        errorWidget: (context, url, error) => _buildErrorWidget(),
      );
    } else {
      return _buildErrorWidget();
    }
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.grey[200],
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      color: Colors.grey[200],
      child: Icon(
        Icons.broken_image,
        color: Colors.grey[400],
        size: 32.r,
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      height: 150.h,
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.grey[300]!,
          style: BorderStyle.solid,
        ),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_outlined,
              size: 48.r,
              color: Colors.grey[400],
            ),
            SizedBox(height: 8.h),
            Text(
              'لا توجد صور',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[600],
              ),
            ),
            if (!widget.readOnly) ...[
              SizedBox(height: 4.h),
              Text(
                'اضغط لإضافة صور',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey[500],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAddImageButton() {
    return Padding(
      padding: EdgeInsets.only(top: 16.h),
      child: SizedBox(
        width: double.infinity,
        child: OutlinedButton.icon(
          onPressed: _showImageSourceDialog,
          icon: const Icon(Icons.add_photo_alternate),
          label: const Text('إضافة صورة'),
          style: OutlinedButton.styleFrom(
            padding: EdgeInsets.symmetric(vertical: 12.h),
          ),
        ),
      ),
    );
  }

  void _onReorder(int oldIndex, int newIndex) {
    setState(() {
      final item = _images.removeAt(oldIndex);
      _images.insert(newIndex, item);

      // Update sort orders
      for (int i = 0; i < _images.length; i++) {
        _images[i] = _images[i].copyWith(sortOrder: i);
      }
    });

    widget.onImagesChanged?.call(_images);
  }

  void _setPrimaryImage(ProductImage image) {
    setState(() {
      // Remove primary from all images
      _images = _images.map((img) => img.copyWith(isPrimary: false)).toList();

      // Set the selected image as primary
      final index = _images.indexWhere((img) => img.id == image.id);
      if (index != -1) {
        _images[index] = _images[index].copyWith(isPrimary: true);
      }
    });

    widget.onImagesChanged?.call(_images);
    context.showSuccessSnackBar('تم تعيين الصورة الرئيسية');
  }

  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Wrap(
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('التقاط صورة'),
              onTap: () {
                Navigator.pop(context);
                _pickImage(ImageSource.camera);
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('اختيار من المعرض'),
              onTap: () {
                Navigator.pop(context);
                _pickImage(ImageSource.gallery);
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library_outlined),
              title: const Text('اختيار متعدد'),
              onTap: () {
                Navigator.pop(context);
                _pickMultipleImages();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showImageOptions(ProductImage image, int index) {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Wrap(
          children: [
            ListTile(
              leading: Icon(
                image.isPrimary ? Icons.star : Icons.star_border,
                color: image.isPrimary ? Colors.yellow : null,
              ),
              title: Text(image.isPrimary
                  ? 'إلغاء الصورة الرئيسية'
                  : 'تعيين كصورة رئيسية'),
              onTap: () {
                Navigator.pop(context);
                _setPrimaryImage(image);
              },
            ),
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('تعديل الوصف'),
              onTap: () {
                Navigator.pop(context);
                _editImageDescription(image);
              },
            ),
            ListTile(
              leading: const Icon(Icons.fullscreen),
              title: const Text('عرض بالحجم الكامل'),
              onTap: () {
                Navigator.pop(context);
                _showFullScreenImage(image);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title:
                  const Text('حذف الصورة', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _deleteImage(image);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showBulkActions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Wrap(
          children: [
            ListTile(
              leading: const Icon(Icons.delete_sweep),
              title: const Text('حذف جميع الصور'),
              onTap: () {
                Navigator.pop(context);
                _deleteAllImages();
              },
            ),
            ListTile(
              leading: const Icon(Icons.download),
              title: const Text('تحميل جميع الصور'),
              onTap: () {
                Navigator.pop(context);
                _downloadAllImages();
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      setState(() {
        _isLoading = true;
      });

      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image != null) {
        await _addImage(image.path);
      }
    } catch (e) {
      context.showErrorSnackBar('فشل في اختيار الصورة');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _pickMultipleImages() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final List<XFile> images = await _imagePicker.pickMultiImage(
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      for (final image in images) {
        if (_images.length < widget.maxImages) {
          await _addImage(image.path);
        }
      }
    } catch (e) {
      context.showErrorSnackBar('فشل في اختيار الصور');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _addImage(String imagePath) async {
    final newImage = ProductImage.create(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      productId: widget.productId,
      imageUrl: '', // Will be set when uploaded
      imagePath: imagePath,
      isPrimary: _images.isEmpty, // First image is primary
      sortOrder: _images.length,
    );

    setState(() {
      _images.add(newImage);
    });

    widget.onImagesChanged?.call(_images);
  }

  void _editImageDescription(ProductImage image) {
    final controller = TextEditingController(text: image.description);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل وصف الصورة'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'الوصف',
            hintText: 'أدخل وصف الصورة',
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _updateImageDescription(image, controller.text);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _updateImageDescription(ProductImage image, String description) {
    setState(() {
      final index = _images.indexWhere((img) => img.id == image.id);
      if (index != -1) {
        _images[index] = _images[index].copyWith(description: description);
      }
    });

    widget.onImagesChanged?.call(_images);
    context.showSuccessSnackBar('تم تحديث وصف الصورة');
  }

  void _showFullScreenImage(ProductImage image) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            iconTheme: const IconThemeData(color: Colors.white),
          ),
          body: Center(
            child: InteractiveViewer(
              child: _buildImageWidget(image),
            ),
          ),
        ),
      ),
    );
  }

  void _deleteImage(ProductImage image) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذه الصورة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _images.removeWhere((img) => img.id == image.id);

                // If deleted image was primary, make first image primary
                if (image.isPrimary && _images.isNotEmpty) {
                  _images[0] = _images[0].copyWith(isPrimary: true);
                }
              });

              widget.onImagesChanged?.call(_images);
              context.showSuccessSnackBar('تم حذف الصورة');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _deleteAllImages() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف جميع الصور؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _images.clear();
              });

              widget.onImagesChanged?.call(_images);
              context.showSuccessSnackBar('تم حذف جميع الصور');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('حذف الكل'),
          ),
        ],
      ),
    );
  }

  void _downloadAllImages() {
    // TODO: Implement download functionality
    context.showInfoSnackBar('تحميل الصور قيد التطوير');
  }
}
