import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:tijari_tech/core/utils/error_handler.dart';
import 'package:tijari_tech/core/utils/formatters.dart';
import 'package:tijari_tech/shared/widgets/loading_widget.dart';

import '../../data/models/cash_box.dart';
import '../../providers/cash_box_provider.dart';
import '../../providers/branch_provider.dart';
import '../../core/utils/app_utils.dart';

class CashBoxFormPage extends ConsumerStatefulWidget {
  final String? cashBoxId;

  const CashBoxFormPage({
    super.key,
    this.cashBoxId,
  });

  @override
  ConsumerState<CashBoxFormPage> createState() => _CashBoxFormPageState();
}

class _CashBoxFormPageState extends ConsumerState<CashBoxFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _balanceController = TextEditingController();
  
  bool _isActive = true;
  String? _selectedBranchId;
  bool _isLoading = false;
  CashBox? _existingCashBox;

  bool get _isEditing => widget.cashBoxId != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _loadExistingCashBox();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _balanceController.dispose();
    super.dispose();
  }

  Future<void> _loadExistingCashBox() async {
    if (widget.cashBoxId == null) return;

    setState(() => _isLoading = true);

    try {
      final cashBox = await ref.read(cashBoxByIdProvider(widget.cashBoxId!).future);
      if (cashBox != null) {
        setState(() {
          _existingCashBox = cashBox;
          _nameController.text = cashBox.name;
          _balanceController.text = cashBox.balance.toString();
          _isActive = cashBox.isActive;
          _selectedBranchId = cashBox.branchId;
        });
      }
    } catch (e) {
      ErrorHandler.showErrorSnackBar(context, 'فشل في تحميل بيانات الصندوق');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        body: LoadingWidget()
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'تعديل الصندوق' : 'إضافة صندوق جديد'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          TextButton(
            onPressed: _saveCashBox,
            child: const Text('حفظ'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Cash box name
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم الصندوق *',
                hintText: 'أدخل اسم الصندوق',
                prefixIcon: Icon(Icons.account_balance_wallet),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'اسم الصندوق مطلوب';
                }
                if (value.trim().length < 2) {
                  return 'اسم الصندوق يجب أن يكون أكثر من حرفين';
                }
                return null;
              },
              textInputAction: TextInputAction.next,
            ),

            const SizedBox(height: 16),

            // Initial balance
            TextFormField(
              controller: _balanceController,
              decoration: const InputDecoration(
                labelText: 'الرصيد الابتدائي',
                hintText: '0.00',
                prefixIcon: Icon(Icons.attach_money),
                border: OutlineInputBorder(),
                suffixText: 'ر.س',
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  final balance = double.tryParse(value);
                  if (balance == null) {
                    return 'يرجى إدخال رقم صحيح';
                  }
                  if (balance < 0) {
                    return 'الرصيد لا يمكن أن يكون سالباً';
                  }
                }
                return null;
              },
              textInputAction: TextInputAction.done,
            ),

            const SizedBox(height: 16),

            // Branch selection
            Consumer(
              builder: (context, ref, child) {
                final branchesAsync = ref.watch(branchesProvider);
                
                return branchesAsync.when(
                  data: (branches) {
                    return DropdownButtonFormField<String>(
                      value: _selectedBranchId,
                      decoration: const InputDecoration(
                        labelText: 'الفرع',
                        hintText: 'اختر الفرع',
                        prefixIcon: Icon(Icons.business),
                        border: OutlineInputBorder(),
                      ),
                      items: [
                        const DropdownMenuItem<String>(
                          value: null,
                          child: Text('جميع الفروع'),
                        ),
                        ...branches.map((branch) {
                          return DropdownMenuItem<String>(
                            value: branch.id,
                            child: Text(branch.name),
                          );
                        }),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedBranchId = value;
                        });
                      },
                    );
                  },
                  loading: () => const LinearProgressIndicator(),
                  error: (error, stackTrace) => const Text('فشل في تحميل الفروع'),
                );
              },
            ),

            const SizedBox(height: 16),

            // Active status
            Card(
              child: SwitchListTile(
                title: const Text('الصندوق نشط'),
                subtitle: Text(_isActive ? 'الصندوق متاح للاستخدام' : 'الصندوق غير متاح'),
                value: _isActive,
                onChanged: (value) {
                  setState(() {
                    _isActive = value;
                  });
                },
                secondary: Icon(
                  _isActive ? Icons.check_circle : Icons.cancel,
                  color: _isActive ? Colors.green : Colors.grey,
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Save button
            ElevatedButton(
              onPressed: _saveCashBox,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                _isEditing ? 'تحديث الصندوق' : 'إنشاء الصندوق',
                style: const TextStyle(fontSize: 16),
              ),
            ),

            if (_isEditing) ...[
              const SizedBox(height: 16),
              
              // Additional info for editing
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معلومات إضافية',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (_existingCashBox?.createdAt != null)
                        _buildInfoRow('تاريخ الإنشاء', 
                            AppFormatters.formatDate(_existingCashBox!.createdAt as DateTime)),
                      if (_existingCashBox?.updatedAt != null)
                        _buildInfoRow('آخر تحديث', 
                          AppFormatters.formatDate(_existingCashBox!.updatedAt as DateTime)),
                      _buildInfoRow('الرصيد الحالي', 
                          _existingCashBox?.formattedBalance ?? '0.00 ر.س'),
                    ],
                  ),
                ),
              ),
            ],

            const SizedBox(height: 16),

            // Help text
            Card(
              color: Colors.blue.shade50,
              child: const Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue),
                        SizedBox(width: 8),
                        Text(
                          'نصائح',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Text('• اختر اسماً واضحاً ومميزاً للصندوق'),
                    Text('• يمكن تعديل الرصيد لاحقاً من خلال المعاملات'),
                    Text('• الصناديق غير النشطة لا تظهر في قوائم الاختيار'),
                    Text('• لا يمكن حذف صندوق يحتوي على رصيد'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String? value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value ?? 'غير محدد')),
        ],
      ),
    );
  }

  Future<void> _saveCashBox() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      final name = _nameController.text.trim();
      final balance = double.tryParse(_balanceController.text) ?? 0.0;

      final cashBox = _isEditing
          ? _existingCashBox!.update(
              name: name,
              isActive: _isActive,
              branchId: _selectedBranchId,
            )
          : CashBox.create(
              id: AppUtils.generateId(),
              name: name,
              balance: balance,
              branchId: _selectedBranchId,
            ).copyWith(isActive: _isActive);

      final success = _isEditing
          ? await ref.read(cashBoxesProvider.notifier).updateCashBox(cashBox)
          : await ref.read(cashBoxesProvider.notifier).createCashBox(cashBox);

      if (success) {
        ErrorHandler.showErrorSnackBar(
          context,
          _isEditing ? 'تم تحديث الصندوق بنجاح' : 'تم إنشاء الصندوق بنجاح',
        );
        
        if (mounted) {
          context.pop();
        }
      } else {
       ErrorHandler.showErrorSnackBar(
          context,
          _isEditing ? 'فشل في تحديث الصندوق' : 'فشل في إنشاء الصندوق',
        );
      }
    } catch (e) {
      ErrorHandler.showErrorSnackBar(context, 'حدث خطأ: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
