import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:tijari_tech/widgets/custom_text_field.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/responsive_wrapper.dart';
import '../../router/routes.dart';

class CompanyInfoPage extends ConsumerStatefulWidget {
  const CompanyInfoPage({super.key});

  @override
  ConsumerState<CompanyInfoPage> createState() => _CompanyInfoPageState();
}

class _CompanyInfoPageState extends ConsumerState<CompanyInfoPage> {
  final _formKey = GlobalKey<FormState>();
  final _companyNameController = TextEditingController();
  final _companyAddressController = TextEditingController();
  final _companyPhoneController = TextEditingController();
  final _companyEmailController = TextEditingController();
  final _taxNumberController = TextEditingController();
  final _commercialRegisterController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadCompanyInfo();
  }

  @override
  void dispose() {
    _companyNameController.dispose();
    _companyAddressController.dispose();
    _companyPhoneController.dispose();
    _companyEmailController.dispose();
    _taxNumberController.dispose();
    _commercialRegisterController.dispose();
    super.dispose();
  }

  void _loadCompanyInfo() {
    // Load existing company info
    _companyNameController.text = 'شركة تجاري تك المحدودة';
    _companyAddressController.text = 'الرياض، المملكة العربية السعودية';
    _companyPhoneController.text = '+966501234567';
    _companyEmailController.text = '<EMAIL>';
    _taxNumberController.text = '123456789012345';
    _commercialRegisterController.text = '1010123456';
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'معلومات الشركة',
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Company Info Card
            _buildCompanyInfoCard(),
            SizedBox(height: 24.h),

            // Management Cards
            _buildManagementCards(),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanyInfoCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(6.r),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(12.r),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Icon(
                      Icons.business,
                      size: 32.r,
                      color: AppColors.primary,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'معلومات الشركة الأساسية',
                          style: AppTextStyles.titleLarge.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          'تحديث بيانات الشركة والمعلومات الأساسية',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 10.h),

              // Form Fields
              /*   ResponsiveGrid(
                mobileColumns: 1,
                tabletColumns: 2,
                desktopColumns: 2,
                children: [
                 ],
              ),
                */
              CustomTextField(
                controller: _companyNameController,
                label: 'اسم الشركة',
                prefixIcon: Icon(Icons.business_outlined),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'اسم الشركة مطلوب';
                  }
                  return null;
                },
              ),
              CustomTextField(
                controller: _companyEmailController,
                label: 'البريد الإلكتروني',
                prefixIcon: Icon(
                  Icons.email_outlined,
                ),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'البريد الإلكتروني مطلوب';
                  }
                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                      .hasMatch(value)) {
                    return 'البريد الإلكتروني غير صحيح';
                  }
                  return null;
                },
              ),
              CustomTextField(
                controller: _companyPhoneController,
                label: 'رقم الهاتف',
                prefixIcon: Icon(Icons.phone_outlined),
                keyboardType: TextInputType.phone,
              ),
              CustomTextField(
                controller: _taxNumberController,
                label: 'الرقم الضريبي',
                prefixIcon: Icon(Icons.receipt_outlined),
                keyboardType: TextInputType.number,
              ),

              // SizedBox(height: 16.h),

              // Address Field (Full Width)
              CustomTextField(
                controller: _companyAddressController,
                label: 'عنوان الشركة',
                prefixIcon: Icon(Icons.location_on_outlined),
                maxLines: 3,
              ),
              SizedBox(height: 16.h),

              // Commercial Register Field
              CustomTextField(
                controller: _commercialRegisterController,
                label: 'رقم السجل التجاري',
                prefixIcon: Icon(Icons.assignment_outlined),
                keyboardType: TextInputType.number,
              ),
              SizedBox(height: 24.h),

              // Save Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _saveCompanyInfo,
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  child: Text(
                    'حفظ التغييرات',
                    style: AppTextStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildManagementCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إدارة النظام',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 2,
          desktopColumns: 3,
          children: [
            _buildManagementCard(
              title: 'إدارة الفروع',
              subtitle: 'إضافة وتعديل فروع الشركة',
              icon: Icons.location_on_outlined,
              color: AppColors.primary,
              onTap: () => context.go(AppRoutes.branches),
            ),
            _buildManagementCard(
              title: 'إدارة المستخدمين',
              subtitle: 'إضافة وإدارة مستخدمي النظام',
              icon: Icons.people_outline,
              color: AppColors.secondary,
              onTap: () => context.go(AppRoutes.userManagement),
            ),
            _buildManagementCard(
              title: 'الصلاحيات والأدوار',
              subtitle: 'إدارة صلاحيات المستخدمين',
              icon: Icons.security_outlined,
              color: AppColors.info,
              onTap: () {
                // TODO: Navigate to permissions page
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildManagementCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(20.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.all(12.r),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Icon(
                  icon,
                  size: 32.r,
                  color: color,
                ),
              ),
              SizedBox(height: 16.h),
              Text(
                title,
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                subtitle,
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _saveCompanyInfo() {
    if (_formKey.currentState!.validate()) {
      // TODO: Save company info to database
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم حفظ معلومات الشركة بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    }
  }
}
