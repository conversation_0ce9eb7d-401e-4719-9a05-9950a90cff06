// lib/data/models/supplier.dart
import 'package:json_annotation/json_annotation.dart';
import '../../core/constants/database_constants.dart';

part 'supplier.g.dart';

/// حالة رصيد المورد
enum SupplierBalanceStatus {
  weOwe,    // نحن مدينون للمورد
  theyOwe,  // المورد مدين لنا
  balanced,  // متوازن
}

extension SupplierBalanceStatusExt on SupplierBalanceStatus {
  String get displayName {
    switch (this) {
      case SupplierBalanceStatus.weOwe:
        return 'لنا عليه';
      case SupplierBalanceStatus.theyOwe:
        return 'له علينا';
      case SupplierBalanceStatus.balanced:
        return 'متوازن';
    }
  }
}

/// نموذج المورد الكامل
@JsonSerializable()
class Supplier {
  final String id;
  final String name;
  final String? phone;
  final String? email;
  final String? address;
  final double balance;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final bool isSynced;

  const Supplier({
    required this.id,
    required this.name,
    this.phone,
    this.email,
    this.address,
    this.balance = 0.0,
    this.notes,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  factory Supplier.fromMap(Map<String, dynamic> map) => Supplier(
        id: map[DatabaseConstants.columnSupplierId] as String,
        name: map[DatabaseConstants.columnSupplierName] as String,
        phone: map[DatabaseConstants.columnSupplierPhone] as String?,
        email: map[DatabaseConstants.columnSupplierEmail] as String?,
        address: map[DatabaseConstants.columnSupplierAddress] as String?,
        balance: (map[DatabaseConstants.columnSupplierBalance] as num?)?.toDouble() ?? 0.0,
        notes: map[DatabaseConstants.columnSupplierNotes] as String?,
        createdAt: map[DatabaseConstants.columnSupplierCreatedAt] != null
            ? DateTime.parse(map[DatabaseConstants.columnSupplierCreatedAt] as String)
            : null,
        updatedAt: map[DatabaseConstants.columnSupplierUpdatedAt] != null
            ? DateTime.parse(map[DatabaseConstants.columnSupplierUpdatedAt] as String)
            : null,
        deletedAt: map[DatabaseConstants.columnSupplierDeletedAt] != null
            ? DateTime.parse(map[DatabaseConstants.columnSupplierDeletedAt] as String)
            : null,
        isSynced: (map[DatabaseConstants.columnSupplierIsSynced] as int? ?? 0) == 1,
      );

  Map<String, dynamic> toMap() => {
        DatabaseConstants.columnSupplierId: id,
        DatabaseConstants.columnSupplierName: name,
        DatabaseConstants.columnSupplierPhone: phone,
        DatabaseConstants.columnSupplierEmail: email,
        DatabaseConstants.columnSupplierAddress: address,
        DatabaseConstants.columnSupplierBalance: balance,
        DatabaseConstants.columnSupplierNotes: notes,
        DatabaseConstants.columnSupplierCreatedAt: createdAt?.toIso8601String(),
        DatabaseConstants.columnSupplierUpdatedAt: updatedAt?.toIso8601String(),
        DatabaseConstants.columnSupplierDeletedAt: deletedAt?.toIso8601String(),
        DatabaseConstants.columnSupplierIsSynced: isSynced ? 1 : 0,
      };

  factory Supplier.fromJson(Map<String, dynamic> json) => _$SupplierFromJson(json);
  Map<String, dynamic> toJson() => _$SupplierToJson(this);

  Supplier copyWith({
    String? id,
    String? name,
    String? phone,
    String? email,
    String? address,
    double? balance,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) =>
      Supplier(
        id: id ?? this.id,
        name: name ?? this.name,
        phone: phone ?? this.phone,
        email: email ?? this.email,
        address: address ?? this.address,
        balance: balance ?? this.balance,
        notes: notes ?? this.notes,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        isSynced: isSynced ?? this.isSynced,
      );

  // Helper methods
  bool get isDeleted => deletedAt != null;
  bool get isActive => !isDeleted;
  bool get hasPhone => phone != null && phone!.isNotEmpty;
  bool get hasEmail => email != null && email!.isNotEmpty;
  bool get hasAddress => address != null && address!.isNotEmpty;
  bool get hasNotes => notes != null && notes!.isNotEmpty;
  bool get hasDebt => balance > 0; // نحن مدينون للمورد
  bool get hasCredit => balance < 0; // المورد مدين لنا
  bool get isBalanced => balance == 0;

  SupplierBalanceStatus get balanceStatus {
    if (balance > 0) return SupplierBalanceStatus.weOwe;
    if (balance < 0) return SupplierBalanceStatus.theyOwe;
    return SupplierBalanceStatus.balanced;
  }

  double get absoluteBalance => balance.abs();

  static Supplier create({
    required String id,
    required String name,
    String? phone,
    String? email,
    String? address,
    double balance = 0.0,
    String? notes,
  }) {
    final now = DateTime.now();
    return Supplier(
      id: id,
      name: name,
      phone: phone,
      email: email,
      address: address,
      balance: balance,
      notes: notes,
      createdAt: now,
      updatedAt: now,
      isSynced: false,
    );
  }

  Supplier update({
    String? name,
    String? phone,
    String? email,
    String? address,
    double? balance,
    String? notes,
  }) =>
      copyWith(
        name: name ?? this.name,
        phone: phone ?? this.phone,
        email: email ?? this.email,
        address: address ?? this.address,
        balance: balance ?? this.balance,
        notes: notes ?? this.notes,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Supplier addToBalance(double amount) => copyWith(
        balance: balance + amount,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Supplier subtractFromBalance(double amount) => copyWith(
        balance: balance - amount,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Supplier clearBalance() => copyWith(
        balance: 0.0,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Supplier markAsDeleted() => copyWith(
        deletedAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Supplier markAsSynced() => copyWith(isSynced: true);

  Supplier removePhone() => copyWith(
        phone: null,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Supplier removeEmail() => copyWith(
        email: null,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Supplier removeAddress() => copyWith(
        address: null,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Supplier removeNotes() => copyWith(
        notes: null,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  @override
  bool operator ==(Object other) => identical(this, other) || other is Supplier && other.id == id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Supplier(id: $id, name: $name, balance: $balance, isSynced: $isSynced)';
}