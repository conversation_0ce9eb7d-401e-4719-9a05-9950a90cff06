import 'package:flutter/material.dart';

import 'Routes.dart';
import 'route_guard.dart';

/// عنصر التنقل في الشريط الجانبي
class NavigationItem {
  final String title;
  final IconData icon;
  final String route;
  final List<NavigationItem>? children;
  final String? permission;

  const NavigationItem({
    required this.title,
    required this.icon,
    required this.route,
    this.children,
    this.permission,
  });
}

/// إدارة التنقل في الشريط الجانبي
class SidebarNavigation {
  /// قائمة عناصر التنقل الرئيسية
  static List<NavigationItem> get mainNavigationItems => [
    // لوحة التحكم
    const NavigationItem(
      title: 'لوحة التحكم',
      icon: Icons.dashboard,
      route: AppRoutes.dashboard,
    ),

    // المخزون
    NavigationItem(
      title: 'المخزون',
      icon: Icons.inventory,
      route: AppRoutes.inventory,
      children: [
        const NavigationItem(
          title: 'المنتجات',
          icon: Icons.shopping_bag,
          route: AppRoutes.products,
          permission: 'inventory_view',
        ),
        const NavigationItem(
          title: 'التصنيفات',
          icon: Icons.category,
          route: AppRoutes.categories,
          permission: 'inventory_view',
        ),
        const NavigationItem(
          title: 'وحدات القياس',
          icon: Icons.straighten,
          route: AppRoutes.units,
          permission: 'inventory_view',
        ),
        const NavigationItem(
          title: 'المخازن',
          icon: Icons.warehouse,
          route: AppRoutes.warehouses,
          permission: 'warehouse_view',
        ),
        const NavigationItem(
          title: 'إدارة المخزون',
          icon: Icons.inventory_2,
          route: AppRoutes.stockManagement,
          permission: 'stock_view',
        ),
        const NavigationItem(
          title: 'حركة المخزون',
          icon: Icons.swap_horiz,
          route: AppRoutes.stockMovements,
          permission: 'stock_view',
        ),
        const NavigationItem(
          title: 'تسوية المخزون',
          icon: Icons.tune,
          route: AppRoutes.stockAdjustment,
          permission: 'stock_adjust',
        ),
        const NavigationItem(
          title: 'نقل المخزون',
          icon: Icons.compare_arrows,
          route: AppRoutes.stockTransfer,
          permission: 'stock_transfer',
        ),
      ],
    ),

    // المبيعات
    NavigationItem(
      title: 'المبيعات',
      icon: Icons.point_of_sale,
      route: AppRoutes.sales,
      children: [
        const NavigationItem(
          title: 'نقطة البيع',
          icon: Icons.store,
          route: AppRoutes.pos,
          permission: 'pos_access',
        ),
        const NavigationItem(
          title: 'إضافة مبيعة',
          icon: Icons.add_shopping_cart,
          route: AppRoutes.saleAdd,
          permission: 'sales_create',
        ),
        NavigationItem(
          title: 'قائمة المبيعات',
          icon: Icons.list_alt,
          route: AppRoutes.saleList,
          permission: 'sales_view',
        ),
        const NavigationItem(
          title: 'مرتجعات المبيعات',
          icon: Icons.keyboard_return,
          route: AppRoutes.saleReturn,
          permission: 'sales_return',
        ),
      ],
    ),

    // المشتريات
    NavigationItem(
      title: 'المشتريات',
      icon: Icons.shopping_cart,
      route: AppRoutes.purchases,
      children: [
        const NavigationItem(
          title: 'إضافة مشتريات',
          icon: Icons.add_shopping_cart,
          route: AppRoutes.purchaseAdd,
          permission: 'purchase_create',
        ),
        const NavigationItem(
          title: 'قائمة المشتريات',
          icon: Icons.list_alt,
          route: AppRoutes.purchaseList,
          permission: 'purchase_view',
        ),
        const NavigationItem(
          title: 'مرتجعات المشتريات',
          icon: Icons.keyboard_return,
          route: AppRoutes.purchaseReturn,
          permission: 'purchase_return',
        ),
      ],
    ),

    // الحسابات
    NavigationItem(
      title: 'الحسابات',
      icon: Icons.account_balance,
      route: AppRoutes.accounts,
      children: [
        const NavigationItem(
          title: 'العملاء',
          icon: Icons.people,
          route: AppRoutes.customers,
          permission: 'customers_view',
        ),
        const NavigationItem(
          title: 'الموردين',
          icon: Icons.business,
          route: AppRoutes.suppliers,
          permission: 'suppliers_view',
        ),
      ],
    ),

    // الخزينة
    NavigationItem(
      title: 'الخزينة',
      icon: Icons.account_balance_wallet,
      route: AppRoutes.cash,
      children: [
        const NavigationItem(
          title: 'سند قبض',
          icon: Icons.receipt,
          route: AppRoutes.receipt,
          permission: 'cash_receipt',
        ),
        const NavigationItem(
          title: 'سند صرف',
          icon: Icons.payment,
          route: AppRoutes.payment,
          permission: 'cash_payment',
        ),
      ],
    ),

    // الموظفين
    const NavigationItem(
      title: 'الموظفين',
      icon: Icons.badge,
      route: AppRoutes.employees,
      permission: 'employees_view',
    ),

    // التقارير
    NavigationItem(
      title: 'التقارير',
      icon: Icons.analytics,
      route: AppRoutes.reports,
      children: [
        const NavigationItem(
          title: 'تقرير المبيعات',
          icon: Icons.trending_up,
          route: AppRoutes.salesReport,
          permission: 'reports_sales',
        ),
        const NavigationItem(
          title: 'تقرير المشتريات',
          icon: Icons.trending_down,
          route: AppRoutes.purchaseReport,
          permission: 'reports_purchase',
        ),
        const NavigationItem(
          title: 'تقارير المخزون',
          icon: Icons.inventory,
          route: AppRoutes.stockReports,
          permission: 'reports_inventory',
        ),
        const NavigationItem(
          title: 'الأرباح والخسائر',
          icon: Icons.account_balance,
          route: AppRoutes.profitLossReport,
          permission: 'reports_financial',
        ),
      ],
    ),

    // الإعدادات
    NavigationItem(
      title: 'الإعدادات',
      icon: Icons.settings,
      route: AppRoutes.settings,
      children: [
        const NavigationItem(
          title: 'إدارة المستخدمين',
          icon: Icons.manage_accounts,
          route: AppRoutes.userManagement,
          permission: 'admin_access',
        ),
        const NavigationItem(
          title: 'الذكاء الاصطناعي',
          icon: Icons.psychology,
          route: AppRoutes.aiDashboard,
          permission: 'ai_access',
        ),
      ],
    ),
  ];

  /// تصفية عناصر التنقل حسب الصلاحيات
  static List<NavigationItem> getFilteredNavigationItems() {
    return mainNavigationItems.where((item) {
      return _hasPermissionForItem(item);
    }).map((item) {
      if (item.children != null) {
        final filteredChildren = item.children!.where((child) {
          return _hasPermissionForItem(child);
        }).toList();
        
        return NavigationItem(
          title: item.title,
          icon: item.icon,
          route: item.route,
          children: filteredChildren.isNotEmpty ? filteredChildren : null,
          permission: item.permission,
        );
      }
      return item;
    }).toList();
  }

  /// التحقق من الصلاحية لعنصر التنقل
  static bool _hasPermissionForItem(NavigationItem item) {
    if (item.permission == null) {
      return true;
    }

    // التحقق من الصلاحيات باستخدام RouteGuard
    switch (item.permission) {
      case 'inventory_view':
        return RouteGuard.canEdit('inventory');
      case 'warehouse_view':
        return RouteGuard.canManageWarehouses();
      case 'stock_view':
        return RouteGuard.canManageStock();
      case 'stock_adjust':
        return RouteGuard.canAdjustStock();
      case 'stock_transfer':
        return RouteGuard.canTransferStock();
      case 'pos_access':
        return RouteGuard.canAccessPOS();
      case 'sales_create':
      case 'sales_view':
      case 'sales_return':
        return RouteGuard.canEdit('sales');
      case 'purchase_create':
      case 'purchase_view':
      case 'purchase_return':
        return RouteGuard.canEdit('purchases');
      case 'customers_view':
      case 'suppliers_view':
        return RouteGuard.canEdit('accounts');
      case 'cash_receipt':
      case 'cash_payment':
        return RouteGuard.canEdit('cash');
      case 'employees_view':
        return RouteGuard.canEdit('employees');
      case 'reports_sales':
        return RouteGuard.canViewReports('sales');
      case 'reports_purchase':
        return RouteGuard.canViewReports('purchases');
      case 'reports_inventory':
        return RouteGuard.canViewReports('inventory');
      case 'reports_financial':
        return RouteGuard.canViewReports('financial');
      case 'admin_access':
        return RouteGuard.canEdit('settings');
      case 'ai_access':
        return RouteGuard.canAccessAI();
      default:
        return true;
    }
  }

  /// الحصول على عنصر التنقل النشط
  static NavigationItem? getActiveItem(String currentRoute) {
    for (final item in mainNavigationItems) {
      if (item.route == currentRoute) {
        return item;
      }
      if (item.children != null) {
        for (final child in item.children!) {
          if (child.route == currentRoute) {
            return child;
          }
        }
      }
    }
    return null;
  }

  /// الحصول على العنصر الأب للمسار
  static NavigationItem? getParentItem(String currentRoute) {
    for (final item in mainNavigationItems) {
      if (item.children != null) {
        for (final child in item.children!) {
          if (child.route == currentRoute) {
            return item;
          }
        }
      }
    }
    return null;
  }

  /// التحقق من كون المسار نشط
  static bool isRouteActive(String route, String currentRoute) {
    return currentRoute.startsWith(route);
  }
}
