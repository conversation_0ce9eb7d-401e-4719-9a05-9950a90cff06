import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tijari_tech/core/uint_type.dart';
import 'package:tijari_tech/data/models/unit.dart';
import 'package:tijari_tech/providers/unit_provider.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/extensions.dart';

/// نسخة طبق الأصل من AddEditedCategoryDialog معدلة للوحدات
/* ------------------------------------------------------------------ */
/* ---------------- حوار إضافة/تعديل الوحدة ------------------------- */

/* ------------------------------------------------------------------ */
/* ---------------- حوار إضافة/تعديل الوحدة ------------------------- */
/* ------------------------------------------------------------------ */
class AddEditUnitDialog extends ConsumerStatefulWidget {
  final Unit? unit;
  final Unit? baseUnit;

  const AddEditUnitDialog({super.key, this.unit, this.baseUnit});

  @override
  ConsumerState<AddEditUnitDialog> createState() => _AddEditUnitDialogState();
}

class _AddEditUnitDialogState extends ConsumerState<AddEditUnitDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _symbolController = TextEditingController();
  final _abbreviationController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _factorController = TextEditingController();

  bool _isBaseUnit = true;
  String? _selectedBaseUnitId;
  UnitType _selectedType = UnitType.count;
  bool _isActive = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    // إذا كانت وحدة مشتقة، قم بإعداد الوحدة الأساسية
    if (widget.baseUnit != null) {
      _selectedBaseUnitId = widget.baseUnit!.id;
      _isBaseUnit = false;
      _selectedType = _parseUnitType(widget.baseUnit!.unitType);
    }

    // تحميل البيانات عند التعديل
    if (widget.unit != null) {
      _loadUnit();
    }
  }

  Future<void> _loadUnit() async {
    final unit = widget.unit!;
    setState(() {
      _nameController.text = unit.name;
      _symbolController.text = unit.symbol ?? '';
      _abbreviationController.text = unit.abbreviation ?? '';
      _descriptionController.text = unit.description ?? '';
      _factorController.text = unit.factor.toString();
      _selectedBaseUnitId = unit.baseUnitId;
      _isBaseUnit = unit.isBaseUnitType;
      _selectedType = _parseUnitType(unit.unitType);
      _isActive = unit.isActiveUnit;
    });
  }

  UnitType _parseUnitType(String type) {
    try {
      return UnitType.values.firstWhere(
        (e) => e.toString().split('.').last == type,
        orElse: () => UnitType.count,
      );
    } catch (_) {
      return UnitType.count;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _symbolController.dispose();
    _abbreviationController.dispose();
    _descriptionController.dispose();
    _factorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final baseUnitsAsync = ref.watch(baseUnitsProvider);

    return Dialog(
      child: Container(
        width: 500.w,
        height: 680.h,
        padding: EdgeInsets.all(24.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  widget.unit != null ? Icons.edit : Icons.straighten,
                  color: AppColors.primary,
                  size: 24.r,
                ),
                SizedBox(width: 8.w),
                Text(
                  widget.unit != null ? 'تعديل الوحدة' : 'إضافة وحدة جديدة',
                  style: AppTextStyles.titleLarge
                      .copyWith(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            // Form
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'اسم الوحدة (عربي) *',
                          border: OutlineInputBorder(),
                        ),
                        validator: (v) => v == null || v.trim().isEmpty
                            ? 'اسم الوحدة مطلوب'
                            : null,
                      ),
                      SizedBox(height: 12.h),
                      TextFormField(
                        controller: _symbolController,
                        decoration: const InputDecoration(
                          labelText: 'الرمز *',
                          border: OutlineInputBorder(),
                          hintText: 'مثال: كجم، قطعة، لتر',
                        ),
                        validator: (v) => v == null || v.trim().isEmpty
                            ? 'الرمز مطلوب'
                            : null,
                      ),
                      SizedBox(height: 12.h),
                      TextFormField(
                        controller: _abbreviationController,
                        decoration: const InputDecoration(
                          labelText: 'الاختصار',
                          border: OutlineInputBorder(),
                          hintText: 'مثال: kg, pc, L',
                        ),
                      ),
                      SizedBox(height: 12.h),
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'الوصف',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 2,
                      ),
                      SizedBox(height: 12.h),
                      DropdownButtonFormField<UnitType>(
                        value: _selectedType,
                        decoration: const InputDecoration(
                          labelText: 'نوع الوحدة *',
                          border: OutlineInputBorder(),
                        ),
                        items: UnitType.values
                            .map((t) => DropdownMenuItem(
                                  value: t,
                                  child: Text(_getUnitTypeDisplayName(t)),
                                ))
                            .toList(),
                        onChanged: (v) => setState(() => _selectedType = v!),
                      ),
                      SizedBox(height: 12.h),
                      CheckboxListTile(
                        title: const Text('وحدة أساسية'),
                        value: _isBaseUnit,
                        onChanged: (v) => setState(() => _isBaseUnit = v!),
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                      if (!_isBaseUnit) ...[
                        SizedBox(height: 12.h),
                        baseUnitsAsync.when(
                          data: (baseUnits) => DropdownButtonFormField<String>(
                            value: _selectedBaseUnitId,
                            decoration: const InputDecoration(
                              labelText: 'الوحدة الأساسية *',
                              border: OutlineInputBorder(),
                            ),
                            items: baseUnits
                                .where((u) =>
                                    u.unitType ==
                                    _selectedType.toString().split('.').last)
                                .map((u) => DropdownMenuItem(
                                      value: u.id,
                                      child: Text(u.displayName),
                                    ))
                                .toList(),
                            onChanged: (v) => setState(() {
                              _selectedBaseUnitId = v;
                            }),
                            validator: (v) =>
                                v == null ? 'الوحدة الأساسية مطلوبة' : null,
                          ),
                          loading: () =>
                              const CircularProgressIndicator(strokeWidth: 2),
                          error: (e, _) => Text('خطأ: $e'),
                        ),
                        SizedBox(height: 12.h),
                        TextFormField(
                          controller: _factorController,
                          decoration: const InputDecoration(
                            labelText: 'معامل التحويل *',
                            border: OutlineInputBorder(),
                            hintText: 'مثال: 1000 (جرام → كيلوجرام)',
                          ),
                          keyboardType: const TextInputType.numberWithOptions(
                              decimal: true),
                          validator: (v) {
                            if (_isBaseUnit) return null;
                            if (v == null || v.trim().isEmpty) {
                              return 'معامل التحويل مطلوب';
                            }
                            final val = double.tryParse(v);
                            return val == null || val <= 0
                                ? 'أدخل رقمًا صحيحًا أكبر من صفر'
                                : null;
                          },
                        ),
                      ],
                      SizedBox(height: 12.h),
                      CheckboxListTile(
                        title: const Text('وحدة نشطة'),
                        value: _isActive,
                        onChanged: (v) => setState(() => _isActive = v!),
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            // Actions
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed:
                        _isLoading ? null : () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _submit,
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2))
                        : Text(widget.unit != null ? 'تحديث' : 'حفظ'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getUnitTypeDisplayName(UnitType type) {
    switch (type) {
      case UnitType.weight:
        return 'وزن';
      case UnitType.length:
        return 'طول';
      case UnitType.volume:
        return 'حجم';
      case UnitType.area:
        return 'مساحة';
      case UnitType.count:
        return 'عدد';
      case UnitType.time:
        return 'وقت';
      case UnitType.other:
        return 'أخرى';
    }
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final notifier = ref.read(unitManagementProvider.notifier);

      if (widget.unit != null) {
        // تعديل وحدة موجودة
        final success = await notifier.updateUnit(
          id: widget.unit!.id,
          name: _nameController.text.trim(),
          symbol: _symbolController.text.trim(),
          abbreviation: _abbreviationController.text.trim(),
          description: _descriptionController.text.trim(),
          factor: _isBaseUnit ? 1.0 : double.parse(_factorController.text),
          baseUnitId: _isBaseUnit ? null : _selectedBaseUnitId,
          unitType: _selectedType.toString().split('.').last,
          isBaseUnit: _isBaseUnit,
          isActive: _isActive,
        );

        if (mounted) {
          if (success) {
            Navigator.of(context).pop();
            context.showSuccessSnackBar('تم التحديث بنجاح');
          } else {
            context.showErrorSnackBar('فشل في التحديث');
          }
        }
      } else if (widget.baseUnit != null) {
        // إضافة وحدة مشتقة
        final success = await notifier.createDerivedUnit(
          name: _nameController.text.trim(),
          symbol: _symbolController.text.trim(),
          abbreviation: _abbreviationController.text.trim(),
          baseUnitId: widget.baseUnit!.id,
          factor: double.parse(_factorController.text),
          description: _descriptionController.text.trim(),
          isActive: _isActive,
        );

        if (mounted) {
          if (success) {
            Navigator.of(context).pop();
            context.showSuccessSnackBar('تمت الإضافة بنجاح');
          } else {
            context.showErrorSnackBar('فشل في الإضافة');
          }
        }
      } else {
        // إضافة وحدة جديدة
        final success = await notifier.createUnit(
          name: _nameController.text.trim(),
          symbol: _symbolController.text.trim(),
          abbreviation: _abbreviationController.text.trim(),
          unitType: _selectedType.toString().split('.').last,
          isBaseUnit: _isBaseUnit,
          baseUnitId: _isBaseUnit ? null : _selectedBaseUnitId,
          factor: _isBaseUnit ? 1.0 : double.parse(_factorController.text),
          description: _descriptionController.text.trim(),
          isActive: _isActive,
        );

        if (mounted) {
          if (success) {
            Navigator.of(context).pop();
            context.showSuccessSnackBar('تمت الإضافة بنجاح');
          } else {
            context.showErrorSnackBar('فشل في الإضافة');
          }
        }
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar('حدث خطأ: ${e.toString()}');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
