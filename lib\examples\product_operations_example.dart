// import '../services/business_service.dart';
// import '../data/repositories/product_repository.dart';
// import '../data/repositories/category_repository.dart';
// import '../data/repositories/unit_repository.dart';

// import '../core/utils/app_utils.dart';
// import '../core/exceptions/app_exceptions.dart';

// /// مثال شامل لعمليات إدارة المنتجات
// /// يوضح كيفية إضافة وتعديل وحذف المنتجات باستخدام BusinessService
// class ProductOperationsExample {
//   final BusinessService _businessService = BusinessService();
//   final ProductRepository _productRepository = ProductRepository();
//   final CategoryRepository _categoryRepository = CategoryRepository();
//   final UnitRepository _unitRepository = UnitRepository();

//   /// مثال شامل: إضافة منتج جديد
//   Future<void> addProductExample() async {
//     try {
//       AppUtils.logInfo('=== مثال إضافة منتج جديد ===');

//       // الحصول على الفئات والوحدات المتاحة
//       final categories = await _categoryRepository.getActiveCategories();
//       final units = await _unitRepository.getActiveUnits();

//       if (categories.isEmpty || units.isEmpty) {
//         AppUtils.logWarning('يجب إضافة فئات ووحدات قياس أولاً');
//         return;
//       }

//       // إضافة منتج جديد
//       final productId = await _businessService.addNewProduct(
//         nameAr: 'لابتوب ديل XPS 13',
//         nameEn: 'Dell XPS 13 Laptop',
//         barcode: '1234567890123',
//         description: 'لابتوب عالي الأداء مع معالج Intel Core i7',
//         categoryId: categories.first.id,
//         unitId: units.first.id,
//         costPrice: 800.0,
//         sellingPrice: 1200.0,
//         minStock: 5,
//         maxStock: 50,
//         reorderPoint: 10,
//         imageUrl: 'https://example.com/laptop.jpg',
//       );

//       AppUtils.logInfo('تم إضافة المنتج بنجاح - ID: $productId');

//       // التحقق من إضافة المنتج
//       final addedProduct = await _productRepository.getProductById(productId);
//       if (addedProduct != null) {
//         AppUtils.logInfo('تفاصيل المنتج المضاف:');
//         AppUtils.logInfo('الاسم: ${addedProduct.nameAr}');
//         AppUtils.logInfo('سعر التكلفة: ${addedProduct.costPrice}');
//         AppUtils.logInfo('سعر البيع: ${addedProduct.sellingPrice}');
//       }
//     } catch (e) {
//       AppUtils.logError('خطأ في مثال إضافة المنتج', e);
//     }
//   }

//   /// مثال شامل: تعديل منتج موجود
//   Future<void> updateProductExample() async {
//     try {
//       AppUtils.logInfo('=== مثال تعديل منتج موجود ===');

//       // الحصول على أول منتج متاح
//       final products = await _productRepository.getActiveProducts();
//       if (products.isEmpty) {
//         AppUtils.logWarning('لا توجد منتجات متاحة للتعديل');
//         return;
//       }

//       final product = products.first;
//       AppUtils.logInfo('تعديل المنتج: ${product.nameAr}');

//       // تعديل المنتج
//       final success = await _businessService.updateProduct(
//         productId: product.id,
//         nameAr: '${product.nameAr} - محدث',
//         sellingPrice: product.sellingPrice + 100, // زيادة السعر
//         minStock: 15, // تحديث الحد الأدنى للمخزون
//         reorderPoint: 20, // تحديث نقطة إعادة الطلب
//       );

//       if (success) {
//         AppUtils.logInfo('تم تعديل المنتج بنجاح');

//         // التحقق من التعديل
//         final updatedProduct =
//             await _productRepository.getProductById(product.id);
//         if (updatedProduct != null) {
//           AppUtils.logInfo('تفاصيل المنتج بعد التعديل:');
//           AppUtils.logInfo('الاسم الجديد: ${updatedProduct.nameAr}');
//           AppUtils.logInfo('السعر الجديد: ${updatedProduct.sellingPrice}');
//           AppUtils.logInfo('الحد الأدنى للمخزون: ${updatedProduct.minStock}');
//         }
//       } else {
//         AppUtils.logError('فشل في تعديل المنتج', null);
//       }
//     } catch (e) {
//       AppUtils.logError('خطأ في مثال تعديل المنتج', e);
//     }
//   }

//   /// مثال شامل: إلغاء تفعيل منتج
//   Future<void> deactivateProductExample() async {
//     try {
//       AppUtils.logInfo('=== مثال إلغاء تفعيل منتج ===');

//       // الحصول على منتج متاح
//       final products = await _productRepository.getActiveProducts();
//       if (products.isEmpty) {
//         AppUtils.logWarning('لا توجد منتجات متاحة لإلغاء التفعيل');
//         return;
//       }

//       final product = products.last; // اختيار آخر منتج
//       AppUtils.logInfo('إلغاء تفعيل المنتج: ${product.nameAr}');

//       // إلغاء تفعيل المنتج
//       final success = await _businessService.deactivateProduct(product.id);

//       if (success) {
//         AppUtils.logInfo('تم إلغاء تفعيل المنتج بنجاح');

//         // التحقق من إلغاء التفعيل
//         final deactivatedProduct =
//             await _productRepository.getProductById(product.id);
//         if (deactivatedProduct != null) {
//           AppUtils.logInfo(
//               'حالة المنتج: ${deactivatedProduct.isActive ? "مفعل" : "غير مفعل"}');
//         }
//       } else {
//         AppUtils.logError('فشل في إلغاء تفعيل المنتج', null);
//       }
//     } catch (e) {
//       AppUtils.logError('خطأ في مثال إلغاء تفعيل المنتج', e);
//     }
//   }

//   /// مثال شامل: حذف منتج
//   Future<void> deleteProductExample() async {
//     try {
//       AppUtils.logInfo('=== مثال حذف منتج ===');

//       // إنشاء منتج تجريبي للحذف
//       final testProductId = await _businessService.addNewProduct(
//         nameAr: 'منتج تجريبي للحذف',
//         nameEn: 'Test Product for Deletion',
//         costPrice: 10.0,
//         sellingPrice: 15.0,
//       );

//       AppUtils.logInfo('تم إنشاء منتج تجريبي للحذف - ID: $testProductId');

//       // محاولة حذف المنتج
//       final success = await _businessService.deleteProduct(testProductId);

//       if (success) {
//         AppUtils.logInfo('تم حذف المنتج التجريبي بنجاح');

//         // التحقق من الحذف
//         final deletedProduct =
//             await _productRepository.getProductById(testProductId);
//         if (deletedProduct == null) {
//           AppUtils.logInfo('تأكيد: المنتج لم يعد موجوداً في قاعدة البيانات');
//         }
//       } else {
//         AppUtils.logError('فشل في حذف المنتج', null);
//       }
//     } catch (e) {
//       if (e is BusinessLogicException) {
//         AppUtils.logWarning('لا يمكن حذف المنتج: ${e.message}');
//       } else {
//         AppUtils.logError('خطأ في مثال حذف المنتج', e);
//       }
//     }
//   }

//   /// مثال شامل: إضافة دفعة من المنتجات
//   Future<void> addProductsBatchExample() async {
//     try {
//       AppUtils.logInfo('=== مثال إضافة دفعة من المنتجات ===');

//       // الحصول على الفئات والوحدات
//       final categories = await _categoryRepository.getActiveCategories();
//       final units = await _unitRepository.getActiveUnits();

//       if (categories.isEmpty || units.isEmpty) {
//         AppUtils.logWarning('يجب إضافة فئات ووحدات قياس أولاً');
//         return;
//       }

//       final categoryId = categories.first.id;
//       final unitId = units.first.id;

//       // قائمة المنتجات للإضافة
//       final productsData = [
//         {
//           'nameAr': 'ماوس لاسلكي',
//           'nameEn': 'Wireless Mouse',
//           'costPrice': 15.0,
//           'sellingPrice': 25.0,
//           'categoryId': categoryId,
//           'unitId': unitId,
//         },
//         {
//           'nameAr': 'لوحة مفاتيح ميكانيكية',
//           'nameEn': 'Mechanical Keyboard',
//           'costPrice': 50.0,
//           'sellingPrice': 80.0,
//           'categoryId': categoryId,
//           'unitId': unitId,
//         },
//         {
//           'nameAr': 'سماعات بلوتوث',
//           'nameEn': 'Bluetooth Headphones',
//           'costPrice': 30.0,
//           'sellingPrice': 50.0,
//           'categoryId': categoryId,
//           'unitId': unitId,
//         },
//       ];

//       // إضافة المنتجات
//       final productIds = <String>[];
//       for (final productData in productsData) {
//         try {
//           final productId = await _businessService.addNewProduct(
//             nameAr: productData['nameAr'] as String,
//             nameEn: productData['nameEn'] as String,
//             costPrice: productData['costPrice'] as double,
//             sellingPrice: productData['sellingPrice'] as double,
//             categoryId: productData['categoryId'] as String,
//             unitId: productData['unitId'] as String,
//           );
//           productIds.add(productId);
//           AppUtils.logInfo('تم إضافة: ${productData['nameAr']}');
//         } catch (e) {
//           AppUtils.logError('فشل في إضافة: ${productData['nameAr']}', e);
//         }
//       }

//       AppUtils.logInfo(
//           'تم إضافة ${productIds.length} منتج من أصل ${productsData.length}');
//     } catch (e) {
//       AppUtils.logError('خطأ في مثال إضافة دفعة المنتجات', e);
//     }
//   }

//   /// تشغيل جميع الأمثلة
//   Future<void> runAllExamples() async {
//     try {
//       AppUtils.logInfo('🚀 بدء تشغيل أمثلة عمليات المنتجات');

//       await addProductExample();
//       await Future.delayed(const Duration(seconds: 1));

//       await updateProductExample();
//       await Future.delayed(const Duration(seconds: 1));

//       await deactivateProductExample();
//       await Future.delayed(const Duration(seconds: 1));

//       await deleteProductExample();
//       await Future.delayed(const Duration(seconds: 1));

//       await addProductsBatchExample();

//       AppUtils.logInfo('✅ تم الانتهاء من جميع أمثلة عمليات المنتجات');
//     } catch (e) {
//       AppUtils.logError('خطأ في تشغيل أمثلة عمليات المنتجات', e);
//     }
//   }
// }

// /// دالة مساعدة لتشغيل الأمثلة
// Future<void> runProductOperationsExamples() async {
//   final example = ProductOperationsExample();
//   await example.runAllExamples();
// }
