import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tijari_tech/core/theme/colors.dart';
import 'package:tijari_tech/core/theme/text_styles.dart';
import 'package:tijari_tech/services/excel_service.dart';

/// حوار إرشادات استيراد البيانات من Excel
class ExcelImportDialog extends StatefulWidget {
  const ExcelImportDialog({super.key});

  @override
  State<ExcelImportDialog> createState() => _ExcelImportDialogState();
}

class _ExcelImportDialogState extends State<ExcelImportDialog> {
  bool _importFromFile = true;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 700.w,
        height: 800.h,
        padding: EdgeInsets.all(24.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            _buildHeader(),
            SizedBox(height: 20.h),

            // Import Options
            _buildImportOptions(),
            SizedBox(height: 20.h),

            // Instructions
            Expanded(
              child: _buildInstructions(),
            ),

            SizedBox(height: 20.h),

            // Action Buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// بناء رأس الحوار
  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(12.r),
          decoration: BoxDecoration(
            color: AppColors.success.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Icon(
            Icons.file_upload,
            color: AppColors.success,
            size: 28.r,
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'استيراد من ملف خارجي',
                style: AppTextStyles.titleLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.success,
                ),
              ),
              Text(
                'تعليمات تحضير ملف Excel للاستيراد',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  /// بناء خيارات الاستيراد
  Widget _buildImportOptions() {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppColors.info.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'خيارات الاستيراد',
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.info,
            ),
          ),
          SizedBox(height: 12.h),
          CheckboxListTile(
            title: const Text('استيراد رقم الصنف من الملف'),
            subtitle: const Text('إذا كان متوفراً في العمود الأول'),
            value: _importFromFile,
            onChanged: (value) {
              setState(() {
                _importFromFile = value ?? true;
              });
            },
            controlAffinity: ListTileControlAffinity.leading,
          ),
        ],
      ),
    );
  }

  /// بناء التعليمات
  Widget _buildInstructions() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Excel Table Preview
          _buildExcelPreview(),
          SizedBox(height: 20.h),

          // Instructions Text
          _buildInstructionsText(),
        ],
      ),
    );
  }

  /// بناء معاينة جدول Excel
  Widget _buildExcelPreview() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8.r),
                topRight: Radius.circular(8.r),
              ),
            ),
            child: Row(
              children: [
                Expanded(child: _buildTableHeader('رقم')),
                Expanded(child: _buildTableHeader('الباركود')),
                Expanded(child: _buildTableHeader('اسم المنتج')),
                Expanded(child: _buildTableHeader('كمية أسعار')),
                Expanded(child: _buildTableHeader('سعر البيع')),
                Expanded(child: _buildTableHeader('الكمية')),
                Expanded(child: _buildTableHeader('المجموعة')),
              ],
            ),
          ),

          // Sample Rows
          _buildSampleRow(
              '1', '100001', 'خبز طازج وسط رغيف', '4958', '99999', '1'),
          _buildSampleRow(
              '2', '100011', 'خبز طازج جاهز رغيف', '7097', '88888', '1'),
          _buildSampleRow(
              '3', '100021', 'خبز طازج وسط شريحة', '16336', '77777', ''),
          _buildSampleRow(
              '4', '100024', 'خبز طازج وسط بذور رغيف', '12047', '66666', ''),
          _buildSampleRow(
              '5', '100023', 'خبز طازج وسط بذور شريحة', '12234', '55555', ''),
          _buildSampleRow(
              '6', '100002', 'خبز طازج وسط رغيف', '19123', '44444', '1'),
          _buildSampleRow(
              '7', '100003', 'خبز طازج وسط 100 رغيف', '22730', '33333', '1'),
          _buildSampleRow(
              '8', '100004', 'خبز طازج وسط بذور رغيف 2', '29181', '22222', '1'),
          _buildSampleRow(
              '9', '100005', 'خبز طازج وسط كامل رغيف', '39790', '11111', '1'),
        ],
      ),
    );
  }

  /// بناء رأس الجدول
  Widget _buildTableHeader(String text) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 4.w),
      child: Text(
        text,
        style: AppTextStyles.bodySmall.copyWith(
          fontWeight: FontWeight.bold,
          color: AppColors.primary,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// بناء صف عينة
  Widget _buildSampleRow(String num, String barcode, String name, String price,
      String sellPrice, String quantity) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(child: _buildTableCell(num)),
          Expanded(child: _buildTableCell(barcode)),
          Expanded(child: _buildTableCell(name)),
          Expanded(child: _buildTableCell(price)),
          Expanded(child: _buildTableCell(sellPrice)),
          Expanded(child: _buildTableCell(quantity)),
          Expanded(child: _buildTableCell('')),
        ],
      ),
    );
  }

  /// بناء خلية الجدول
  Widget _buildTableCell(String text) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 4.w),
      child: Text(
        text,
        style: AppTextStyles.bodySmall,
        textAlign: TextAlign.center,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// بناء نص التعليمات
  Widget _buildInstructionsText() {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppColors.warning.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.warning,
                size: 20.r,
              ),
              SizedBox(width: 8.w),
              Text(
                'تعليمات مهمة',
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.warning,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          _buildInstructionItem(
            'يتم استيراد البيانات من ملف أكسل بشكل تسلسلي حسب ترتيب الأعمدة المرفقة حسب الجدول المرجعي.',
          ),
          _buildInstructionItem(
            'العمود الأول رقم الصنف ويجب أن يكون رقماً',
          ),
          _buildInstructionItem(
            'العمود الثاني اسم الصنف',
          ),
          _buildInstructionItem(
            'العمود الثالث أسعار الصنف',
          ),
          _buildInstructionItem(
            'العمود الرابع الكمية',
          ),
          _buildInstructionItem(
            'العمود الخامس سعر البيع',
          ),
          _buildInstructionItem(
            'العمود السادس الكمية',
          ),
          _buildInstructionItem(
            'العمود السابع رقم المجموعة أو اتركها فارغة ستكون ضمن المجموعة العامة',
          ),
          SizedBox(height: 16.h),
          Container(
            padding: EdgeInsets.all(12.r),
            decoration: BoxDecoration(
              color: AppColors.error.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'ملاحظات مهمة:',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.error,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'كما يجب أن يبدأ صفوف الملف من الصف رقم 2 ويترك الصف الأول',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.error,
                  ),
                ),
                Text(
                  'كما هو موضح بالترتيب أعلاه',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.error,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر تعليمات
  Widget _buildInstructionItem(String text) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(top: 6.h, left: 8.w),
            width: 4.w,
            height: 4.h,
            decoration: BoxDecoration(
              color: AppColors.warning,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: AppTextStyles.bodySmall.copyWith(
                color: Colors.grey[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close),
            label: const Text('إلغاء'),
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          flex: 2,
          child: ElevatedButton.icon(
            onPressed: () async {
              Navigator.of(context).pop();
              await _importFromExcel();
            },
            icon: const Icon(Icons.file_upload),
            label: const Text('فضلاً قم بتحديد الملف الذي يحتوي على البيانات'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  /// استيراد البيانات من Excel
  Future<void> _importFromExcel() async {
    try {
      // استيراد البيانات
      final importedData = await ExcelService.importProductsFromExcel();

      if (importedData.isEmpty) {
        _showMessage('لا توجد بيانات صالحة في الملف', Colors.orange);
        return;
      }

      // التحقق من صحة البيانات
      final errors = ExcelService.validateImportData(importedData);

      if (errors.isNotEmpty) {
        _showErrorDialog(errors);
        return;
      }

      // TODO: حفظ البيانات في قاعدة البيانات
      _showMessage(
          'تم استيراد ${importedData.length} منتج بنجاح', Colors.green);
    } catch (e) {
      _showMessage('فشل في الاستيراد: ${e.toString()}', Colors.red);
    }
  }

  /// إظهار رسالة
  void _showMessage(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// إظهار حوار الأخطاء
  void _showErrorDialog(List<String> errors) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('أخطاء في البيانات'),
        content: SizedBox(
          width: 400,
          height: 300,
          child: ListView.builder(
            itemCount: errors.length,
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Text(
                  '• ${errors[index]}',
                  style: const TextStyle(color: Colors.red),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
