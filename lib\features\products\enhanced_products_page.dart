import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:tijari_tech/data/models/product.dart';
import 'package:tijari_tech/examples/business_operations_example.dart';
import 'package:tijari_tech/features/import_export/excel_export_dialog.dart';
import 'package:tijari_tech/features/import_export/excel_import_dialog.dart';
import 'package:tijari_tech/features/inventory/stock_management_dialog.dart';
import 'package:tijari_tech/router/routes.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/extensions.dart';
import '../../providers/enhanced_product_provider.dart';
import '../../widgets/main_layout.dart';
// import 'add_product_dialog.dart';

class EnhancedProductsPage extends ConsumerStatefulWidget {
  const EnhancedProductsPage({super.key});

  @override
  ConsumerState<EnhancedProductsPage> createState() =>
      _EnhancedProductsPageState();
}

class _EnhancedProductsPageState extends ConsumerState<EnhancedProductsPage> {
  final _searchController = TextEditingController();
  final _scrollController = ScrollController();

  // Table state
  int _sortColumnIndex = 0;
  bool _sortAscending = true;
  final Set<String> _selectedProducts = {};
  bool _selectAll = false;

  // Filter state
  String? _selectedCategory;
  String? _selectedUnit;
  String _stockFilter = 'all'; // all, low, out, available
  double? _minPrice;
  double? _maxPrice;
  bool _showInactive = false;

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final productState = ref.watch(enhancedProductManagementProvider);

    return MainLayout(
      title: 'إدارة المنتجات المتقدمة',
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAddProductDialog(context),
        icon: const Icon(Icons.add),
        label: const Text('منتج جديد'),
      ),
      child: Column(
        children: [
          // Filters and search bar
          _buildFiltersSection(context, productState),
          SizedBox(height: 16.h),

          // Action buttons
          _buildActionButtons(context, productState),
          SizedBox(height: 16.h),

          // Products table
          Expanded(
            child: _buildProductsTable(context, productState),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection(
      BuildContext context, EnhancedProductManagementState state) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          children: [
            // Search and Filters bar - Responsive Design
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  // Search Field
                  Container(
                    width: MediaQuery.of(context).size.width *
                        0.6, // 60% of screen width
                    padding: EdgeInsets.symmetric(horizontal: 8.w),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'البحث في المنتجات (الاسم، الباركود، SKU)...',
                        prefixIcon: const Icon(Icons.search),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () {
                                  _searchController.clear();
                                  ref
                                      .read(enhancedProductManagementProvider
                                          .notifier)
                                      .searchProducts('');
                                },
                              )
                            : null,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        contentPadding: EdgeInsets.symmetric(
                            vertical: 12.h, horizontal: 12.w),
                      ),
                      onChanged: (value) {
                        ref
                            .read(enhancedProductManagementProvider.notifier)
                            .searchProducts(value);
                      },
                    ),
                  ),

                  // Quick Filters
                  Container(
                    width: MediaQuery.of(context).size.width *
                        0.4, // 40% of screen width
                    padding: EdgeInsets.symmetric(horizontal: 8.w),
                    child: Row(
                      children: [
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _stockFilter,
                            isExpanded: true,
                            decoration: InputDecoration(
                              labelText: 'حالة المخزون',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 12.h, horizontal: 12.w),
                            ),
                            items: const [
                              DropdownMenuItem(
                                  value: 'all', child: Text('الكل')),
                              DropdownMenuItem(
                                  value: 'available', child: Text('متوفر')),
                              DropdownMenuItem(
                                  value: 'low', child: Text('مخزون منخفض')),
                              DropdownMenuItem(
                                  value: 'out', child: Text('نفد المخزون')),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _stockFilter = value ?? 'all';
                              });
                              _applyFilters();
                            },
                          ),
                        ),
                        SizedBox(width: 8.w),
                        IconButton(
                          icon: const Icon(Icons.filter_list),
                          onPressed: () => _showAdvancedFilters(context),
                          tooltip: 'فلاتر متقدمة',
                          padding: EdgeInsets.all(12.w),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 12.h),
            // Filter chips
            Wrap(
              spacing: 8.w,
              runSpacing: 4.h,
              children: [
                FilterChip(
                  label: const Text('إظهار غير النشطة'),
                  selected: _showInactive,
                  onSelected: (selected) {
                    setState(() {
                      _showInactive = selected;
                    });
                    _applyFilters();
                  },
                ),
                if (_selectedCategory != null)
                  Chip(
                    label: Text('الفئة: $_selectedCategory'),
                    deleteIcon: const Icon(Icons.close, size: 16),
                    onDeleted: () {
                      setState(() {
                        _selectedCategory = null;
                      });
                      _applyFilters();
                    },
                  ),
                if (_selectedUnit != null)
                  Chip(
                    label: Text('الوحدة: $_selectedUnit'),
                    deleteIcon: const Icon(Icons.close, size: 16),
                    onDeleted: () {
                      setState(() {
                        _selectedUnit = null;
                      });
                      _applyFilters();
                    },
                  ),
                if (_minPrice != null || _maxPrice != null)
                  Chip(
                    label:
                        Text('السعر: ${_minPrice ?? 0} - ${_maxPrice ?? '∞'}'),
                    deleteIcon: const Icon(Icons.close, size: 16),
                    onDeleted: () {
                      setState(() {
                        _minPrice = null;
                        _maxPrice = null;
                      });
                      _applyFilters();
                    },
                  ),
                Chip(
                  label: Text('${state.filteredProducts.length} منتج'),
                  backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(
      BuildContext context, EnhancedProductManagementState state) {
    return Row(
      children: [
        // Bulk actions
        if (_selectedProducts.isNotEmpty) ...[
          ElevatedButton.icon(
            onPressed: () => _showBulkEditDialog(context),
            icon: const Icon(Icons.edit),
            label: Text('تعديل متعدد (${_selectedProducts.length})'),
          ),
          SizedBox(width: 8.w),
          ElevatedButton.icon(
            onPressed: () => _showBulkDeleteDialog(context),
            icon: const Icon(Icons.delete),
            label: Text('حذف متعدد (${_selectedProducts.length})'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
          ),
          SizedBox(width: 8.w),
        ],

        const Spacer(),

        // Export and import buttons
        OutlinedButton.icon(
          onPressed: () => _exportProducts(context),
          icon: const Icon(Icons.download),
          label: const Text('تصدير'),
        ),
        SizedBox(width: 8.w),
        OutlinedButton.icon(
          onPressed: () => _importProducts(context),
          icon: const Icon(Icons.upload),
          label: const Text('استيراد'),
        ),
        SizedBox(width: 8.w),
        IconButton(
          onPressed: () {
            ref.read(enhancedProductManagementProvider.notifier).refresh();
          },
          icon: const Icon(Icons.refresh),
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  Widget _buildProductsTable(
      BuildContext context, EnhancedProductManagementState state) {
    if (state.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            SizedBox(height: 16),
            Text(
              'حدث خطأ: ${state.error}',
              style: TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref.read(enhancedProductManagementProvider.notifier).refresh();
              },
              child: Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (state.filteredProducts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 64.r,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16.h),
            Text(
              state.searchQuery.isNotEmpty ? 'لا توجد نتائج' : 'لا توجد منتجات',
              style: AppTextStyles.titleMedium.copyWith(
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              state.searchQuery.isNotEmpty
                  ? 'جرب البحث بكلمات أخرى أو تعديل الفلاتر'
                  : 'ابدأ بإضافة منتج جديد',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[500],
              ),
            ),
            if (state.searchQuery.isEmpty) ...[
              SizedBox(height: 16.h),
              ElevatedButton(
                onPressed: () => _showAddProductDialog(context),
                child: const Text('إضافة منتج'),
              ),
            ],
          ],
        ),
      );
    }

    return Card(
      child: Column(
        children: [
          // Table header with select all
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
            ),
            child: Row(
              children: [
                Checkbox(
                  value: _selectAll,
                  onChanged: (value) {
                    setState(() {
                      _selectAll = value ?? false;
                      if (_selectAll) {
                        _selectedProducts.addAll(
                          state.filteredProducts.map((p) => p.id),
                        );
                      } else {
                        _selectedProducts.clear();
                      }
                    });
                  },
                ),
                SizedBox(width: 8.w),
                Text(
                  'تحديد الكل',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  '${state.filteredProducts.length} من ${state.products.length} منتج',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          // Data table
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SingleChildScrollView(
                child: DataTable(
                  sortColumnIndex: _sortColumnIndex,
                  sortAscending: _sortAscending,
                  showCheckboxColumn: false,
                  columns: _buildTableColumns(),
                  rows: _buildTableRows(state.filteredProducts),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<DataColumn> _buildTableColumns() {
    return [
      const DataColumn(
        label: Text(''),
      ),
      DataColumn(
        label: const Text('المنتج'),
        onSort: (columnIndex, ascending) =>
            _onSort(columnIndex, ascending, 'name'),
      ),
      DataColumn(
        label: const Text('الباركود'),
        onSort: (columnIndex, ascending) =>
            _onSort(columnIndex, ascending, 'barcode'),
      ),
      const DataColumn(
        label: Text('الفئة'),
      ),
      const DataColumn(
        label: Text('الوحدة'),
      ),
      DataColumn(
        label: const Text('سعر التكلفة'),
        numeric: true,
        onSort: (columnIndex, ascending) =>
            _onSort(columnIndex, ascending, 'cost_price'),
      ),
      DataColumn(
        label: const Text('سعر البيع'),
        numeric: true,
        onSort: (columnIndex, ascending) =>
            _onSort(columnIndex, ascending, 'selling_price'),
      ),
      const DataColumn(
        label: Text('المخزون'),
        numeric: true,
      ),
      const DataColumn(
        label: Text('الحالة'),
      ),
      const DataColumn(
        label: Text('الإجراءات'),
      ),
    ];
  }

  List<DataRow> _buildTableRows(List<Product> products) {
    return products.map((product) {
      final isSelected = _selectedProducts.contains(product.id);

      return DataRow(
        selected: isSelected,
        onSelectChanged: (selected) {
          setState(() {
            if (selected == true) {
              _selectedProducts.add(product.id);
            } else {
              _selectedProducts.remove(product.id);
            }
            _selectAll = _selectedProducts.length == products.length;
          });
        },
        cells: [
          // Checkbox
          DataCell(
            Checkbox(
              value: isSelected,
              onChanged: (selected) {
                setState(() {
                  if (selected == true) {
                    _selectedProducts.add(product.id);
                  } else {
                    _selectedProducts.remove(product.id);
                  }
                  _selectAll = _selectedProducts.length == products.length;
                });
              },
            ),
          ),

          // Product info - قابل للنقر للتعديل
          DataCell(
            InkWell(
              onTap: () => _editProduct(context, product),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    product.getDisplayName(),
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.bold,
                      color:
                          AppColors.primary, // لون مختلف للإشارة أنه قابل للنقر
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (product.sku != null)
                    Text(
                      'SKU: ${product.sku}',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Barcode
          DataCell(
            Text(
              product.barcode ?? '-',
              style: AppTextStyles.bodySmall,
            ),
          ),

          // Category
          DataCell(
            Text(
              product.categoryName ?? '-',
              style: AppTextStyles.bodySmall,
            ),
          ),

          // Unit
          DataCell(
            Text(
              product.unitName ?? '-',
              style: AppTextStyles.bodySmall,
            ),
          ),

          // Cost price
          DataCell(
            Text(
              '${product.costPrice.toStringAsFixed(2)} ر.س',
              style: AppTextStyles.bodySmall,
            ),
          ),

          // Selling price
          DataCell(
            Text(
              '${product.sellingPrice.toStringAsFixed(2)} ر.س',
              style: AppTextStyles.bodySmall,
            ),
          ),

          // Stock
          DataCell(
            _buildStockCell(product),
          ),

          // Status
          DataCell(
            _buildStatusChip(product),
          ),

          // Actions
          DataCell(
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit, size: 16),
                  onPressed: () => _showEditProductDialog(context, product),
                  tooltip: 'تعديل',
                ),
                IconButton(
                  icon: const Icon(Icons.delete, size: 16),
                  onPressed: () => _showDeleteConfirmation(context, product),
                  tooltip: 'حذف',
                ),
                IconButton(
                  icon: const Icon(Icons.more_vert, size: 16),
                  onPressed: () => _showProductMenu(context, product),
                  tooltip: 'المزيد',
                ),
              ],
            ),
          ),
        ],
      );
    }).toList();
  }

  Widget _buildStockCell(Product product) {
    if (!product.trackStock) {
      return const Text(
        'غير متتبع',
        style: TextStyle(color: Colors.grey),
      );
    }

    final stock = product.currentStock ?? 0;
    Color color = Colors.green;

    if (product.isOutOfStock) {
      color = Colors.red;
    } else if (product.isLowStock) {
      color = Colors.orange;
    }

    return Text(
      stock.toStringAsFixed(0),
      style: TextStyle(
        color: color,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildStatusChip(Product product) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: product.isActiveProduct ? Colors.green : Colors.grey,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Text(
        product.isActiveProduct ? 'نشط' : 'غير نشط',
        style: AppTextStyles.bodySmall.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  void _onSort(int columnIndex, bool ascending, String field) {
    setState(() {
      _sortColumnIndex = columnIndex;
      _sortAscending = ascending;
    });

    ref
        .read(enhancedProductManagementProvider.notifier)
        .sortProducts(field, ascending);
  }

  void _applyFilters() {
    ref.read(enhancedProductManagementProvider.notifier).applyFilters(
          categoryId: _selectedCategory,
          unitId: _selectedUnit,
          stockFilter: _stockFilter,
          minPrice: _minPrice,
          maxPrice: _maxPrice,
          showInactive: _showInactive,
        );
  }

  // Dialog and action methods
  void _showAddProductDialog(BuildContext context) {
    context.go(AppRoutes.productAdd);
    // addSampleProducts();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('إضافة المنتجات قيد التطوير'),
      ),
    );
  }

  void _showEditProductDialog(BuildContext context, Product product) {
    context.go('${AppRoutes.productEdit}/${product.id}');

    context.showInfoSnackBar('تعديل المنتج قيد التطوير');
  }

  void _showDeleteConfirmation(BuildContext context, Product product) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content:
            Text('هل أنت متأكد من حذف منتج "${product.getDisplayName()}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref
                  .read(enhancedProductManagementProvider.notifier)
                  .deleteProduct(product.id);
              context.showSuccessSnackBar('تم حذف المنتج بنجاح');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showProductMenu(BuildContext context, Product product) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) => SafeArea(
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'إجراءات المنتج',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 16.h),

              // عرض تفاصيل المنتج
              ListTile(
                leading: const Icon(Icons.visibility, color: AppColors.info),
                title: const Text('عرض التفاصيل'),
                subtitle: const Text('عرض جميع تفاصيل المنتج'),
                onTap: () {
                  Navigator.pop(context);
                  _showProductDetails(context, product);
                },
              ),

              // تعديل المنتج
              ListTile(
                leading: const Icon(Icons.edit, color: AppColors.primary),
                title: const Text('تعديل المنتج'),
                subtitle: const Text('تعديل معلومات المنتج'),
                onTap: () {
                  Navigator.pop(context);
                  _editProduct(context, product);
                },
              ),

              // إدارة المخزون
              ListTile(
                leading: const Icon(Icons.inventory, color: AppColors.warning),
                title: const Text('إدارة المخزون'),
                subtitle: const Text('إضافة أو تعديل كمية المخزون'),
                onTap: () {
                  Navigator.pop(context);
                  _manageStock(context, product);
                },
              ),

              // حذف المنتج
              ListTile(
                leading: const Icon(Icons.delete, color: AppColors.error),
                title: const Text('حذف المنتج'),
                subtitle: const Text('حذف المنتج نهائياً'),
                onTap: () {
                  Navigator.pop(context);
                  _showDeleteConfirmation(context, product);
                },
              ),

              SizedBox(height: 8.h),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// عرض تفاصيل المنتج
  void _showProductDetails(BuildContext context, Product product) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل المنتج: ${product.getDisplayName()}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('الاسم العربي', product.nameAr),
              if (product.nameEn != null)
                _buildDetailRow('الاسم الإنجليزي', product.nameEn!),
              if (product.barcode != null)
                _buildDetailRow('الباركود', product.barcode!),
              if (product.sku != null)
                _buildDetailRow('رمز المنتج', product.sku!),
              _buildDetailRow('سعر التكلفة', '${product.costPrice} ر.س'),
              _buildDetailRow('سعر البيع', '${product.sellingPrice} ر.س'),
              if (product.description != null)
                _buildDetailRow('الوصف', product.description!),
              _buildDetailRow(
                  'الحالة', product.isActiveProduct ? 'نشط' : 'غير نشط'),
              _buildDetailRow(
                  'تتبع المخزون', product.trackStock ? 'نعم' : 'لا'),
              if (product.trackStock) ...[
                _buildDetailRow(
                    'الكمية الحالية', '${product.currentStock ?? 0}'),
                _buildDetailRow('الحد الأدنى', '${product.minStock}'),
                _buildDetailRow('الحد الأقصى', '${product.maxStock}'),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// بناء صف تفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100.w,
            child: Text(
              '$label:',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  /// تعديل المنتج
  void _editProduct(BuildContext context, Product product) {
    context.go('/inventory/products/edit/${product.id}');
  }

  /// إدارة المخزون
  void _manageStock(BuildContext context, Product product) {
    showDialog(
      context: context,
      builder: (context) => StockManagementDialog(product: product),
    );
  }

  void _showAdvancedFilters(BuildContext context) {
    // TODO: Implement advanced filters dialog
    context.showInfoSnackBar('الفلاتر المتقدمة قيد التطوير');
  }

  void _showBulkEditDialog(BuildContext context) {
    // TODO: Implement bulk edit dialog
  }

  void _showBulkDeleteDialog(BuildContext context) {
    // TODO: Implement bulk delete dialog
    context.showInfoSnackBar('الحذف المتعدد قيد التطوير');
  }

  void _exportProducts(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const ExcelExportDialog(),
    );
  }

  void _importProducts(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const ExcelImportDialog(),
    );
  }
}
