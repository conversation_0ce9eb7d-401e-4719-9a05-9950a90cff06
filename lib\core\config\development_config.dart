import 'package:flutter/foundation.dart';
import '../utils/app_utils.dart';

/// Development configuration for bypassing production constraints
/// This should ONLY be used in development/testing environments
class DevelopmentConfig {
  static const bool _isDevelopment = kDebugMode;

  // Development flags
  static bool get isEnabled => _isDevelopment && _enableDevMode;
  static bool _enableDevMode = true; // Set to false for production testing

  // Mock user and context data
  static const String mockUserId = 'system';
  static const String mockUserName = 'Dev User';
  static const String mockUserEmail = '<EMAIL>';
  static const String mockBranchId = 'main-branch';
  static const String mockWarehouseId = 'main-warehouse';
  static const String mockUnitId = 'unit_piece';

  // Development settings
  static bool get bypassAuth => isEnabled && _bypassAuth;
  static bool get bypassPermissions => isEnabled && _bypassPermissions;
  static bool get bypassValidation => isEnabled && _bypassValidation;
  static bool get useMockData => isEnabled && _useMockData;
  static bool get skipEncryption => isEnabled && _skipEncryption;
  static bool get logAllOperations => isEnabled && _logAllOperations;
  static bool get usePredictableIds => isEnabled && _usePredictableIds;

  // Internal flags (can be toggled)
  static bool _bypassAuth = true;
  static bool _bypassPermissions = true;
  static bool _bypassValidation = true;
  static bool _useMockData = true;
  static bool _skipEncryption = true;
  static bool _logAllOperations = true;
  static bool _usePredictableIds = false; // Keep UUIDs by default

  /// Enable/disable development mode
  static void setEnabled(bool enabled) {
    if (_isDevelopment) {
      _enableDevMode = enabled;
      AppUtils.logInfo('Development mode ${enabled ? 'enabled' : 'disabled'}');
    }
  }

  /// Configure specific development features
  static void configure({
    bool? bypassAuth,
    bool? bypassPermissions,
    bool? bypassValidation,
    bool? useMockData,
    bool? skipEncryption,
    bool? logAllOperations,
    bool? usePredictableIds,
  }) {
    if (!_isDevelopment) return;

    if (bypassAuth != null) _bypassAuth = bypassAuth;
    if (bypassPermissions != null) _bypassPermissions = bypassPermissions;
    if (bypassValidation != null) _bypassValidation = bypassValidation;
    if (useMockData != null) _useMockData = useMockData;
    if (skipEncryption != null) _skipEncryption = skipEncryption;
    if (logAllOperations != null) _logAllOperations = logAllOperations;
    if (usePredictableIds != null) _usePredictableIds = usePredictableIds;

    AppUtils.logInfo('Development config updated: '
        'auth=$_bypassAuth, permissions=$_bypassPermissions, '
        'validation=$_bypassValidation, mockData=$_useMockData');
  }

  /// Get mock user context
  static Map<String, dynamic> getMockUserContext() {
    return {
      'userId': mockUserId,
      'userName': mockUserName,
      'userEmail': mockUserEmail,
      'branchId': mockBranchId,
      'warehouseId': mockWarehouseId,
      'role': 'admin',
      'permissions': ['all'],
    };
  }

  /// Get mock transaction data
  static Map<String, dynamic> getMockTransactionData() {
    return {
      'createdBy': mockUserId,
      'approvedBy': mockUserId,
      'branchId': mockBranchId,
      'warehouseId': mockWarehouseId,
      'unitId': mockUnitId,
    };
  }

  /// Generate predictable ID for testing (if enabled)
  static String generateId([String? prefix]) {
    if (usePredictableIds) {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      return '${prefix ?? 'dev'}_${timestamp % 100000}';
    }
    return AppUtils.generateId();
  }

  /// Log development operation
  static void logDevOperation(String operation, Map<String, dynamic>? data) {
    if (logAllOperations) {
      AppUtils.logInfo('DEV: $operation ${data != null ? '- $data' : ''}');
    }
  }

  /// Check if we should bypass a specific validation
  static bool shouldBypass(String validationType) {
    if (!isEnabled) return false;

    switch (validationType) {
      case 'auth':
        return bypassAuth;
      case 'permissions':
        return bypassPermissions;
      case 'validation':
        return bypassValidation;
      case 'encryption':
        return skipEncryption;
      default:
        return false;
    }
  }

  /// Get development status summary
  static Map<String, dynamic> getStatus() {
    return {
      'isDevelopment': _isDevelopment,
      'isEnabled': isEnabled,
      'bypassAuth': bypassAuth,
      'bypassPermissions': bypassPermissions,
      'bypassValidation': bypassValidation,
      'useMockData': useMockData,
      'skipEncryption': skipEncryption,
      'logAllOperations': logAllOperations,
      'usePredictableIds': usePredictableIds,
    };
  }

  /// Reset to default development settings
  static void resetToDefaults() {
    if (!_isDevelopment) return;

    _enableDevMode = true;
    _bypassAuth = true;
    _bypassPermissions = true;
    _bypassValidation = true;
    _useMockData = true;
    _skipEncryption = true;
    _logAllOperations = true;
    _usePredictableIds = false;

    AppUtils.logInfo('Development config reset to defaults');
  }

  /// Disable all development features (for production-like testing)
  static void disableAll() {
    if (!_isDevelopment) return;

    _enableDevMode = false;
    AppUtils.logInfo('All development features disabled');
  }
}

/// Development helper for mocking user context
class DevUserContext {
  static Map<String, dynamic>? _mockContext;

  /// Set custom mock user context
  static void setMockContext(Map<String, dynamic> context) {
    if (DevelopmentConfig.isEnabled) {
      _mockContext = context;
      DevelopmentConfig.logDevOperation('Mock context set', context);
    }
  }

  /// Get current user context (mock or real)
  static Map<String, dynamic> getCurrentContext() {
    if (DevelopmentConfig.useMockData && _mockContext != null) {
      return _mockContext!;
    }
    return DevelopmentConfig.getMockUserContext();
  }

  /// Clear mock context
  static void clearMockContext() {
    _mockContext = null;
    DevelopmentConfig.logDevOperation('Mock context cleared', null);
  }
}

/// Development helper for database operations
class DevDatabaseHelper {
  /// Get safe values for database insertion
  static Map<String, dynamic> getSafeInsertValues({
    String? userId,
    String? branchId,
    String? warehouseId,
    String? unitId,
  }) {
    final context = DevUserContext.getCurrentContext();

    return {
      'created_by': userId ?? context['userId'] ?? DevelopmentConfig.mockUserId,
      'approved_by': context['userId'] ??
          DevelopmentConfig.mockUserId, // Only use when needed
      'branch_id':
          branchId ?? context['branchId'] ?? DevelopmentConfig.mockBranchId,
      'warehouse_id': warehouseId ??
          context['warehouseId'] ??
          DevelopmentConfig.mockWarehouseId,
      'unit_id': unitId ?? context['unitId'] ?? DevelopmentConfig.mockUnitId,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
      'is_synced': 0,
    };
  }

  /// Generate safe ID for development
  static String generateSafeId([String? prefix]) {
    return DevelopmentConfig.generateId(prefix);
  }
}
