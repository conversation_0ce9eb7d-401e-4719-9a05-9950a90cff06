import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sqflite/sqflite.dart';

import '../data/local/database.dart';
import '../data/local/dao/branch_dao.dart';
import '../core/utils/app_utils.dart';
// Import missing classes
import 'dart:async';
import 'dart:io';
// Database provider
final databaseProvider = Provider<DatabaseHelper>((ref) {
  return DatabaseHelper();
});

// Database instance provider
final databaseInstanceProvider = FutureProvider<Database>((ref) async {
  final databaseHelper = ref.read(databaseProvider);
  return await databaseHelper.database;
});

// Database initialization provider
final databaseInitProvider = FutureProvider<bool>((ref) async {
  try {
    final database = await ref.read(databaseInstanceProvider.future);
    AppUtils.logInfo('Database initialized successfully');
    return true;
  } catch (e) {
    AppUtils.logError('Failed to initialize database', e);
    return false;
  }
});

// DAO Providers
final branchDaoProvider = Provider<BranchDao>((ref) {
  return BranchDao();
});

// Database status provider
final databaseStatusProvider = StateNotifierProvider<DatabaseStatusNotifier, DatabaseStatus>((ref) {
  return DatabaseStatusNotifier(ref);
});

// Database status notifier
class DatabaseStatusNotifier extends StateNotifier<DatabaseStatus> {
  final Ref _ref;

  DatabaseStatusNotifier(this._ref) : super(DatabaseStatus.initializing) {
    _initializeDatabase();
  }

  Future<void> _initializeDatabase() async {
    try {
      state = DatabaseStatus.initializing;
      
      final isInitialized = await _ref.read(databaseInitProvider.future);
      
      if (isInitialized) {
        state = DatabaseStatus.ready;
        AppUtils.logInfo('Database is ready');
      } else {
        state = DatabaseStatus.error;
        AppUtils.logError('Database initialization failed', null);
      }
    } catch (e) {
      state = DatabaseStatus.error;
      AppUtils.logError('Database initialization error', e);
    }
  }

  Future<void> reinitialize() async {
    await _initializeDatabase();
  }

  Future<bool> resetDatabase() async {
    try {
      state = DatabaseStatus.resetting;
      
      final databaseHelper = _ref.read(databaseProvider);
      await databaseHelper.deleteDatabase();
      
      // Reinitialize
      await _initializeDatabase();
      
      return state == DatabaseStatus.ready;
    } catch (e) {
      state = DatabaseStatus.error;
      AppUtils.logError('Database reset error', e);
      return false;
    }
  }

  Future<int> getDatabaseSize() async {
    try {
      final databaseHelper = _ref.read(databaseProvider);
      return await databaseHelper.getDatabaseSize();
    } catch (e) {
      AppUtils.logError('Error getting database size', e);
      return 0;
    }
  }
}

// Database status enum
enum DatabaseStatus {
  initializing,
  ready,
  error,
  resetting,
}

// Extension for database status
extension DatabaseStatusExtension on DatabaseStatus {
  String get displayName {
    switch (this) {
      case DatabaseStatus.initializing:
        return 'جاري التهيئة...';
      case DatabaseStatus.ready:
        return 'جاهز';
      case DatabaseStatus.error:
        return 'خطأ';
      case DatabaseStatus.resetting:
        return 'جاري إعادة التعيين...';
    }
  }
  
  bool get isReady => this == DatabaseStatus.ready;
  bool get isError => this == DatabaseStatus.error;
  bool get isLoading => this == DatabaseStatus.initializing || this == DatabaseStatus.resetting;
}

// Database operations provider
final databaseOperationsProvider = Provider<DatabaseOperations>((ref) {
  return DatabaseOperations(ref);
});

// Database operations class
class DatabaseOperations {
  final Ref _ref;

  DatabaseOperations(this._ref);

  // Check if database is ready
  bool get isReady {
    final status = _ref.read(databaseStatusProvider);
    return status.isReady;
  }

  // Wait for database to be ready
  Future<bool> waitForReady({Duration timeout = const Duration(seconds: 30)}) async {
    final completer = Completer<bool>();
    Timer? timer;
    
    // Set timeout
    timer = Timer(timeout, () {
      if (!completer.isCompleted) {
        completer.complete(false);
      }
    });
    
    // Listen for status changes
    final subscription = _ref.listen(databaseStatusProvider, (previous, next) {
      if (next.isReady && !completer.isCompleted) {
        timer?.cancel();
        completer.complete(true);
      } else if (next.isError && !completer.isCompleted) {
        timer?.cancel();
        completer.complete(false);
      }
    });
    
    // Check current status
    if (isReady) {
      timer.cancel();
      subscription.close();
      return true;
    }
    
    final result = await completer.future;
    subscription.close();
    return result;
  }

  // Execute operation with database ready check
  Future<T> executeWithReadyCheck<T>(Future<T> Function() operation) async {
    if (!isReady) {
      final isReady = await waitForReady();
      if (!isReady) {
        throw Exception('Database is not ready');
      }
    }
    
    return await operation();
  }

  // Get database instance safely
  Future<Database> getDatabaseInstance() async {
    return await executeWithReadyCheck(() async {
      return await _ref.read(databaseInstanceProvider.future);
    });
  }

  // Reset database
  Future<bool> resetDatabase() async {
    final notifier = _ref.read(databaseStatusProvider.notifier);
    return await notifier.resetDatabase();
  }

  // Get database size
  Future<int> getDatabaseSize() async {
    final notifier = _ref.read(databaseStatusProvider.notifier);
    return await notifier.getDatabaseSize();
  }

  // Check database health
  Future<bool> checkDatabaseHealth() async {
    try {
      final db = await getDatabaseInstance();
      
      // Try to execute a simple query
      final result = await db.rawQuery('SELECT 1');
      
      return result.isNotEmpty;
    } catch (e) {
      AppUtils.logError('Database health check failed', e);
      return false;
    }
  }

  // Get database info
  Future<Map<String, dynamic>> getDatabaseInfo() async {
    try {
      final db = await getDatabaseInstance();
      final size = await getDatabaseSize();
      
      // Get table count
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
      );
      
      return {
        'size': size,
        'table_count': tables.length,
        'tables': tables.map((t) => t['name']).toList(),
        'version': await db.getVersion(),
        'path': db.path,
      };
    } catch (e) {
      AppUtils.logError('Error getting database info', e);
      return {};
    }
  }
}


