// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bank_account.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BankAccount _$BankAccountFromJson(Map<String, dynamic> json) => BankAccount(
      id: json['id'] as String,
      accountName: json['accountName'] as String,
      accountNumber: json['accountNumber'] as String,
      bankName: json['bankName'] as String,
      branchName: json['branchName'] as String?,
      iban: json['iban'] as String?,
      swiftCode: json['swiftCode'] as String?,
      accountType: json['accountType'] as String? ?? 'current',
      currency: json['currency'] as String? ?? 'SAR',
      balance: (json['balance'] as num?)?.toDouble() ?? 0.0,
      creditLimit: (json['creditLimit'] as num?)?.toDouble(),
      isActive: json['isActive'] as bool? ?? true,
      notes: json['notes'] as String?,
      branchId: json['branchId'] as String?,
      createdBy: json['createdBy'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      isSynced: json['isSynced'] as bool? ?? false,
    );

Map<String, dynamic> _$BankAccountToJson(BankAccount instance) =>
    <String, dynamic>{
      'id': instance.id,
      'accountName': instance.accountName,
      'accountNumber': instance.accountNumber,
      'bankName': instance.bankName,
      'branchName': instance.branchName,
      'iban': instance.iban,
      'swiftCode': instance.swiftCode,
      'accountType': instance.accountType,
      'currency': instance.currency,
      'balance': instance.balance,
      'creditLimit': instance.creditLimit,
      'isActive': instance.isActive,
      'notes': instance.notes,
      'branchId': instance.branchId,
      'createdBy': instance.createdBy,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'isSynced': instance.isSynced,
    };
