import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tijari_tech/widgets/custom_text_field.dart';

import '../../../core/theme/colors.dart';
import '../../../core/theme/text_styles.dart';
import '../../../data/models/stock.dart';
import '../../../providers/stock_provider.dart';
import '../../../providers/warehouse_provider.dart';

/// حوار تعديل المخزون
///
/// يوفر واجهة لتعديل كميات المخزون مع:
/// - عرض معلومات المنتج والمخزن الحالية
/// - إدخال الكمية الجديدة
/// - تسجيل سبب التعديل والملاحظات
/// - التحقق من صحة البيانات المدخلة
///
/// Ref: تم تطوير هذا الحوار لضمان دقة تعديلات المخزون وتوثيقها

class StockAdjustmentDialog extends ConsumerStatefulWidget {
  final Stock? stock;

  const StockAdjustmentDialog({
    super.key,
    this.stock,
  });

  @override
  ConsumerState<StockAdjustmentDialog> createState() =>
      _StockAdjustmentDialogState();
}

class _StockAdjustmentDialogState extends ConsumerState<StockAdjustmentDialog> {
  final _formKey = GlobalKey<FormState>();
  final _quantityController = TextEditingController();
  final _reasonController = TextEditingController();
  final _notesController = TextEditingController();

  String? _selectedWarehouseId; // إضافة دعم اختيار المخزن

  @override
  void initState() {
    super.initState();
    if (widget.stock != null) {
      _quantityController.text = widget.stock!.quantity.toString();
      _selectedWarehouseId = widget.stock!.warehouseId; // تحديد المخزن الحالي
    }

    // تحميل المخازن عند فتح الحوار
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(warehouseManagementProvider.notifier).loadWarehouses();
    });
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _reasonController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(stockManagementProvider);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        padding: EdgeInsets.all(24.w),
        constraints: BoxConstraints(
          maxWidth: 400.w,
          maxHeight: 600.h,
        ),
        child: Form(
          key: _formKey,
          child: ListView(
            // mainAxisSize: MainAxisSize.min,
            // crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Icon(
                    Icons.tune,
                    color: AppColors.primary,
                    size: 24.sp,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'تعديل المخزون',
                    style: AppTextStyles.titleLarge.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),

              SizedBox(height: 24.h),

              // Product info
              if (widget.stock != null) ...[
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'المنتج',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        widget.stock!.productName ?? 'منتج غير محدد',
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        'المخزن',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        widget.stock!.warehouseName ?? 'مخزن غير محدد',
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        'الكمية الحالية',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        '${widget.stock!.quantity.toStringAsFixed(2)} ${widget.stock!.unitName ?? ''}',
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.primary,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 16.h),
              ],

              // New quantity
              CustomTextField(
                controller: _quantityController,
                label: 'الكمية الجديدة',
                hint: 'أدخل الكمية الجديدة',
                keyboardType: TextInputType.number,
                prefixIcon: Icon(Icons.inventory),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال الكمية';
                  }
                  final quantity = double.tryParse(value);
                  if (quantity == null || quantity < 0) {
                    return 'يرجى إدخال كمية صحيحة';
                  }
                  return null;
                },
              ),

              SizedBox(height: 16.h),

              // Reason
              CustomTextField(
                controller: _reasonController,
                label: 'سبب التعديل',
                hint: 'أدخل سبب تعديل المخزون',
                prefixIcon: Icon(Icons.info_outline),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال سبب التعديل';
                  }
                  return null;
                },
              ),

              SizedBox(height: 16.h),

              // اختيار المخزن
              Consumer(
                builder: (context, ref, child) {
                  final warehouseState = ref.watch(warehouseManagementProvider);

                  if (warehouseState.isLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  final warehouses = warehouseState.warehouses;
                  if (warehouses.isEmpty) {
                    return Container(
                      padding: EdgeInsets.all(12.w),
                      decoration: BoxDecoration(
                        color: Colors.orange[50],
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(color: Colors.orange[200]!),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.warning, color: Colors.orange[600]),
                          SizedBox(width: 8.w),
                          Text(
                            'لا توجد مخازن متاحة',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Colors.orange[700],
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  // تحديد المخزن الافتراضي إذا لم يتم اختيار مخزن
                  if (_selectedWarehouseId == null) {
                    final defaultWarehouse = warehouses.firstWhere(
                      (w) => w.isDefault,
                      orElse: () => warehouses.first,
                    );
                    _selectedWarehouseId = defaultWarehouse.id;
                  }

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'المخزن',
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Container(
                        height: 100.h,
                        child: DropdownButtonFormField<String>(
                          value: _selectedWarehouseId,
                          decoration: InputDecoration(
                            hintText: 'اختر المخزن',
                            prefixIcon: const Icon(Icons.warehouse),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 12.w,
                              vertical: 16.h,
                            ),
                          ),
                          items: warehouses.map((warehouse) {
                            return DropdownMenuItem<String>(
                              value: warehouse.id,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    warehouse.isDefault
                                        ? Icons.star
                                        : Icons.warehouse,
                                    size: 16.r,
                                    color: warehouse.isDefault
                                        ? Colors.amber
                                        : Colors.grey,
                                  ),
                                  SizedBox(width: 8.w),
                                  Flexible(
                                    child: Text(
                                      warehouse.name,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  if (warehouse.isDefault) ...[
                                    SizedBox(width: 4.w),
                                    Text(
                                      '(افتراضي)',
                                      style: AppTextStyles.bodySmall.copyWith(
                                        color: Colors.amber[700],
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedWarehouseId = value;
                            });
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى اختيار المخزن';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  );
                },
              ),

              SizedBox(height: 16.h),

              // Notes
              CustomTextField(
                controller: _notesController,
                label: 'ملاحظات (اختياري)',
                hint: 'أدخل أي ملاحظات إضافية',
                prefixIcon: Icon(Icons.note),
                maxLines: 3,
              ),

              SizedBox(height: 32.h),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        'إلغاء',
                        style: AppTextStyles.bodyMedium,
                      ),
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: state.isLoading ? null : _adjustStock,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: state.isLoading
                          ? SizedBox(
                              height: 20.h,
                              width: 20.w,
                              child: const CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : Text(
                              'تعديل',
                              style: AppTextStyles.bodyMedium.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _adjustStock() async {
    if (!_formKey.currentState!.validate()) return;
    if (widget.stock == null) return;

    final newQuantity = double.parse(_quantityController.text);
    final reason = _reasonController.text;
    final notes = _notesController.text; // سيتم استخدامها لاحقاً

    try {
      // استخدام النظام المحسن لتحديث المخزون مع دعم المخازن والملاحظات
      await ref.read(stockManagementProvider.notifier).updateStockQuantity(
            stockId: widget.stock!.id,
            newQuantity: newQuantity,
            reason: reason,
            userId: 'system', // Use system user for now
          );

      // TODO: في المستقبل، يمكن استخدام النظام المحسن:
      // await ref.read(stockManagementProvider.notifier).adjustStock(
      //   productId: widget.stock!.productId,
      //   operationType: 'set',
      //   quantity: newQuantity,
      //   reason: reason,
      //   notes: notes,
      //   warehouseId: _selectedWarehouseId,
      //   userId: 'current-user',
      // );

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تعديل المخزون بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تعديل المخزون: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
