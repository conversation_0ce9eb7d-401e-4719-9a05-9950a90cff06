
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../../core/theme/app_theme.dart';
import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/formatters.dart';
import '../../data/repositories/sale_repository.dart';
import '../../data/repositories/purchase_repository.dart';
import '../../widgets/custom_card.dart';
import '../../services/financial_reports_export.dart';

/// صفحة تقارير الأرباح والخسائر
class ProfitLossReportsPage extends ConsumerStatefulWidget {
  const ProfitLossReportsPage({super.key});

  @override
  ConsumerState<ProfitLossReportsPage> createState() =>
      _ProfitLossReportsPageState();
}

class _ProfitLossReportsPageState extends ConsumerState<ProfitLossReportsPage> {
  DateTimeRange? _dateRange;
  String _selectedPeriod = 'thisMonth';
  bool _isLoading = false;

  // بيانات التقارير
  Map<String, dynamic> _profitLossData = {};

  @override
  void initState() {
    super.initState();
    _initializeDateRange();
    _loadProfitLossData();
  }

  void _initializeDateRange() {
    final now = DateTime.now();
    switch (_selectedPeriod) {
      case 'today':
        _dateRange = DateTimeRange(
          start: DateTime(now.year, now.month, now.day),
          end: DateTime(now.year, now.month, now.day, 23, 59, 59),
        );
        break;
      case 'thisWeek':
        final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
        _dateRange = DateTimeRange(
          start: DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day),
          end: DateTime(now.year, now.month, now.day, 23, 59, 59),
        );
        break;
      case 'thisMonth':
        _dateRange = DateTimeRange(
          start: DateTime(now.year, now.month, 1),
          end: DateTime(now.year, now.month + 1, 0, 23, 59, 59),
        );
        break;
      case 'thisQuarter':
        final quarterStart =
            DateTime(now.year, ((now.month - 1) ~/ 3) * 3 + 1, 1);
        final quarterEnd =
            DateTime(quarterStart.year, quarterStart.month + 3, 0, 23, 59, 59);
        _dateRange = DateTimeRange(start: quarterStart, end: quarterEnd);
        break;
      case 'thisYear':
        _dateRange = DateTimeRange(
          start: DateTime(now.year, 1, 1),
          end: DateTime(now.year, 12, 31, 23, 59, 59),
        );
        break;
    }
  }

  Future<void> _loadProfitLossData() async {
    if (_dateRange == null) return;

    setState(() => _isLoading = true);

    try {
      // تحميل بيانات المبيعات
      final saleRepository = SaleRepository();
      final allSales = await saleRepository.getAllSales();
      final filteredSales = allSales.where((sale) {
        final saleDateStr = sale['sale_date'] as String?;
        if (saleDateStr == null) return false;
        final saleDate = DateTime.parse(saleDateStr);
        return saleDate
                .isAfter(_dateRange!.start.subtract(const Duration(days: 1))) &&
            saleDate.isBefore(_dateRange!.end.add(const Duration(days: 1)));
      }).toList();

      // تحميل بيانات المشتريات
      final purchaseRepository = PurchaseRepository();
      final allPurchases = await purchaseRepository.getAllPurchases();
      final filteredPurchases = allPurchases.where((purchase) {
        final purchaseDateStr = purchase['purchase_date'] as String?;
        if (purchaseDateStr == null) return false;
        final purchaseDate = DateTime.parse(purchaseDateStr);
        return purchaseDate
                .isAfter(_dateRange!.start.subtract(const Duration(days: 1))) &&
            purchaseDate.isBefore(_dateRange!.end.add(const Duration(days: 1)));
      }).toList();

      // حساب الإيرادات
      final totalRevenue = filteredSales.fold<double>(0, (sum, sale) {
        final total = (sale['total'] as num?)?.toDouble() ?? 0.0;
        return sum + total;
      });

      // حساب تكلفة البضاعة المباعة (COGS)
      final totalCOGS = filteredPurchases.fold<double>(0, (sum, purchase) {
        final total = (purchase['total_amount'] as num?)?.toDouble() ?? 0.0;
        return sum + total;
      });

      // حساب الربح الإجمالي
      final grossProfit = totalRevenue - totalCOGS;
      final grossProfitMargin =
          totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0;

      // حساب المصروفات التشغيلية (تقديرية)
      final operatingExpenses =
          totalRevenue * 0.15; // 15% من الإيرادات كمصروفات تشغيلية

      // حساب الربح التشغيلي
      final operatingProfit = grossProfit - operatingExpenses;
      final operatingProfitMargin =
          totalRevenue > 0 ? (operatingProfit / totalRevenue) * 100 : 0;

      // حساب صافي الربح
      final netProfit = operatingProfit;
      final netProfitMargin =
          totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;

      _profitLossData = {
        'totalRevenue': totalRevenue,
        'totalCOGS': totalCOGS,
        'grossProfit': grossProfit,
        'grossProfitMargin': grossProfitMargin,
        'operatingExpenses': operatingExpenses,
        'operatingProfit': operatingProfit,
        'operatingProfitMargin': operatingProfitMargin,
        'netProfit': netProfit,
        'netProfitMargin': netProfitMargin,
        'salesCount': filteredSales.length,
        'purchasesCount': filteredPurchases.length,
      };
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل البيانات: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقارير الأرباح والخسائر'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        actions: [
          IconButton(
            onPressed: _exportReport,
            icon: const Icon(Icons.file_download),
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFiltersSection(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : SingleChildScrollView(
                    padding: EdgeInsets.all(16.r),
                    child: Column(
                      children: [
                        _buildSummaryCards(),
                        SizedBox(height: 16.h),
                        _buildProfitLossStatement(),
                        SizedBox(height: 16.h),
                        _buildProfitabilityAnalysis(),
                        SizedBox(height: 16.h),
                        _buildTrendAnalysis(),
                      ],
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return CustomCard(
      margin: EdgeInsets.all(16.r),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'فترة التقرير',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: _buildPeriodSelector(),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildCustomDateRange(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return DropdownButtonFormField<String>(
      value: _selectedPeriod,
      decoration: const InputDecoration(
        labelText: 'الفترة الزمنية',
        border: OutlineInputBorder(),
      ),
      items: const [
        DropdownMenuItem(value: 'today', child: Text('اليوم')),
        DropdownMenuItem(value: 'thisWeek', child: Text('هذا الأسبوع')),
        DropdownMenuItem(value: 'thisMonth', child: Text('هذا الشهر')),
        DropdownMenuItem(value: 'thisQuarter', child: Text('هذا الربع')),
        DropdownMenuItem(value: 'thisYear', child: Text('هذا العام')),
        DropdownMenuItem(value: 'custom', child: Text('فترة مخصصة')),
      ],
      onChanged: (value) {
        setState(() {
          _selectedPeriod = value!;
          if (value != 'custom') {
            _initializeDateRange();
            _loadProfitLossData();
          }
        });
      },
    );
  }

  Widget _buildCustomDateRange() {
    return OutlinedButton.icon(
      onPressed: _selectedPeriod == 'custom' ? _selectDateRange : null,
      icon: const Icon(Icons.date_range),
      label: Text(
        _dateRange != null
            ? '${DateFormat('dd/MM/yyyy').format(_dateRange!.start)} - ${DateFormat('dd/MM/yyyy').format(_dateRange!.end)}'
            : 'اختر التاريخ',
      ),
    );
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _dateRange,
      locale: const Locale('ar'),
    );

    if (picked != null) {
      setState(() {
        _dateRange = picked;
      });
      _loadProfitLossData();
    }
  }

  Widget _buildSummaryCards() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 12.w,
      mainAxisSpacing: 12.h,
      childAspectRatio: 1.0,
      children: [
        _buildSummaryCard(
          'إجمالي الإيرادات',
          AppFormatters.formatCurrency(_profitLossData['totalRevenue'] ?? 0),
          Icons.trending_up,
          AppColors.success,
        ),
        _buildSummaryCard(
          'تكلفة البضاعة',
          AppFormatters.formatCurrency(_profitLossData['totalCOGS'] ?? 0),
          Icons.trending_down,
          AppColors.error,
        ),
        _buildSummaryCard(
          'الربح الإجمالي',
          AppFormatters.formatCurrency(_profitLossData['grossProfit'] ?? 0),
          Icons.account_balance_wallet,
          AppColors.primary,
        ),
        _buildSummaryCard(
          'صافي الربح',
          AppFormatters.formatCurrency(_profitLossData['netProfit'] ?? 0),
          Icons.monetization_on,
          AppColors.info,
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
      String title, String value, IconData icon, Color color) {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32.r),
            SizedBox(height: 8.h),
            Text(
              title,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 4.h),
            Text(
              value,
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfitLossStatement() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'قائمة الأرباح والخسائر',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            _buildStatementRow(
              'الإيرادات',
              _profitLossData['totalRevenue'] ?? 0,
              AppColors.success,
              isHeader: true,
            ),
            const Divider(),
            _buildStatementRow(
              'تكلفة البضاعة المباعة',
              _profitLossData['totalCOGS'] ?? 0,
              AppColors.error,
            ),
            const Divider(),
            _buildStatementRow(
              'الربح الإجمالي',
              _profitLossData['grossProfit'] ?? 0,
              AppColors.primary,
              isSubtotal: true,
            ),
            SizedBox(height: 8.h),
            _buildStatementRow(
              'المصروفات التشغيلية',
              _profitLossData['operatingExpenses'] ?? 0,
              AppColors.warning,
            ),
            const Divider(),
            _buildStatementRow(
              'الربح التشغيلي',
              _profitLossData['operatingProfit'] ?? 0,
              AppColors.info,
              isSubtotal: true,
            ),
            const Divider(thickness: 2),
            _buildStatementRow(
              'صافي الربح',
              _profitLossData['netProfit'] ?? 0,
              AppColors.primary,
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatementRow(
    String label,
    double amount,
    Color color, {
    bool isHeader = false,
    bool isSubtotal = false,
    bool isTotal = false,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: isHeader || isTotal
                  ? FontWeight.bold
                  : isSubtotal
                      ? FontWeight.w600
                      : FontWeight.normal,
              fontSize: isTotal ? 18.sp : null,
            ),
          ),
          Text(
            AppFormatters.formatCurrency(amount),
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: isHeader || isTotal
                  ? FontWeight.bold
                  : isSubtotal
                      ? FontWeight.w600
                      : FontWeight.normal,
              color: color,
              fontSize: isTotal ? 18.sp : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfitabilityAnalysis() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تحليل الربحية',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: _buildMarginCard(
                    'هامش الربح الإجمالي',
                    (_profitLossData['grossProfitMargin'] as num?)?.toDouble() ?? 0,
                    AppColors.primary,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildMarginCard(
                    'هامش الربح التشغيلي',
                    (_profitLossData['operatingProfitMargin'] as num?)?.toDouble() ?? 0,
                    AppColors.info,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: _buildMarginCard(
                    'هامش صافي الربح',
                    (_profitLossData['netProfitMargin'] as num?)?.toDouble() ?? 0,
                    AppColors.success,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildKPICard(
                    'متوسط قيمة المبيعة',
                    _calculateAverageSaleValue(),
                    AppColors.warning,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMarginCard(String title, double percentage, Color color) {
    return Container(
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8.h),
          Text(
            '${percentage.toStringAsFixed(1)}%',
            style: AppTextStyles.titleLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKPICard(String title, double value, Color color) {
    return Container(
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8.h),
          Text(
            AppFormatters.formatCurrency(value),
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTrendAnalysis() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تحليل الاتجاهات',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            _buildTrendItem(
              'عدد المبيعات',
              '${_profitLossData['salesCount'] ?? 0}',
              Icons.receipt_long,
              AppColors.primary,
            ),
            SizedBox(height: 12.h),
            _buildTrendItem(
              'عدد المشتريات',
              '${_profitLossData['purchasesCount'] ?? 0}',
              Icons.shopping_cart,
              AppColors.error,
            ),
            SizedBox(height: 12.h),
            _buildTrendItem(
              'نسبة التكلفة للإيرادات',
              '${_calculateCostRatio().toStringAsFixed(1)}%',
              Icons.analytics,
              AppColors.warning,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendItem(
      String title, String value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 24.r),
        SizedBox(width: 12.w),
        Expanded(
          child: Text(
            title,
            style: AppTextStyles.bodyMedium,
          ),
        ),
        Text(
          value,
          style: AppTextStyles.titleSmall.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  double _calculateAverageSaleValue() {
    final totalRevenue = _profitLossData['totalRevenue'] ?? 0.0;
    final salesCount = _profitLossData['salesCount'] ?? 0;
    return salesCount > 0 ? totalRevenue / salesCount : 0.0;
  }

  double _calculateCostRatio() {
    final totalRevenue = _profitLossData['totalRevenue'] ?? 0.0;
    final totalCOGS = _profitLossData['totalCOGS'] ?? 0.0;
    return totalRevenue > 0 ? (totalCOGS / totalRevenue) * 100 : 0.0;
  }

  Future<void> _exportReport() async {
    if (_dateRange == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى تحديد فترة زمنية أولاً')),
      );
      return;
    }

    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // تصدير البيانات
      final filePath = await FinancialReportsExport.exportProfitLossToExcel(
        profitLossData: _profitLossData,
        dateRange: _dateRange!,
      );

      // التحقق من أن الويدجت ما زال موجود
      if (!mounted) return;

      // إخفاء مؤشر التحميل
      Navigator.of(context).pop();

      // إظهار رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تصدير التقرير بنجاح إلى: $filePath'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      // التحقق من أن الويدجت ما زال موجود
      if (!mounted) return;

      // إخفاء مؤشر التحميل
      Navigator.of(context).pop();

      // إظهار رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء التصدير: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }
}

