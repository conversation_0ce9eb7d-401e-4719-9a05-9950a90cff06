
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:tijari_tech/shared/widgets/loading_widget.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/formatters.dart';
import '../../data/models/sale.dart';
import '../../data/models/sale_item.dart';
import '../../providers/sale_provider.dart';
import '../../data/repositories/sale_repository.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/error_widget.dart';
import '../../widgets/custom_card.dart';

class SaleViewPage extends ConsumerStatefulWidget {
  final String saleId;
  
  const SaleViewPage({super.key, required this.saleId});

  @override
  ConsumerState<SaleViewPage> createState() => _SaleViewPageState();
}

class _SaleViewPageState extends ConsumerState<SaleViewPage> {
  Sale? _sale;
  List<SaleItem> _items = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadSaleDetails();
  }

  Future<void> _loadSaleDetails() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final saleProvider = ref.read(saleManagementProvider.notifier);

      // تحميل تفاصيل الفاتورة
      final saleRepository = SaleRepository();
      final saleData = await saleRepository.getSaleById(widget.saleId);
      if (saleData == null) {
        throw Exception('لم يتم العثور على الفاتورة');
      }

      final sale = Sale.fromMap(saleData['sale']);
      final items = (saleData['items'] as List<Map<String, dynamic>>)
          .map((item) => SaleItem.fromMap(item))
          .toList();

      setState(() {
        _sale = sale;
        _items = items;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'عرض الفاتورة',
      actions: [
        if (_sale != null) ...[
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _editSale(),
            tooltip: 'تعديل الفاتورة',
          ),
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: () => _printSale(),
            tooltip: 'طباعة الفاتورة',
          ),
        ],
      ],
      child: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: LoadingWidget());
    }

    if (_error != null) {
      return Center(
        child: CustomErrorWidget(
          message: _error!,
          onRetry: _loadSaleDetails,
        ),
      );
    }

    if (_sale == null) {
      return const Center(
        child: Text('لم يتم العثور على الفاتورة'),
      );
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSaleHeader(),
          SizedBox(height: 16.h),
          _buildCustomerInfo(),
          SizedBox(height: 16.h),
          _buildSaleItems(),
          SizedBox(height: 16.h),
          _buildSaleSummary(),
          SizedBox(height: 16.h),
          _buildPaymentInfo(),
          SizedBox(height: 24.h),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildSaleHeader() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'فاتورة مبيعات',
                  style: AppTextStyles.headlineMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: _getStatusColor(_sale!.status ?? 'pending'),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Text(
                    _getStatusText(_sale!.status ?? 'pending'),
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('رقم الفاتورة', _sale!.invoiceNo),
                ),
                Expanded(
                  child: _buildInfoItem(
                      'التاريخ', AppFormatters.formatDate(_sale!.date)),
                ),
              ],
            ),
            if (_sale!.notes?.isNotEmpty == true) ...[
              SizedBox(height: 12.h),
              _buildInfoItem('ملاحظات', _sale!.notes!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerInfo() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات العميل',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            _buildInfoItem('اسم العميل', 'عميل نقدي'),
            if (_sale!.customerId != null) ...[
              SizedBox(height: 8.h),
              _buildInfoItem('رقم العميل', _sale!.customerId!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSaleItems() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المنتجات',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _items.length,
              separatorBuilder: (context, index) => Divider(height: 16.h),
              itemBuilder: (context, index) {
                final item = _items[index];
                return _buildSaleItemRow(item);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaleItemRow(SaleItem item) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'منتج غير محدد',
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (item.productId.isNotEmpty) ...[
                SizedBox(height: 4.h),
                Text(
                  'كود: ${item.productId}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ],
          ),
        ),
        Expanded(
          child: Text(
            '${item.qty}',
            textAlign: TextAlign.center,
            style: AppTextStyles.bodyMedium,
          ),
        ),
        Expanded(
          child: Text(
            AppFormatters.formatCurrency(item.unitPrice),
            textAlign: TextAlign.center,
            style: AppTextStyles.bodyMedium,
          ),
        ),
        Expanded(
          child: Text(
            AppFormatters.formatCurrency(item.total),
            textAlign: TextAlign.center,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.success,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSaleSummary() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          children: [
            _buildSummaryRow('المجموع الفرعي', _sale!.subtotal),
            if ((_sale!.discount ?? 0) > 0) ...[
              SizedBox(height: 8.h),
              _buildSummaryRow('الخصم', _sale!.discount ?? 0, isDiscount: true),
            ],
            if ((_sale!.tax ?? 0) > 0) ...[
              SizedBox(height: 8.h),
              _buildSummaryRow('الضريبة', _sale!.tax ?? 0),
            ],
            SizedBox(height: 12.h),
            Divider(thickness: 2.h),
            SizedBox(height: 12.h),
            _buildSummaryRow(
              'الإجمالي',
              _sale!.total,
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInfo() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الدفع',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            _buildSummaryRow('المبلغ المدفوع', _sale!.paid),
            SizedBox(height: 8.h),
            _buildSummaryRow(
              'المبلغ المستحق',
              _sale!.due,
              isDebt: _sale!.due > 0,
            ),
            SizedBox(height: 8.h),
            _buildInfoItem('طريقة الدفع',
                _getPaymentMethodText(_sale!.paymentMethod ?? 'cash')),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _editSale,
            icon: const Icon(Icons.edit),
            label: const Text('تعديل الفاتورة'),
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 12.h),
            ),
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _printSale,
            icon: const Icon(Icons.print),
            label: const Text('طباعة'),
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 12.h),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 4.h),
        Text(
          value,
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryRow(String label, double amount,
      {bool isTotal = false, bool isDiscount = false, bool isDebt = false}) {
    Color textColor = AppColors.primary;
    if (isTotal) textColor = AppColors.primary;
    if (isDiscount) textColor = AppColors.warning;
    if (isDebt && amount > 0) textColor = AppColors.error;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style:
              (isTotal ? AppTextStyles.titleMedium : AppTextStyles.bodyMedium)
                  .copyWith(
            fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
            color: textColor,
          ),
        ),
        Text(
          AppFormatters.formatCurrency(amount),
          style:
              (isTotal ? AppTextStyles.titleMedium : AppTextStyles.bodyMedium)
                  .copyWith(
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'مكتملة':
        return AppColors.success;
      case 'pending':
      case 'معلقة':
        return AppColors.warning;
      case 'cancelled':
      case 'ملغية':
        return AppColors.error;
      default:
        return AppColors.primary;
    }
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'مكتملة';
      case 'pending':
        return 'معلقة';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  }

  String _getPaymentMethodText(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
        return 'نقدي';
      case 'credit':
        return 'آجل';
      case 'card':
        return 'بطاقة';
      case 'bank_transfer':
        return 'تحويل بنكي';
      default:
        return method;
    }
  }

  void _editSale() {
    context.push('/sales/edit/${widget.saleId}');
  }

  void _printSale() {
    // TODO: تنفيذ وظيفة الطباعة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تنفيذ وظيفة الطباعة قريباً')),
    );
  }
}

