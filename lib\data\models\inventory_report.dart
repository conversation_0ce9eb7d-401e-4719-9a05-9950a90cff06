import '../../core/constants/database_constants.dart';

class InventoryReport {
  final String id;
  final String reportType; // stock_summary, movement_analysis, low_stock, expiry_report, etc.
  final String title;
  final String description;
  final Map<String, dynamic> parameters;
  final Map<String, dynamic> data;
  final DateTime generatedAt;
  final String generatedBy;
  final DateTime? scheduledAt;
  final bool isScheduled;
  final String status; // generating, completed, failed
  final String? filePath;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;

  const InventoryReport({
    required this.id,
    required this.reportType,
    required this.title,
    required this.description,
    required this.parameters,
    required this.data,
    required this.generatedAt,
    required this.generatedBy,
    this.scheduledAt,
    required this.isScheduled,
    required this.status,
    this.filePath,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  // Factory constructor for creating new report
  factory InventoryReport.create({
    required String id,
    required String reportType,
    required String title,
    required String description,
    Map<String, dynamic> parameters = const {},
    Map<String, dynamic> data = const {},
    DateTime? generatedAt,
    required String generatedBy,
    DateTime? scheduledAt,
    bool isScheduled = false,
    String status = 'generating',
    String? filePath,
  }) {
    final now = DateTime.now();
    return InventoryReport(
      id: id,
      reportType: reportType,
      title: title,
      description: description,
      parameters: parameters,
      data: data,
      generatedAt: generatedAt ?? now,
      generatedBy: generatedBy,
      scheduledAt: scheduledAt,
      isScheduled: isScheduled,
      status: status,
      filePath: filePath,
      createdAt: now,
      updatedAt: now,
    );
  }

  // Factory constructor from map
  factory InventoryReport.fromMap(Map<String, dynamic> map) {
    return InventoryReport(
      id: map['id'] as String,
      reportType: map['report_type'] as String,
      title: map['title'] as String,
      description: map['description'] as String,
      parameters: map['parameters'] is String 
          ? {} // Handle JSON string if needed
          : Map<String, dynamic>.from(map['parameters'] ?? {}),
      data: map['data'] is String 
          ? {} // Handle JSON string if needed
          : Map<String, dynamic>.from(map['data'] ?? {}),
      generatedAt: DateTime.parse(map['generated_at'] as String),
      generatedBy: map['generated_by'] as String,
      scheduledAt: map['scheduled_at'] != null
          ? DateTime.parse(map['scheduled_at'] as String)
          : null,
      isScheduled: (map['is_scheduled'] as int) == 1,
      status: map['status'] as String,
      filePath: map['file_path'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      deletedAt: map['deleted_at'] != null
          ? DateTime.parse(map['deleted_at'] as String)
          : null,
    );
  }

  // Convert to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'report_type': reportType,
      'title': title,
      'description': description,
      'parameters': parameters,
      'data': data,
      'generated_at': generatedAt.toIso8601String(),
      'generated_by': generatedBy,
      'scheduled_at': scheduledAt?.toIso8601String(),
      'is_scheduled': isScheduled ? 1 : 0,
      'status': status,
      'file_path': filePath,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'deleted_at': deletedAt?.toIso8601String(),
    };
  }

  // Copy with method
  InventoryReport copyWith({
    String? id,
    String? reportType,
    String? title,
    String? description,
    Map<String, dynamic>? parameters,
    Map<String, dynamic>? data,
    DateTime? generatedAt,
    String? generatedBy,
    DateTime? scheduledAt,
    bool? isScheduled,
    String? status,
    String? filePath,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
  }) {
    return InventoryReport(
      id: id ?? this.id,
      reportType: reportType ?? this.reportType,
      title: title ?? this.title,
      description: description ?? this.description,
      parameters: parameters ?? this.parameters,
      data: data ?? this.data,
      generatedAt: generatedAt ?? this.generatedAt,
      generatedBy: generatedBy ?? this.generatedBy,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      isScheduled: isScheduled ?? this.isScheduled,
      status: status ?? this.status,
      filePath: filePath ?? this.filePath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
    );
  }

  // Mark as completed
  InventoryReport markAsCompleted({
    Map<String, dynamic>? data,
    String? filePath,
  }) {
    return copyWith(
      status: 'completed',
      data: data ?? this.data,
      filePath: filePath ?? this.filePath,
      updatedAt: DateTime.now(),
    );
  }

  // Mark as failed
  InventoryReport markAsFailed(String error) {
    return copyWith(
      status: 'failed',
      data: {'error': error},
      updatedAt: DateTime.now(),
    );
  }

  // Mark as deleted
  InventoryReport markAsDeleted() {
    return copyWith(
      deletedAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  // Getters
  bool get isDeleted => deletedAt != null;
  bool get isGenerating => status == 'generating';
  bool get isCompleted => status == 'completed';
  bool get isFailed => status == 'failed';
  bool get hasFile => filePath != null && filePath!.isNotEmpty;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InventoryReport && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'InventoryReport(id: $id, type: $reportType, title: $title, status: $status)';
  }
}

// Stock summary data model
class StockSummaryData {
  final int totalProducts;
  final int totalWarehouses;
  final double totalValue;
  final int lowStockItems;
  final int outOfStockItems;
  final int expiredItems;
  final int nearExpiryItems;
  final List<ProductStockSummary> productSummaries;
  final List<WarehouseStockSummary> warehouseSummaries;

  const StockSummaryData({
    required this.totalProducts,
    required this.totalWarehouses,
    required this.totalValue,
    required this.lowStockItems,
    required this.outOfStockItems,
    required this.expiredItems,
    required this.nearExpiryItems,
    required this.productSummaries,
    required this.warehouseSummaries,
  });

  factory StockSummaryData.fromMap(Map<String, dynamic> map) {
    return StockSummaryData(
      totalProducts: map['total_products'] as int,
      totalWarehouses: map['total_warehouses'] as int,
      totalValue: (map['total_value'] as num).toDouble(),
      lowStockItems: map['low_stock_items'] as int,
      outOfStockItems: map['out_of_stock_items'] as int,
      expiredItems: map['expired_items'] as int,
      nearExpiryItems: map['near_expiry_items'] as int,
      productSummaries: (map['product_summaries'] as List<dynamic>)
          .map((item) => ProductStockSummary.fromMap(item))
          .toList(),
      warehouseSummaries: (map['warehouse_summaries'] as List<dynamic>)
          .map((item) => WarehouseStockSummary.fromMap(item))
          .toList(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'total_products': totalProducts,
      'total_warehouses': totalWarehouses,
      'total_value': totalValue,
      'low_stock_items': lowStockItems,
      'out_of_stock_items': outOfStockItems,
      'expired_items': expiredItems,
      'near_expiry_items': nearExpiryItems,
      'product_summaries': productSummaries.map((item) => item.toMap()).toList(),
      'warehouse_summaries': warehouseSummaries.map((item) => item.toMap()).toList(),
    };
  }
}

class ProductStockSummary {
  final String productId;
  final String productName;
  final String categoryName;
  final double totalQuantity;
  final double totalValue;
  final int warehouseCount;
  final bool isLowStock;
  final bool isOutOfStock;

  const ProductStockSummary({
    required this.productId,
    required this.productName,
    required this.categoryName,
    required this.totalQuantity,
    required this.totalValue,
    required this.warehouseCount,
    required this.isLowStock,
    required this.isOutOfStock,
  });

  factory ProductStockSummary.fromMap(Map<String, dynamic> map) {
    return ProductStockSummary(
      productId: map['product_id'] as String,
      productName: map['product_name'] as String,
      categoryName: map['category_name'] as String,
      totalQuantity: (map['total_quantity'] as num).toDouble(),
      totalValue: (map['total_value'] as num).toDouble(),
      warehouseCount: map['warehouse_count'] as int,
      isLowStock: (map['is_low_stock'] as int) == 1,
      isOutOfStock: (map['is_out_of_stock'] as int) == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'product_id': productId,
      'product_name': productName,
      'category_name': categoryName,
      'total_quantity': totalQuantity,
      'total_value': totalValue,
      'warehouse_count': warehouseCount,
      'is_low_stock': isLowStock ? 1 : 0,
      'is_out_of_stock': isOutOfStock ? 1 : 0,
    };
  }
}

class WarehouseStockSummary {
  final String warehouseId;
  final String warehouseName;
  final int productCount;
  final double totalValue;
  final int lowStockItems;
  final int outOfStockItems;

  const WarehouseStockSummary({
    required this.warehouseId,
    required this.warehouseName,
    required this.productCount,
    required this.totalValue,
    required this.lowStockItems,
    required this.outOfStockItems,
  });

  factory WarehouseStockSummary.fromMap(Map<String, dynamic> map) {
    return WarehouseStockSummary(
      warehouseId: map['warehouse_id'] as String,
      warehouseName: map['warehouse_name'] as String,
      productCount: map['product_count'] as int,
      totalValue: (map['total_value'] as num).toDouble(),
      lowStockItems: map['low_stock_items'] as int,
      outOfStockItems: map['out_of_stock_items'] as int,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'warehouse_id': warehouseId,
      'warehouse_name': warehouseName,
      'product_count': productCount,
      'total_value': totalValue,
      'low_stock_items': lowStockItems,
      'out_of_stock_items': outOfStockItems,
    };
  }
}
