// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'settings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Settings _$SettingsFromJson(Map<String, dynamic> json) => Settings(
      id: json['id'] as String,
      key: json['key'] as String,
      value: json['value'] as String,
      description: json['description'] as String?,
      category: json['category'] as String? ?? 'general',
      dataType: json['dataType'] as String? ?? 'string',
      isSystem: json['isSystem'] as bool? ?? false,
      isEncrypted: json['isEncrypted'] as bool? ?? false,
      validationRule: json['validationRule'] as String?,
      defaultValue: json['defaultValue'] as String?,
      branchId: json['branchId'] as String?,
      userId: json['userId'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      isSynced: json['isSynced'] as bool? ?? false,
    );

Map<String, dynamic> _$SettingsToJson(Settings instance) => <String, dynamic>{
      'id': instance.id,
      'key': instance.key,
      'value': instance.value,
      'description': instance.description,
      'category': instance.category,
      'dataType': instance.dataType,
      'isSystem': instance.isSystem,
      'isEncrypted': instance.isEncrypted,
      'validationRule': instance.validationRule,
      'defaultValue': instance.defaultValue,
      'branchId': instance.branchId,
      'userId': instance.userId,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'isSynced': instance.isSynced,
    };
