import 'package:intl/intl.dart';
import 'package:jiffy/jiffy.dart';
import '../constants/app_constants.dart';

class AppFormatters {
  // Currency Formatter
  static String formatCurrency(double amount, {String? currency}) {
    final formatter = NumberFormat.currency(
      locale: 'ar_SA',
      symbol: currency ?? AppConstants.defaultCurrency,
      decimalDigits: AppConstants.currencyDecimalPlaces,
    );
    return formatter.format(amount);
  }

  // Number Formatter
  static String formatNumber(double number, {int decimalPlaces = 2}) {
    final formatter = NumberFormat.decimalPattern('ar_SA');
    if (decimalPlaces == 0) {
      return formatter.format(number.round());
    }
    return number.toStringAsFixed(decimalPlaces);
  }

  // Percentage Formatter
  static String formatPercentage(double percentage, {int decimalPlaces = 1}) {
    return '${percentage.toStringAsFixed(decimalPlaces)}%';
  }

  // Date Formatters
  static String formatDate(DateTime date, {String? format}) {
    final formatter =
        DateFormat(format ?? AppConstants.displayDateFormat, 'ar');
    return formatter.format(date);
  }

  static String formatDateTime(DateTime dateTime, {String? format}) {
    final formatter =
        DateFormat(format ?? AppConstants.displayDateTimeFormat, 'ar');
    return formatter.format(dateTime);
  }

  static String formatTime(DateTime time) {
    final formatter = DateFormat('HH:mm', 'ar');
    return formatter.format(time);
  }

  // Arabic Date Formatter
  static String formatArabicDate(DateTime date) {
    final formatter = DateFormat(AppConstants.arabicDateFormat, 'ar');
    return formatter.format(date);
  }

  // Relative Time Formatter
  static String formatRelativeTime(DateTime dateTime) {
    final jiffy = Jiffy.parseFromDateTime(dateTime);
    return jiffy.fromNow();
  }

  // Phone Number Formatter
  static String formatPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // Add country code if not present
    if (cleaned.length == 9 && cleaned.startsWith('5')) {
      cleaned = '966$cleaned';
    } else if (cleaned.length == 10 && cleaned.startsWith('05')) {
      cleaned = '966${cleaned.substring(1)}';
    }

    // Format as +966 5X XXX XXXX
    if (cleaned.length == 12 && cleaned.startsWith('966')) {
      return '+${cleaned.substring(0, 3)} ${cleaned.substring(3, 4)}${cleaned.substring(4, 5)} ${cleaned.substring(5, 8)} ${cleaned.substring(8)}';
    }

    return phoneNumber; // Return original if can't format
  }

  // Invoice Number Formatter
  static String formatInvoiceNumber(int number, String prefix) {
    return '$prefix${number.toString().padLeft(AppConstants.invoiceNumberLength, '0')}';
  }

  // Barcode Formatter
  static String formatBarcode(String barcode) {
    // Remove all non-digit characters
    String cleaned = barcode.replaceAll(RegExp(r'[^\d]'), '');

    // Pad with zeros if needed for EAN-13
    if (cleaned.length < AppConstants.barcodeLength) {
      cleaned = cleaned.padLeft(AppConstants.barcodeLength, '0');
    }

    return cleaned;
  }

  // File Size Formatter
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  // Duration Formatter
  static String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));

    if (duration.inHours > 0) {
      return '${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds';
    } else {
      return '$twoDigitMinutes:$twoDigitSeconds';
    }
  }

  // Tax Amount Calculator and Formatter
  static double calculateTaxAmount(double amount, double taxRate) {
    return amount * taxRate;
  }

  static double calculateAmountWithTax(double amount, double taxRate) {
    return amount + calculateTaxAmount(amount, taxRate);
  }

  static double calculateAmountWithoutTax(
      double amountWithTax, double taxRate) {
    return amountWithTax / (1 + taxRate);
  }

  // Discount Calculator
  static double calculateDiscountAmount(
      double amount, double discountPercentage) {
    return amount * (discountPercentage / 100);
  }

  static double calculateAmountAfterDiscount(
      double amount, double discountPercentage) {
    return amount - calculateDiscountAmount(amount, discountPercentage);
  }

  // Profit Margin Calculator
  static double calculateProfitMargin(double salePrice, double purchasePrice) {
    if (purchasePrice == 0) return 0;
    return ((salePrice - purchasePrice) / purchasePrice) * 100;
  }

  static double calculateProfitAmount(double salePrice, double purchasePrice) {
    return salePrice - purchasePrice;
  }

  // Unit Conversion
  static double convertUnits(
      double quantity, double fromFactor, double toFactor) {
    // Convert to base unit first, then to target unit
    double baseQuantity = quantity * fromFactor;
    return baseQuantity / toFactor;
  }

  // Text Formatters
  static String capitalizeFirst(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  static String capitalizeWords(String text) {
    return text.split(' ').map((word) => capitalizeFirst(word)).join(' ');
  }

  // Arabic Number Formatter
  static String formatArabicNumbers(String text) {
    const englishNumbers = '0123456789';
    const arabicNumbers = '٠١٢٣٤٥٦٧٨٩';

    String result = text;
    for (int i = 0; i < englishNumbers.length; i++) {
      result = result.replaceAll(englishNumbers[i], arabicNumbers[i]);
    }
    return result;
  }

  // English Number Formatter
  static String formatEnglishNumbers(String text) {
    const arabicNumbers = '٠١٢٣٤٥٦٧٨٩';
    const englishNumbers = '0123456789';

    String result = text;
    for (int i = 0; i < arabicNumbers.length; i++) {
      result = result.replaceAll(arabicNumbers[i], englishNumbers[i]);
    }
    return result;
  }

  // Quantity Formatter with Unit
  static String formatQuantityWithUnit(double quantity, String unitName) {
    String formattedQuantity =
        formatNumber(quantity, decimalPlaces: quantity % 1 == 0 ? 0 : 2);
    return '$formattedQuantity $unitName';
  }

  // Stock Status Formatter
  static String formatStockStatus(double currentStock, double reorderPoint) {
    if (currentStock <= 0) {
      return 'نفد المخزون';
    } else if (currentStock <= reorderPoint) {
      return 'مخزون منخفض';
    } else {
      return 'متوفر';
    }
  }

  // Payment Status Formatter
  static String formatPaymentStatus(double total, double paid) {
    if (paid >= total) {
      return 'مدفوع';
    } else if (paid > 0) {
      return 'مدفوع جزئياً';
    } else {
      return 'غير مدفوع';
    }
  }
}
