// import 'dart:io';
// import 'package:sqflite_common_ffi/sqflite_ffi.dart';
// import 'lib/data/local/database.dart';
// import 'lib/data/local/dao/sale_dao.dart';
// import 'lib/data/models/sale.dart';
// import 'lib/data/models/sale_item.dart';
// import 'lib/core/utils/app_utils.dart';
// import 'lib/core/constants/database_constants.dart';

// void main() async {
//   print('🚀 بدء اختبار إنشاء المبيعة...');
  
//   try {
//     // Initialize FFI for desktop
//     sqfliteFfiInit();
//     databaseFactory = databaseFactoryFfi;
    
//     // Create database helper
//     final dbHelper = DatabaseHelper();
//     final database = await dbHelper.database;
    
//     print('✅ تم إنشاء قاعدة البيانات بنجاح');
    
//     // Create SaleDao
//     final saleDao = SaleDao(database);
    
//     // التحقق من وجود البيانات المطلوبة
//     await _verifyRequiredData(database);
    
//     // إنشاء منتج للاختبار
//     final productId = await _createTestProduct(database);
//     print('✅ تم إنشاء منتج الاختبار: $productId');
    
//     // إنشاء عميل للاختبار
//     final customerId = await _createTestCustomer(database);
//     print('✅ تم إنشاء عميل الاختبار: $customerId');
    
//     // إنشاء مخزون أولي
//     await _createInitialStock(database, productId);
//     print('✅ تم إنشاء المخزون الأولي');
    
//     // إنشاء المبيعة
//     final sale = Sale.create(
//       id: AppUtils.generateId(),
//       customerId: customerId,
//       invoiceNo: 'TEST-${DateTime.now().millisecondsSinceEpoch}',
//       branchId: 'default-branch',
//       date: DateTime.now(),
//       total: 100.0,
//       paid: 100.0,
//       notes: 'اختبار إنشاء مبيعة',
//     );
    
//     // إنشاء عنصر المبيعة
//     final saleItem = SaleItem.create(
//       id: AppUtils.generateId(),
//       saleId: sale.id,
//       productId: productId,
//       unitId: 'unit_piece',
//       qty: 1.0,
//       unitPrice: 100.0,
//     );
    
//     print('🔄 محاولة إنشاء المبيعة...');
    
//     // محاولة إنشاء المبيعة
//     await saleDao.createSale(sale, [saleItem]);
    
//     print('🎉 تم إنشاء المبيعة بنجاح!');
//     print('📋 رقم الفاتورة: ${sale.invoiceNo}');
//     print('💰 المبلغ الإجمالي: ${sale.totalAmount}');
    
//     // التحقق من إنشاء حركة المخزون
//     final stockMovements = await database.query(
//       DatabaseConstants.tableStockMovements,
//       where: '${DatabaseConstants.columnStockMovementReferenceId} = ?',
//       whereArgs: [sale.id],
//     );
    
//     if (stockMovements.isNotEmpty) {
//       print('✅ تم إنشاء حركة المخزون بنجاح');
//       final movement = stockMovements.first;
//       print('   - نوع الحركة: ${movement[DatabaseConstants.columnStockMovementType]}');
//       print('   - الكمية: ${movement[DatabaseConstants.columnStockMovementQuantity]}');
//       print('   - الوحدة: ${movement[DatabaseConstants.columnStockMovementUnitId]}');
//       print('   - المخزن: ${movement[DatabaseConstants.columnStockMovementWarehouseId]}');
//       print('   - المنشئ: ${movement[DatabaseConstants.columnStockMovementCreatedBy]}');
//     } else {
//       print('⚠️ لم يتم إنشاء حركة المخزون');
//     }
    
//     print('\n🎯 نتيجة الاختبار: نجح الاختبار بالكامل! ✅');
//     print('🔧 تم حل مشكلة FOREIGN KEY constraint بنجاح');
    
//   } catch (e, stackTrace) {
//     print('❌ فشل في إنشاء المبيعة: $e');
//     print('📍 تفاصيل الخطأ: $stackTrace');
//     exit(1);
//   }
// }

// Future<void> _verifyRequiredData(Database database) async {
//   print('🔍 التحقق من البيانات المطلوبة...');
  
//   // التحقق من الوحدات
//   final units = await database.query(DatabaseConstants.tableUnits);
//   print('   - عدد الوحدات: ${units.length}');
  
//   // التحقق من المخازن
//   final warehouses = await database.query(DatabaseConstants.tableWarehouses);
//   print('   - عدد المخازن: ${warehouses.length}');
  
//   // التحقق من المستخدمين
//   final users = await database.query(DatabaseConstants.tableUsers);
//   print('   - عدد المستخدمين: ${users.length}');
  
//   // التحقق من وجود البيانات الأساسية
//   final unitPiece = await database.query(
//     DatabaseConstants.tableUnits,
//     where: '${DatabaseConstants.columnUnitId} = ?',
//     whereArgs: ['unit_piece'],
//   );
  
//   final mainWarehouse = await database.query(
//     DatabaseConstants.tableWarehouses,
//     where: '${DatabaseConstants.columnWarehouseId} = ?',
//     whereArgs: ['main-warehouse'],
//   );
  
//   final systemUser = await database.query(
//     DatabaseConstants.tableUsers,
//     where: '${DatabaseConstants.columnUserId} = ?',
//     whereArgs: ['system'],
//   );
  
//   if (unitPiece.isEmpty) throw Exception('الوحدة الافتراضية unit_piece غير موجودة');
//   if (mainWarehouse.isEmpty) throw Exception('المخزن الافتراضي main-warehouse غير موجود');
//   if (systemUser.isEmpty) throw Exception('المستخدم النظام system غير موجود');
  
//   print('✅ جميع البيانات المطلوبة موجودة');
// }

// Future<String> _createTestProduct(Database database) async {
//   final productId = AppUtils.generateId();
  
//   await database.insert(DatabaseConstants.tableProducts, {
//     DatabaseConstants.columnProductId: productId,
//     DatabaseConstants.columnProductNameAr: 'منتج اختبار',
//     DatabaseConstants.columnProductNameEn: 'Test Product',
//     DatabaseConstants.columnProductBarcode: 'TEST${DateTime.now().millisecondsSinceEpoch}',
//     DatabaseConstants.columnProductSellingPrice: 100.0,
//     DatabaseConstants.columnProductCostPrice: 80.0,
//     DatabaseConstants.columnProductBaseUnitId: 'unit_piece',
//     DatabaseConstants.columnProductCategoryId: 'default-category',
//     DatabaseConstants.columnProductTrackStock: 1,
//     DatabaseConstants.columnProductIsActive: 1,
//     DatabaseConstants.columnProductCreatedAt: DateTime.now().toIso8601String(),
//     DatabaseConstants.columnProductIsSynced: 0,
//   });
  
//   return productId;
// }

// Future<String> _createTestCustomer(Database database) async {
//   final customerId = AppUtils.generateId();
  
//   await database.insert(DatabaseConstants.tableCustomers, {
//     DatabaseConstants.columnCustomerId: customerId,
//     DatabaseConstants.columnCustomerName: 'عميل اختبار',
//     DatabaseConstants.columnCustomerPhone: '123456789',
//     DatabaseConstants.columnCustomerEmail: '<EMAIL>',
//     DatabaseConstants.columnCustomerBalance: 0.0,
//     DatabaseConstants.columnCustomerCreatedAt: DateTime.now().toIso8601String(),
//     DatabaseConstants.columnCustomerIsSynced: 0,
//   });
  
//   return customerId;
// }

// Future<void> _createInitialStock(Database database, String productId) async {
//   await database.insert(DatabaseConstants.tableStocks, {
//     DatabaseConstants.columnStockId: AppUtils.generateId(),
//     DatabaseConstants.columnStockProductId: productId,
//     DatabaseConstants.columnStockWarehouseId: 'main-warehouse',
//     DatabaseConstants.columnStockQuantity: 100.0,
//     DatabaseConstants.columnStockReorderPoint: 10.0,
//     DatabaseConstants.columnStockCreatedAt: DateTime.now().toIso8601String(),
//     DatabaseConstants.columnStockIsSynced: 0,
//   });
// }
