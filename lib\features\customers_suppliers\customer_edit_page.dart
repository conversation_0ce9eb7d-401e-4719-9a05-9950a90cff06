import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../data/models/customer.dart';
import '../../providers/customer_provider.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_button.dart';

/// صفحة تعديل العميل
/// Customer Edit Page
class CustomerEditPage extends ConsumerStatefulWidget {
  final String customerId;
  
  const CustomerEditPage({
    super.key,
    required this.customerId,
  });

  @override
  ConsumerState<CustomerEditPage> createState() => _CustomerEditPageState();
}

class _CustomerEditPageState extends ConsumerState<CustomerEditPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _balanceController = TextEditingController();
  final _notesController = TextEditingController();

  bool _isLoading = false;
  Customer? _customer;

  @override
  void initState() {
    super.initState();
    _loadCustomer();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _balanceController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadCustomer() async {
    setState(() => _isLoading = true);
    
    try {
      final customerState = ref.read(customerProvider);
      _customer = customerState.customers.firstWhere(
        (c) => c.id == widget.customerId,
        orElse: () => throw Exception('العميل غير موجود'),
      );
      
      // تعبئة الحقول بالبيانات الحالية
      _nameController.text = _customer!.name;
      _phoneController.text = _customer!.phone ?? '';
      _emailController.text = _customer!.email ?? '';
      _addressController.text = _customer!.address ?? '';
      _balanceController.text = _customer!.balance.toString();
      _notesController.text = _customer!.notes ?? '';
      
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل بيانات العميل: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
        context.pop();
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _updateCustomer() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final updatedCustomer = _customer!.copyWith(
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty ? null : _emailController.text.trim(),
        address: _addressController.text.trim().isEmpty ? null : _addressController.text.trim(),
        balance: double.tryParse(_balanceController.text.trim()) ?? 0.0,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        updatedAt: DateTime.now(),
      );

      await ref.read(customerProvider.notifier).updateCustomer(updatedCustomer);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث العميل بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث العميل: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'تعديل العميل',
      child: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.r),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // معلومات أساسية
                    _buildSectionTitle('المعلومات الأساسية'),
                    SizedBox(height: 16.h),
                    
                    CustomTextField(
                      controller: _nameController,
                      label: 'اسم العميل',
                      hint: 'أدخل اسم العميل',
                      prefixIcon:Icon( Icons.person),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'اسم العميل مطلوب';
                        }
                        if (value.trim().length < 2) {
                          return 'اسم العميل يجب أن يكون أكثر من حرفين';
                        }
                        return null;
                      },
                    ),
                    
                    SizedBox(height: 16.h),
                    
                    CustomTextField(
                      controller: _phoneController,
                      label: 'رقم الهاتف',
                      hint: 'أدخل رقم الهاتف (اختياري)',
                      prefixIcon:Icon(Icons.phone),
                      keyboardType: TextInputType.phone,
                    ),
                    
                    SizedBox(height: 16.h),
                    
                    CustomTextField(
                      controller: _emailController,
                      label: 'البريد الإلكتروني',
                      hint: 'أدخل البريد الإلكتروني (اختياري)',
                      prefixIcon:Icon( Icons.email),
                      keyboardType: TextInputType.emailAddress,
                      validator: (value) {
                        if (value != null && value.trim().isNotEmpty) {
                          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                            return 'البريد الإلكتروني غير صحيح';
                          }
                        }
                        return null;
                      },
                    ),
                    
                    SizedBox(height: 16.h),
                    
                    CustomTextField(
                      controller: _addressController,
                      label: 'العنوان',
                      hint: 'أدخل العنوان (اختياري)',
                      prefixIcon:Icon( Icons.location_on),
                      maxLines: 2,
                    ),
                    
                    SizedBox(height: 24.h),
                    
                    // معلومات مالية
                    _buildSectionTitle('المعلومات المالية'),
                    SizedBox(height: 16.h),
                    
                    CustomTextField(
                      controller: _balanceController,
                      label: 'الرصيد الحالي',
                      hint: 'أدخل الرصيد الحالي',
                      prefixIcon:Icon( Icons.account_balance_wallet),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.trim().isNotEmpty) {
                          if (double.tryParse(value) == null) {
                            return 'الرصيد يجب أن يكون رقماً صحيحاً';
                          }
                        }
                        return null;
                      },
                    ),
                    
                    SizedBox(height: 16.h),
                    
                    CustomTextField(
                      controller: _notesController,
                      label: 'ملاحظات',
                      hint: 'أدخل ملاحظات إضافية (اختياري)',
                      prefixIcon: Icon(Icons.note),
                      maxLines: 3,
                    ),
                    
                    SizedBox(height: 32.h),
                    
                    // أزرار الحفظ والإلغاء
                    Row(
                      children: [
                        Expanded(
                          child: CustomButton(
                            text: 'حفظ التغييرات',
                            onPressed: _isLoading ? null : _updateCustomer,
                            isLoading: _isLoading,
                          ),
                        ),
                        SizedBox(width: 16.w),
                        Expanded(
                          child: CustomButton(
                            text: 'إلغاء',
                            onPressed: () => context.pop(),
                            // variant: ButtonVariant.outlined,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: AppTextStyles.headlineSmall.copyWith(
        color: AppColors.primary,
        fontWeight: FontWeight.bold,
      ),
    );
  }
}
