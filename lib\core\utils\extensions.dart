import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../theme/colors.dart';
import 'formatters.dart';

// String Extensions
extension StringExtensions on String {
  // Check if string is empty or null
  bool get isNullOrEmpty => isEmpty;

  // Capitalize first letter
  String get capitalizeFirst => AppFormatters.capitalizeFirst(this);

  // Capitalize all words
  String get capitalizeWords => AppFormatters.capitalizeWords(this);

  // Convert to Arabic numbers
  String get toArabicNumbers => AppFormatters.formatArabicNumbers(this);

  // Convert to English numbers
  String get toEnglishNumbers => AppFormatters.formatEnglishNumbers(this);

  // Check if string is a valid email
  bool get isValidEmail =>
      RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
          .hasMatch(this);

  // Check if string is a valid phone number
  bool get isValidPhone {
    String cleaned = replaceAll(RegExp(r'[^\d]'), '');
    return cleaned.length >= 9 && cleaned.length <= 12;
  }

  // Remove all whitespace
  String get removeWhitespace => replaceAll(RegExp(r'\s+'), '');

  // Truncate string with ellipsis
  String truncate(int maxLength) {
    if (length <= maxLength) return this;
    return '${substring(0, maxLength)}...';
  }

  // Parse to double safely
  double? get toDoubleOrNull => double.tryParse(this);

  // Parse to int safely
  int? get toIntOrNull => int.tryParse(this);

  // Check if string contains only digits
  bool get isNumeric => RegExp(r'^[0-9]+$').hasMatch(this);

  // Check if string contains only Arabic characters
  bool get isArabic => RegExp(r'^[\u0600-\u06FF\s]+$').hasMatch(this);

  // Check if string contains only English characters
  bool get isEnglish => RegExp(r'^[a-zA-Z\s]+$').hasMatch(this);
}

// DateTime Extensions
extension DateTimeExtensions on DateTime {
  // Format as display date
  String get toDisplayDate => AppFormatters.formatDate(this);

  // Format as display date time
  String get toDisplayDateTime => AppFormatters.formatDateTime(this);

  // Format as time only
  String get toDisplayTime => AppFormatters.formatTime(this);

  // Format as Arabic date
  String get toArabicDate => AppFormatters.formatArabicDate(this);

  // Format as relative time
  String get toRelativeTime => AppFormatters.formatRelativeTime(this);

  // Check if date is today
  bool get isToday {
    final now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }

  // Check if date is yesterday
  bool get isYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return year == yesterday.year &&
        month == yesterday.month &&
        day == yesterday.day;
  }

  // Check if date is in current week
  bool get isThisWeek {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    return isAfter(startOfWeek.subtract(const Duration(days: 1))) &&
        isBefore(endOfWeek.add(const Duration(days: 1)));
  }

  // Check if date is in current month
  bool get isThisMonth {
    final now = DateTime.now();
    return year == now.year && month == now.month;
  }

  // Check if date is in current year
  bool get isThisYear {
    final now = DateTime.now();
    return year == now.year;
  }

  // Get start of day
  DateTime get startOfDay => DateTime(year, month, day);

  // Get end of day
  DateTime get endOfDay => DateTime(year, month, day, 23, 59, 59, 999);

  // Get start of month
  DateTime get startOfMonth => DateTime(year, month, 1);

  // Get end of month
  DateTime get endOfMonth => DateTime(year, month + 1, 0, 23, 59, 59, 999);
}

// Double Extensions
extension DoubleExtensions on double {
  // Format as currency
  String get toCurrency => AppFormatters.formatCurrency(this);

  // Format as number
  String toFormattedNumber({int decimalPlaces = 2}) =>
      AppFormatters.formatNumber(this, decimalPlaces: decimalPlaces);

  // Format as percentage
  String toPercentage({int decimalPlaces = 1}) =>
      AppFormatters.formatPercentage(this, decimalPlaces: decimalPlaces);

  // Check if number is positive
  bool get isPositive => this > 0;

  // Check if number is negative
  bool get isNegative => this < 0;

  // Check if number is zero
  bool get isZero => this == 0;

  // Round to specific decimal places
  double roundToDecimalPlaces(int decimalPlaces) {
    final factor = pow(10, decimalPlaces);
    return (this * factor).round() / factor;
  }
}

// Int Extensions
extension IntExtensions on int {
  // Format as currency
  String get toCurrency => toDouble().toCurrency;

  // Format as number
  String get toFormattedNumber =>
      AppFormatters.formatNumber(toDouble(), decimalPlaces: 0);

  // Format file size
  String get toFileSize => AppFormatters.formatFileSize(this);

  // Check if number is positive
  bool get isPositive => this > 0;

  // Check if number is negative
  bool get isNegative => this < 0;

  // Check if number is zero
  bool get isZero => this == 0;

  // Check if number is even
  bool get isEven => this % 2 == 0;

  // Check if number is odd
  bool get isOdd => this % 2 != 0;
}

// BuildContext Extensions
extension BuildContextExtensions on BuildContext {
  // Theme shortcuts
  ThemeData get theme => Theme.of(this);
  ColorScheme get colorScheme => theme.colorScheme;
  TextTheme get textTheme => theme.textTheme;

  // Screen size shortcuts
  Size get screenSize => MediaQuery.of(this).size;
  double get screenWidth => screenSize.width;
  double get screenHeight => screenSize.height;

  // Safe area shortcuts
  EdgeInsets get padding => MediaQuery.of(this).padding;
  EdgeInsets get viewInsets => MediaQuery.of(this).viewInsets;

  // Navigation shortcuts
  NavigatorState get navigator => Navigator.of(this);

  void pop<T>([T? result]) => navigator.pop(result);

  Future<T?> push<T>(Route<T> route) => navigator.push(route);

  Future<T?> pushNamed<T>(String routeName, {Object? arguments}) =>
      navigator.pushNamed(routeName, arguments: arguments);

  Future<T?> pushReplacementNamed<T, TO>(String routeName,
          {Object? arguments}) =>
      navigator.pushReplacementNamed(routeName, arguments: arguments);

  // Snackbar shortcuts
  void showSnackBar(String message,
      {Color? backgroundColor, Duration? duration}) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor,
        duration: duration ?? const Duration(seconds: 3),
      ),
    );
  }

  void showSuccessSnackBar(String message) {
    showSnackBar(message, backgroundColor: AppColors.success);
  }

  void showErrorSnackBar(String message) {
    showSnackBar(message, backgroundColor: AppColors.error);
  }

  void showWarningSnackBar(String message) {
    showSnackBar(message, backgroundColor: AppColors.warning);
  }

  void showInfoSnackBar(String message) {
    showSnackBar(message, backgroundColor: AppColors.info);
  }

  // Dialog shortcuts
  Future<T?> showCustomDialog<T>(Widget dialog) {
    return showDialog<T>(
      context: this,
      builder: (context) => dialog,
    );
  }

  Future<bool?> showConfirmDialog({
    required String title,
    required String content,
    String confirmText = 'تأكيد',
    String cancelText = 'إلغاء',
  }) {
    return showDialog<bool>(
      context: this,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  // Check if device is tablet
  bool get isTablet => screenWidth >= 768;

  // Check if device is mobile
  bool get isMobile => screenWidth < 768;

  // Check if keyboard is visible
  bool get isKeyboardVisible => viewInsets.bottom > 0;
}

// List Extensions
extension ListExtensions<T> on List<T> {
  // Check if list is null or empty
  bool get isNullOrEmpty => isEmpty;

  // Get first element safely
  T? get firstOrNull => isEmpty ? null : first;

  // Get last element safely
  T? get lastOrNull => isEmpty ? null : last;

  // Add element if not null
  void addIfNotNull(T? element) {
    if (element != null) add(element);
  }

  // Remove duplicates
  List<T> get unique => toSet().toList();

  // Chunk list into smaller lists
  List<List<T>> chunk(int size) {
    List<List<T>> chunks = [];
    for (int i = 0; i < length; i += size) {
      chunks.add(sublist(i, i + size > length ? length : i + size));
    }
    return chunks;
  }
}

// Widget Extensions
extension WidgetExtensions on Widget {
  // Add padding
  Widget paddingAll(double padding) => Padding(
        padding: EdgeInsets.all(padding.r),
        child: this,
      );

  Widget paddingSymmetric({double horizontal = 0, double vertical = 0}) =>
      Padding(
        padding: EdgeInsets.symmetric(
            horizontal: horizontal.w, vertical: vertical.h),
        child: this,
      );

  Widget paddingOnly({
    double left = 0,
    double top = 0,
    double right = 0,
    double bottom = 0,
  }) =>
      Padding(
        padding: EdgeInsets.only(
          left: left.w,
          top: top.h,
          right: right.w,
          bottom: bottom.h,
        ),
        child: this,
      );

  // Add margin using Container
  Widget marginAll(double margin) => Container(
        margin: EdgeInsets.all(margin.r),
        child: this,
      );

  Widget marginSymmetric({double horizontal = 0, double vertical = 0}) =>
      Container(
        margin: EdgeInsets.symmetric(
            horizontal: horizontal.w, vertical: vertical.h),
        child: this,
      );

  // Center widget
  Widget get center => Center(child: this);

  // Expand widget
  Widget get expanded => Expanded(child: this);

  // Flexible widget
  Widget flexible({int flex = 1}) => Flexible(flex: flex, child: this);

  // Add tap gesture
  Widget onTap(VoidCallback onTap) => GestureDetector(
        onTap: onTap,
        child: this,
      );

  // Add visibility
  Widget visible(bool visible) => Visibility(
        visible: visible,
        child: this,
      );

  // Add opacity
  Widget opacity(double opacity) => Opacity(
        opacity: opacity,
        child: this,
      );
}
