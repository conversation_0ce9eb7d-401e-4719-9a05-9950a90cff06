import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:tijari_tech/core/utils/error_handler.dart';
import 'package:tijari_tech/data/models/product.dart';
import 'package:tijari_tech/shared/widgets/loading_widget.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/extensions.dart';
import '../../providers/enhanced_product_provider.dart';
import '../../providers/category_provider.dart';
import '../../widgets/main_layout.dart';

class ProductsReportsPage extends ConsumerStatefulWidget {
  const ProductsReportsPage({super.key});

  @override
  ConsumerState<ProductsReportsPage> createState() =>
      _ProductsReportsPageState();
}

class _ProductsReportsPageState extends ConsumerState<ProductsReportsPage> {
  String _selectedReportType = 'inventory';
  String? _selectedCategory;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  bool _includeInactive = false;
  bool _isGenerating = false;

  final List<ReportType> _reportTypes = [
    ReportType(
        'inventory', 'تقرير المخزون', 'قائمة شاملة بجميع المنتجات والمخزون'),
    ReportType('low_stock', 'تقرير المخزون المنخفض',
        'المنتجات التي تحتاج إعادة تجديد'),
    ReportType('out_of_stock', 'تقرير نفاد المخزون', 'المنتجات غير المتوفرة'),
    ReportType('price_list', 'قائمة الأسعار', 'أسعار جميع المنتجات'),
    ReportType(
        'category_summary', 'ملخص الفئات', 'إحصائيات المنتجات حسب الفئة'),
    ReportType('valuation', 'تقرير التقييم', 'قيمة المخزون الإجمالية'),
  ];

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'تقارير المنتجات',
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Report configuration
            _buildReportConfiguration(),
            SizedBox(height: 24.h),

            // Report preview
            Expanded(
              child: _buildReportPreview(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportConfiguration() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات التقرير',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),

            // Report type selection
            _buildReportTypeSelection(),
            SizedBox(height: 16.h),

            // Filters
            _buildFilters(),
            SizedBox(height: 16.h),

            // Date range
            _buildDateRange(),
            SizedBox(height: 16.h),

            // Options
            _buildOptions(),
            SizedBox(height: 16.h),

            // Action buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildReportTypeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع التقرير',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        DropdownButtonFormField<String>(
          value: _selectedReportType,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
          ),
          items: _reportTypes.map((type) {
            return DropdownMenuItem(
              value: type.id,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    type.name,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    type.description,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedReportType = value;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildFilters() {
    final categoriesAsync = ref.watch(mainCategoriesProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الفلاتر',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),

        // Category filter
        categoriesAsync.when(
          data: (categories) => DropdownButtonFormField<String>(
            value: _selectedCategory,
            decoration: InputDecoration(
              labelText: 'الفئة',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            items: [
              const DropdownMenuItem(
                value: null,
                child: Text('جميع الفئات'),
              ),
              ...categories.map((category) {
                return DropdownMenuItem(
                  value: category.id,
                  child: Text(category.getDisplayName()),
                );
              }),
            ],
            onChanged: (value) {
              setState(() {
                _selectedCategory = value;
              });
            },
          ),
          loading: () => const CircularProgressIndicator(),
          error: (error, stack) => Text('خطأ في تحميل الفئات'),
        ),
      ],
    );
  }

  Widget _buildDateRange() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الفترة الزمنية',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _selectStartDate,
                icon: const Icon(Icons.calendar_today),
                label: Text('من: ${_formatDate(_startDate)}'),
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _selectEndDate,
                icon: const Icon(Icons.calendar_today),
                label: Text('إلى: ${_formatDate(_endDate)}'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'خيارات إضافية',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        CheckboxListTile(
          title: const Text('تضمين المنتجات غير النشطة'),
          value: _includeInactive,
          onChanged: (value) {
            setState(() {
              _includeInactive = value ?? false;
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _previewReport,
            icon: const Icon(Icons.preview),
            label: const Text('معاينة'),
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isGenerating ? null : _generatePDF,
            icon: _isGenerating
                ? SizedBox(
                    width: 16.w,
                    height: 16.h,
                    child: const CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.picture_as_pdf),
            label: const Text('تصدير PDF'),
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _exportExcel,
            icon: const Icon(Icons.table_chart),
            label: const Text('تصدير Excel'),
          ),
        ),
      ],
    );
  }

  Widget _buildReportPreview() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'معاينة التقرير',
                  style: AppTextStyles.titleLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  _getReportTypeName(),
                  style: AppTextStyles.titleMedium.copyWith(
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Expanded(
              child: _buildReportContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportContent() {
    switch (_selectedReportType) {
      case 'inventory':
        return _buildInventoryReport();
      case 'low_stock':
        return _buildLowStockReport();
      case 'out_of_stock':
        return _buildOutOfStockReport();
      case 'price_list':
        return _buildPriceListReport();
      case 'category_summary':
        return _buildCategorySummaryReport();
      case 'valuation':
        return _buildValuationReport();
      default:
        return _buildInventoryReport();
    }
  }

  Widget _buildInventoryReport() {
    final productsState = ref.watch(enhancedProductManagementProvider);

    if (productsState.isLoading) {
      return const LoadingWidget();
    }

    if (productsState.error != null) {
      return ErrorDisplayWidget(
        error: productsState.error!,
        onRetry: () =>
            ref.read(enhancedProductManagementProvider.notifier).refresh(),
      );
    }

    var products = productsState.products;

    // Apply filters
    if (_selectedCategory != null) {
      products =
          products.where((p) => p.categoryId == _selectedCategory).toList();
    }

    if (!_includeInactive) {
      products = products.where((p) => p.isActiveProduct).toList();
    }

    return _buildProductsTable(products, [
      'الاسم',
      'الباركود',
      'الفئة',
      'المخزون',
      'سعر التكلفة',
      'سعر البيع',
      'الحالة',
    ]);
  }

  Widget _buildLowStockReport() {
    final lowStockAsync = ref.watch(lowStockProductsProvider);

    return lowStockAsync.when(
      data: (products) => _buildProductsTable(products, [
        'الاسم',
        'المخزون الحالي',
        'نقطة إعادة الطلب',
        'الحد الأدنى',
        'الحد الأقصى',
        'الحالة',
      ]),
      loading: () => const LoadingWidget(),
      error: (error, stack) => ErrorDisplayWidget(
        error: error.toString(),
        onRetry: () => ref.refresh(lowStockProductsProvider),
      ),
    );
  }

  Widget _buildOutOfStockReport() {
    final outOfStockAsync = ref.watch(outOfStockProductsProvider);

    return outOfStockAsync.when(
      data: (products) => _buildProductsTable(products, [
        'الاسم',
        'الباركود',
        'الفئة',
        'آخر تحديث',
        'سعر البيع',
      ]),
      loading: () => const LoadingWidget(),
      error: (error, stack) => ErrorDisplayWidget(
        error: error.toString(),
        onRetry: () => ref.refresh(outOfStockProductsProvider),
      ),
    );
  }

  Widget _buildPriceListReport() {
    final productsState = ref.watch(enhancedProductManagementProvider);

    if (productsState.isLoading) {
      return const LoadingWidget();
    }

    if (productsState.error != null) {
      return ErrorDisplayWidget(
        error: productsState.error!,
        onRetry: () =>
            ref.read(enhancedProductManagementProvider.notifier).refresh(),
      );
    }

    var products = productsState.products;

    if (_selectedCategory != null) {
      products =
          products.where((p) => p.categoryId == _selectedCategory).toList();
    }

    if (!_includeInactive) {
      products = products.where((p) => p.isActiveProduct).toList();
    }

    return _buildProductsTable(products, [
      'الاسم',
      'الباركود',
      'سعر التكلفة',
      'سعر البيع',
      'هامش الربح',
      'الوحدة',
    ]);
  }

  Widget _buildCategorySummaryReport() {
    final categoriesAsync = ref.watch(mostUsedCategoriesProvider);

    return categoriesAsync.when(
      data: (categories) => SingleChildScrollView(
        child: DataTable(
          columns: const [
            DataColumn(label: Text('الفئة')),
            DataColumn(label: Text('عدد المنتجات')),
            DataColumn(label: Text('النسبة')),
          ],
          rows: categories.map((category) {
            final productCount = category.productCount ?? 0;
            final totalProducts = categories.fold<int>(
              0,
              (sum, cat) => sum + (cat.productCount ?? 0),
            );
            final percentage = totalProducts > 0
                ? (productCount / totalProducts * 100).toStringAsFixed(1)
                : '0.0';

            return DataRow(
              cells: [
                DataCell(Text(category.nameAr)),
                DataCell(Text(productCount.toString())),
                DataCell(Text('$percentage%')),
              ],
            );
          }).toList(),
        ),
      ),
      loading: () => const LoadingWidget(),
      error: (error, stack) => ErrorDisplayWidget(
        error: error.toString(),
        onRetry: () => ref.refresh(mostUsedCategoriesProvider),
      ),
    );
  }

  Widget _buildValuationReport() {
    final productsState = ref.watch(enhancedProductManagementProvider);

    if (productsState.isLoading) {
      return const LoadingWidget();
    }

    if (productsState.error != null) {
      return ErrorDisplayWidget(
        error: productsState.error!,
        onRetry: () =>
            ref.read(enhancedProductManagementProvider.notifier).refresh(),
      );
    }

    var products = productsState.products;

    if (_selectedCategory != null) {
      products =
          products.where((p) => p.categoryId == _selectedCategory).toList();
    }

    if (!_includeInactive) {
      products = products.where((p) => p.isActiveProduct).toList();
    }

    final totalCostValue = products.fold<double>(
      0,
      (sum, product) => sum + (product.costPrice * (product.currentStock ?? 0)),
    );

    final totalSellingValue = products.fold<double>(
      0,
      (sum, product) =>
          sum + (product.sellingPrice * (product.currentStock ?? 0)),
    );

    return Column(
      children: [
        // Summary cards
        Row(
          children: [
            Expanded(
              child: Card(
                color: Colors.blue[50],
                child: Padding(
                  padding: EdgeInsets.all(16.r),
                  child: Column(
                    children: [
                      Text(
                        'إجمالي قيمة التكلفة',
                        style: AppTextStyles.titleMedium.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${totalCostValue.toStringAsFixed(2)} ر.س',
                        style: AppTextStyles.titleLarge.copyWith(
                          color: Colors.blue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Card(
                color: Colors.green[50],
                child: Padding(
                  padding: EdgeInsets.all(16.r),
                  child: Column(
                    children: [
                      Text(
                        'إجمالي قيمة البيع',
                        style: AppTextStyles.titleMedium.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${totalSellingValue.toStringAsFixed(2)} ر.س',
                        style: AppTextStyles.titleLarge.copyWith(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Card(
                color: Colors.orange[50],
                child: Padding(
                  padding: EdgeInsets.all(16.r),
                  child: Column(
                    children: [
                      Text(
                        'هامش الربح المتوقع',
                        style: AppTextStyles.titleMedium.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${(totalSellingValue - totalCostValue).toStringAsFixed(2)} ر.س',
                        style: AppTextStyles.titleLarge.copyWith(
                          color: Colors.orange,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),

        // Detailed table
        Expanded(
          child: _buildProductsTable(products, [
            'الاسم',
            'المخزون',
            'سعر التكلفة',
            'قيمة التكلفة',
            'سعر البيع',
            'قيمة البيع',
            'هامش الربح',
          ]),
        ),
      ],
    );
  }

  Widget _buildProductsTable(List<Product> products, List<String> columns) {
    return SingleChildScrollView(
      child: DataTable(
        columns:
            columns.map((column) => DataColumn(label: Text(column))).toList(),
        rows: products.take(50).map((product) {
          return DataRow(
            cells: _buildTableCells(product, columns),
          );
        }).toList(),
      ),
    );
  }

  List<DataCell> _buildTableCells(Product product, List<String> columns) {
    return columns.map((column) {
      switch (column) {
        case 'الاسم':
          return DataCell(Text(product.getDisplayName()));
        case 'الباركود':
          return DataCell(Text(product.barcode ?? '-'));
        case 'الفئة':
          return DataCell(Text(product.categoryName ?? '-'));
        case 'المخزون':
        case 'المخزون الحالي':
          return DataCell(Text((product.currentStock ?? 0).toStringAsFixed(0)));
        case 'سعر التكلفة':
          return DataCell(Text('${product.costPrice.toStringAsFixed(2)} ر.س'));
        case 'سعر البيع':
          return DataCell(
              Text('${product.sellingPrice.toStringAsFixed(2)} ر.س'));
        case 'الحالة':
          return DataCell(
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
              decoration: BoxDecoration(
                color: product.isActiveProduct ? Colors.green : Colors.grey,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Text(
                product.isActiveProduct ? 'نشط' : 'غير نشط',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          );
        case 'نقطة إعادة الطلب':
          return DataCell(Text(product.reorderPoint.toStringAsFixed(0)));
        case 'الحد الأدنى':
          return DataCell(Text(product.minStock.toStringAsFixed(0)));
        case 'الحد الأقصى':
          return DataCell(Text(product.maxStock.toStringAsFixed(0)));
        case 'آخر تحديث':
          return DataCell(
              Text(product.updatedAt?.toString().split(' ')[0] ?? '-'));
        case 'هامش الربح':
          final margin = product.sellingPrice - product.costPrice;
          return DataCell(Text('${margin.toStringAsFixed(2)} ر.س'));
        case 'الوحدة':
          return DataCell(Text(product.unitName ?? '-'));
        case 'قيمة التكلفة':
          final costValue = product.costPrice * (product.currentStock ?? 0);
          return DataCell(Text('${costValue.toStringAsFixed(2)} ر.س'));
        case 'قيمة البيع':
          final sellValue = product.sellingPrice * (product.currentStock ?? 0);
          return DataCell(Text('${sellValue.toStringAsFixed(2)} ر.س'));
        default:
          return const DataCell(Text('-'));
      }
    }).toList();
  }

  String _getReportTypeName() {
    return _reportTypes
        .firstWhere((type) => type.id == _selectedReportType)
        .name;
  }

  void _previewReport() {
    // The preview is already shown in the UI
    context.showInfoSnackBar('المعاينة معروضة أدناه');
  }

  Future<void> _generatePDF() async {
    setState(() {
      _isGenerating = true;
    });

    try {
      final pdf = await _createPDF();

      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name:
            '${_getReportTypeName()}_${DateTime.now().millisecondsSinceEpoch}',
      );
    } catch (e) {
      context.showErrorSnackBar('فشل في إنشاء ملف PDF');
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  Future<pw.Document> _createPDF() async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Header
              pw.Text(
                _getReportTypeName(),
                style: pw.TextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 10),

              pw.Text(
                'تاريخ التقرير: ${_formatDate(DateTime.now())}',
                style: const pw.TextStyle(fontSize: 12),
              ),

              if (_selectedCategory != null)
                pw.Text(
                  'الفئة: $_selectedCategory',
                  style: const pw.TextStyle(fontSize: 12),
                ),

              pw.Text(
                'الفترة: ${_formatDate(_startDate)} - ${_formatDate(_endDate)}',
                style: const pw.TextStyle(fontSize: 12),
              ),

              pw.SizedBox(height: 20),

              // Content placeholder
              pw.Text(
                'محتوى التقرير سيتم إضافته هنا',
                style: const pw.TextStyle(fontSize: 14),
              ),
            ],
          );
        },
      ),
    );

    return pdf;
  }

  void _exportExcel() {
    // TODO: Implement Excel export
    context.showInfoSnackBar('تصدير Excel قيد التطوير');
  }

  Future<void> _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() {
        _startDate = date;
      });
    }
  }

  Future<void> _selectEndDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _endDate,
      firstDate: _startDate,
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() {
        _endDate = date;
      });
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

class ReportType {
  final String id;
  final String name;
  final String description;

  ReportType(this.id, this.name, this.description);
}
