import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../shared/widgets/loading_widget.dart';
import '../../shared/widgets/error_display_widget.dart';
import '../../shared/widgets/empty_state_widget.dart';
import '../../shared/widgets/search_bar_widget.dart';
import '../../shared/widgets/filter_chip_widget.dart';
import '../../data/models/stock.dart';
import '../../providers/stock_provider.dart';
import 'widgets/stock_card.dart';
import 'widgets/stock_filters_dialog.dart';
import 'widgets/stock_adjustment_dialog.dart';
import 'widgets/stock_transfer_dialog.dart';

class StockManagementPage extends ConsumerStatefulWidget {
  const StockManagementPage({super.key});

  @override
  ConsumerState<StockManagementPage> createState() =>
      _StockManagementPageState();
}

class _StockManagementPageState extends ConsumerState<StockManagementPage> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(stockManagementProvider.notifier).loadStocks();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(stockManagementProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'إدارة المخزون',
          style: AppTextStyles.titleLarge.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onPressed: () => _showFiltersDialog(),
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () =>
                ref.read(stockManagementProvider.notifier).refresh(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filters section
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20.r),
                bottomRight: Radius.circular(20.r),
              ),
            ),
            child: Column(
              children: [
                // Search bar
                SearchBarWidget(
                  controller: _searchController,
                  hintText: 'البحث في المخزون...',
                  onChanged: (value) {
                    ref
                        .read(stockManagementProvider.notifier)
                        .searchStocks(value);
                  },
                ),
                SizedBox(height: 12.h),

                // Active filters
                _buildActiveFilters(),
              ],
            ),
          ),

          // Statistics section
          _buildStatisticsSection(),

          // Stock list
          Expanded(
            child: _buildStockList(state),
          ),
        ],
      ),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          FloatingActionButton(
            heroTag: "transfer",
            onPressed: () => _showTransferDialog(),
            backgroundColor: Colors.blue,
            child: const Icon(Icons.swap_horiz, color: Colors.white),
          ),
          SizedBox(height: 16.h),
          FloatingActionButton(
            heroTag: "adjustment",
            onPressed: () => _showAdjustmentDialog(),
            backgroundColor: AppColors.primary,
            child: const Icon(Icons.tune, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveFilters() {
    final state = ref.watch(stockManagementProvider);
    final filters = <Widget>[];

    if (state.selectedWarehouse != null) {
      filters.add(FilterChipWidget(
        label: 'مخزن محدد',
        onDeleted: () =>
            ref.read(stockManagementProvider.notifier).filterByWarehouse(null),
      ));
    }

    if (state.selectedProduct != null) {
      filters.add(FilterChipWidget(
        label: 'منتج محدد',
        onDeleted: () =>
            ref.read(stockManagementProvider.notifier).filterByProduct(null),
      ));
    }

    if (state.statusFilter != null) {
      filters.add(FilterChipWidget(
        label: _getStatusText(state.statusFilter!),
        onDeleted: () =>
            ref.read(stockManagementProvider.notifier).filterByStatus(null),
      ));
    }

    if (filters.isNotEmpty) {
      filters.add(
        TextButton.icon(
          onPressed: () =>
              ref.read(stockManagementProvider.notifier).clearFilters(),
          icon: const Icon(Icons.clear_all, color: Colors.white70, size: 16),
          label: Text(
            'مسح الكل',
            style: AppTextStyles.bodySmall.copyWith(color: Colors.white70),
          ),
        ),
      );
    }

    return filters.isNotEmpty
        ? SizedBox(
            height: 40.h,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: filters.length,
              separatorBuilder: (context, index) => SizedBox(width: 8.w),
              itemBuilder: (context, index) => filters[index],
            ),
          )
        : const SizedBox.shrink();
  }

  Widget _buildStatisticsSection() {
    return Consumer(
      builder: (context, ref, child) {
        final statisticsAsync = ref.watch(stockStatisticsProvider);

        return statisticsAsync.when(
          data: (statistics) => Container(
            margin: EdgeInsets.all(16.w),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي الأصناف',
                    '${statistics['total_stock_items'] ?? 0}',
                    Icons.inventory,
                    AppColors.primary,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildStatCard(
                    'مخزون منخفض',
                    '${statistics['low_stock_items'] ?? 0}',
                    Icons.warning,
                    Colors.orange,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildStatCard(
                    'نفد المخزون',
                    '${statistics['out_of_stock_items'] ?? 0}',
                    Icons.error,
                    Colors.red,
                  ),
                ),
              ],
            ),
          ),
          loading: () => const SizedBox.shrink(),
          error: (error, stack) => const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.sp),
          SizedBox(height: 4.h),
          Text(
            value,
            style: AppTextStyles.titleMedium.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStockList(StockManagementState state) {
    if (state.isLoading) {
      return const LoadingWidget();
    }

    if (state.error != null) {
      return ErrorDisplayWidget(
        error: state.error!,
        onRetry: () => ref.read(stockManagementProvider.notifier).refresh(),
      );
    }

    if (state.filteredStocks.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.inventory,
        title: state.searchQuery.isNotEmpty ? 'لا توجد نتائج' : 'لا يوجد مخزون',
        subtitle: state.searchQuery.isNotEmpty
            ? 'جرب تغيير كلمات البحث أو المرشحات'
            : 'لا توجد أصناف في المخزون حالياً',
        actionText: 'تحديث',
        onActionPressed: () =>
            ref.read(stockManagementProvider.notifier).refresh(),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: state.filteredStocks.length,
      itemBuilder: (context, index) {
        final stock = state.filteredStocks[index];
        return StockCard(
          stock: stock,
          onTap: () => _showStockDetails(stock),
          onAdjust: () => _showAdjustmentDialog(stock: stock),
          onTransfer: () => _showTransferDialog(stock: stock),
        );
      },
    );
  }

  void _showFiltersDialog() {
    showDialog(
      context: context,
      builder: (context) => const StockFiltersDialog(),
    );
  }

  void _showAdjustmentDialog({Stock? stock}) {
    showDialog(
      context: context,
      builder: (context) => StockAdjustmentDialog(stock: stock),
    );
  }

  void _showTransferDialog({Stock? stock}) {
    showDialog(
      context: context,
      builder: (context) => StockTransferDialog(stock: stock),
    );
  }

  void _showStockDetails(Stock stock) {
    // Navigate to stock details page or show dialog
    // Implementation depends on your navigation structure
  }

  String _getStatusText(StockStatus status) {
    switch (status) {
      case StockStatus.inStock:
        return 'متوفر';
      case StockStatus.lowStock:
        return 'منخفض';
      case StockStatus.outOfStock:
        return 'نفد';
      case StockStatus.overStock:
        return 'زائد';
    }
  }
}
