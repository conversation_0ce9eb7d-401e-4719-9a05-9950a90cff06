import 'package:json_annotation/json_annotation.dart';
import '../../core/constants/database_constants.dart';

part 'category.g.dart';

@JsonSerializable()
class Category {
  final String id;
  final String nameAr;
  final String? nameEn;
  final String? parentId;
  final String? description;
  final String? imageUrl;
  final String? iconName;
  final String? color; // Hex color code
  final int sortOrder;
  final bool isActive;
  final double? taxRate; // معدل الضريبة للفئة
  final double? discountRate; // معدل الخصم للفئة
  final String? tags; // JSON string of tags
  final String? customFields; // JSON string for custom fields
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final bool isSynced;

  // Related data (not stored in main table)
  final List<Category>? children;
  final Category? parent;
  final int? productCount;
  final int? level; // مستوى الفئة في الشجرة

  const Category({
    required this.id,
    required this.nameAr,
    this.nameEn,
    this.parentId,
    this.description,
    this.imageUrl,
    this.iconName,
    this.color,
    this.sortOrder = 0,
    this.isActive = true,
    this.taxRate,
    this.discountRate,
    this.tags,
    this.customFields,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
    this.children,
    this.parent,
    this.productCount,
    this.level,
  });

 /* ============================================================
     Factory & Map – يستخدمان الثوابت الكاملة الآن
  ============================================================ */

  factory Category.fromMap(Map<String, dynamic> map) => Category(
        id: map[DatabaseConstants.columnCategoryId] as String,
        nameAr: map[DatabaseConstants.columnCategoryNameAr] as String,
        nameEn: map[DatabaseConstants.columnCategoryNameEn] as String?,
        parentId: map[DatabaseConstants.columnCategoryParentId] as String?,
        description: map[DatabaseConstants.columnCategoryDescription] as String?,
        imageUrl: map[DatabaseConstants.columnCategoryImageUrl] as String?,
        iconName: map[DatabaseConstants.columnCategoryIconName] as String?,
        color: map[DatabaseConstants.columnCategoryColor] as String?,
        sortOrder: (map[DatabaseConstants.columnCategorySortOrder] as int?) ?? 0,
        isActive:
            (map[DatabaseConstants.columnCategoryIsActive] as int? ?? 1) == 1,
        taxRate: (map[DatabaseConstants.columnCategoryTaxRate] as num?)?.toDouble(),
        discountRate:
            (map[DatabaseConstants.columnCategoryDiscountRate] as num?)?.toDouble(),
        tags: map[DatabaseConstants.columnCategoryTags] as String?,
        customFields:
            map[DatabaseConstants.columnCategoryCustomFieds] as String?,
        createdAt: map[DatabaseConstants.columnCategoryCreatedAt] != null
            ? DateTime.parse(map[DatabaseConstants.columnCategoryCreatedAt])
            : null,
        updatedAt: map[DatabaseConstants.columnCategoryUpdatedAt] != null
            ? DateTime.parse(map[DatabaseConstants.columnCategoryUpdatedAt])
            : null,
        deletedAt: map[DatabaseConstants.columnCategoryDeletedAt] != null
            ? DateTime.parse(map[DatabaseConstants.columnCategoryDeletedAt])
            : null,
        isSynced:
            (map[DatabaseConstants.columnCategoryIsSynced] as int? ?? 0) == 1,
        productCount: map['product_count'] as int?,
        level: map['level'] as int?,
      );

  Map<String, dynamic> toMap() => {
        DatabaseConstants.columnCategoryId: id,
        DatabaseConstants.columnCategoryNameAr: nameAr,
        DatabaseConstants.columnCategoryNameEn: nameEn,
        DatabaseConstants.columnCategoryParentId: parentId,
        DatabaseConstants.columnCategoryDescription: description,
        DatabaseConstants.columnCategoryImageUrl: imageUrl,
        DatabaseConstants.columnCategoryIconName: iconName,
        DatabaseConstants.columnCategoryColor: color,
        DatabaseConstants.columnCategorySortOrder: sortOrder,
        DatabaseConstants.columnCategoryIsActive: isActive ? 1 : 0,
        DatabaseConstants.columnCategoryTaxRate: taxRate,
        DatabaseConstants.columnCategoryDiscountRate: discountRate,
        DatabaseConstants.columnCategoryTags: tags,
        DatabaseConstants.columnCategoryCustomFieds: customFields,
        DatabaseConstants.columnCategoryCreatedAt: createdAt?.toIso8601String(),
        DatabaseConstants.columnCategoryUpdatedAt: updatedAt?.toIso8601String(),
        DatabaseConstants.columnCategoryDeletedAt: deletedAt?.toIso8601String(),
        DatabaseConstants.columnCategoryIsSynced: isSynced ? 1 : 0,
      };

  // JSON serialization
  factory Category.fromJson(Map<String, dynamic> json) =>
      _$CategoryFromJson(json);
  Map<String, dynamic> toJson() => _$CategoryToJson(this);

  // Copy with method for creating modified copies
  Category copyWith({
    String? id,
    String? nameAr,
    String? nameEn,
    String? parentId,
    String? description,
    String? imageUrl,
    String? iconName,
    String? color,
    int? sortOrder,
    bool? isActive,
    double? taxRate,
    double? discountRate,
    String? tags,
    String? customFields,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
    List<Category>? children,
    Category? parent,
    int? productCount,
    int? level,
  }) {
    return Category(
      id: id ?? this.id,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      parentId: parentId ?? this.parentId,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      iconName: iconName ?? this.iconName,
      color: color ?? this.color,
      sortOrder: sortOrder ?? this.sortOrder,
      isActive: isActive ?? this.isActive,
      taxRate: taxRate ?? this.taxRate,
      discountRate: discountRate ?? this.discountRate,
      tags: tags ?? this.tags,
      customFields: customFields ?? this.customFields,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
      children: children ?? this.children,
      parent: parent ?? this.parent,
      productCount: productCount ?? this.productCount,
      level: level ?? this.level,
    );
  }

  // Equality and hashCode
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Category && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // String representation
  @override
  String toString() {
    return 'Category(id: $id, nameAr: $nameAr, nameEn: $nameEn, parentId: $parentId, isSynced: $isSynced)';
  }

  // Helper methods
  bool get isDeleted => deletedAt != null;
  bool get isActiveCategory => !isDeleted && isActive;
  bool get hasParent => parentId != null && parentId!.isNotEmpty;
  bool get isMainCategory => !hasParent;
  bool get hasChildren => children != null && children!.isNotEmpty;
  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;
  bool get hasIcon => iconName != null && iconName!.isNotEmpty;
  bool get hasColor => color != null && color!.isNotEmpty;
  bool get hasTaxRate => taxRate != null && taxRate! > 0;
  bool get hasDiscountRate => discountRate != null && discountRate! > 0;

  // Get display name based on locale
  String getDisplayName([String locale = 'ar']) {
    if (locale == 'en' && nameEn != null && nameEn!.isNotEmpty) {
      return nameEn!;
    }
    return nameAr;
  }

  // Parse tags
  List<String> get tagsList {
    if (tags == null || tags!.isEmpty) return [];
    try {
      // Assuming tags are stored as comma-separated values
      return tags!
          .split(',')
          .map((tag) => tag.trim())
          .where((tag) => tag.isNotEmpty)
          .toList();
    } catch (e) {
      return [];
    }
  }

  // Get category path (breadcrumb)
  String getCategoryPath([String separator = ' > ']) {
    if (parent == null) return getDisplayName();
    return '${parent!.getCategoryPath(separator)}$separator${getDisplayName()}';
  }

  // Get category level in hierarchy
  int getCategoryLevel() {
    if (level != null) return level!;
    if (parent == null) return 0;
    return parent!.getCategoryLevel() + 1;
  }

  // Check if this category is ancestor of another category
  bool isAncestorOf(Category other) {
    if (other.parentId == id) return true;
    if (other.parent == null) return false;
    return isAncestorOf(other.parent!);
  }

  // Check if this category is descendant of another category
  bool isDescendantOf(Category other) {
    return other.isAncestorOf(this);
  }

  // Create a new category with current timestamp
  static Category create({
    required String id,
    required String nameAr,
    String? nameEn,
    String? parentId,
    String? description,
    String? imageUrl,
    String? iconName,
    String? color,
    int sortOrder = 0,
    bool isActive = true,
    double? taxRate,
    double? discountRate,
    String? tags,
    String? customFields,
  }) {
    final now = DateTime.now();
    return Category(
      id: id,
      nameAr: nameAr,
      nameEn: nameEn,
      parentId: parentId,
      description: description,
      imageUrl: imageUrl,
      iconName: iconName,
      color: color,
      sortOrder: sortOrder,
      isActive: isActive,
      taxRate: taxRate,
      discountRate: discountRate,
      tags: tags,
      customFields: customFields,
      createdAt: now,
      updatedAt: now,
      isSynced: false,
    );
  }

  // Update category with new timestamp
  Category update({
    String? nameAr,
    String? nameEn,
    String? parentId,
    String? description,
    String? imageUrl,
    String? iconName,
    String? color,
    int? sortOrder,
    bool? isActive,
    double? taxRate,
    double? discountRate,
    String? tags,
    String? customFields,
  }) {
    return copyWith(
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      parentId: parentId ?? this.parentId,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      iconName: iconName ?? this.iconName,
      color: color ?? this.color,
      sortOrder: sortOrder ?? this.sortOrder,
      isActive: isActive ?? this.isActive,
      taxRate: taxRate ?? this.taxRate,
      discountRate: discountRate ?? this.discountRate,
      tags: tags ?? this.tags,
      customFields: customFields ?? this.customFields,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as deleted
  Category markAsDeleted() {
    return copyWith(
      deletedAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as synced
  Category markAsSynced() {
    return copyWith(isSynced: true);
  }
}
