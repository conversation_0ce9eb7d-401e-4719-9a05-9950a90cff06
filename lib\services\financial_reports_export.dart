import 'dart:io';
import 'package:excel/excel.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import '../core/utils/formatters.dart';

/// خدمة تصدير التقارير المالية
class FinancialReportsExport {
  /// تصدير تقرير المبيعات والمشتريات إلى Excel
  static Future<String> exportSalesPurchasesToExcel({
    required List<Map<String, dynamic>> sales,
    required List<Map<String, dynamic>> purchases,
    required Map<String, dynamic> salesStats,
    required Map<String, dynamic> purchasesStats,
    required DateTimeRange dateRange,
  }) async {
    final excel = Excel.createExcel();

    // حذف الورقة الافتراضية
    excel.delete('Sheet1');

    // إنشاء ورقة المبيعات
    final salesSheet = excel['المبيعات'];
    _addSalesData(salesSheet, sales, salesStats);

    // إنشاء ورقة المشتريات
    final purchasesSheet = excel['المشتريات'];
    _addPurchasesData(purchasesSheet, purchases, purchasesStats);

    // إنشاء ورقة الملخص
    final summarySheet = excel['الملخص'];
    _addSummaryData(summarySheet, salesStats, purchasesStats, dateRange);

    // حفظ الملف
    final directory = await getApplicationDocumentsDirectory();
    final fileName =
        'تقرير_المبيعات_والمشتريات_${DateTime.now().millisecondsSinceEpoch}.xlsx';
    final file = File('${directory.path}/$fileName');

    final bytes = excel.encode();
    if (bytes != null) {
      await file.writeAsBytes(bytes);
    }

    return file.path;
  }

  /// تصدير تقرير الأرباح والخسائر إلى Excel
  static Future<String> exportProfitLossToExcel({
    required Map<String, dynamic> profitLossData,
    required DateTimeRange dateRange,
  }) async {
    final excel = Excel.createExcel();
    excel.delete('Sheet1');

    final sheet = excel['الأرباح والخسائر'];

    // إضافة العنوان
    sheet.cell(CellIndex.indexByString('A1')).value =
        TextCellValue('تقرير الأرباح والخسائر');
    sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue(
        'من ${AppFormatters.formatDate(dateRange.start)} إلى ${AppFormatters.formatDate(dateRange.end)}');

    int row = 4;

    // الإيرادات
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('الإيرادات');
    sheet.cell(CellIndex.indexByString('B$row')).value = DoubleCellValue(
        (profitLossData['totalRevenue'] as num?)?.toDouble() ?? 0.0);
    row++;

    // تكلفة البضاعة المباعة
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('تكلفة البضاعة المباعة');
    sheet.cell(CellIndex.indexByString('B$row')).value = DoubleCellValue(
        (profitLossData['totalCOGS'] as num?)?.toDouble() ?? 0.0);
    row++;

    // الربح الإجمالي
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('الربح الإجمالي');
    sheet.cell(CellIndex.indexByString('B$row')).value = DoubleCellValue(
        (profitLossData['grossProfit'] as num?)?.toDouble() ?? 0.0);
    row++;

    // هامش الربح الإجمالي
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('هامش الربح الإجمالي %');
    sheet.cell(CellIndex.indexByString('B$row')).value = DoubleCellValue(
        (profitLossData['grossProfitMargin'] as num?)?.toDouble() ?? 0.0);
    row++;

    // المصروفات التشغيلية
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('المصروفات التشغيلية');
    sheet.cell(CellIndex.indexByString('B$row')).value = DoubleCellValue(
        (profitLossData['operatingExpenses'] as num?)?.toDouble() ?? 0.0);
    row++;

    // الربح التشغيلي
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('الربح التشغيلي');
    sheet.cell(CellIndex.indexByString('B$row')).value = DoubleCellValue(
        (profitLossData['operatingProfit'] as num?)?.toDouble() ?? 0.0);
    row++;

    // صافي الربح
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('صافي الربح');
    sheet.cell(CellIndex.indexByString('B$row')).value = DoubleCellValue(
        (profitLossData['netProfit'] as num?)?.toDouble() ?? 0.0);

    // حفظ الملف
    final directory = await getApplicationDocumentsDirectory();
    final fileName =
        'تقرير_الأرباح_والخسائر_${DateTime.now().millisecondsSinceEpoch}.xlsx';
    final file = File('${directory.path}/$fileName');

    final bytes = excel.encode();
    if (bytes != null) {
      await file.writeAsBytes(bytes);
    }

    return file.path;
  }

  /// تصدير تقرير العملاء والموردين إلى Excel
  static Future<String> exportAccountsToExcel({
    required List<Map<String, dynamic>> customers,
    required List<Map<String, dynamic>> suppliers,
    required Map<String, dynamic> accountsSummary,
  }) async {
    final excel = Excel.createExcel();
    excel.delete('Sheet1');

    // ورقة العملاء
    final customersSheet = excel['العملاء'];
    _addCustomersData(customersSheet, customers);

    // ورقة الموردين
    final suppliersSheet = excel['الموردين'];
    _addSuppliersData(suppliersSheet, suppliers);

    // ورقة الملخص
    final summarySheet = excel['ملخص الحسابات'];
    _addAccountsSummaryData(summarySheet, accountsSummary);

    // حفظ الملف
    final directory = await getApplicationDocumentsDirectory();
    final fileName =
        'تقرير_العملاء_والموردين_${DateTime.now().millisecondsSinceEpoch}.xlsx';
    final file = File('${directory.path}/$fileName');

    final bytes = excel.encode();
    if (bytes != null) {
      await file.writeAsBytes(bytes);
    }

    return file.path;
  }

  /// تصدير لوحة التحكم المالية إلى PDF
  static Future<String> exportDashboardToPDF({
    required Map<String, dynamic> dashboardData,
    required List<Map<String, dynamic>> recentTransactions,
  }) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // العنوان
              pw.Text(
                'لوحة التحكم المالية',
                style:
                    pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(height: 20),

              // المؤشرات المالية
              pw.Text(
                'المؤشرات المالية الرئيسية',
                style:
                    pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(height: 10),

              _buildPDFTable([
                ['المؤشر', 'القيمة'],
                [
                  'إجمالي الإيرادات',
                  AppFormatters.formatCurrency(
                     double.parse(dashboardData['totalRevenue'].toString()) ?? 0.0)
                ],
                [
                  'إجمالي المصروفات',
                  AppFormatters.formatCurrency(
                     double.parse(dashboardData['totalExpenses'].toString()) ?? 0.0)
                ],
                [
                  'صافي الربح',
                  AppFormatters.formatCurrency(
                      double.parse(dashboardData['netProfit'].toString()) ?? 0.0)
                ],
                [
                  'هامش الربح %',
                  '${(dashboardData['profitMargin'] ?? 0.0).toStringAsFixed(1)}%'
                ],
                ['عدد المبيعات', '${dashboardData['totalSales'] ?? 0}'],
                ['عدد المشتريات', '${dashboardData['totalPurchases'] ?? 0}'],
                ['عدد العملاء', '${dashboardData['totalCustomers'] ?? 0}'],
                ['عدد الموردين', '${dashboardData['totalSuppliers'] ?? 0}'],
              ]),

              pw.SizedBox(height: 20),

              // المعاملات الأخيرة
              pw.Text(
                'المعاملات الأخيرة',
                style:
                    pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(height: 10),

              _buildTransactionsPDFTable(recentTransactions),
            ],
          );
        },
      ),
    );

    // حفظ الملف
    final directory = await getApplicationDocumentsDirectory();
    final fileName =
        'لوحة_التحكم_المالية_${DateTime.now().millisecondsSinceEpoch}.pdf';
    final file = File('${directory.path}/$fileName');

    await file.writeAsBytes(await pdf.save());

    return file.path;
  }

  // طرق مساعدة لإضافة البيانات إلى Excel
  static void _addSalesData(Sheet sheet, List<Map<String, dynamic>> sales,
      Map<String, dynamic> stats) {
    // إضافة العناوين
    sheet.cell(CellIndex.indexByString('A1')).value =
        TextCellValue('رقم الفاتورة');
    sheet.cell(CellIndex.indexByString('B1')).value = TextCellValue('التاريخ');
    sheet.cell(CellIndex.indexByString('C1')).value = TextCellValue('العميل');
    sheet.cell(CellIndex.indexByString('D1')).value =
        TextCellValue('المبلغ الإجمالي');
    sheet.cell(CellIndex.indexByString('E1')).value = TextCellValue('المدفوع');
    sheet.cell(CellIndex.indexByString('F1')).value = TextCellValue('المتبقي');

    // إضافة البيانات
    for (int i = 0; i < sales.length; i++) {
      final sale = sales[i];
      final row = i + 2;

      sheet.cell(CellIndex.indexByString('A$row')).value =
          TextCellValue(sale['invoice_no']?.toString() ?? '');
      sheet.cell(CellIndex.indexByString('B$row')).value =
          TextCellValue(sale['sale_date']?.toString() ?? '');
      sheet.cell(CellIndex.indexByString('C$row')).value =
          TextCellValue(sale['customer_name']?.toString() ?? 'عميل نقدي');
      sheet.cell(CellIndex.indexByString('D$row')).value =
          DoubleCellValue((sale['total'] as num?)?.toDouble() ?? 0.0);
      sheet.cell(CellIndex.indexByString('E$row')).value =
          DoubleCellValue((sale['paid'] as num?)?.toDouble() ?? 0.0);
      sheet.cell(CellIndex.indexByString('F$row')).value =
          DoubleCellValue((sale['due'] as num?)?.toDouble() ?? 0.0);
    }

    // إضافة الإحصائيات
    final statsRow = sales.length + 3;
    sheet.cell(CellIndex.indexByString('A$statsRow')).value =
        TextCellValue('الإجماليات:');
    sheet.cell(CellIndex.indexByString('D$statsRow')).value =
        DoubleCellValue((stats['totalAmount'] as num?)?.toDouble() ?? 0.0);
    sheet.cell(CellIndex.indexByString('E$statsRow')).value =
        DoubleCellValue((stats['totalPaid'] as num?)?.toDouble() ?? 0.0);
    sheet.cell(CellIndex.indexByString('F$statsRow')).value =
        DoubleCellValue((stats['totalDue'] as num?)?.toDouble() ?? 0.0);
  }

  static void _addPurchasesData(Sheet sheet,
      List<Map<String, dynamic>> purchases, Map<String, dynamic> stats) {
    // إضافة العناوين
    sheet.cell(CellIndex.indexByString('A1')).value =
        TextCellValue('رقم الفاتورة');
    sheet.cell(CellIndex.indexByString('B1')).value = TextCellValue('التاريخ');
    sheet.cell(CellIndex.indexByString('C1')).value = TextCellValue('المورد');
    sheet.cell(CellIndex.indexByString('D1')).value =
        TextCellValue('المبلغ الإجمالي');
    sheet.cell(CellIndex.indexByString('E1')).value = TextCellValue('المدفوع');
    sheet.cell(CellIndex.indexByString('F1')).value = TextCellValue('المتبقي');

    // إضافة البيانات
    for (int i = 0; i < purchases.length; i++) {
      final purchase = purchases[i];
      final row = i + 2;

      sheet.cell(CellIndex.indexByString('A$row')).value =
          TextCellValue(purchase['invoice_no']?.toString() ?? '');
      sheet.cell(CellIndex.indexByString('B$row')).value =
          TextCellValue(purchase['purchase_date']?.toString() ?? '');
      sheet.cell(CellIndex.indexByString('C$row')).value =
          TextCellValue(purchase['supplier_name']?.toString() ?? 'مورد نقدي');
      sheet.cell(CellIndex.indexByString('D$row')).value = DoubleCellValue(
          (purchase['total_amount'] as num?)?.toDouble() ?? 0.0);
      sheet.cell(CellIndex.indexByString('E$row')).value =
          DoubleCellValue((purchase['paid_amount'] as num?)?.toDouble() ?? 0.0);
      sheet.cell(CellIndex.indexByString('F$row')).value =
          DoubleCellValue((purchase['due_amount'] as num?)?.toDouble() ?? 0.0);
    }

    // إضافة الإحصائيات
    final statsRow = purchases.length + 3;
    sheet.cell(CellIndex.indexByString('A$statsRow')).value =
        TextCellValue('الإجماليات:');
    sheet.cell(CellIndex.indexByString('D$statsRow')).value =
        DoubleCellValue((stats['totalAmount'] as num?)?.toDouble() ?? 0.0);
    sheet.cell(CellIndex.indexByString('E$statsRow')).value =
        DoubleCellValue((stats['totalPaid'] as num?)?.toDouble() ?? 0.0);
    sheet.cell(CellIndex.indexByString('F$statsRow')).value =
        DoubleCellValue((stats['totalDue'] as num?)?.toDouble() ?? 0.0);
  }

  static void _addSummaryData(Sheet sheet, Map<String, dynamic> salesStats,
      Map<String, dynamic> purchasesStats, DateTimeRange dateRange) {
    sheet.cell(CellIndex.indexByString('A1')).value =
        TextCellValue('ملخص المبيعات والمشتريات');
    sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue(
        'من ${AppFormatters.formatDate(dateRange.start)} إلى ${AppFormatters.formatDate(dateRange.end)}');

    int row = 4;

    // إحصائيات المبيعات
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('المبيعات');
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('عدد الفواتير');
    sheet.cell(CellIndex.indexByString('B$row')).value =
        IntCellValue(salesStats['totalSales'] ?? 0);
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('إجمالي المبلغ');
    sheet.cell(CellIndex.indexByString('B$row')).value =
        DoubleCellValue((salesStats['totalAmount'] as num?)?.toDouble() ?? 0.0);
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('المدفوع');
    sheet.cell(CellIndex.indexByString('B$row')).value =
        DoubleCellValue((salesStats['totalPaid'] as num?)?.toDouble() ?? 0.0);
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('المتبقي');
    sheet.cell(CellIndex.indexByString('B$row')).value =
        DoubleCellValue((salesStats['totalDue'] as num?)?.toDouble() ?? 0.0);
    row += 2;

    // إحصائيات المشتريات
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('المشتريات');
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('عدد الفواتير');
    sheet.cell(CellIndex.indexByString('B$row')).value =
        IntCellValue(purchasesStats['totalPurchases'] ?? 0);
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('إجمالي المبلغ');
    sheet.cell(CellIndex.indexByString('B$row')).value = DoubleCellValue(
        (purchasesStats['totalAmount'] as num?)?.toDouble() ?? 0.0);
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('المدفوع');
    sheet.cell(CellIndex.indexByString('B$row')).value = DoubleCellValue(
        (purchasesStats['totalPaid'] as num?)?.toDouble() ?? 0.0);
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('المتبقي');
    sheet.cell(CellIndex.indexByString('B$row')).value = DoubleCellValue(
        (purchasesStats['totalDue'] as num?)?.toDouble() ?? 0.0);
    row += 2;

    // الربح
    final profit = ((salesStats['totalAmount'] as num?)?.toDouble() ?? 0.0) -
        ((purchasesStats['totalAmount'] as num?)?.toDouble() ?? 0.0);
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('صافي الربح');
    sheet.cell(CellIndex.indexByString('B$row')).value =
        DoubleCellValue(profit);
  }

  static void _addCustomersData(
      Sheet sheet, List<Map<String, dynamic>> customers) {
    // إضافة العناوين
    sheet.cell(CellIndex.indexByString('A1')).value =
        TextCellValue('اسم العميل');
    sheet.cell(CellIndex.indexByString('B1')).value =
        TextCellValue('إجمالي المبيعات');
    sheet.cell(CellIndex.indexByString('C1')).value = TextCellValue('المدفوع');
    sheet.cell(CellIndex.indexByString('D1')).value = TextCellValue('المتبقي');
    sheet.cell(CellIndex.indexByString('E1')).value =
        TextCellValue('عدد الفواتير');

    // إضافة البيانات
    for (int i = 0; i < customers.length; i++) {
      final customer = customers[i];
      final row = i + 2;

      sheet.cell(CellIndex.indexByString('A$row')).value =
          TextCellValue(customer['name']?.toString() ?? '');
      sheet.cell(CellIndex.indexByString('B$row')).value =
          DoubleCellValue((customer['totalSales'] as num?)?.toDouble() ?? 0.0);
      sheet.cell(CellIndex.indexByString('C$row')).value =
          DoubleCellValue((customer['totalPaid'] as num?)?.toDouble() ?? 0.0);
      sheet.cell(CellIndex.indexByString('D$row')).value =
          DoubleCellValue((customer['totalDue'] as num?)?.toDouble() ?? 0.0);
      sheet.cell(CellIndex.indexByString('E$row')).value =
          IntCellValue(customer['salesCount'] ?? 0);
    }
  }

  static void _addSuppliersData(
      Sheet sheet, List<Map<String, dynamic>> suppliers) {
    // إضافة العناوين
    sheet.cell(CellIndex.indexByString('A1')).value =
        TextCellValue('اسم المورد');
    sheet.cell(CellIndex.indexByString('B1')).value =
        TextCellValue('إجمالي المشتريات');
    sheet.cell(CellIndex.indexByString('C1')).value = TextCellValue('المدفوع');
    sheet.cell(CellIndex.indexByString('D1')).value = TextCellValue('المتبقي');
    sheet.cell(CellIndex.indexByString('E1')).value =
        TextCellValue('عدد الفواتير');

    // إضافة البيانات
    for (int i = 0; i < suppliers.length; i++) {
      final supplier = suppliers[i];
      final row = i + 2;

      sheet.cell(CellIndex.indexByString('A$row')).value =
          TextCellValue(supplier['name']?.toString() ?? '');
      sheet.cell(CellIndex.indexByString('B$row')).value = DoubleCellValue(
          (supplier['totalPurchases'] as num?)?.toDouble() ?? 0.0);
      sheet.cell(CellIndex.indexByString('C$row')).value =
          DoubleCellValue((supplier['totalPaid'] as num?)?.toDouble() ?? 0.0);
      sheet.cell(CellIndex.indexByString('D$row')).value =
          DoubleCellValue((supplier['totalDue'] as num?)?.toDouble() ?? 0.0);
      sheet.cell(CellIndex.indexByString('E$row')).value =
          IntCellValue(supplier['purchasesCount'] ?? 0);
    }
  }

  static void _addAccountsSummaryData(
      Sheet sheet, Map<String, dynamic> summary) {
    sheet.cell(CellIndex.indexByString('A1')).value =
        TextCellValue('ملخص الحسابات');

    int row = 3;

    // إحصائيات العملاء
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('العملاء');
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('إجمالي العملاء');
    sheet.cell(CellIndex.indexByString('B$row')).value =
        IntCellValue(summary['totalCustomers'] ?? 0);
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('العملاء النشطين');
    sheet.cell(CellIndex.indexByString('B$row')).value =
        IntCellValue(summary['activeCustomers'] ?? 0);
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('إجمالي مبيعات العملاء');
    sheet.cell(CellIndex.indexByString('B$row')).value = DoubleCellValue(
        (summary['totalCustomerSales'] as num?)?.toDouble() ?? 0.0);
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('المتبقي من العملاء');
    sheet.cell(CellIndex.indexByString('B$row')).value = DoubleCellValue(
        (summary['totalCustomerDue'] as num?)?.toDouble() ?? 0.0);
    row += 2;

    // إحصائيات الموردين
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('الموردين');
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('إجمالي الموردين');
    sheet.cell(CellIndex.indexByString('B$row')).value =
        IntCellValue(summary['totalSuppliers'] ?? 0);
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('الموردين النشطين');
    sheet.cell(CellIndex.indexByString('B$row')).value =
        IntCellValue(summary['activeSuppliers'] ?? 0);
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('إجمالي مشتريات الموردين');
    sheet.cell(CellIndex.indexByString('B$row')).value = DoubleCellValue(
        (summary['totalSupplierPurchases'] as num?)?.toDouble() ?? 0.0);
    row++;
    sheet.cell(CellIndex.indexByString('A$row')).value =
        TextCellValue('المتبقي للموردين');
    sheet.cell(CellIndex.indexByString('B$row')).value = DoubleCellValue(
        (summary['totalSupplierDue'] as num?)?.toDouble() ?? 0.0);
  }

  // طرق مساعدة لـ PDF
  static pw.Widget _buildPDFTable(List<List<String>> data) {
    return pw.Table(
      border: pw.TableBorder.all(),
      children: data.asMap().entries.map((entry) {
        final isHeader = entry.key == 0;
        return pw.TableRow(
          children: entry.value.map((cell) {
            return pw.Container(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                cell,
                style: pw.TextStyle(
                  fontWeight:
                      isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
                ),
                textAlign: pw.TextAlign.center,
              ),
            );
          }).toList(),
        );
      }).toList(),
    );
  }

  static pw.Widget _buildTransactionsPDFTable(
      List<Map<String, dynamic>> transactions) {
    final data = <List<String>>[
      ['النوع', 'الوصف', 'المبلغ', 'التاريخ'],
    ];

    for (final transaction in transactions.take(10)) {
      data.add([
        transaction['type'] == 'sale' ? 'مبيعات' : 'مشتريات',
        transaction['description']?.toString() ?? '',
        AppFormatters.formatCurrency(
            (transaction['amount'] as num?)?.toDouble() ?? 0.0),
        AppFormatters.formatDate(
            DateTime.tryParse(transaction['date']?.toString() ?? '') ??
                DateTime.now()),
      ]);
    }

    return pw.Table(
      border: pw.TableBorder.all(),
      children: data.asMap().entries.map((entry) {
        final isHeader = entry.key == 0;
        return pw.TableRow(
          children: entry.value.map((cell) {
            return pw.Container(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                cell,
                style: pw.TextStyle(
                  fontWeight:
                      isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
                ),
                textAlign: pw.TextAlign.center,
              ),
            );
          }).toList(),
        );
      }).toList(),
    );
  }
}
