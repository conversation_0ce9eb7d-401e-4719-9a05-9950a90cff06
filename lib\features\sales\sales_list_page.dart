import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:tijari_tech/shared/widgets/loading_widget.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/formatters.dart';
import '../../data/models/sale.dart';
import '../../providers/sale_provider.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/error_widget.dart';
import '../../widgets/empty_state_widget.dart';
import '../../widgets/custom_card.dart';
import '../../widgets/custom_button.dart';

class SalesListPage extends ConsumerStatefulWidget {
  const SalesListPage({super.key});

  @override
  ConsumerState<SalesListPage> createState() => _SalesListPageState();
}

class _SalesListPageState extends ConsumerState<SalesListPage> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedFilter = 'all'; // all, paid, unpaid, partial

  @override
  void initState() {
    super.initState();
    // تحميل المبيعات عند فتح الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(saleManagementProvider.notifier).refresh();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final saleState = ref.watch(saleManagementProvider);

    return MainLayout(
      title: 'قائمة المبيعات',
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go('/sales/add'),
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
      child: Column(
        children: [
          _buildSearchAndFilters(),
          SizedBox(height: 16.h),
          _buildSalesStats(saleState),
          SizedBox(height: 16.h),
          Expanded(
            child: _buildSalesList(saleState),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث برقم الفاتورة أو اسم العميل...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          SizedBox(height: 12.h),

          // فلاتر الحالة
          Row(
            children: [
              Expanded(
                child: _buildFilterChip('الكل', 'all'),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildFilterChip('مدفوع', 'paid'),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildFilterChip('غير مدفوع', 'unpaid'),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildFilterChip('جزئي', 'partial'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = _selectedFilter == value;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedFilter = value;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.grey[200],
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Text(
          label,
          textAlign: TextAlign.center,
          style: AppTextStyles.bodyMedium.copyWith(
            color: isSelected ? Colors.white : Colors.grey[700],
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildSalesStats(SaleManagementState state) {
    final filteredSales = _getFilteredSales(state.sales);
    final totalAmount =
        filteredSales.fold<double>(0, (sum, sale) => sum + sale.total);
    final paidAmount =
        filteredSales.fold<double>(0, (sum, sale) => sum + sale.paid);
    final dueAmount =
        filteredSales.fold<double>(0, (sum, sale) => sum + sale.due);

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'إجمالي المبيعات',
              AppFormatters.formatCurrency(totalAmount),
              Icons.trending_up,
              AppColors.success,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildStatCard(
              'المبلغ المدفوع',
              AppFormatters.formatCurrency(paidAmount),
              Icons.payment,
              AppColors.primary,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildStatCard(
              'المبلغ المستحق',
              AppFormatters.formatCurrency(dueAmount),
              Icons.schedule,
              AppColors.warning,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return CustomCard(
      padding: EdgeInsets.all(12.r),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.r),
          SizedBox(height: 8.h),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 4.h),
          Text(
            value,
            style: AppTextStyles.titleMedium.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSalesList(SaleManagementState state) {
    if (state.isLoading) {
      return const LoadingWidget();
    }

    if (state.error != null) {
      return CustomErrorWidget(
        message: state.error!,
        onRetry: () => ref.read(saleManagementProvider.notifier).refresh(),
      );
    }

    final filteredSales = _getFilteredSales(state.sales);

    if (filteredSales.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.receipt_long,
        title: 'لا توجد مبيعات',
        subtitle: _searchQuery.isNotEmpty || _selectedFilter != 'all'
            ? 'لا توجد مبيعات تطابق البحث أو الفلتر المحدد'
            : 'لم يتم إنشاء أي فاتورة مبيعات بعد',
        actionText: 'إنشاء فاتورة جديدة',
        onAction: () => context.go('/sales/add'),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      itemCount: filteredSales.length,
      itemBuilder: (context, index) {
        final sale = filteredSales[index];
        return _buildSaleCard(sale);
      },
    );
  }

  Widget _buildSaleCard(Sale sale) {
    return CustomCard(
      margin: EdgeInsets.only(bottom: 12.h),
      child: InkWell(
        onTap: () => context.go('/sales/view/${sale.id}'),
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'فاتورة رقم: ${sale.invoiceNo}',
                    style: AppTextStyles.titleMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  _buildPaymentStatusChip(sale),
                ],
              ),
              SizedBox(height: 8.h),
              if (sale.hasCustomer) ...[
                Row(
                  children: [
                    Icon(Icons.person, size: 16.r, color: Colors.grey[600]),
                    SizedBox(width: 4.w),
                    Text(
                      'العميل: ${sale.customerId}', // يمكن تحسينه لعرض اسم العميل
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4.h),
              ],
              Row(
                children: [
                  Icon(Icons.calendar_today,
                      size: 16.r, color: Colors.grey[600]),
                  SizedBox(width: 4.w),
                  Text(
                    AppFormatters.formatDate(sale.date),
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الإجمالي: ${AppFormatters.formatCurrency(sale.total)}',
                        style: AppTextStyles.titleMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.success,
                        ),
                      ),
                      if (sale.due > 0) ...[
                        SizedBox(height: 2.h),
                        Text(
                          'المستحق: ${AppFormatters.formatCurrency(sale.due)}',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.warning,
                          ),
                        ),
                      ],
                    ],
                  ),
                  Row(
                    children: [
                      IconButton(
                        onPressed: () => _showSaleActions(sale),
                        icon: const Icon(Icons.more_vert),
                        iconSize: 20.r,
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentStatusChip(Sale sale) {
    String text;
    Color color;

    if (sale.isPaid) {
      text = 'مدفوع';
      color = AppColors.success;
    } else if (sale.isPartiallyPaid) {
      text = 'جزئي';
      color = AppColors.warning;
    } else {
      text = 'غير مدفوع';
      color = AppColors.error;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: AppTextStyles.bodySmall.copyWith(
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  List<Sale> _getFilteredSales(List<Sale> sales) {
    var filtered = sales.where((sale) {
      // فلتر البحث
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!sale.invoiceNo.toLowerCase().contains(query) &&
            !(sale.customerId?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // فلتر الحالة
      switch (_selectedFilter) {
        case 'paid':
          return sale.isPaid;
        case 'unpaid':
          return sale.isUnpaid;
        case 'partial':
          return sale.isPartiallyPaid;
        default:
          return true;
      }
    }).toList();

    // ترتيب حسب التاريخ (الأحدث أولاً)
    filtered.sort((a, b) => b.date.compareTo(a.date));
    return filtered;
  }

  void _showSaleActions(Sale sale) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(16.r),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.visibility),
              title: const Text('عرض الفاتورة'),
              onTap: () {
                Navigator.pop(context);
                context.go('/sales/view/${sale.id}');
              },
            ),
            if (sale.due > 0) ...[
              ListTile(
                leading: const Icon(Icons.payment),
                title: const Text('إضافة دفعة'),
                onTap: () {
                  Navigator.pop(context);
                  _showPaymentDialog(sale);
                },
              ),
            ],
            ListTile(
              leading: const Icon(Icons.print),
              title: const Text('طباعة الفاتورة'),
              onTap: () {
                Navigator.pop(context);
                _printSale(sale);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('حذف الفاتورة',
                  style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _confirmDeleteSale(sale);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showPaymentDialog(Sale sale) {
    // TODO: تنفيذ حوار إضافة دفعة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تنفيذ إضافة الدفعة قريباً')),
    );
  }

  void _printSale(Sale sale) {
    // TODO: تنفيذ طباعة الفاتورة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تنفيذ الطباعة قريباً')),
    );
  }

  void _confirmDeleteSale(Sale sale) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف فاتورة رقم ${sale.invoiceNo}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteSale(sale);
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _deleteSale(Sale sale) async {
    try {
      await ref.read(saleManagementProvider.notifier).deleteSale(sale.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حذف الفاتورة بنجاح')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في حذف الفاتورة: $e')),
        );
      }
    }
  }
}
