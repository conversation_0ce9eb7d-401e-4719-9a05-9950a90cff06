import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../shared/widgets/loading_widget.dart';
import '../../shared/widgets/error_display_widget.dart';
import '../../shared/widgets/empty_state_widget.dart';
import '../../shared/widgets/search_bar_widget.dart';
import '../../shared/widgets/filter_chip_widget.dart';

import '../../data/models/warehouse.dart';
import '../../providers/warehouse_provider.dart';
import 'widgets/warehouse_card.dart';
import 'widgets/warehouse_form_dialog.dart';
import 'widgets/warehouse_details_dialog.dart';
import 'widgets/warehouse_filters_dialog.dart';

class WarehousesPage extends ConsumerStatefulWidget {
  const WarehousesPage({super.key});

  @override
  ConsumerState<WarehousesPage> createState() => _WarehousesPageState();
}

class _WarehousesPageState extends ConsumerState<WarehousesPage> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(warehouseManagementProvider.notifier).loadWarehouses();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(warehouseManagementProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'إدارة المخازن',
          style: AppTextStyles.titleLarge.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onPressed: () => _showFiltersDialog(),
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () =>
                ref.read(warehouseManagementProvider.notifier).refresh(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filters section
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20.r),
                bottomRight: Radius.circular(20.r),
              ),
            ),
            child: Column(
              children: [
                // Search bar
                SearchBarWidget(
                  controller: _searchController,
                  hintText: 'البحث في المخازن...',
                  onChanged: (value) {
                    ref
                        .read(warehouseManagementProvider.notifier)
                        .searchWarehouses(value);
                  },
                ),
                SizedBox(height: 12.h),

                // Active filters
                _buildActiveFilters(),
              ],
            ),
          ),

          // Statistics section
          _buildStatisticsSection(),

          // Warehouses list
          Expanded(
            child: _buildWarehousesList(state),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAddWarehouseDialog(),
        backgroundColor: AppColors.primary,
        icon: const Icon(Icons.add, color: Colors.white),
        label: Text(
          'إضافة مخزن',
          style: AppTextStyles.bodyMedium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildActiveFilters() {
    final state = ref.watch(warehouseManagementProvider);
    final filters = <Widget>[];

    if (state.selectedBranch != null) {
      filters.add(FilterChipWidget(
        label: 'فرع محدد',
        onDeleted: () =>
            ref.read(warehouseManagementProvider.notifier).filterByBranch(null),
      ));
    }

    if (state.selectedManager != null) {
      filters.add(FilterChipWidget(
        label: 'مدير محدد',
        onDeleted: () => ref
            .read(warehouseManagementProvider.notifier)
            .filterByManager(null),
      ));
    }

    if (state.isActiveFilter != null) {
      filters.add(FilterChipWidget(
        label: state.isActiveFilter! ? 'نشط' : 'غير نشط',
        onDeleted: () => ref
            .read(warehouseManagementProvider.notifier)
            .filterByActiveStatus(null),
      ));
    }

    if (state.warehouseTypeFilter != null) {
      filters.add(FilterChipWidget(
        label: _getWarehouseTypeText(state.warehouseTypeFilter!),
        onDeleted: () => ref
            .read(warehouseManagementProvider.notifier)
            .filterByWarehouseType(null),
      ));
    }

    if (filters.isNotEmpty) {
      filters.add(
        TextButton.icon(
          onPressed: () =>
              ref.read(warehouseManagementProvider.notifier).clearFilters(),
          icon: const Icon(Icons.clear_all, color: Colors.white70, size: 16),
          label: Text(
            'مسح الكل',
            style: AppTextStyles.bodySmall.copyWith(color: Colors.white70),
          ),
        ),
      );
    }

    return filters.isNotEmpty
        ? SizedBox(
            height: 40.h,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: filters.length,
              separatorBuilder: (context, index) => SizedBox(width: 8.w),
              itemBuilder: (context, index) => filters[index],
            ),
          )
        : const SizedBox.shrink();
  }

  Widget _buildStatisticsSection() {
    return Consumer(
      builder: (context, ref, child) {
        final statisticsAsync = ref.watch(warehousesStatisticsProvider);

        return statisticsAsync.when(
          data: (statistics) => Container(
            margin: EdgeInsets.all(16.w),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي المخازن',
                    '${statistics['total_warehouses'] ?? 0}',
                    Icons.warehouse,
                    AppColors.primary,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildStatCard(
                    'المخازن النشطة',
                    '${statistics['active_warehouses'] ?? 0}',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildStatCard(
                    'المخازن الافتراضية',
                    '${statistics['default_warehouses'] ?? 0}',
                    Icons.star,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ),
          loading: () => const SizedBox.shrink(),
          error: (error, stack) => const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.sp),
          SizedBox(height: 4.h),
          Text(
            value,
            style: AppTextStyles.titleMedium.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildWarehousesList(WarehouseManagementState state) {
    if (state.isLoading) {
      return const LoadingWidget();
    }

    if (state.error != null) {
      return ErrorDisplayWidget(
        error: state.error!,
        onRetry: () => ref.read(warehouseManagementProvider.notifier).refresh(),
      );
    }

    if (state.filteredWarehouses.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.warehouse,
        title: state.searchQuery.isNotEmpty ? 'لا توجد نتائج' : 'لا توجد مخازن',
        subtitle: state.searchQuery.isNotEmpty
            ? 'جرب تغيير كلمات البحث أو المرشحات'
            : 'ابدأ بإضافة مخزن جديد',
        actionText: 'إضافة مخزن',
        onActionPressed: () => _showAddWarehouseDialog(),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: state.filteredWarehouses.length,
      itemBuilder: (context, index) {
        final warehouse = state.filteredWarehouses[index];
        return WarehouseCard(
          warehouse: warehouse,
          onTap: () => _showWarehouseDetails(warehouse),
          onEdit: () => _showEditWarehouseDialog(warehouse),
          onDelete: () => _showDeleteConfirmation(warehouse),
          onSetDefault:
              warehouse.isDefault ? null : () => _setAsDefault(warehouse),
        );
      },
    );
  }

  void _showFiltersDialog() {
    showDialog(
      context: context,
      builder: (context) => const WarehouseFiltersDialog(),
    );
  }

  void _showAddWarehouseDialog() {
    showDialog(
      context: context,
      builder: (context) => const WarehouseFormDialog(),
    );
  }

  void _showEditWarehouseDialog(Warehouse warehouse) {
    showDialog(
      context: context,
      builder: (context) => WarehouseFormDialog(warehouse: warehouse),
    );
  }

  void _showWarehouseDetails(Warehouse warehouse) {
    showDialog(
      context: context,
      builder: (context) => WarehouseDetailsDialog(warehouse: warehouse),
    );
  }

  void _showDeleteConfirmation(Warehouse warehouse) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المخزن "${warehouse.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref
                  .read(warehouseManagementProvider.notifier)
                  .deleteWarehouse(warehouse.id);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _setAsDefault(Warehouse warehouse) {
    ref.read(warehouseManagementProvider.notifier).setAsDefault(warehouse.id);
  }

  String _getWarehouseTypeText(String type) {
    switch (type) {
      case 'main':
        return 'رئيسي';
      case 'branch':
        return 'فرع';
      case 'temporary':
        return 'مؤقت';
      default:
        return type;
    }
  }
}
