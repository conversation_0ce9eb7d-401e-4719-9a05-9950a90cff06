import 'package:sqflite/sqflite.dart';
import 'package:tijari_tech/core/constants/database_constants.dart';
import 'package:tijari_tech/core/utils/app_utils.dart';
import 'package:tijari_tech/data/models/unit.dart';
import 'base_dao.dart';

class UnitDao extends BaseDao<Unit> {
  @override
  String get tableName => DatabaseConstants.tableUnits;

  @override
  Unit fromMap(Map<String, dynamic> map) => Unit.fromMap(map);

  @override
  Map<String, dynamic> toMap(Unit entity) => entity.toMap();

  // ------------------------------------------------------------------
  // Unit-specific queries
  // ------------------------------------------------------------------

  /// استرجاع شجرة الوحدات (الوحدات الأساسية مع مشتقاتها)
  Future<List<Unit>> getUnitTree() async {
    try {
      // الحصول على جميع الوحدات النشطة
      final allUnits = await findWhere(
        where: '${DatabaseConstants.columnUnitDeletedAt} IS NULL',
        orderBy: DatabaseConstants.columnUnitName,
      );

      // إنشاء خريطة للوحدات
      final unitMap = <String, Unit>{};
      final baseUnits = <Unit>[];

      // تجهيز الخريطة
      for (final unit in allUnits) {
        unitMap[unit.id] = unit;

        // إذا كانت وحدة أساسية، أضفها إلى قائمة الوحدات الأساسية
        if (unit.isBaseUnitType) {
          baseUnits.add(unit);
        }
      }

      // ربط الوحدات المشتقة بالوحدات الأساسية
      for (final unit in allUnits) {
        if (unit.hasBaseUnit &&
            unit.baseUnitId != null &&
            unitMap.containsKey(unit.baseUnitId)) {
          final baseUnit = unitMap[unit.baseUnitId!];
          if (baseUnit != null) {
            // إنشاء قائمة المشتقات إذا لم تكن موجودة
            final derivedUnits = baseUnit.derivedUnits ?? <Unit>[];

            // إضافة الوحدة المشتقة
            derivedUnits.add(unit.copyWith(baseUnit: baseUnit));

            // تحديث الوحدة الأساسية مع قائمة المشتقات
            final updatedBaseUnit =
                baseUnit.copyWith(derivedUnits: derivedUnits);
            unitMap[baseUnit.id] = updatedBaseUnit;

            // تحديث قائمة الوحدات الأساسية
            final baseUnitIndex =
                baseUnits.indexWhere((u) => u.id == baseUnit.id);
            if (baseUnitIndex != -1) {
              baseUnits[baseUnitIndex] = updatedBaseUnit;
            }
          }
        }
      }

      return baseUnits;
    } catch (e) {
      AppUtils.logError('Error getting unit tree', e);
      return [];
    }
  }

  /// استرجاع الوحدات حسب النوع مع شكل الشجرة
  Future<List<Unit>> getUnitsByType(String unitType) async {
    try {
      // الحصول على الوحدات الأساسية من هذا النوع
      final baseUnits = await findWhere(
        where: '''
          ${DatabaseConstants.columnUnitType} = ? AND
          ${DatabaseConstants.columnUnitIsBaseUnit} = 1 AND
          ${DatabaseConstants.columnUnitDeletedAt} IS NULL
        ''',
        whereArgs: [unitType],
        orderBy: DatabaseConstants.columnUnitName,
      );

      // الحصول على الوحدات المشتقة من هذا النوع
      final derivedUnits = await findWhere(
        where: '''
          ${DatabaseConstants.columnUnitType} = ? AND
          ${DatabaseConstants.columnUnitIsBaseUnit} = 0 AND
          ${DatabaseConstants.columnUnitDeletedAt} IS NULL
        ''',
        whereArgs: [unitType],
        orderBy: DatabaseConstants.columnUnitName,
      );

      // إنشاء خريطة للوحدات المشتقة حسب الوحدة الأساسية
      final derivedUnitsMap = <String, List<Unit>>{};
      for (final unit in derivedUnits) {
        if (unit.baseUnitId != null) {
          derivedUnitsMap.putIfAbsent(unit.baseUnitId!, () => []).add(unit);
        }
      }

      // ربط الوحدات المشتقة بالوحدات الأساسية
      final result = <Unit>[];
      for (final baseUnit in baseUnits) {
        final derived = derivedUnitsMap[baseUnit.id] ?? [];
        result.add(baseUnit.copyWith(derivedUnits: derived));
      }

      return result;
    } catch (e) {
      AppUtils.logError('Error getting units by type', e);
      return [];
    }
  }

  /// استرجاع الوحدات مع عدد الاستخدامات
  Future<List<Unit>> getUnitsWithUsageCount() async {
    final sql = '''
      SELECT
        u.*,
        COUNT(p.${DatabaseConstants.columnProductId}) as usage_count
      FROM ${DatabaseConstants.tableUnits} u
      LEFT JOIN ${DatabaseConstants.tableProducts} p
        ON u.${DatabaseConstants.columnUnitId} = p.${DatabaseConstants.columnProductBaseUnitId}
        AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
      WHERE u.${DatabaseConstants.columnUnitDeletedAt} IS NULL
      GROUP BY u.${DatabaseConstants.columnUnitId}
      ORDER BY u.${DatabaseConstants.columnUnitName}
    ''';

    final result = await rawQuery(sql);
    return result.map((map) => Unit.fromMap(map)).toList();
  }

  /// استرجاع الوحدات الأساسية (factor = 1.0)
  Future<List<Unit>> getBaseUnits() async {
    return await findWhere(
      where: '${DatabaseConstants.columnUnitFactor} = 1.0',
      orderBy: DatabaseConstants.columnUnitName,
    );
  }

  /// استرجاع الوحدات المشتقة من وحدة أساسية
  Future<List<Unit>> findDerivedUnits(String baseUnitId) async {
    return await findWhere(
      where:
          'base_unit_id = ? AND ${DatabaseConstants.columnUnitDeletedAt} IS NULL',
      whereArgs: [baseUnitId],
      orderBy: DatabaseConstants.columnUnitName,
    );
  }

  /// التحقق من استخدام الوحدة في المنتجات
  Future<bool> isUsedByProducts(String unitId) async {
    try {
      final db = await database;

      // التحقق من جدول المنتجات
      final productResult = await db.rawQuery('''
        SELECT COUNT(*) as count 
        FROM ${DatabaseConstants.tableProducts} 
        WHERE ${DatabaseConstants.columnProductBaseUnitId} = ? 
          AND ${DatabaseConstants.columnProductDeletedAt} IS NULL
      ''', [unitId]);

      if ((productResult.first['count'] as int) > 0) return true;

      // التحقق من جدول أسعار المنتجات
      final priceResult = await db.rawQuery('''
        SELECT COUNT(*) as count 
        FROM ${DatabaseConstants.tableProductPrices} 
        WHERE ${DatabaseConstants.columnProductPriceUnitId} = ? 
          AND ${DatabaseConstants.columnProductPriceDeletedAt} IS NULL
      ''', [unitId]);

      return (priceResult.first['count'] as int) > 0;
    } catch (e) {
      AppUtils.logError('Error checking if unit is used by products', e);
      return false;
    }
  }

  /// استرجاع الوحدات للقائمة المنسدلة
  Future<List<Map<String, dynamic>>> getUnitsForSelection() async {
    final sql = '''
      SELECT 
        ${DatabaseConstants.columnUnitId} as id,
        ${DatabaseConstants.columnUnitName} as name,
        ${DatabaseConstants.columnUnitFactor} as factor,
        CASE 
          WHEN ${DatabaseConstants.columnUnitFactor} = 1.0 
            THEN ${DatabaseConstants.columnUnitName}
          ELSE ${DatabaseConstants.columnUnitName} || ' (x' || ${DatabaseConstants.columnUnitFactor} || ')'
        END as display_name
      FROM ${DatabaseConstants.tableUnits}
      WHERE ${DatabaseConstants.columnUnitDeletedAt} IS NULL
      ORDER BY ${DatabaseConstants.columnUnitFactor}, ${DatabaseConstants.columnUnitName}
    ''';

    return await rawQuery(sql);
  }

  /// حذف وحدة مع التحقق
  Future<bool> deleteUnitSafely(String unitId) async {
    try {
      final isUsed = await isUsedByProducts(unitId);
      if (isUsed) {
        throw Exception('Cannot delete unit: it is being used by products');
      }

      final db = await database;

      final transactionCount = await db.rawQuery('''
        SELECT COUNT(*) as count FROM (
          SELECT 1 FROM ${DatabaseConstants.tableSaleItems} 
            WHERE ${DatabaseConstants.columnSaleItemUnitId} = ?
          UNION ALL
          SELECT 1 FROM ${DatabaseConstants.tablePurchaseItems} 
            WHERE ${DatabaseConstants.columnPurchaseItemUnitId} = ?
          UNION ALL
          SELECT 1 FROM ${DatabaseConstants.tableStockMovements} 
            WHERE ${DatabaseConstants.columnStockMovementUnitId} = ?
        )
      ''', [unitId, unitId, unitId]);

      if ((transactionCount.first['count'] as int) > 0) {
        throw Exception('Cannot delete unit: it has transaction history');
      }

      return await delete(unitId);
    } catch (e) {
      AppUtils.logError('Error deleting unit safely', e);
      return false;
    }
  }

  /// البحث المتقدم في الوحدات
  Future<List<Unit>> advancedSearch({
    String? searchTerm,
    String? unitType,
    bool? isActive,
    bool? isBaseUnit,
    String? orderBy,
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    final conditions = <String>[];
    final args = <dynamic>[];

    // Always exclude deleted units
    conditions.add('${DatabaseConstants.columnUnitDeletedAt} IS NULL');

    if (searchTerm != null && searchTerm.isNotEmpty) {
      conditions.add('''
        (${DatabaseConstants.columnUnitName} LIKE ?
         OR symbol LIKE ?
         OR abbreviation LIKE ?)
      ''');
      final searchPattern = '%$searchTerm%';
      args.addAll([searchPattern, searchPattern, searchPattern]);
    }

    if (unitType != null) {
      conditions.add('${DatabaseConstants.columnUnitType} = ?');
      args.add(unitType);
    }

    if (isActive != null) {
      conditions.add('${DatabaseConstants.columnUnitIsActive} = ?');
      args.add(isActive ? 1 : 0);
    }

    if (isBaseUnit != null) {
      conditions.add('${DatabaseConstants.columnUnitIsBaseUnit} = ?');
      args.add(isBaseUnit ? 1 : 0);
    }

    final whereClause = conditions.join(' AND ');

    final sql = '''
      SELECT
        u.*,
        COUNT(p.${DatabaseConstants.columnProductId}) as usage_count
      FROM ${DatabaseConstants.tableUnits} u
      LEFT JOIN ${DatabaseConstants.tableProducts} p
        ON u.${DatabaseConstants.columnUnitId} = p.${DatabaseConstants.columnProductBaseUnitId}
        AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
      WHERE $whereClause
      GROUP BY u.${DatabaseConstants.columnUnitId}
      ORDER BY ${orderBy ?? DatabaseConstants.columnUnitName} ${ascending ? 'ASC' : 'DESC'}
      ${limit != null ? 'LIMIT $limit' : ''}
      ${offset != null ? 'OFFSET $offset' : ''}
    ''';

    final result = await rawQuery(sql, args);
    return result.map((map) => Unit.fromMap(map)).toList();
  }

  /// استرجاع إحصائيات الوحدات
  Future<Map<String, dynamic>> getUnitsStatistics() async {
    final sql = '''
      SELECT
        COUNT(*) as total_units,
        COUNT(CASE WHEN ${DatabaseConstants.columnUnitIsActive} = 1 THEN 1 END) as active_units,
        COUNT(CASE WHEN ${DatabaseConstants.columnUnitIsBaseUnit} = 1 THEN 1 END) as base_units,
        COUNT(DISTINCT ${DatabaseConstants.columnUnitType}) as unit_types_count,
        AVG(${DatabaseConstants.columnUnitFactor}) as avg_factor
      FROM ${DatabaseConstants.tableUnits}
      WHERE ${DatabaseConstants.columnUnitDeletedAt} IS NULL
    ''';

    final result = await rawQuery(sql);
    return result.isNotEmpty ? result.first : {};
  }

  /// استرجاع أنواع الوحدات المتاحة
  Future<List<String>> getUnitTypes() async {
    try {
      final sql = '''
        SELECT DISTINCT ${DatabaseConstants.columnUnitType}
        FROM ${DatabaseConstants.tableUnits}
        WHERE ${DatabaseConstants.columnUnitDeletedAt} IS NULL
        ORDER BY ${DatabaseConstants.columnUnitType}
      ''';

      final result = await rawQuery(sql);
      return result
          .map((row) => row[DatabaseConstants.columnUnitType] as String)
          .toList();
    } catch (e) {
      AppUtils.logError('Error getting unit types', e);
      return [];
    }
  }

  /// استرجاع أكثر الوحدات استخداماً
  Future<List<Map<String, dynamic>>> getMostUsedUnits({int limit = 10}) async {
    final sql = '''
      SELECT 
        u.*,
        COUNT(DISTINCT p.${DatabaseConstants.columnProductId}) as product_count,
        COUNT(DISTINCT si.${DatabaseConstants.columnSaleItemId}) as sale_count,
        COUNT(DISTINCT pi.${DatabaseConstants.columnPurchaseItemId}) as purchase_count,
        (
          COUNT(DISTINCT p.${DatabaseConstants.columnProductId}) + 
          COUNT(DISTINCT si.${DatabaseConstants.columnSaleItemId}) + 
          COUNT(DISTINCT pi.${DatabaseConstants.columnPurchaseItemId})
        ) as total_usage
      FROM ${DatabaseConstants.tableUnits} u
      LEFT JOIN ${DatabaseConstants.tableProducts} p 
        ON u.${DatabaseConstants.columnUnitId} = p.${DatabaseConstants.columnProductBaseUnitId} 
       AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
      LEFT JOIN ${DatabaseConstants.tableSaleItems} si 
        ON u.${DatabaseConstants.columnUnitId} = si.${DatabaseConstants.columnSaleItemUnitId}
      LEFT JOIN ${DatabaseConstants.tablePurchaseItems} pi 
        ON u.${DatabaseConstants.columnUnitId} = pi.${DatabaseConstants.columnPurchaseItemUnitId}
      WHERE u.${DatabaseConstants.columnUnitDeletedAt} IS NULL
      GROUP BY u.${DatabaseConstants.columnUnitId}
      ORDER BY total_usage DESC
      LIMIT ?
    ''';

    return await rawQuery(sql, [limit]);
  }

// في UnitDao، أضف هذه الطرق الجديدة

  /// استرجاع الوحدات مع معلومات الفروع والتصنيفات
  Future<List<Unit>> getUnitsWithBranchAndCategoryInfo() async {
    final sql = '''
    SELECT 
      u.*,
      COUNT(DISTINCT p.${DatabaseConstants.columnProductId}) as usage_count,
      COUNT(DISTINCT b.${DatabaseConstants.columnBranchId}) as branch_count,
      COUNT(DISTINCT c.${DatabaseConstants.columnCategoryId}) as category_count
    FROM ${DatabaseConstants.tableUnits} u
    LEFT JOIN ${DatabaseConstants.tableProducts} p 
      ON u.${DatabaseConstants.columnUnitId} = p.${DatabaseConstants.columnProductBaseUnitId} 
     AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
    LEFT JOIN ${DatabaseConstants.tableBranches} b 
      ON 1=1 -- ربط غير مباشر عبر المنتجات
    LEFT JOIN ${DatabaseConstants.tableCategories} c 
      ON 1=1 -- ربط غير مباشر عبر المنتجات
    WHERE u.${DatabaseConstants.columnUnitDeletedAt} IS NULL
    GROUP BY u.${DatabaseConstants.columnUnitId}
    ORDER BY u.${DatabaseConstants.columnUnitName}
  ''';

    final result = await rawQuery(sql);
    return result.map((map) => Unit.fromMap(map)).toList();
  }

  /// استرجاع إحصائيات الوحدات مع الفروع والتصنيفات
  Future<Map<String, dynamic>> getEnhancedUnitsStatistics() async {
    final sql = '''
    SELECT
      COUNT(*) as total_units,
      COUNT(CASE WHEN u.${DatabaseConstants.columnUnitIsActive} = 1 THEN 1 END) as active_units,
      COUNT(CASE WHEN u.${DatabaseConstants.columnUnitIsBaseUnit} = 1 THEN 1 END) as base_units,
      COUNT(DISTINCT u.${DatabaseConstants.columnUnitType}) as unit_types_count,
      AVG(u.${DatabaseConstants.columnUnitFactor}) as avg_factor,
      COUNT(DISTINCT p.${DatabaseConstants.columnProductId}) as total_products_using_units,
      COUNT(DISTINCT b.${DatabaseConstants.columnBranchId}) as total_branches,
      COUNT(DISTINCT c.${DatabaseConstants.columnCategoryId}) as total_categories
    FROM ${DatabaseConstants.tableUnits} u
    LEFT JOIN ${DatabaseConstants.tableProducts} p
      ON u.${DatabaseConstants.columnUnitId} = p.${DatabaseConstants.columnProductBaseUnitId}
     AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
    LEFT JOIN ${DatabaseConstants.tableBranches} b ON 1=1
    LEFT JOIN ${DatabaseConstants.tableCategories} c ON 1=1
    WHERE u.${DatabaseConstants.columnUnitDeletedAt} IS NULL
  ''';

    final result = await rawQuery(sql);
    return result.isNotEmpty ? result.first : {};
  }
}
