import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../data/models/warehouse.dart';
import '../../providers/warehouse_provider.dart';
import 'widgets/warehouse_form_dialog.dart';

class WarehouseFormPage extends ConsumerWidget {
  final String? warehouseId;

  const WarehouseFormPage({
    super.key,
    this.warehouseId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (warehouseId != null) {
      // Edit mode - load warehouse data
      final warehouseAsync = ref.watch(warehouseByIdProvider(warehouseId!));

      return warehouseAsync.when(
        data: (warehouse) => _buildPage(context, warehouse),
        loading: () => Scaffold(
          appBar: AppBar(
            title: Text(
              'تحميل...',
              style: AppTextStyles.titleLarge.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: AppColors.primary,
          ),
          body: const Center(child: CircularProgressIndicator()),
        ),
        error: (error, stack) => Scaffold(
          appBar: AppBar(
            title: Text(
              'خطأ',
              style: AppTextStyles.titleLarge.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: AppColors.primary,
          ),
          body: Center(
            child: Text('خطأ في تحميل بيانات المخزن: $error'),
          ),
        ),
      );
    } else {
      // Add mode
      return _buildPage(context, null);
    }
  }

  Widget _buildPage(BuildContext context, Warehouse? warehouse) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          warehouse != null ? 'تعديل المخزن' : 'إضافة مخزن جديد',
          style: AppTextStyles.titleLarge.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: WarehouseFormDialog(
          warehouse: warehouse,
        ),
      ),
    );
  }
}
