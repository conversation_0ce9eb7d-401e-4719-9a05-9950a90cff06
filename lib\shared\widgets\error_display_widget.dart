import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';

class ErrorDisplayWidget extends StatelessWidget {
  final String error;
  final VoidCallback? onRetry;
  final String? retryText;
  final IconData? icon;
  final Color? iconColor;
  final bool showIcon;
  final EdgeInsets? padding;

  const ErrorDisplayWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.retryText,
    this.icon,
    this.iconColor,
    this.showIcon = true,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: padding ?? EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showIcon) ...[
              Container(
                padding: EdgeInsets.all(24.w),
                decoration: BoxDecoration(
                  color: (iconColor ?? Colors.red).withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon ?? Icons.error_outline,
                  size: 64.sp,
                  color: iconColor ?? Colors.red,
                ),
              ),
              SizedBox(height: 24.h),
            ],
            
            Text(
              'حدث خطأ',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
              textAlign: TextAlign.center,
            ),
            
            SizedBox(height: 12.h),
            
            Text(
              error,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            
            if (onRetry != null) ...[
              SizedBox(height: 32.h),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh, color: Colors.white),
                label: Text(
                  retryText ?? 'إعادة المحاولة',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: EdgeInsets.symmetric(
                    horizontal: 24.w,
                    vertical: 12.h,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class NetworkErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;
  final String? message;

  const NetworkErrorWidget({
    super.key,
    this.onRetry,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorDisplayWidget(
      error: message ?? 'تحقق من اتصالك بالإنترنت وحاول مرة أخرى',
      onRetry: onRetry,
      icon: Icons.wifi_off,
      iconColor: Colors.orange,
    );
  }
}

class ServerErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;
  final String? message;

  const ServerErrorWidget({
    super.key,
    this.onRetry,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorDisplayWidget(
      error: message ?? 'خطأ في الخادم، يرجى المحاولة لاحقاً',
      onRetry: onRetry,
      icon: Icons.cloud_off,
      iconColor: Colors.red,
    );
  }
}

class NotFoundErrorWidget extends StatelessWidget {
  final String? message;
  final VoidCallback? onGoBack;
  final String? actionText;

  const NotFoundErrorWidget({
    super.key,
    this.message,
    this.onGoBack,
    this.actionText,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorDisplayWidget(
      error: message ?? 'الصفحة المطلوبة غير موجودة',
      onRetry: onGoBack,
      retryText: actionText ?? 'العودة',
      icon: Icons.search_off,
      iconColor: Colors.grey,
    );
  }
}

class PermissionErrorWidget extends StatelessWidget {
  final String? message;
  final VoidCallback? onRequestPermission;

  const PermissionErrorWidget({
    super.key,
    this.message,
    this.onRequestPermission,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorDisplayWidget(
      error: message ?? 'ليس لديك صلاحية للوصول إلى هذا المحتوى',
      onRetry: onRequestPermission,
      retryText: 'طلب الصلاحية',
      icon: Icons.lock,
      iconColor: Colors.orange,
    );
  }
}

class ValidationErrorWidget extends StatelessWidget {
  final List<String> errors;
  final VoidCallback? onDismiss;

  const ValidationErrorWidget({
    super.key,
    required this.errors,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.red[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(
                Icons.error,
                color: Colors.red,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  'يرجى تصحيح الأخطاء التالية:',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.red[700],
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (onDismiss != null)
                IconButton(
                  onPressed: onDismiss,
                  icon: Icon(
                    Icons.close,
                    color: Colors.red,
                    size: 20.sp,
                  ),
                ),
            ],
          ),
          SizedBox(height: 8.h),
          ...errors.map((error) => Padding(
                padding: EdgeInsets.only(bottom: 4.h),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '• ',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.red[700],
                      ),
                    ),
                    Expanded(
                      child: Text(
                        error,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.red[700],
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }
}

class InlineErrorWidget extends StatelessWidget {
  final String error;
  final IconData? icon;
  final Color? color;

  const InlineErrorWidget({
    super.key,
    required this.error,
    this.icon,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 12.w,
        vertical: 8.h,
      ),
      decoration: BoxDecoration(
        color: (color ?? Colors.red).withOpacity(0.1),
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(
          color: (color ?? Colors.red).withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon ?? Icons.error,
            size: 16.sp,
            color: color ?? Colors.red,
          ),
          SizedBox(width: 8.w),
          Flexible(
            child: Text(
              error,
              style: AppTextStyles.bodySmall.copyWith(
                color: color ?? Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class SnackBarErrorWidget {
  static void show(BuildContext context, String error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.error,
              color: Colors.white,
              size: 20.sp,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(
                error,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
        margin: EdgeInsets.all(16.w),
      ),
    );
  }
}

class DialogErrorWidget {
  static void show(BuildContext context, String error, {VoidCallback? onRetry}) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        title: Row(
          children: [
            Icon(
              Icons.error,
              color: Colors.red,
              size: 24.sp,
            ),
            SizedBox(width: 12.w),
            const Text('خطأ'),
          ],
        ),
        content: Text(error),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          if (onRetry != null)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onRetry();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
              ),
              child: const Text(
                'إعادة المحاولة',
                style: TextStyle(color: Colors.white),
              ),
            ),
        ],
      ),
    );
  }
}
