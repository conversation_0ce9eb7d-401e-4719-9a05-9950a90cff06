import 'package:sqflite/sqflite.dart';
import '../constants/database_constants.dart';
import '../utils/app_utils.dart';

/// فئة لإدراج البيانات الافتراضية في قاعدة البيانات
class DefaultDataSeeder {
  
  /// إدراج الوحدات الافتراضية
  static Future<void> seedDefaultUnits(Database db) async {
    try {
      AppUtils.logInfo('Seeding default units...');
      
      final defaultUnits = [
        {
          DatabaseConstants.columnUnitId: 'unit_piece',
          DatabaseConstants.columnUnitName: 'قطعة',
          DatabaseConstants.columnUnitSymbol: 'قطعة',
          DatabaseConstants.columnUnitAbbreviation: 'ق',
          DatabaseConstants.columnUnitFactor: 1.0,
          DatabaseConstants.columnUnitBaseUnitId: null,
          DatabaseConstants.columnUnitType: 'count',
          DatabaseConstants.columnUnitIsBaseUnit: 1,
          DatabaseConstants.columnUnitIsActive: 1,
          DatabaseConstants.columnUnitCreatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnUnitUpdatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnUnitIsSynced: 0,
        },
        {
          DatabaseConstants.columnUnitId: 'unit_kg',
          DatabaseConstants.columnUnitName: 'كيلوجرام',
          DatabaseConstants.columnUnitSymbol: 'كجم',
          DatabaseConstants.columnUnitAbbreviation: 'كجم',
          DatabaseConstants.columnUnitFactor: 1000.0,
          DatabaseConstants.columnUnitBaseUnitId: 'unit_gram',
          DatabaseConstants.columnUnitType: 'weight',
          DatabaseConstants.columnUnitIsBaseUnit: 0,
          DatabaseConstants.columnUnitIsActive: 1,
          DatabaseConstants.columnUnitCreatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnUnitUpdatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnUnitIsSynced: 0,
        },
        {
          DatabaseConstants.columnUnitId: 'unit_gram',
          DatabaseConstants.columnUnitName: 'جرام',
          DatabaseConstants.columnUnitSymbol: 'جم',
          DatabaseConstants.columnUnitAbbreviation: 'جم',
          DatabaseConstants.columnUnitFactor: 1.0,
          DatabaseConstants.columnUnitBaseUnitId: null,
          DatabaseConstants.columnUnitType: 'weight',
          DatabaseConstants.columnUnitIsBaseUnit: 1,
          DatabaseConstants.columnUnitIsActive: 1,
          DatabaseConstants.columnUnitCreatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnUnitUpdatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnUnitIsSynced: 0,
        },
        {
          DatabaseConstants.columnUnitId: 'unit_liter',
          DatabaseConstants.columnUnitName: 'لتر',
          DatabaseConstants.columnUnitSymbol: 'ل',
          DatabaseConstants.columnUnitAbbreviation: 'ل',
          DatabaseConstants.columnUnitFactor: 1000.0,
          DatabaseConstants.columnUnitBaseUnitId: 'unit_ml',
          DatabaseConstants.columnUnitType: 'volume',
          DatabaseConstants.columnUnitIsBaseUnit: 0,
          DatabaseConstants.columnUnitIsActive: 1,
          DatabaseConstants.columnUnitCreatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnUnitUpdatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnUnitIsSynced: 0,
        },
        {
          DatabaseConstants.columnUnitId: 'unit_ml',
          DatabaseConstants.columnUnitName: 'مليلتر',
          DatabaseConstants.columnUnitSymbol: 'مل',
          DatabaseConstants.columnUnitAbbreviation: 'مل',
          DatabaseConstants.columnUnitFactor: 1.0,
          DatabaseConstants.columnUnitBaseUnitId: null,
          DatabaseConstants.columnUnitType: 'volume',
          DatabaseConstants.columnUnitIsBaseUnit: 1,
          DatabaseConstants.columnUnitIsActive: 1,
          DatabaseConstants.columnUnitCreatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnUnitUpdatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnUnitIsSynced: 0,
        },
      ];

      for (final unit in defaultUnits) {
        try {
          // التحقق من عدم وجود الوحدة مسبقاً
          final existing = await db.query(
            DatabaseConstants.tableUnits,
            where: '${DatabaseConstants.columnUnitId} = ?',
            whereArgs: [unit[DatabaseConstants.columnUnitId]],
          );

          if (existing.isEmpty) {
            await db.insert(DatabaseConstants.tableUnits, unit);
            AppUtils.logInfo('Inserted unit: ${unit[DatabaseConstants.columnUnitName]}');
          } else {
            AppUtils.logInfo('Unit already exists: ${unit[DatabaseConstants.columnUnitName]}');
          }
        } catch (e) {
          AppUtils.logError('Error inserting unit ${unit[DatabaseConstants.columnUnitName]}', e);
        }
      }

      AppUtils.logInfo('Default units seeding completed');
    } catch (e) {
      AppUtils.logError('Error seeding default units', e);
      rethrow;
    }
  }

  /// إدراج المخازن الافتراضية
  static Future<void> seedDefaultWarehouses(Database db) async {
    try {
      AppUtils.logInfo('Seeding default warehouses...');
      
      final defaultWarehouses = [
        {
          DatabaseConstants.columnWarehouseId: 'main-warehouse',
          DatabaseConstants.columnWarehouseName: 'المخزن الرئيسي',
          DatabaseConstants.columnWarehouseCode: 'MAIN',
          DatabaseConstants.columnWarehouseLocation: 'الموقع الرئيسي',
          DatabaseConstants.columnWarehouseDescription: 'المخزن الرئيسي للشركة',
          DatabaseConstants.columnWarehouseIsActive: 1,
          DatabaseConstants.columnWarehouseCreatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnWarehouseUpdatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnWarehouseIsSynced: 0,
        },
      ];

      for (final warehouse in defaultWarehouses) {
        try {
          // التحقق من عدم وجود المخزن مسبقاً
          final existing = await db.query(
            DatabaseConstants.tableWarehouses,
            where: '${DatabaseConstants.columnWarehouseId} = ?',
            whereArgs: [warehouse[DatabaseConstants.columnWarehouseId]],
          );

          if (existing.isEmpty) {
            await db.insert(DatabaseConstants.tableWarehouses, warehouse);
            AppUtils.logInfo('Inserted warehouse: ${warehouse[DatabaseConstants.columnWarehouseName]}');
          } else {
            AppUtils.logInfo('Warehouse already exists: ${warehouse[DatabaseConstants.columnWarehouseName]}');
          }
        } catch (e) {
          AppUtils.logError('Error inserting warehouse ${warehouse[DatabaseConstants.columnWarehouseName]}', e);
        }
      }

      AppUtils.logInfo('Default warehouses seeding completed');
    } catch (e) {
      AppUtils.logError('Error seeding default warehouses', e);
      rethrow;
    }
  }

  /// إدراج المستخدمين الافتراضيين
  static Future<void> seedDefaultUsers(Database db) async {
    try {
      AppUtils.logInfo('Seeding default users...');
      
      final defaultUsers = [
        {
          DatabaseConstants.columnUserId: 'system',
          DatabaseConstants.columnUserName: 'النظام',
          DatabaseConstants.columnUserEmail: '<EMAIL>',
          DatabaseConstants.columnUserPhone: '',
          DatabaseConstants.columnUserRole: 'system',
          DatabaseConstants.columnUserIsActive: 1,
          DatabaseConstants.columnUserCreatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnUserUpdatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnUserIsSynced: 0,
        },
      ];

      for (final user in defaultUsers) {
        try {
          // التحقق من عدم وجود المستخدم مسبقاً
          final existing = await db.query(
            DatabaseConstants.tableUsers,
            where: '${DatabaseConstants.columnUserId} = ?',
            whereArgs: [user[DatabaseConstants.columnUserId]],
          );

          if (existing.isEmpty) {
            await db.insert(DatabaseConstants.tableUsers, user);
            AppUtils.logInfo('Inserted user: ${user[DatabaseConstants.columnUserName]}');
          } else {
            AppUtils.logInfo('User already exists: ${user[DatabaseConstants.columnUserName]}');
          }
        } catch (e) {
          AppUtils.logError('Error inserting user ${user[DatabaseConstants.columnUserName]}', e);
        }
      }

      AppUtils.logInfo('Default users seeding completed');
    } catch (e) {
      AppUtils.logError('Error seeding default users', e);
      rethrow;
    }
  }

  /// إدراج جميع البيانات الافتراضية
  static Future<void> seedAllDefaultData(Database db) async {
    try {
      AppUtils.logInfo('Starting default data seeding...');
      
      await seedDefaultUnits(db);
      await seedDefaultWarehouses(db);
      await seedDefaultUsers(db);
      
      AppUtils.logInfo('All default data seeding completed successfully');
    } catch (e) {
      AppUtils.logError('Error seeding default data', e);
      rethrow;
    }
  }
}
