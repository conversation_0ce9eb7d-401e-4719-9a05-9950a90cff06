import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tijari_tech/router/index.dart';


/// خدمة التنقل المركزية للتطبيق
class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  // ------------------------------------------------------------------
  // Dashboard Navigation
  // ------------------------------------------------------------------

  /// الانتقال إلى لوحة التحكم
  static void goToDashboard(BuildContext context) {
    context.go(AppRoutes.dashboard);
  }

  // ------------------------------------------------------------------
  // Inventory Navigation
  // ------------------------------------------------------------------

  /// الانتقال إلى صفحة المخزون
  static void goToInventory(BuildContext context) {
    context.go(AppRoutes.inventory);
  }

  /// الانتقال إلى صفحة المنتجات
  static void goToProducts(BuildContext context) {
    context.go(AppRoutes.products);
  }

  /// الانتقال إلى إضافة منتج
  static void goToProductAdd(BuildContext context) {
    context.go(AppRoutes.productAdd);
  }

  /// الانتقال إلى تعديل منتج
  static void goToProductEdit(BuildContext context, String productId) {
    context.go(AppRoutes.productEditWithId(productId));
  }

  /// الانتقال إلى صفحة التصنيفات
  static void goToCategories(BuildContext context) {
    context.go(AppRoutes.categories);
  }

  /// الانتقال إلى إضافة تصنيف
  static void goToCategoryAdd(BuildContext context) {
    context.go(AppRoutes.categoryAdd);
  }

  /// الانتقال إلى تعديل تصنيف
  static void goToCategoryEdit(BuildContext context, String categoryId) {
    context.go(AppRoutes.categoryEditWithId(categoryId));
  }

  /// الانتقال إلى صفحة وحدات القياس
  static void goToUnits(BuildContext context) {
    context.go(AppRoutes.units);
  }

  /// الانتقال إلى إضافة وحدة قياس
  static void goToUnitAdd(BuildContext context) {
    context.go(AppRoutes.unitAdd);
  }

  /// الانتقال إلى تعديل وحدة قياس
  static void goToUnitEdit(BuildContext context, String unitId) {
    context.go(AppRoutes.unitEditWithId(unitId));
  }

  /// الانتقال إلى صفحة المخازن
  static void goToWarehouses(BuildContext context) {
    context.go(AppRoutes.warehouses);
  }

  /// الانتقال إلى إضافة مخزن
  static void goToWarehouseAdd(BuildContext context) {
    context.go(AppRoutes.warehouseAdd);
  }

  /// الانتقال إلى تعديل مخزن
  static void goToWarehouseEdit(BuildContext context, String warehouseId) {
    context.go(AppRoutes.warehouseEditWithId(warehouseId));
  }

  /// الانتقال إلى عرض تفاصيل مخزن
  static void goToWarehouseView(BuildContext context, String warehouseId) {
    context.go(AppRoutes.warehouseViewWithId(warehouseId));
  }

  /// الانتقال إلى إدارة المخزون
  static void goToStockManagement(BuildContext context) {
    context.go(AppRoutes.stockManagement);
  }

  /// الانتقال إلى حركة المخزون
  static void goToStockMovements(BuildContext context) {
    context.go(AppRoutes.stockMovements);
  }

  /// الانتقال إلى تقارير المخزون
  static void goToStockReports(BuildContext context) {
    context.go(AppRoutes.stockReports);
  }

  /// الانتقال إلى تسوية المخزون
  static void goToStockAdjustment(BuildContext context) {
    context.go(AppRoutes.stockAdjustment);
  }

  /// الانتقال إلى نقل المخزون
  static void goToStockTransfer(BuildContext context) {
    context.go(AppRoutes.stockTransfer);
  }

  // ------------------------------------------------------------------
  // Sales Navigation
  // ------------------------------------------------------------------

  /// الانتقال إلى صفحة المبيعات
  static void goToSales(BuildContext context) {
    context.go(AppRoutes.sales);
  }

  /// الانتقال إلى نقطة البيع
  static void goToPOS(BuildContext context) {
    context.go(AppRoutes.pos);
  }

  /// الانتقال إلى إضافة مبيعة
  static void goToSaleAdd(BuildContext context) {
    context.go(AppRoutes.saleAdd);
  }

  /// الانتقال إلى عرض مبيعة
  static void goToSaleView(BuildContext context, String saleId) {
    context.go(AppRoutes.saleViewWithId(saleId));
  }

  /// الانتقال إلى تعديل مبيعة
  static void goToSaleEdit(BuildContext context, String saleId) {
    context.go(AppRoutes.saleEditWithId(saleId));
  }

  /// الانتقال إلى مرتجعات المبيعات
  static void goToSalesReturn(BuildContext context) {
    context.go(AppRoutes.saleReturn);
  }

  // ------------------------------------------------------------------
  // Purchase Navigation
  // ------------------------------------------------------------------

  /// الانتقال إلى صفحة المشتريات
  static void goToPurchases(BuildContext context) {
    context.go(AppRoutes.purchases);
  }

  /// الانتقال إلى إضافة مشتريات
  static void goToPurchaseAdd(BuildContext context) {
    context.go(AppRoutes.purchaseAdd);
  }

  /// الانتقال إلى عرض مشتريات
  static void goToPurchaseView(BuildContext context, String purchaseId) {
    context.go(AppRoutes.purchaseViewWithId(purchaseId));
  }

  /// الانتقال إلى تعديل مشتريات
  static void goToPurchaseEdit(BuildContext context, String purchaseId) {
    context.go(AppRoutes.purchaseEditWithId(purchaseId));
  }

  /// الانتقال إلى مرتجعات المشتريات
  static void goToPurchaseReturn(BuildContext context) {
    context.go(AppRoutes.purchaseReturn);
  }

  // ------------------------------------------------------------------
  // Accounts Navigation
  // ------------------------------------------------------------------

  /// الانتقال إلى صفحة الحسابات
  static void goToAccounts(BuildContext context) {
    context.go(AppRoutes.accounts);
  }

  /// الانتقال إلى صفحة العملاء
  static void goToCustomers(BuildContext context) {
    context.go(AppRoutes.customers);
  }

  /// الانتقال إلى إضافة عميل
  static void goToCustomerAdd(BuildContext context) {
    context.go(AppRoutes.customerAdd);
  }

  /// الانتقال إلى تعديل عميل
  static void goToCustomerEdit(BuildContext context, String customerId) {
    context.go(AppRoutes.customerEditWithId(customerId));
  }

  /// الانتقال إلى صفحة الموردين
  static void goToSuppliers(BuildContext context) {
    context.go(AppRoutes.suppliers);
  }

  /// الانتقال إلى إضافة مورد
  static void goToSupplierAdd(BuildContext context) {
    context.go(AppRoutes.supplierAdd);
  }

  /// الانتقال إلى تعديل مورد
  static void goToSupplierEdit(BuildContext context, String supplierId) {
    context.go(AppRoutes.supplierEditWithId(supplierId));
  }

  // ------------------------------------------------------------------
  // Cash Navigation
  // ------------------------------------------------------------------

  /// الانتقال إلى صفحة الخزينة
  static void goToCash(BuildContext context) {
    context.go(AppRoutes.cash);
  }

  /// الانتقال إلى سند قبض
  static void goToReceiptVoucher(BuildContext context) {
    context.go(AppRoutes.receipt);
  }

  /// الانتقال إلى سند صرف
  static void goToPaymentVoucher(BuildContext context) {
    context.go(AppRoutes.payment);
  }

  // ------------------------------------------------------------------
  // Employees Navigation
  // ------------------------------------------------------------------

  /// الانتقال إلى صفحة الموظفين
  static void goToEmployees(BuildContext context) {
    context.go(AppRoutes.employees);
  }

  /// الانتقال إلى إضافة موظف
  static void goToEmployeeAdd(BuildContext context) {
    context.go(AppRoutes.employeeAdd);
  }

  /// الانتقال إلى تعديل موظف
  static void goToEmployeeEdit(BuildContext context, String employeeId) {
    context.go(AppRoutes.employeeEditWithId(employeeId));
  }

  // ------------------------------------------------------------------
  // Reports Navigation
  // ------------------------------------------------------------------

  /// الانتقال إلى صفحة التقارير
  static void goToReports(BuildContext context) {
    context.go(AppRoutes.reports);
  }

  /// الانتقال إلى تقرير المبيعات
  static void goToSalesReport(BuildContext context) {
    context.go(AppRoutes.salesReport);
  }

  /// الانتقال إلى تقرير المشتريات
  static void goToPurchaseReport(BuildContext context) {
    context.go(AppRoutes.purchaseReport);
  }

  /// الانتقال إلى تقرير الأرباح والخسائر
  static void goToProfitLossReport(BuildContext context) {
    context.go(AppRoutes.profitLossReport);
  }

  // ------------------------------------------------------------------
  // Settings Navigation
  // ------------------------------------------------------------------

  /// الانتقال إلى صفحة الإعدادات
  static void goToSettings(BuildContext context) {
    context.go(AppRoutes.settings);
  }

  /// الانتقال إلى إدارة المستخدمين
  static void goToUserManagement(BuildContext context) {
    context.go(AppRoutes.userManagement);
  }

  /// الانتقال إلى لوحة تحكم الذكاء الاصطناعي
  static void goToAIDashboard(BuildContext context) {
    context.go(AppRoutes.aiDashboard);
  }

  // ------------------------------------------------------------------
  // Utility Methods
  // ------------------------------------------------------------------

  /// العودة للصفحة السابقة
  static void goBack(BuildContext context) {
    if (context.canPop()) {
      context.pop();
    } else {
      goToDashboard(context);
    }
  }

  /// استبدال الصفحة الحالية
  static void pushReplacement(BuildContext context, String route) {
    context.pushReplacement(route);
  }

  /// فتح صفحة جديدة
  static Future<T?> push<T>(BuildContext context, String route) {
    return context.push<T>(route);
  }

  /// التحقق من إمكانية العودة
  static bool canPop(BuildContext context) {
    return context.canPop();
  }

  /// الحصول على المسار الحالي
  static String getCurrentRoute(BuildContext context) {
    return GoRouterState.of(context).uri.toString();
  }

  /// التحقق من المسار الحالي
  static bool isCurrentRoute(BuildContext context, String route) {
    return getCurrentRoute(context) == route;
  }
}
