import 'package:tijari_tech/core/constants/database_constants.dart';
import 'base_dao.dart';
import '../../models/stock_movement.dart';
import '../../../core/utils/app_utils.dart';

class StockMovementDao extends BaseDao<StockMovement> {
  @override
  String get tableName => DatabaseConstants.tableStockMovements;

  @override
  StockMovement fromMap(Map<String, dynamic> map) => StockMovement.fromMap(map);

  @override
  Map<String, dynamic> toMap(StockMovement entity) => entity.toMap();

  // ------------------------------------------------------------------
  // StockMovement-specific queries
  // ------------------------------------------------------------------

  /// استرجاع حركات المخزون حسب المنتج
  Future<List<StockMovement>> getMovementsByProduct(String productId) async {
    return await findWhere(
      where: '''
        ${DatabaseConstants.columnStockMovementProductId} = ? AND
        ${DatabaseConstants.columnStockMovementDeletedAt} IS NULL
      ''',
      whereArgs: [productId],
      orderBy: '${DatabaseConstants.columnStockMovementCreatedAt} DESC',
    );
  }

  /// استرجاع حركات المخزون حسب المخزن
  Future<List<StockMovement>> getMovementsByWarehouse(
      String warehouseId) async {
    return await findWhere(
      where: '''
        ${DatabaseConstants.columnStockMovementWarehouseId} = ? AND
        ${DatabaseConstants.columnStockMovementDeletedAt} IS NULL
      ''',
      whereArgs: [warehouseId],
      orderBy: '${DatabaseConstants.columnStockMovementCreatedAt} DESC',
    );
  }

  /// استرجاع حركات المخزون حسب النوع
  Future<List<StockMovement>> getMovementsByType(String movementType) async {
    return await findWhere(
      where: '''
        ${DatabaseConstants.columnStockMovementType} = ? AND
        ${DatabaseConstants.columnStockMovementDeletedAt} IS NULL
      ''',
      whereArgs: [movementType],
      orderBy: '${DatabaseConstants.columnStockMovementCreatedAt} DESC',
    );
  }

  /// استرجاع حركات المخزون حسب المرجع
  Future<List<StockMovement>> getMovementsByReference({
    required String referenceType,
    required String referenceId,
  }) async {
    return await findWhere(
      where: '''
        ${DatabaseConstants.columnStockMovementReferenceType} = ? AND
        ${DatabaseConstants.columnStockMovementReferenceId} = ? AND
        ${DatabaseConstants.columnStockMovementDeletedAt} IS NULL
      ''',
      whereArgs: [referenceType, referenceId],
      orderBy: '${DatabaseConstants.columnStockMovementCreatedAt} DESC',
    );
  }

  /// استرجاع حركات المخزون مع التفاصيل الكاملة
  Future<List<StockMovement>> getMovementsWithDetails({
    String? productId,
    String? warehouseId,
    String? movementType,
    String? referenceType,
    DateTime? fromDate,
    DateTime? toDate,
    String? orderBy,
    bool ascending = false,
    int? limit,
    int? offset,
  }) async {
    final conditions = <String>[];
    final args = <dynamic>[];

    // Always exclude deleted movements
    conditions
        .add('sm.${DatabaseConstants.columnStockMovementDeletedAt} IS NULL');

    if (productId != null) {
      conditions
          .add('sm.${DatabaseConstants.columnStockMovementProductId} = ?');
      args.add(productId);
    }

    if (warehouseId != null) {
      conditions
          .add('sm.${DatabaseConstants.columnStockMovementWarehouseId} = ?');
      args.add(warehouseId);
    }

    if (movementType != null) {
      conditions.add('sm.${DatabaseConstants.columnStockMovementType} = ?');
      args.add(movementType);
    }

    if (referenceType != null) {
      conditions
          .add('sm.${DatabaseConstants.columnStockMovementReferenceType} = ?');
      args.add(referenceType);
    }

    if (fromDate != null) {
      conditions
          .add('sm.${DatabaseConstants.columnStockMovementCreatedAt} >= ?');
      args.add(fromDate.toIso8601String());
    }

    if (toDate != null) {
      conditions
          .add('sm.${DatabaseConstants.columnStockMovementCreatedAt} <= ?');
      args.add(toDate.toIso8601String());
    }

    final whereClause = conditions.join(' AND ');

    final sqlQuery = '''
      SELECT sm.*, 
             p.name_ar as product_name, 
             p.barcode as product_barcode,
             w.name as warehouse_name,
             fw.name as from_warehouse_name,
             tw.name as to_warehouse_name,
             u.name as created_by_name
      FROM ${DatabaseConstants.tableStockMovements} sm
      LEFT JOIN ${DatabaseConstants.tableProducts} p 
        ON sm.${DatabaseConstants.columnStockMovementProductId} = p.${DatabaseConstants.columnProductId}
      LEFT JOIN ${DatabaseConstants.tableWarehouses} w 
        ON sm.${DatabaseConstants.columnStockMovementWarehouseId} = w.${DatabaseConstants.columnWarehouseId}
      LEFT JOIN ${DatabaseConstants.tableWarehouses} fw 
        ON sm.from_warehouse_id = fw.${DatabaseConstants.columnWarehouseId}
      LEFT JOIN ${DatabaseConstants.tableWarehouses} tw 
        ON sm.to_warehouse_id = tw.${DatabaseConstants.columnWarehouseId}
      LEFT JOIN ${DatabaseConstants.tableUsers} u 
        ON sm.${DatabaseConstants.columnStockMovementCreatedBy} = u.${DatabaseConstants.columnUserId}
      WHERE $whereClause
      ORDER BY ${orderBy ?? 'sm.${DatabaseConstants.columnStockMovementCreatedAt}'} ${ascending ? 'ASC' : 'DESC'}
      ${limit != null ? 'LIMIT $limit' : ''}
      ${offset != null ? 'OFFSET $offset' : ''}
    ''';

    final result = await rawQuery(sqlQuery, args);
    return result.map((map) => StockMovement.fromMap(map)).toList();
  }

  /// استرجاع حركات المخزون اليومية
  Future<List<StockMovement>> getTodayMovements() async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    return await getMovementsWithDetails(
      fromDate: startOfDay,
      toDate: endOfDay,
      orderBy: 'sm.${DatabaseConstants.columnStockMovementCreatedAt}',
      ascending: false,
    );
  }

  /// استرجاع حركات المخزون الأسبوعية
  Future<List<StockMovement>> getWeeklyMovements() async {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final startOfWeekDay =
        DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day);

    return await getMovementsWithDetails(
      fromDate: startOfWeekDay,
      orderBy: 'sm.${DatabaseConstants.columnStockMovementCreatedAt}',
      ascending: false,
    );
  }

  /// استرجاع حركات المخزون الشهرية
  Future<List<StockMovement>> getMonthlyMovements() async {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);

    return await getMovementsWithDetails(
      fromDate: startOfMonth,
      orderBy: 'sm.${DatabaseConstants.columnStockMovementCreatedAt}',
      ascending: false,
    );
  }

  /// استرجاع إحصائيات حركة المخزون
  Future<Map<String, dynamic>> getMovementStatistics({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    final conditions = <String>[];
    final args = <dynamic>[];

    conditions.add('${DatabaseConstants.columnStockMovementDeletedAt} IS NULL');

    if (fromDate != null) {
      conditions.add('${DatabaseConstants.columnStockMovementCreatedAt} >= ?');
      args.add(fromDate.toIso8601String());
    }

    if (toDate != null) {
      conditions.add('${DatabaseConstants.columnStockMovementCreatedAt} <= ?');
      args.add(toDate.toIso8601String());
    }

    final whereClause = conditions.join(' AND ');

    final sqlQuery = '''
      SELECT 
        COUNT(*) as total_movements,
        COUNT(CASE WHEN ${DatabaseConstants.columnStockMovementType} = 'in' THEN 1 END) as inbound_movements,
        COUNT(CASE WHEN ${DatabaseConstants.columnStockMovementType} = 'out' THEN 1 END) as outbound_movements,
        COUNT(CASE WHEN ${DatabaseConstants.columnStockMovementType} = 'transfer' THEN 1 END) as transfer_movements,
        COUNT(CASE WHEN ${DatabaseConstants.columnStockMovementType} = 'adjustment' THEN 1 END) as adjustment_movements,
        SUM(CASE WHEN ${DatabaseConstants.columnStockMovementType} = 'in' THEN ${DatabaseConstants.columnStockMovementQuantity} ELSE 0 END) as total_inbound_quantity,
        SUM(CASE WHEN ${DatabaseConstants.columnStockMovementType} = 'out' THEN ${DatabaseConstants.columnStockMovementQuantity} ELSE 0 END) as total_outbound_quantity,
        SUM(${DatabaseConstants.columnStockMovementTotalValue}) as total_movement_value,
        AVG(${DatabaseConstants.columnStockMovementQuantity}) as avg_movement_quantity
      FROM ${DatabaseConstants.tableStockMovements}
      WHERE $whereClause
    ''';

    final result = await rawQuery(sqlQuery, args);
    return result.isNotEmpty ? result.first : {};
  }

  /// استرجاع أكثر المنتجات حركة
  Future<List<Map<String, dynamic>>> getMostActiveProducts({
    int limit = 10,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    final conditions = <String>[];
    final args = <dynamic>[];

    conditions
        .add('sm.${DatabaseConstants.columnStockMovementDeletedAt} IS NULL');

    if (fromDate != null) {
      conditions
          .add('sm.${DatabaseConstants.columnStockMovementCreatedAt} >= ?');
      args.add(fromDate.toIso8601String());
    }

    if (toDate != null) {
      conditions
          .add('sm.${DatabaseConstants.columnStockMovementCreatedAt} <= ?');
      args.add(toDate.toIso8601String());
    }

    final whereClause = conditions.join(' AND ');

    final sqlQuery = '''
      SELECT 
        sm.${DatabaseConstants.columnStockMovementProductId},
        p.name_ar as product_name,
        p.barcode as product_barcode,
        COUNT(*) as movement_count,
        SUM(sm.${DatabaseConstants.columnStockMovementQuantity}) as total_quantity,
        SUM(sm.${DatabaseConstants.columnStockMovementTotalValue}) as total_value
      FROM ${DatabaseConstants.tableStockMovements} sm
      LEFT JOIN ${DatabaseConstants.tableProducts} p 
        ON sm.${DatabaseConstants.columnStockMovementProductId} = p.${DatabaseConstants.columnProductId}
      WHERE $whereClause
      GROUP BY sm.${DatabaseConstants.columnStockMovementProductId}, p.name_ar, p.barcode
      ORDER BY movement_count DESC
      LIMIT $limit
    ''';

    return await rawQuery(sqlQuery, args);
  }

  /// إنشاء حركة مخزون
  Future<String> createMovement({
    required String productId,
    required String warehouseId,
    required String movementType,
    required double quantity,
    required double unitCost,
    required String unitId,
    String? referenceType,
    String? referenceId,
    String? referenceNumber,
    String? fromWarehouseId,
    String? toWarehouseId,
    String? batchNumber,
    DateTime? expiryDate,
    String? notes,
    String? reason,
    required String createdBy,
    String? branchId,
  }) async {
    try {
      final movement = StockMovement.create(
        id: AppUtils.generateId(),
        productId: productId,
        warehouseId: warehouseId,
        movementType: movementType,
        quantity: quantity,
        unitCost: unitCost,
        unitId: unitId,
        referenceType: referenceType,
        referenceId: referenceId,
        referenceNumber: referenceNumber,
        fromWarehouseId: fromWarehouseId,
        toWarehouseId: toWarehouseId,
        batchNumber: batchNumber,
        expiryDate: expiryDate,
        notes: notes,
        reason: reason,
        createdBy: createdBy,
        branchId: branchId,
      );

      return await insert(movement);
    } catch (e) {
      AppUtils.logError('Error creating stock movement', e);
      rethrow;
    }
  }
}
