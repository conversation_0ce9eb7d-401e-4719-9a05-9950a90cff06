// lib/ui/accounts/manages_customer_suppliers_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../providers/customer_provider.dart';
import '../../providers/supplier_provider.dart';
import '../../router/routes.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/responsive_wrapper.dart';

/// الصفحة الرئيسية لإدارة العملاء والموردين
class ManagesCustomerSuppliersPage extends ConsumerWidget {
  const ManagesCustomerSuppliersPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // تحميل البيانات تلقائياً عند أول بناء للصفحة
    ref.listen<CustomerState>(customerProvider, (_, state) {
      if (state.customers.isEmpty) ref.read(customerProvider.notifier).refresh();
    });
    ref.listen<SupplierState>(supplierProvider, (_, state) {
      if (state.suppliers.isEmpty) ref.read(supplierProvider.notifier).refresh();
    });

    // الاستماع للتغييرات لإعادة بناء الواجهة عند الحاجة
    final customerState = ref.watch(customerProvider);
    final supplierState = ref.watch(supplierProvider);

    return MainLayout(
      title: 'الحسابات',
      child: RefreshIndicator(
        onRefresh: () async {
          await ref.read(customerProvider.notifier).refresh();
          await ref.read(supplierProvider.notifier).refresh();
        },
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildQuickStats(context, ref, customerState, supplierState),
              SizedBox(height: 24.h),
              _buildAccountTypes(context, ref, customerState, supplierState),
              SizedBox(height: 24.h),
              _buildRecentActivities(context),
            ],
          ),
        ),
      ),
    );
  }

  /// الإحصائيات السريعة
  Widget _buildQuickStats(
    BuildContext context,
    WidgetRef ref,
    CustomerState customerState,
    SupplierState supplierState,
  ) {
    final currency = NumberFormat.currency(
      locale: 'ar_SA',
      symbol: 'ر.س',
      decimalDigits: 0,
    );

    final customerCount = customerState.activeCustomersCount;
    final customerDebt = customerState.totalDebt;
    final supplierCount = supplierState.activeSuppliersCount;
    final supplierCredit = supplierState.totalTheyOwe;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'ملخص الحسابات',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            IconButton(
              onPressed: () async {
                await ref.read(customerProvider.notifier).refresh();
                await ref.read(supplierProvider.notifier).refresh();
              },
              icon: const Icon(Icons.refresh),
              tooltip: 'تحديث البيانات',
            ),
          ],
        ),
        SizedBox(height: 16.h),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 4,
          desktopColumns: 4,
          children: [
            _buildStatCard(
              context,
              title: 'إجمالي العملاء',
              value: customerCount.toString(),
              icon: Icons.people_outline,
              color: AppColors.primary,
              onTap: () => context.go(AppRoutes.customers),
            ),
            _buildStatCard(
              context,
              title: 'إجمالي الموردين',
              value: supplierCount.toString(),
              icon: Icons.business_outlined,
              color: AppColors.secondary,
              onTap: () => context.go(AppRoutes.suppliers),
            ),
            _buildStatCard(
              context,
              title: 'مديونية العملاء',
              value: currency.format(customerDebt),
              icon: Icons.account_balance_outlined,
              color: AppColors.success,
              onTap: () => context.go(AppRoutes.customers),
            ),
            _buildStatCard(
              context,
              title: 'مستحقات الموردين',
              value: currency.format(supplierCredit),
              icon: Icons.payment_outlined,
              color: AppColors.warning,
              onTap: () => context.go(AppRoutes.suppliers),
            ),
          ],
        ),
      ],
    );
  }

  /// بطاقة إحصائية
  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    VoidCallback? onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 32.r, color: color),
              SizedBox(height: 8.h),
              Text(
                value,
                style: AppTextStyles.titleLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                title,
                style: AppTextStyles.bodySmall,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// أنواع الحسابات (بطاقات العملاء والموردين)
  Widget _buildAccountTypes(
    BuildContext context,
    WidgetRef ref,
    CustomerState customerState,
    SupplierState supplierState,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'أنواع الحسابات',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        ResponsiveGrid(
          mobileColumns: 1,
          tabletColumns: 2,
          desktopColumns: 2,
          children: [
            _buildAccountTypeCard(
              context,
              title: 'العملاء',
              subtitle: 'إدارة بيانات العملاء والمبيعات',
              icon: Icons.people_outline,
              color: AppColors.primary,
              count: '${customerState.activeCustomersCount} عميل',
              isLoading: customerState.isLoading,
              onTap: () => context.go(AppRoutes.customers),
            ),
            _buildAccountTypeCard(
              context,
              title: 'الموردين',
              subtitle: 'إدارة بيانات الموردين والمشتريات',
              icon: Icons.business_outlined,
              color: AppColors.secondary,
              count: '${supplierState.activeSuppliersCount} مورد',
              isLoading: supplierState.isLoading,
              onTap: () => context.go(AppRoutes.suppliers),
            ),
          ],
        ),
      ],
    );
  }

  /// بطاقة نوع الحساب
  Widget _buildAccountTypeCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required String count,
    required bool isLoading,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(20.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(12.r),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: isLoading
                        ? SizedBox(
                            width: 32.r,
                            height: 32.r,
                            child: CircularProgressIndicator(
                              strokeWidth: 2.r,
                              valueColor: AlwaysStoppedAnimation<Color>(color),
                            ),
                          )
                        : Icon(icon, size: 32.r, color: color),
                  ),
                  const Spacer(),
                  Icon(Icons.arrow_forward_ios, size: 16.r, color: Colors.grey[400]),
                ],
              ),
              SizedBox(height: 16.h),
              Text(title, style: AppTextStyles.titleMedium.copyWith(fontWeight: FontWeight.bold)),
              SizedBox(height: 4.h),
              Text(subtitle, style: AppTextStyles.bodySmall.copyWith(color: Colors.grey[600])),
              SizedBox(height: 12.h),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Text(
                  count,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// النشاطات الأخيرة (نموذجية)
  Widget _buildRecentActivities(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'النشاطات الأخيرة',
          style: AppTextStyles.titleLarge.copyWith(fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 16.h),
        Card(
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 6,
            separatorBuilder: (_, __) => const Divider(height: 1),
            itemBuilder: (_, index) {
              final activities = [
                {
                  'type': 'customer',
                  'title': 'عميل جديد',
                  'subtitle': 'تم إضافة العميل: أحمد محمد',
                  'time': 'منذ ساعتين',
                  'icon': Icons.person_add_outlined,
                  'color': AppColors.success,
                  'route': AppRoutes.customers,
                },
                {
                  'type': 'payment',
                  'title': 'دفعة جديدة',
                  'subtitle': 'تم استلام 2,500 ر.س من العميل: سارة أحمد',
                  'time': 'منذ 3 ساعات',
                  'icon': Icons.payment_outlined,
                  'color': AppColors.primary,
                  'route': AppRoutes.customers,
                },
                {
                  'type': 'supplier',
                  'title': 'مورد جديد',
                  'subtitle': 'تم إضافة المورد: شركة التوريد المحدودة',
                  'time': 'منذ 5 ساعات',
                  'icon': Icons.business_outlined,
                  'color': AppColors.info,
                  'route': AppRoutes.suppliers,
                },
              ];
              final activity = activities[index % activities.length];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: (activity['color'] as Color).withOpacity(0.1),
                  child: Icon(
                    activity['icon'] as IconData,
                    color: activity['color'] as Color,
                    size: 20.r,
                  ),
                ),
                title: Text(activity['title'] as String,
                    style: AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.w600)),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(activity['subtitle'] as String, style: AppTextStyles.bodySmall),
                    Text(activity['time'] as String,
                        style: AppTextStyles.bodySmall.copyWith(color: Colors.grey[500])),
                  ],
                ),
                onTap: () => context.go(activity['route'] as String),
              );
            },
          ),
        ),
      ],
    );
  }
}