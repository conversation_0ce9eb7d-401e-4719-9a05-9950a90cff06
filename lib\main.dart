import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:tijari_tech/core/config/dev_initializer.dart';
import 'package:tijari_tech/data/local/delete_db.dart';
import 'package:flutter/foundation.dart';

import 'app.dart';
import 'core/utils/app_utils.dart';
import 'core/bootstrap/bootstrap_system.dart';
import 'core/services/service_locator.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize development environment
  if (kDebugMode) {
    await DevInitializer.initialize();
    DevInitializer.configureForTesting(scenario: 'full_bypass');
  }

  // تهيئة sqflite للعمل على سطح المكتب
  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  }

  // تهيئة النظام باستخدام BootstrapSystem
  try {
    AppUtils.logInfo('بدء تهيئة النظام...');

    // تهيئة Service Locator
    await ServiceLocator.init();
    AppUtils.logInfo('تم تهيئة Service Locator بنجاح');

    // تهيئة النظام الكامل
    await BootstrapSystem.bootstrapSystem();
    AppUtils.logInfo('تم تهيئة النظام بنجاح');

    // التحقق من صحة النظام
    final systemInfo = await BootstrapSystem.getSystemInfo();
    AppUtils.logInfo('معلومات النظام: $systemInfo');
  } catch (e) {
    AppUtils.logError('خطأ في تهيئة النظام', e);

    // محاولة إعادة تعيين قاعدة البيانات
    try {
      AppUtils.logInfo('محاولة إعادة تعيين قاعدة البيانات...');
      await deleteDatabaseFile();

      // إعادة تهيئة النظام
      await ServiceLocator.init();
      await BootstrapSystem.bootstrapSystem();
      AppUtils.logInfo('تم إعادة تعيين النظام بنجاح');
    } catch (resetError) {
      AppUtils.logError('فشل في إعادة تعيين النظام', resetError);
    }
  }

  runApp(
    const ProviderScope(
      child: TijariApp(),
    ),
  );
}
