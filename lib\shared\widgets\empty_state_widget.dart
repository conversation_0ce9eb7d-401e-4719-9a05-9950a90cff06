import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';

class EmptyStateWidget extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final String? actionText;
  final VoidCallback? onActionPressed;
  final Widget? customAction;
  final Color? iconColor;
  final double? iconSize;
  final bool showAnimation;

  const EmptyStateWidget({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.actionText,
    this.onActionPressed,
    this.customAction,
    this.iconColor,
    this.iconSize,
    this.showAnimation = true,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon
            TweenAnimationBuilder<double>(
              duration: showAnimation 
                  ? const Duration(milliseconds: 800)
                  : Duration.zero,
              tween: Tween(begin: 0.0, end: 1.0),
              builder: (context, value, child) {
                return Transform.scale(
                  scale: showAnimation ? value : 1.0,
                  child: Opacity(
                    opacity: showAnimation ? value : 1.0,
                    child: Container(
                      padding: EdgeInsets.all(24.w),
                      decoration: BoxDecoration(
                        color: (iconColor ?? AppColors.primary).withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        icon,
                        size: iconSize ?? 64.sp,
                        color: iconColor ?? AppColors.primary,
                      ),
                    ),
                  ),
                );
              },
            ),
            
            SizedBox(height: 24.h),
            
            // Title
            TweenAnimationBuilder<double>(
              duration: showAnimation 
                  ? const Duration(milliseconds: 800)
                  : Duration.zero,
              tween: Tween(begin: 0.0, end: 1.0),
              builder: (context, value, child) {
                return Transform.translate(
                  offset: showAnimation 
                      ? Offset(0, 20 * (1 - value))
                      : Offset.zero,
                  child: Opacity(
                    opacity: showAnimation ? value : 1.0,
                    child: Text(
                      title,
                      style: AppTextStyles.titleLarge.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[800],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                );
              },
            ),
            
            if (subtitle != null) ...[
              SizedBox(height: 12.h),
              TweenAnimationBuilder<double>(
                duration: showAnimation 
                    ? const Duration(milliseconds: 800)
                    : Duration.zero,
                tween: Tween(begin: 0.0, end: 1.0),
                builder: (context, value, child) {
                  return Transform.translate(
                    offset: showAnimation 
                        ? Offset(0, 20 * (1 - value))
                        : Offset.zero,
                    child: Opacity(
                      opacity: showAnimation ? value : 1.0,
                      child: Text(
                        subtitle!,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  );
                },
              ),
            ],
            
            if (customAction != null) ...[
              SizedBox(height: 32.h),
              TweenAnimationBuilder<double>(
                duration: showAnimation 
                    ? const Duration(milliseconds: 1000)
                    : Duration.zero,
                tween: Tween(begin: 0.0, end: 1.0),
                builder: (context, value, child) {
                  return Transform.translate(
                    offset: showAnimation 
                        ? Offset(0, 20 * (1 - value))
                        : Offset.zero,
                    child: Opacity(
                      opacity: showAnimation ? value : 1.0,
                      child: customAction!,
                    ),
                  );
                },
              ),
            ] else if (actionText != null && onActionPressed != null) ...[
              SizedBox(height: 32.h),
              TweenAnimationBuilder<double>(
                duration: showAnimation 
                    ? const Duration(milliseconds: 1000)
                    : Duration.zero,
                tween: Tween(begin: 0.0, end: 1.0),
                builder: (context, value, child) {
                  return Transform.translate(
                    offset: showAnimation 
                        ? Offset(0, 20 * (1 - value))
                        : Offset.zero,
                    child: Opacity(
                      opacity: showAnimation ? value : 1.0,
                      child: ElevatedButton.icon(
                        onPressed: onActionPressed,
                        icon: const Icon(Icons.add, color: Colors.white),
                        label: Text(
                          actionText!,
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          padding: EdgeInsets.symmetric(
                            horizontal: 24.w,
                            vertical: 12.h,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class SearchEmptyState extends StatelessWidget {
  final String searchQuery;
  final VoidCallback? onClearSearch;
  final String? suggestion;

  const SearchEmptyState({
    super.key,
    required this.searchQuery,
    this.onClearSearch,
    this.suggestion,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.search_off,
      title: 'لا توجد نتائج',
      subtitle: 'لم نجد أي نتائج لـ "$searchQuery"\n${suggestion ?? 'جرب تغيير كلمات البحث'}',
      actionText: 'مسح البحث',
      onActionPressed: onClearSearch,
      iconColor: Colors.orange,
    );
  }
}

class NoDataEmptyState extends StatelessWidget {
  final String dataType;
  final String? actionText;
  final VoidCallback? onActionPressed;
  final IconData? icon;

  const NoDataEmptyState({
    super.key,
    required this.dataType,
    this.actionText,
    this.onActionPressed,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: icon ?? Icons.inbox,
      title: 'لا توجد $dataType',
      subtitle: 'ابدأ بإضافة $dataType جديد',
      actionText: actionText ?? 'إضافة $dataType',
      onActionPressed: onActionPressed,
    );
  }
}

class ErrorEmptyState extends StatelessWidget {
  final String error;
  final VoidCallback? onRetry;

  const ErrorEmptyState({
    super.key,
    required this.error,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.error_outline,
      title: 'حدث خطأ',
      subtitle: error,
      actionText: 'إعادة المحاولة',
      onActionPressed: onRetry,
      iconColor: Colors.red,
    );
  }
}

class LoadingEmptyState extends StatelessWidget {
  final String? message;

  const LoadingEmptyState({
    super.key,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
          if (message != null) ...[
            SizedBox(height: 16.h),
            Text(
              message!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

class CustomEmptyState extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;

  const CustomEmptyState({
    super.key,
    required this.child,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: padding ?? EdgeInsets.all(32.w),
        child: child,
      ),
    );
  }
}
