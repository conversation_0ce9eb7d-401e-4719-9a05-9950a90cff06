import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../providers/warehouse_provider.dart';
import 'widgets/warehouse_details_dialog.dart';

class WarehouseDetailsPage extends ConsumerWidget {
  final String warehouseId;

  const WarehouseDetailsPage({
    super.key,
    required this.warehouseId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final warehouseAsync = ref.watch(warehouseByIdProvider(warehouseId));

    return warehouseAsync.when(
      data: (warehouse) => Scaffold(
        appBar: AppBar(
          title: Text(
            'تفاصيل المخزن',
            style: AppTextStyles.titleLarge.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: AppColors.primary,
          elevation: 0,
        ),
        body: warehouse != null
            ? Padding(
                padding: const EdgeInsets.all(16.0),
                child: WarehouseDetailsDialog(
                  warehouse: warehouse,
                ),
              )
            : const Center(
                child: Text('المخزن غير موجود'),
              ),
      ),
      loading: () => Scaffold(
        appBar: AppBar(
          title: Text(
            'تحميل...',
            style: AppTextStyles.titleLarge.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: AppColors.primary,
        ),
        body: const Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) => Scaffold(
        appBar: AppBar(
          title: Text(
            'خطأ',
            style: AppTextStyles.titleLarge.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: AppColors.primary,
        ),
        body: Center(
          child: Text('خطأ في تحميل بيانات المخزن: $error'),
        ),
      ),
    );
  }
}
