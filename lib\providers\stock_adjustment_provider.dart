import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/stock_adjustment.dart';
import '../data/repositories/stock_adjustment_repository.dart';
import '../core/utils/app_utils.dart';
import '../core/utils/error_handler.dart';
import 'database_provider.dart';

// Stock adjustment repository provider
final stockAdjustmentRepositoryProvider = Provider<StockAdjustmentRepository>((ref) {
  return StockAdjustmentRepository();
});

// Stock adjustment management provider
final stockAdjustmentManagementProvider = StateNotifierProvider<
    StockAdjustmentManagementNotifier, StockAdjustmentManagementState>((ref) {
  return StockAdjustmentManagementNotifier(ref);
});

// Stock adjustment management state
class StockAdjustmentManagementState {
  final bool isLoading;
  final String? error;
  final List<StockAdjustment> adjustments;
  final List<StockAdjustment> filteredAdjustments;
  final String searchQuery;
  final String? selectedWarehouse;
  final String? selectedStatus;
  final DateTime? fromDate;
  final DateTime? toDate;
  final StockAdjustment? selectedAdjustment;
  final List<StockAdjustmentItem> adjustmentItems;
  final Map<String, dynamic> statistics;
  final String sortField;
  final bool sortAscending;

  const StockAdjustmentManagementState({
    this.isLoading = false,
    this.error,
    this.adjustments = const [],
    this.filteredAdjustments = const [],
    this.searchQuery = '',
    this.selectedWarehouse,
    this.selectedStatus,
    this.fromDate,
    this.toDate,
    this.selectedAdjustment,
    this.adjustmentItems = const [],
    this.statistics = const {},
    this.sortField = 'created_at',
    this.sortAscending = false,
  });

  StockAdjustmentManagementState copyWith({
    bool? isLoading,
    String? error,
    List<StockAdjustment>? adjustments,
    List<StockAdjustment>? filteredAdjustments,
    String? searchQuery,
    String? selectedWarehouse,
    String? selectedStatus,
    DateTime? fromDate,
    DateTime? toDate,
    StockAdjustment? selectedAdjustment,
    List<StockAdjustmentItem>? adjustmentItems,
    Map<String, dynamic>? statistics,
    String? sortField,
    bool? sortAscending,
  }) {
    return StockAdjustmentManagementState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      adjustments: adjustments ?? this.adjustments,
      filteredAdjustments: filteredAdjustments ?? this.filteredAdjustments,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedWarehouse: selectedWarehouse ?? this.selectedWarehouse,
      selectedStatus: selectedStatus ?? this.selectedStatus,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      selectedAdjustment: selectedAdjustment ?? this.selectedAdjustment,
      adjustmentItems: adjustmentItems ?? this.adjustmentItems,
      statistics: statistics ?? this.statistics,
      sortField: sortField ?? this.sortField,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }
}

// Stock adjustment management notifier
class StockAdjustmentManagementNotifier extends StateNotifier<StockAdjustmentManagementState> {
  final Ref _ref;

  StockAdjustmentManagementNotifier(this._ref) : super(const StockAdjustmentManagementState());

  StockAdjustmentRepository get _repository => _ref.read(stockAdjustmentRepositoryProvider);
  DatabaseOperations get _dbOperations => _ref.read(databaseOperationsProvider);

  // Load adjustments
  Future<void> loadAdjustments() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final adjustments = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getAllAdjustments(
          warehouseId: state.selectedWarehouse,
          status: state.selectedStatus,
          fromDate: state.fromDate,
          toDate: state.toDate,
          limit: 1000, // Limit for performance
        );
      });

      final statistics = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getAdjustmentStatistics(
          warehouseId: state.selectedWarehouse,
          fromDate: state.fromDate,
          toDate: state.toDate,
        );
      });

      state = state.copyWith(
        isLoading: false,
        adjustments: adjustments,
        filteredAdjustments: _filterAndSortAdjustments(adjustments),
        statistics: statistics,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error loading adjustments', e);
    }
  }

  // Search adjustments
  Future<void> search(String query) async {
    state = state.copyWith(searchQuery: query);
    
    if (query.isEmpty) {
      state = state.copyWith(
        filteredAdjustments: _filterAndSortAdjustments(state.adjustments),
      );
      return;
    }

    try {
      state = state.copyWith(isLoading: true, error: null);

      final adjustments = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.searchAdjustments(
          searchTerm: query,
          warehouseId: state.selectedWarehouse,
          status: state.selectedStatus,
          fromDate: state.fromDate,
          toDate: state.toDate,
          limit: 1000,
        );
      });

      state = state.copyWith(
        isLoading: false,
        filteredAdjustments: _filterAndSortAdjustments(adjustments),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error searching adjustments', e);
    }
  }

  // Filter by warehouse
  void filterByWarehouse(String? warehouseId) {
    state = state.copyWith(selectedWarehouse: warehouseId);
    loadAdjustments();
  }

  // Filter by status
  void filterByStatus(String? status) {
    state = state.copyWith(selectedStatus: status);
    loadAdjustments();
  }

  // Filter by date range
  void filterByDateRange(DateTime? fromDate, DateTime? toDate) {
    state = state.copyWith(fromDate: fromDate, toDate: toDate);
    loadAdjustments();
  }

  // Sort adjustments
  void sortAdjustments(String field, bool ascending) {
    state = state.copyWith(
      sortField: field,
      sortAscending: ascending,
      filteredAdjustments: _filterAndSortAdjustments(state.adjustments),
    );
  }

  // Select adjustment
  Future<void> selectAdjustment(String adjustmentId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final adjustment = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getAdjustmentById(adjustmentId);
      });

      final items = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getAdjustmentItems(adjustmentId);
      });

      state = state.copyWith(
        isLoading: false,
        selectedAdjustment: adjustment,
        adjustmentItems: items,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error selecting adjustment', e);
    }
  }

  // Create adjustment
  Future<bool> createAdjustment({
    required String referenceNumber,
    required String warehouseId,
    required String reason,
    required String notes,
    required DateTime adjustmentDate,
    required String createdBy,
    required List<Map<String, dynamic>> items,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final adjustment = StockAdjustment.create(
        id: AppUtils.generateId(),
        referenceNumber: referenceNumber,
        warehouseId: warehouseId,
        reason: reason,
        notes: notes,
        adjustmentDate: adjustmentDate,
        createdBy: createdBy,
      );

      final adjustmentItems = items.map((item) => 
        StockAdjustmentItem.create(
          id: AppUtils.generateId(),
          adjustmentId: adjustment.id,
          productId: item['productId'] as String,
          currentQuantity: (item['currentQty'] as num).toDouble(),
          adjustmentQuantity: (item['adjustmentQty'] as num).toDouble(),
          adjustmentType: item['type'] as String,
          reason: item['reason'] as String,
          notes: item['notes'] as String? ?? '',
        )
      ).toList();

      await _dbOperations.executeWithReadyCheck(() async {
        await _repository.createAdjustmentWithItems(
          adjustment: adjustment,
          items: adjustmentItems,
        );
      });

      await loadAdjustments();
      
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error creating adjustment', e);
      return false;
    }
  }

  // Approve adjustment
  Future<bool> approveAdjustment(String adjustmentId, String approvedBy) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _dbOperations.executeWithReadyCheck(() async {
        await _repository.approveAdjustment(adjustmentId, approvedBy);
      });

      await loadAdjustments();
      
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error approving adjustment', e);
      return false;
    }
  }

  // Cancel adjustment
  Future<bool> cancelAdjustment(String adjustmentId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _dbOperations.executeWithReadyCheck(() async {
        await _repository.cancelAdjustment(adjustmentId);
      });

      await loadAdjustments();
      
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error cancelling adjustment', e);
      return false;
    }
  }

  // Delete adjustment
  Future<bool> deleteAdjustment(String adjustmentId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _dbOperations.executeWithReadyCheck(() async {
        await _repository.deleteAdjustment(adjustmentId);
      });

      await loadAdjustments();
      
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error deleting adjustment', e);
      return false;
    }
  }

  // Generate reference number
  Future<String> generateReferenceNumber() async {
    try {
      return await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.generateReferenceNumber();
      });
    } catch (e) {
      AppUtils.logError('Error generating reference number', e);
      return 'ADJ-${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  // Clear selection
  void clearSelection() {
    state = state.copyWith(
      selectedAdjustment: null,
      adjustmentItems: [],
    );
  }

  // Clear filters
  void clearFilters() {
    state = state.copyWith(
      searchQuery: '',
      selectedWarehouse: null,
      selectedStatus: null,
      fromDate: null,
      toDate: null,
      filteredAdjustments: _filterAndSortAdjustments(state.adjustments),
    );
  }

  // Private helper methods
  List<StockAdjustment> _filterAndSortAdjustments(List<StockAdjustment> adjustments) {
    var filtered = adjustments.where((adjustment) {
      // Apply filters
      if (state.selectedWarehouse != null && 
          adjustment.warehouseId != state.selectedWarehouse) {
        return false;
      }

      if (state.selectedStatus != null && 
          adjustment.status != state.selectedStatus) {
        return false;
      }

      if (state.fromDate != null && 
          adjustment.adjustmentDate.isBefore(state.fromDate!)) {
        return false;
      }

      if (state.toDate != null && 
          adjustment.adjustmentDate.isAfter(state.toDate!)) {
        return false;
      }

      return true;
    }).toList();

    // Apply sorting
    filtered.sort((a, b) {
      int comparison = 0;
      
      switch (state.sortField) {
        case 'reference_number':
          comparison = a.referenceNumber.compareTo(b.referenceNumber);
          break;
        case 'adjustment_date':
          comparison = a.adjustmentDate.compareTo(b.adjustmentDate);
          break;
        case 'status':
          comparison = a.status.compareTo(b.status);
          break;
        case 'created_at':
        default:
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
      }

      return state.sortAscending ? comparison : -comparison;
    });

    return filtered;
  }
}
