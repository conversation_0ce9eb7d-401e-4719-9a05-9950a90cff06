import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:json_annotation/json_annotation.dart';

import '../models/user.dart';
import '../models/transaction.dart';
import '../models/stock_adjustment.dart';

part 'api_client.g.dart';

@RestApi()
abstract class ApiClient {
  factory ApiClient(Dio dio, {String baseUrl}) = _ApiClient;

  // Authentication endpoints
  @POST('/auth/login')
  Future<ApiResponse<AuthResponse>> login(@Body() LoginRequest request);

  @POST('/auth/logout')
  Future<ApiResponse<void>> logout();

  @POST('/auth/refresh')
  Future<ApiResponse<AuthResponse>> refreshToken(@Body() RefreshTokenRequest request);

  // User endpoints
  @GET('/users')
  Future<ApiResponse<List<User>>> getUsers({
    @Query('page') int? page,
    @Query('limit') int? limit,
    @Query('search') String? search,
    @Query('role') String? role,
  });

  @GET('/users/{id}')
  Future<ApiResponse<User>> getUserById(@Path('id') String id);

  @POST('/users')
  Future<ApiResponse<User>> createUser(@Body() User user);

  @PUT('/users/{id}')
  Future<ApiResponse<User>> updateUser(@Path('id') String id, @Body() User user);

  @DELETE('/users/{id}')
  Future<ApiResponse<void>> deleteUser(@Path('id') String id);

  // Transaction endpoints
  @GET('/transactions')
  Future<ApiResponse<List<Transactions>>> getTransactions({
    @Query('page') int? page,
    @Query('limit') int? limit,
    @Query('type') String? type,
    @Query('start_date') String? startDate,
    @Query('end_date') String? endDate,
  });

  @GET('/transactions/{id}')
  Future<ApiResponse<Transactions>> getTransactionById(@Path('id') String id);

  @POST('/transactions')
  Future<ApiResponse<Transactions>> createTransaction(@Body() Transactions transaction);

  @PUT('/transactions/{id}')
  Future<ApiResponse<Transactions>> updateTransaction(
    @Path('id') String id, 
    @Body() Transactions transaction,
  );

  @DELETE('/transactions/{id}')
  Future<ApiResponse<void>> deleteTransaction(@Path('id') String id);

  // Stock adjustment endpoints
  @GET('/stock-adjustments')
  Future<ApiResponse<List<StockAdjustment>>> getStockAdjustments({
    @Query('page') int? page,
    @Query('limit') int? limit,
    @Query('warehouse_id') String? warehouseId,
    @Query('status') String? status,
  });

  @GET('/stock-adjustments/{id}')
  Future<ApiResponse<StockAdjustment>> getStockAdjustmentById(@Path('id') String id);

  @POST('/stock-adjustments')
  Future<ApiResponse<StockAdjustment>> createStockAdjustment(@Body() StockAdjustment adjustment);

  @PUT('/stock-adjustments/{id}')
  Future<ApiResponse<StockAdjustment>> updateStockAdjustment(
    @Path('id') String id, 
    @Body() StockAdjustment adjustment,
  );

  @DELETE('/stock-adjustments/{id}')
  Future<ApiResponse<void>> deleteStockAdjustment(@Path('id') String id);

  // Reports endpoints
  @GET('/reports/profit-loss')
  Future<ApiResponse<ProfitLossReport>> getProfitLossReport({
    @Query('start_date') required String startDate,
    @Query('end_date') required String endDate,
    @Query('branch_id') String? branchId,
  });

  @GET('/reports/cash-flow')
  Future<ApiResponse<CashFlowReport>> getCashFlowReport({
    @Query('start_date') required String startDate,
    @Query('end_date') required String endDate,
    @Query('branch_id') String? branchId,
  });

  @GET('/reports/sales-summary')
  Future<ApiResponse<SalesSummaryReport>> getSalesSummaryReport({
    @Query('start_date') required String startDate,
    @Query('end_date') required String endDate,
    @Query('branch_id') String? branchId,
  });

  // Sync endpoints
  @POST('/sync/upload')
  Future<ApiResponse<SyncResponse>> uploadData(@Body() SyncUploadRequest request);

  @POST('/sync/download')
  Future<ApiResponse<SyncDownloadResponse>> downloadData(@Body() SyncDownloadRequest request);

  @GET('/sync/status')
  Future<ApiResponse<SyncStatus>> getSyncStatus();

  // Backup endpoints
  @POST('/backup/create')
  Future<ApiResponse<BackupResponse>> createBackup(@Body() BackupRequest request);

  @GET('/backup/list')
  Future<ApiResponse<List<BackupInfo>>> getBackupList();

  @POST('/backup/restore')
  Future<ApiResponse<RestoreResponse>> restoreBackup(@Body() RestoreRequest request);
}

// API Response wrapper
@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> {
  final bool success;
  final String? message;
  final T? data;
  final Map<String, dynamic>? errors;
  final PaginationInfo? pagination;

  const ApiResponse({
    required this.success,
    this.message,
    this.data,
    this.errors,
    this.pagination,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) => _$ApiResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);
}

// Pagination info
@JsonSerializable()
class PaginationInfo {
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int itemsPerPage;

  const PaginationInfo({
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.itemsPerPage,
  });

  factory PaginationInfo.fromJson(Map<String, dynamic> json) =>
      _$PaginationInfoFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationInfoToJson(this);
}

// Authentication models
@JsonSerializable()
class LoginRequest {
  final String email;
  final String password;
  final String? deviceId;
  final String? deviceName;

  const LoginRequest({
    required this.email,
    required this.password,
    this.deviceId,
    this.deviceName,
  });

  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);

  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}

@JsonSerializable()
class AuthResponse {
  final String accessToken;
  final String refreshToken;
  final User user;
  final int expiresIn;

  const AuthResponse({
    required this.accessToken,
    required this.refreshToken,
    required this.user,
    required this.expiresIn,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);
}

@JsonSerializable()
class RefreshTokenRequest {
  final String refreshToken;

  const RefreshTokenRequest({required this.refreshToken});

  factory RefreshTokenRequest.fromJson(Map<String, dynamic> json) =>
      _$RefreshTokenRequestFromJson(json);

  Map<String, dynamic> toJson() => _$RefreshTokenRequestToJson(this);
}

// Report models
@JsonSerializable()
class ProfitLossReport {
  final double totalRevenue;
  final double totalExpenses;
  final double grossProfit;
  final double netProfit;
  final double profitMargin;
  final Map<String, double> revenueBreakdown;
  final Map<String, double> expenseBreakdown;
  final String startDate;
  final String endDate;

  const ProfitLossReport({
    required this.totalRevenue,
    required this.totalExpenses,
    required this.grossProfit,
    required this.netProfit,
    required this.profitMargin,
    required this.revenueBreakdown,
    required this.expenseBreakdown,
    required this.startDate,
    required this.endDate,
  });

  factory ProfitLossReport.fromJson(Map<String, dynamic> json) =>
      _$ProfitLossReportFromJson(json);

  Map<String, dynamic> toJson() => _$ProfitLossReportToJson(this);
}

@JsonSerializable()
class CashFlowReport {
  final double totalInflows;
  final double totalOutflows;
  final double netCashFlow;
  final Map<String, double> inflowBreakdown;
  final Map<String, double> outflowBreakdown;
  final String startDate;
  final String endDate;

  const CashFlowReport({
    required this.totalInflows,
    required this.totalOutflows,
    required this.netCashFlow,
    required this.inflowBreakdown,
    required this.outflowBreakdown,
    required this.startDate,
    required this.endDate,
  });

  factory CashFlowReport.fromJson(Map<String, dynamic> json) =>
      _$CashFlowReportFromJson(json);

  Map<String, dynamic> toJson() => _$CashFlowReportToJson(this);
}

@JsonSerializable()
class SalesSummaryReport {
  final double totalSales;
  final int totalTransactions;
  final double averageTransactionValue;
  final Map<String, double> salesByCategory;
  final Map<String, double> salesByPaymentMethod;
  final String startDate;
  final String endDate;

  const SalesSummaryReport({
    required this.totalSales,
    required this.totalTransactions,
    required this.averageTransactionValue,
    required this.salesByCategory,
    required this.salesByPaymentMethod,
    required this.startDate,
    required this.endDate,
  });

  factory SalesSummaryReport.fromJson(Map<String, dynamic> json) =>
      _$SalesSummaryReportFromJson(json);

  Map<String, dynamic> toJson() => _$SalesSummaryReportToJson(this);
}

// Sync models
@JsonSerializable()
class SyncUploadRequest {
  final List<Map<String, dynamic>> users;
  final List<Map<String, dynamic>> transactions;
  final List<Map<String, dynamic>> stockAdjustments;
  final String lastSyncTimestamp;

  const SyncUploadRequest({
    required this.users,
    required this.transactions,
    required this.stockAdjustments,
    required this.lastSyncTimestamp,
  });

  factory SyncUploadRequest.fromJson(Map<String, dynamic> json) =>
      _$SyncUploadRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SyncUploadRequestToJson(this);
}

@JsonSerializable()
class SyncResponse {
  final bool success;
  final String message;
  final int uploadedRecords;
  final String syncTimestamp;

  const SyncResponse({
    required this.success,
    required this.message,
    required this.uploadedRecords,
    required this.syncTimestamp,
  });

  factory SyncResponse.fromJson(Map<String, dynamic> json) =>
      _$SyncResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SyncResponseToJson(this);
}

@JsonSerializable()
class SyncDownloadRequest {
  final String lastSyncTimestamp;
  final List<String> tables;

  const SyncDownloadRequest({
    required this.lastSyncTimestamp,
    required this.tables,
  });

  factory SyncDownloadRequest.fromJson(Map<String, dynamic> json) =>
      _$SyncDownloadRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SyncDownloadRequestToJson(this);
}

@JsonSerializable()
class SyncDownloadResponse {
  final Map<String, List<Map<String, dynamic>>> data;
  final String syncTimestamp;

  const SyncDownloadResponse({
    required this.data,
    required this.syncTimestamp,
  });

  factory SyncDownloadResponse.fromJson(Map<String, dynamic> json) =>
      _$SyncDownloadResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SyncDownloadResponseToJson(this);
}

@JsonSerializable()
class SyncStatus {
  final String lastSyncTimestamp;
  final bool isOnline;
  final int pendingUploads;
  final int pendingDownloads;

  const SyncStatus({
    required this.lastSyncTimestamp,
    required this.isOnline,
    required this.pendingUploads,
    required this.pendingDownloads,
  });

  factory SyncStatus.fromJson(Map<String, dynamic> json) =>
      _$SyncStatusFromJson(json);

  Map<String, dynamic> toJson() => _$SyncStatusToJson(this);
}

// Backup models
@JsonSerializable()
class BackupRequest {
  final String name;
  final List<String> tables;
  final bool includeFiles;

  const BackupRequest({
    required this.name,
    required this.tables,
    this.includeFiles = false,
  });

  factory BackupRequest.fromJson(Map<String, dynamic> json) =>
      _$BackupRequestFromJson(json);

  Map<String, dynamic> toJson() => _$BackupRequestToJson(this);
}

@JsonSerializable()
class BackupResponse {
  final String backupId;
  final String downloadUrl;
  final int fileSize;
  final String createdAt;

  const BackupResponse({
    required this.backupId,
    required this.downloadUrl,
    required this.fileSize,
    required this.createdAt,
  });

  factory BackupResponse.fromJson(Map<String, dynamic> json) =>
      _$BackupResponseFromJson(json);

  Map<String, dynamic> toJson() => _$BackupResponseToJson(this);
}

@JsonSerializable()
class BackupInfo {
  final String id;
  final String name;
  final int fileSize;
  final String createdAt;
  final bool isAvailable;

  const BackupInfo({
    required this.id,
    required this.name,
    required this.fileSize,
    required this.createdAt,
    required this.isAvailable,
  });

  factory BackupInfo.fromJson(Map<String, dynamic> json) =>
      _$BackupInfoFromJson(json);

  Map<String, dynamic> toJson() => _$BackupInfoToJson(this);
}

@JsonSerializable()
class RestoreRequest {
  final String backupId;
  final List<String> tables;
  final bool overwriteExisting;

  const RestoreRequest({
    required this.backupId,
    required this.tables,
    this.overwriteExisting = false,
  });

  factory RestoreRequest.fromJson(Map<String, dynamic> json) =>
      _$RestoreRequestFromJson(json);

  Map<String, dynamic> toJson() => _$RestoreRequestToJson(this);
}

@JsonSerializable()
class RestoreResponse {
  final bool success;
  final String message;
  final int restoredRecords;

  const RestoreResponse({
    required this.success,
    required this.message,
    required this.restoredRecords,
  });

  factory RestoreResponse.fromJson(Map<String, dynamic> json) =>
      _$RestoreResponseFromJson(json);

  Map<String, dynamic> toJson() => _$RestoreResponseToJson(this);
}
