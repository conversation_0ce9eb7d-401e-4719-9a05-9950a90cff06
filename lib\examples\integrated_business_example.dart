// import '../services/integrated_business_service.dart';
// import '../services/entity_relationship_service.dart';
// import '../services/default_data_service.dart';
// import '../services/system_initialization_service.dart';
// import '../core/utils/app_utils.dart';

// /// مثال شامل لاستخدام النظام المتكامل للعمليات التجارية
// class IntegratedBusinessExample {
//   static final IntegratedBusinessService _businessService =
//       IntegratedBusinessService();
//   static final EntityRelationshipService _relationshipService =
//       EntityRelationshipService();
//   static final DefaultDataService _defaultDataService = DefaultDataService();

//   /// تشغيل جميع الأمثلة
//   static Future<void> runAllExamples() async {
//     try {
//       AppUtils.logInfo('=== بدء تشغيل أمثلة النظام المتكامل ===');

//       // 1. التأكد من وجود البيانات الافتراضية
//       await _ensureDefaultDataExample();

//       // 2. مثال على عملية بيع بدون تحديد كيانات (استخدام افتراضي)
//       await _saleWithDefaultEntitiesExample();

//       // 3. مثال على عملية شراء مع تحديد مورد معين
//       await _purchaseWithSpecificSupplierExample();

//       // 4. مثال على التحقق من العلاقات قبل العملية
//       await _validateRelationshipsExample();

//       // 5. مثال على التحقق من المخزون
//       await _stockValidationExample();

//       // 6. مثال على عملية بيع مع نقص في المخزون
//       await _saleWithInsufficientStockExample();

//       AppUtils.logInfo('=== انتهاء تشغيل جميع الأمثلة بنجاح ===');
//     } catch (e) {
//       AppUtils.logError('خطأ في تشغيل الأمثلة', e);
//     }
//   }

//   /// مثال 1: التأكد من وجود البيانات الافتراضية
//   static Future<void> _ensureDefaultDataExample() async {
//     AppUtils.logInfo('\n--- مثال 1: التأكد من البيانات الافتراضية ---');

//     try {
//       // ضمان وجود البيانات الافتراضية
//       await _defaultDataService.ensureDefaultDataExists();

//       // الحصول على ملخص الكيانات الافتراضية
//       final defaultEntities =
//           await _businessService.getDefaultEntitiesSummary();

//       AppUtils.logInfo('الكيانات الافتراضية المتاحة:');
//       defaultEntities.forEach((key, value) {
//         if (value is Map<String, dynamic>) {
//           final name = value['name'] ?? value['nameAr'] ?? 'غير محدد';
//           AppUtils.logInfo('- $key: $name');
//         }
//       });
//     } catch (e) {
//       AppUtils.logError('خطأ في مثال البيانات الافتراضية', e);
//     }
//   }

//   /// مثال 2: عملية بيع بدون تحديد كيانات (استخدام افتراضي)
//   static Future<void> _saleWithDefaultEntitiesExample() async {
//     AppUtils.logInfo('\n--- مثال 2: عملية بيع مع الكيانات الافتراضية ---');

//     try {
//       // عملية بيع بدون تحديد عميل أو مخزن أو مستخدم (سيستخدم الافتراضي)
//       final result = await _businessService.createSmartSaleTransaction(
//         items: [
//           {
//             'productId': 'product-1', // يجب أن يكون موجود في قاعدة البيانات
//             'qty': 2.0,
//             'unitPrice': 150.0,
//           },
//           {
//             'productId': 'product-2',
//             'qty': 1.0,
//             'unitPrice': 300.0,
//           },
//         ],
//         paidAmount: 400.0,
//         notes: 'بيع نقدي مع الكيانات الافتراضية',
//       );

//       if (result['success']) {
//         AppUtils.logInfo('✅ تم إنشاء عملية البيع بنجاح');
//         AppUtils.logInfo('معرف البيع: ${result['sale_id']}');
//         AppUtils.logInfo('إجمالي المبلغ: ${result['total_amount']} ر.س');
//         AppUtils.logInfo('المبلغ المدفوع: ${result['paid_amount']} ر.س');
//         AppUtils.logInfo('المبلغ المتبقي: ${result['remaining_amount']} ر.س');

//         // عرض التحذيرات إن وجدت
//         final warnings = result['warnings'] as List<String>?;
//         if (warnings != null && warnings.isNotEmpty) {
//           AppUtils.logInfo('تحذيرات:');
//           for (final warning in warnings) {
//             AppUtils.logInfo('⚠️ $warning');
//           }
//         }
//       } else {
//         AppUtils.logError('❌ فشل في إنشاء عملية البيع', null);
//         final errors = result['errors'] as List<String>?;
//         if (errors != null) {
//           for (final error in errors) {
//             AppUtils.logError('خطأ: $error', null);
//           }
//         }
//       }
//     } catch (e) {
//       AppUtils.logError('خطأ في مثال البيع الافتراضي', e);
//     }
//   }

//   /// مثال 3: عملية شراء مع تحديد مورد معين
//   static Future<void> _purchaseWithSpecificSupplierExample() async {
//     AppUtils.logInfo('\n--- مثال 3: عملية شراء مع مورد محدد ---');

//     try {
//       final result = await _businessService.createSmartPurchaseTransaction(
//         supplierId:
//             'supplier-specific', // مورد محدد (إذا لم يوجد سيستخدم الافتراضي)
//         items: [
//           {
//             'productId': 'product-1',
//             'qty': 10.0,
//             'unitPrice': 100.0,
//           },
//         ],
//         paidAmount: 500.0,
//         notes: 'شراء من مورد محدد',
//       );

//       if (result['success']) {
//         AppUtils.logInfo('✅ تم إنشاء عملية الشراء بنجاح');
//         AppUtils.logInfo('معرف الشراء: ${result['purchase_id']}');
//         AppUtils.logInfo('إجمالي المبلغ: ${result['total_amount']} ر.س');

//         // عرض الكيانات المستخدمة
//         final resolvedEntities =
//             result['resolved_entities'] as Map<String, dynamic>?;
//         if (resolvedEntities != null &&
//             resolvedEntities.containsKey('supplier')) {
//           final supplier = resolvedEntities['supplier'] as Map<String, dynamic>;
//           AppUtils.logInfo('المورد المستخدم: ${supplier['name']}');
//         }
//       } else {
//         AppUtils.logError('❌ فشل في إنشاء عملية الشراء', null);
//         final errors = result['errors'] as List<String>?;
//         if (errors != null) {
//           for (final error in errors) {
//             AppUtils.logError('خطأ: $error', null);
//           }
//         }
//       }
//     } catch (e) {
//       AppUtils.logError('خطأ في مثال الشراء المحدد', e);
//     }
//   }

//   /// مثال 4: التحقق من العلاقات قبل العملية
//   static Future<void> _validateRelationshipsExample() async {
//     AppUtils.logInfo('\n--- مثال 4: التحقق من العلاقات ---');

//     try {
//       final validation = await _businessService.validateTransactionData(
//         customerId: 'non-existent-customer', // عميل غير موجود
//         warehouseId: 'non-existent-warehouse', // مخزن غير موجود
//         items: [
//           {'productId': 'product-1', 'qty': 1.0, 'unitPrice': 100.0},
//         ],
//       );

//       AppUtils.logInfo('نتيجة التحقق من العلاقات:');
//       AppUtils.logInfo('صالح: ${validation['valid']}');

//       final errors = validation['errors'] as List<String>?;
//       if (errors != null && errors.isNotEmpty) {
//         AppUtils.logInfo('الأخطاء المكتشفة:');
//         for (final error in errors) {
//           AppUtils.logInfo('❌ $error');
//         }
//       }

//       final warnings = validation['warnings'] as List<String>?;
//       if (warnings != null && warnings.isNotEmpty) {
//         AppUtils.logInfo('التحذيرات:');
//         for (final warning in warnings) {
//           AppUtils.logInfo('⚠️ $warning');
//         }
//       }
//     } catch (e) {
//       AppUtils.logError('خطأ في مثال التحقق من العلاقات', e);
//     }
//   }

//   /// مثال 5: التحقق من المخزون
//   static Future<void> _stockValidationExample() async {
//     AppUtils.logInfo('\n--- مثال 5: التحقق من المخزون ---');

//     try {
//       final stockValidation =
//           await _relationshipService.validateStockAvailability(
//         items: [
//           {'productId': 'product-1', 'qty': 5.0},
//           {'productId': 'product-2', 'qty': 2.0},
//         ],
//       );

//       AppUtils.logInfo('نتيجة التحقق من المخزون:');
//       AppUtils.logInfo('صالح: ${stockValidation['valid']}');
//       AppUtils.logInfo('المخزن: ${stockValidation['warehouse_id']}');

//       final items = stockValidation['items'] as List<dynamic>?;
//       if (items != null) {
//         AppUtils.logInfo('تفاصيل المنتجات:');
//         for (final item in items) {
//           final itemMap = item as Map<String, dynamic>;
//           AppUtils.logInfo('- ${itemMap['product_name']}: '
//               'مطلوب ${itemMap['requested_qty']}, '
//               'متاح ${itemMap['available_qty']}, '
//               'صالح: ${itemMap['valid']}');
//         }
//       }
//     } catch (e) {
//       AppUtils.logError('خطأ في مثال التحقق من المخزون', e);
//     }
//   }

//   /// مثال 6: عملية بيع مع نقص في المخزون
//   static Future<void> _saleWithInsufficientStockExample() async {
//     AppUtils.logInfo('\n--- مثال 6: عملية بيع مع نقص في المخزون ---');

//     try {
//       final result = await _businessService.createSmartSaleTransaction(
//         items: [
//           {
//             'productId': 'product-1',
//             'qty': 1000.0, // كمية كبيرة قد تتجاوز المخزون المتاح
//             'unitPrice': 150.0,
//           },
//         ],
//         paidAmount: 10000.0,
//         notes: 'محاولة بيع بكمية كبيرة',
//       );

//       if (result['success']) {
//         AppUtils.logInfo('✅ تم إنشاء عملية البيع رغم الكمية الكبيرة');
//         AppUtils.logInfo('معرف البيع: ${result['sale_id']}');
//       } else {
//         AppUtils.logInfo('❌ فشل في إنشاء عملية البيع بسبب نقص المخزون');

//         final stockValidation =
//             result['stock_validation'] as Map<String, dynamic>?;
//         if (stockValidation != null) {
//           AppUtils.logInfo(
//               'إجمالي النقص: ${stockValidation['total_shortage']}');

//           final items = stockValidation['items'] as List<dynamic>?;
//           if (items != null) {
//             for (final item in items) {
//               final itemMap = item as Map<String, dynamic>;
//               if (!itemMap['valid']) {
//                 AppUtils.logInfo('نقص في ${itemMap['product_name']}: '
//                     'مطلوب ${itemMap['requested_qty']}, '
//                     'متاح ${itemMap['available_qty']}, '
//                     'نقص ${itemMap['shortage']}');
//               }
//             }
//           }
//         }
//       }
//     } catch (e) {
//       AppUtils.logError('خطأ في مثال البيع مع نقص المخزون', e);
//     }
//   }
// }

// /// تشغيل الأمثلة
// Future<void> main() async {
//   await IntegratedBusinessExample.runAllExamples();
// }

// /// مثال لاستخدام النظام في التطبيق الرئيسي
// class MainAppIntegrationExample {
//   /// تهيئة النظام عند بدء التطبيق
//   static Future<void> initializeAppWithIntegratedSystem() async {
//     try {
//       AppUtils.logInfo('=== تهيئة التطبيق مع النظام المتكامل ===');

//       // 1. تهيئة النظام
//       final SystemInitializationService initService =
//           SystemInitializationService();
//       final initResult = await initService.initializeSystem();

//       if (initResult['success']) {
//         AppUtils.logInfo('✅ تم تهيئة النظام بنجاح');
//         AppUtils.logInfo('وقت التهيئة: ${initResult['initialization_time']}ms');

//         // عرض تفاصيل التهيئة
//         final details = initResult['details'] as Map<String, dynamic>;
//         final steps = details['steps'] as List<dynamic>;

//         AppUtils.logInfo('خطوات التهيئة المكتملة:');
//         for (final step in steps) {
//           final stepMap = step as Map<String, dynamic>;
//           AppUtils.logInfo('- ${stepMap['name']}: ${stepMap['status']}');
//         }

//         // عرض التحذيرات إن وجدت
//         final warnings = details['warnings'] as List<String>;
//         if (warnings.isNotEmpty) {
//           AppUtils.logInfo('تحذيرات:');
//           for (final warning in warnings) {
//             AppUtils.logInfo('⚠️ $warning');
//           }
//         }

//         // 2. التحقق من جاهزية النظام للعمليات التجارية
//         final isReady = await initService.isSystemReadyForBusiness();
//         if (isReady) {
//           AppUtils.logInfo('✅ النظام جاهز للعمليات التجارية');

//           // 3. تشغيل مثال على العمليات
//           await _runBusinessOperationsExample();
//         } else {
//           AppUtils.logError('❌ النظام غير جاهز للعمليات التجارية', null);
//         }
//       } else {
//         AppUtils.logError(
//             '❌ فشل في تهيئة النظام: ${initResult['message']}', null);
//       }
//     } catch (e) {
//       AppUtils.logError('خطأ في تهيئة التطبيق', e);
//     }
//   }

//   /// تشغيل مثال على العمليات التجارية
//   static Future<void> _runBusinessOperationsExample() async {
//     try {
//       AppUtils.logInfo('\n=== مثال على العمليات التجارية ===');

//       final IntegratedBusinessService businessService =
//           IntegratedBusinessService();

//       // مثال 1: عملية بيع سريعة
//       AppUtils.logInfo('تنفيذ عملية بيع سريعة...');
//       final saleResult = await businessService.createSmartSaleTransaction(
//         items: [
//           {
//             'productId': 'demo-product-1', // منتج تجريبي
//             'qty': 1.0,
//             'unitPrice': 100.0,
//           },
//         ],
//         paidAmount: 100.0,
//         notes: 'بيع تجريبي من النظام المتكامل',
//       );

//       if (saleResult['success']) {
//         AppUtils.logInfo('✅ تم إنشاء عملية البيع: ${saleResult['sale_id']}');
//       } else {
//         AppUtils.logInfo(
//             'ℹ️ لم يتم إنشاء البيع (قد يكون بسبب عدم وجود المنتج التجريبي)');
//       }

//       // مثال 2: عملية شراء سريعة
//       AppUtils.logInfo('تنفيذ عملية شراء سريعة...');
//       final purchaseResult =
//           await businessService.createSmartPurchaseTransaction(
//         items: [
//           {
//             'productId': 'demo-product-1',
//             'qty': 5.0,
//             'unitPrice': 80.0,
//           },
//         ],
//         paidAmount: 400.0,
//         notes: 'شراء تجريبي من النظام المتكامل',
//       );

//       if (purchaseResult['success']) {
//         AppUtils.logInfo(
//             '✅ تم إنشاء عملية الشراء: ${purchaseResult['purchase_id']}');
//       } else {
//         AppUtils.logInfo(
//             'ℹ️ لم يتم إنشاء الشراء (قد يكون بسبب عدم وجود المنتج التجريبي)');
//       }

//       AppUtils.logInfo('=== انتهاء مثال العمليات التجارية ===');
//     } catch (e) {
//       AppUtils.logError('خطأ في تشغيل مثال العمليات التجارية', e);
//     }
//   }

//   /// الحصول على حالة النظام
//   static Future<void> checkSystemStatus() async {
//     try {
//       final SystemInitializationService initService =
//           SystemInitializationService();
//       final status = await initService.getSystemStatus();

//       AppUtils.logInfo('=== حالة النظام ===');
//       AppUtils.logInfo('مهيأ: ${status['initialized']}');
//       AppUtils.logInfo('الوقت: ${status['timestamp']}');

//       if (status['initialized']) {
//         AppUtils.logInfo(
//             'عدد الكيانات الافتراضية: ${status['default_entities_count']}');
//         AppUtils.logInfo(
//             'خدمة العمليات جاهزة: ${status['business_service_ready']}');
//       }

//       if (status.containsKey('error')) {
//         AppUtils.logError('خطأ في حالة النظام: ${status['error']}', null);
//       }
//     } catch (e) {
//       AppUtils.logError('خطأ في فحص حالة النظام', e);
//     }
//   }
// }
