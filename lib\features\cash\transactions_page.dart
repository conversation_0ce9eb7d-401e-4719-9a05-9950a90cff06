import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tijari_tech/core/utils/formatters.dart';
import 'package:tijari_tech/shared/widgets/empty_state_widget.dart';
import 'package:tijari_tech/shared/widgets/loading_widget.dart';
import 'package:tijari_tech/widgets/error_widget.dart';

import '../../data/models/transaction.dart';
import '../../providers/transaction_provider.dart';
import '../../core/utils/app_utils.dart';

class TransactionsPage extends ConsumerStatefulWidget {
  const TransactionsPage({super.key});

  @override
  ConsumerState<TransactionsPage> createState() => _TransactionsPageState();
}

class _TransactionsPageState extends ConsumerState<TransactionsPage> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedType = 'all';
  String _selectedStatus = 'all';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final transactionsAsync = ref.watch(transactionsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('سجل المعاملات'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(),
            tooltip: 'تصفية',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filters
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Search bar
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'البحث في المعاملات...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {});
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onChanged: (value) => setState(() {}),
                ),
                
                const SizedBox(height: 12),
                
                // Filter chips
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip('الكل', 'all', _selectedType, (value) {
                        setState(() => _selectedType = value);
                      }),
                      _buildFilterChip('قبض', 'receipt', _selectedType, (value) {
                        setState(() => _selectedType = value);
                      }),
                      _buildFilterChip('صرف', 'payment', _selectedType, (value) {
                        setState(() => _selectedType = value);
                      }),
                      _buildFilterChip('مبيعات', 'sale', _selectedType, (value) {
                        setState(() => _selectedType = value);
                      }),
                      _buildFilterChip('مشتريات', 'purchase', _selectedType, (value) {
                        setState(() => _selectedType = value);
                      }),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Transactions list
          Expanded(
            child: _buildTransactionsList(transactionsAsync),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value, String selectedValue, Function(String) onSelected) {
    final isSelected = selectedValue == value;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) => onSelected(value),
        selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
      ),
    );
  }

  Widget _buildTransactionsList(AsyncValue<List<Transactions>> transactionsAsync) {
    return transactionsAsync.when(
      data: (transactions) {
        final filteredTransactions = _filterTransactions(transactions);

        if (filteredTransactions.isEmpty) {
          return EmptyStateWidget(
            icon: Icons.receipt_long,
            title: 'لا توجد معاملات',
            subtitle: _searchController.text.isNotEmpty 
                ? 'لم يتم العثور على معاملات تطابق البحث'
                : 'لم يتم تسجيل أي معاملات بعد',
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(transactionsProvider);
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredTransactions.length,
            itemBuilder: (context, index) {
              final transaction = filteredTransactions[index];
              return _buildTransactionCard(transaction);
            },
          ),
        );
      },
      loading: () => const LoadingWidget(),
      error: (error, stackTrace) => CustomErrorWidget(
        message: error.toString(),
        onRetry: () => ref.invalidate(transactionsProvider),
      ),
    );
  }

  List<Transactions> _filterTransactions(List<Transactions> transactions) {
    var filtered = transactions;

    // Filter by search query
    if (_searchController.text.isNotEmpty) {
      final query = _searchController.text.toLowerCase();
      filtered = filtered.where((transaction) {
        return transaction.referenceNumber.toLowerCase().contains(query) ||
               (transaction.description?.toLowerCase().contains(query) ?? false) ||
               transaction.amount.toString().contains(query);
      }).toList();
    }

    // Filter by type
    if (_selectedType != 'all') {
      filtered = filtered.where((transaction) => transaction.type == _selectedType).toList();
    }

    // Filter by status
    if (_selectedStatus != 'all') {
      filtered = filtered.where((transaction) => transaction.status == _selectedStatus).toList();
    }

    // Sort by date (newest first)
    filtered.sort((a, b) => b.transactionDate.compareTo(a.transactionDate));

    return filtered;
  }

  Widget _buildTransactionCard(Transactions transaction) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getTransactionColor(transaction),
          child: Icon(
            _getTransactionIcon(transaction),
            color: Colors.white,
          ),
        ),
        title: Text(
          transaction.referenceNumber,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(_getTransactionTypeText(transaction.type)),
            Text(AppFormatters.formatDate(transaction.transactionDate)),
            if (transaction.description != null)
              Text(
                transaction.description!,
                style: const TextStyle(fontSize: 12),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${transaction.amount.toStringAsFixed(2)} ر.س',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _getAmountColor(transaction),
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: _getStatusColor(transaction.status),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                _getStatusText(transaction.status),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                ),
              ),
            ),
          ],
        ),
        onTap: () => _showTransactionDetails(transaction),
      ),
    );
  }

  Color _getTransactionColor(Transactions transaction) {
    switch (transaction.type) {
      case 'receipt':
        return Colors.green;
      case 'payment':
        return Colors.red;
      case 'sale':
        return Colors.blue;
      case 'purchase':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getTransactionIcon(Transactions transaction) {
    switch (transaction.type) {
      case 'receipt':
        return Icons.arrow_downward;
      case 'payment':
        return Icons.arrow_upward;
      case 'sale':
        return Icons.shopping_cart;
      case 'purchase':
        return Icons.shopping_bag;
      default:
        return Icons.receipt;
    }
  }

  String _getTransactionTypeText(String type) {
    switch (type) {
      case 'receipt':
        return 'إيصال قبض';
      case 'payment':
        return 'إيصال صرف';
      case 'sale':
        return 'مبيعات';
      case 'purchase':
        return 'مشتريات';
      case 'adjustment':
        return 'تسوية';
      default:
        return type;
    }
  }

  Color _getAmountColor(Transactions transaction) {
    switch (transaction.type) {
      case 'receipt':
      case 'sale':
        return Colors.green;
      case 'payment':
      case 'purchase':
        return Colors.red;
      default:
        return Colors.black;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'completed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'completed':
        return 'مكتمل';
      case 'pending':
        return 'معلق';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }

  void _showTransactionDetails(Transactions transaction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل المعاملة'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('رقم المرجع', transaction.referenceNumber),
              _buildDetailRow('النوع', _getTransactionTypeText(transaction.type)),
              _buildDetailRow('المبلغ', '${transaction.amount.toStringAsFixed(2)} ر.س'),
              _buildDetailRow('التاريخ', AppFormatters.formatDate(transaction.transactionDate)),
              _buildDetailRow('الحالة', _getStatusText(transaction.status)),
              _buildDetailRow('طريقة الدفع', transaction.paymentMethod),
              if (transaction.description != null)
                _buildDetailRow('الوصف', transaction.description!),
              if (transaction.notes != null)
                _buildDetailRow('ملاحظات', transaction.notes!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصفية المعاملات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Status filter
            DropdownButtonFormField<String>(
              value: _selectedStatus,
              decoration: const InputDecoration(
                labelText: 'الحالة',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                DropdownMenuItem(value: 'completed', child: Text('مكتمل')),
                DropdownMenuItem(value: 'pending', child: Text('معلق')),
                DropdownMenuItem(value: 'cancelled', child: Text('ملغي')),
              ],
              onChanged: (value) {
                setState(() => _selectedStatus = value!);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {});
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }
}
