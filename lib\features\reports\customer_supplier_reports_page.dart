import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/formatters.dart';
import '../../widgets/custom_card.dart';
import '../../data/repositories/customer_repository.dart';
import '../../data/repositories/supplier_repository.dart';
import '../../data/repositories/sale_repository.dart';
import '../../data/repositories/purchase_repository.dart';
import '../../services/financial_reports_export.dart';

/// صفحة تقارير حسابات العملاء والموردين
class CustomerSupplierReportsPage extends ConsumerStatefulWidget {
  const CustomerSupplierReportsPage({super.key});

  @override
  ConsumerState<CustomerSupplierReportsPage> createState() =>
      _CustomerSupplierReportsPageState();
}

class _CustomerSupplierReportsPageState
    extends ConsumerState<CustomerSupplierReportsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;

  // بيانات التقارير
  Map<String, dynamic> _accountsSummary = {};
  List<Map<String, dynamic>> _customersData = [];
  List<Map<String, dynamic>> _suppliersData = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadAccountsData();
  }

  Future<void> _loadAccountsData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل بيانات العملاء
      await _loadCustomersData();

      // تحميل بيانات الموردين
      await _loadSuppliersData();

      // حساب الملخص
      _calculateAccountsSummary();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل البيانات: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _loadCustomersData() async {
    try {
      final saleRepository = SaleRepository();

      // الحصول على جميع المبيعات
      final allSales = await saleRepository.getAllSales();

      // تجميع البيانات حسب العميل
      final Map<String, Map<String, dynamic>> customerSummary = {};

      for (final sale in allSales) {
        final customerId = sale['customer_id'] as String?;
        final customerName = sale['customer_name'] as String? ?? 'عميل نقدي';

        if (customerId != null) {
          if (!customerSummary.containsKey(customerId)) {
            customerSummary[customerId] = {
              'id': customerId,
              'name': customerName,
              'totalSales': 0.0,
              'totalPaid': 0.0,
              'totalDue': 0.0,
              'salesCount': 0,
            };
          }

          final total = (sale['total'] as num?)?.toDouble() ?? 0.0;
          final paid = (sale['paid'] as num?)?.toDouble() ?? 0.0;
          final due = (sale['due'] as num?)?.toDouble() ?? 0.0;

          customerSummary[customerId]!['totalSales'] += total;
          customerSummary[customerId]!['totalPaid'] += paid;
          customerSummary[customerId]!['totalDue'] += due;
          customerSummary[customerId]!['salesCount']++;
        }
      }

      _customersData = customerSummary.values.toList();
    } catch (e) {
      print('خطأ في تحميل بيانات العملاء: $e');
      _customersData = [];
    }
  }

  Future<void> _loadSuppliersData() async {
    try {
      final purchaseRepository = PurchaseRepository();

      // الحصول على جميع المشتريات
      final allPurchases = await purchaseRepository.getAllPurchases();

      // تجميع البيانات حسب المورد
      final Map<String, Map<String, dynamic>> supplierSummary = {};

      for (final purchase in allPurchases) {
        final supplierId = purchase['supplier_id'] as String?;
        final supplierName =
            purchase['supplier_name'] as String? ?? 'مورد نقدي';

        if (supplierId != null) {
          if (!supplierSummary.containsKey(supplierId)) {
            supplierSummary[supplierId] = {
              'id': supplierId,
              'name': supplierName,
              'totalPurchases': 0.0,
              'totalPaid': 0.0,
              'totalDue': 0.0,
              'purchasesCount': 0,
            };
          }

          final total = (purchase['total_amount'] as num?)?.toDouble() ?? 0.0;
          final paid = (purchase['paid_amount'] as num?)?.toDouble() ?? 0.0;
          final due = (purchase['due_amount'] as num?)?.toDouble() ?? 0.0;

          supplierSummary[supplierId]!['totalPurchases'] += total;
          supplierSummary[supplierId]!['totalPaid'] += paid;
          supplierSummary[supplierId]!['totalDue'] += due;
          supplierSummary[supplierId]!['purchasesCount']++;
        }
      }

      _suppliersData = supplierSummary.values.toList();
    } catch (e) {
      print('خطأ في تحميل بيانات الموردين: $e');
      _suppliersData = [];
    }
  }

  void _calculateAccountsSummary() {
    // حساب إحصائيات العملاء
    final totalCustomers = _customersData.length;
    final activeCustomers =
        _customersData.where((c) => (c['salesCount'] as int) > 0).length;
    final totalCustomerSales = _customersData.fold<double>(
        0, (sum, c) => sum + (c['totalSales'] as double));
    final totalCustomerDue = _customersData.fold<double>(
        0, (sum, c) => sum + (c['totalDue'] as double));

    // حساب إحصائيات الموردين
    final totalSuppliers = _suppliersData.length;
    final activeSuppliers =
        _suppliersData.where((s) => (s['purchasesCount'] as int) > 0).length;
    final totalSupplierPurchases = _suppliersData.fold<double>(
        0, (sum, s) => sum + (s['totalPurchases'] as double));
    final totalSupplierDue = _suppliersData.fold<double>(
        0, (sum, s) => sum + (s['totalDue'] as double));

    _accountsSummary = {
      'totalCustomers': totalCustomers,
      'activeCustomers': activeCustomers,
      'totalCustomerSales': totalCustomerSales,
      'totalCustomerDue': totalCustomerDue,
      'totalSuppliers': totalSuppliers,
      'activeSuppliers': activeSuppliers,
      'totalSupplierPurchases': totalSupplierPurchases,
      'totalSupplierDue': totalSupplierDue,
    };
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقارير حسابات العملاء والموردين'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportToExcel,
            tooltip: 'تصدير إلى Excel',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.onPrimary,
          unselectedLabelColor: AppColors.onPrimary.withValues(alpha: 0.7),
          indicatorColor: AppColors.onPrimary,
          tabs: const [
            Tab(text: 'العملاء', icon: Icon(Icons.people)),
            Tab(text: 'الموردين', icon: Icon(Icons.business)),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildSummarySection(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _buildCustomersTab(),
                      _buildSuppliersTab(),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return CustomCard(
      margin: EdgeInsets.all(16.r),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الحسابات',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 4,
              crossAxisSpacing: 8.w,
              mainAxisSpacing: 8.h,
              childAspectRatio: 0.5,
              children: [
                _buildSummaryCard(
                  'إجمالي العملاء',
                  '${_accountsSummary['totalCustomers']}',
                  Icons.people,
                  AppColors.primary,
                ),
                _buildSummaryCard(
                  'العملاء النشطين',
                  '${_accountsSummary['activeCustomers']}',
                  Icons.person,
                  AppColors.success,
                ),
                _buildSummaryCard(
                  'إجمالي الموردين',
                  '${_accountsSummary['totalSuppliers']}',
                  Icons.business,
                  AppColors.info,
                ),
                _buildSummaryCard(
                  'الموردين النشطين',
                  '${_accountsSummary['activeSuppliers']}',
                  Icons.business_center,
                  AppColors.warning,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(8.r),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24.r),
          SizedBox(height: 4.h),
          Text(
            value,
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildCustomersTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        children: [
          _buildCustomersSummary(),
          SizedBox(height: 16.h),
          _buildCustomersList(),
        ],
      ),
    );
  }

  Widget _buildSuppliersTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        children: [
          _buildSuppliersSummary(),
          SizedBox(height: 16.h),
          _buildSuppliersList(),
        ],
      ),
    );
  }

  Widget _buildCustomersSummary() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص العملاء',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: _buildFinancialCard(
                    'إجمالي المبيعات',
                    AppFormatters.formatCurrency(
                        _accountsSummary['totalCustomerSales']),
                    Icons.trending_up,
                    AppColors.success,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildFinancialCard(
                    'المبالغ المستحقة',
                    AppFormatters.formatCurrency(
                        _accountsSummary['totalCustomerDue']),
                    Icons.account_balance,
                    AppColors.warning,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSuppliersSummary() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الموردين',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: _buildFinancialCard(
                    'إجمالي المشتريات',
                    AppFormatters.formatCurrency(
                        _accountsSummary['totalSupplierPurchases']),
                    Icons.shopping_cart,
                    AppColors.error,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildFinancialCard(
                    'المبالغ المستحقة',
                    AppFormatters.formatCurrency(
                        _accountsSummary['totalSupplierDue']),
                    Icons.account_balance,
                    AppColors.warning,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32.r),
          SizedBox(height: 8.h),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 4.h),
          Text(
            value,
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCustomersList() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(16.r),
            child: Text(
              'تفاصيل العملاء',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const Divider(height: 1),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _customersData.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final customer = _customersData[index];
              return _buildCustomerItem(customer);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSuppliersList() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(16.r),
            child: Text(
              'تفاصيل الموردين',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const Divider(height: 1),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _suppliersData.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final supplier = _suppliersData[index];
              return _buildSupplierItem(supplier);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerItem(Map<String, dynamic> customer) {
    final name = customer['name'] as String;
    final totalSales = (customer['totalSales'] as num).toDouble();
    final totalDue = (customer['totalDue'] as num).toDouble();
    final salesCount = customer['salesCount'] as int;

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: AppColors.primary.withValues(alpha: 0.1),
        child: Icon(
          Icons.person,
          color: AppColors.primary,
          size: 20.r,
        ),
      ),
      title: Text(
        name,
        style: AppTextStyles.titleSmall.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'عدد المبيعات: $salesCount',
            style: AppTextStyles.bodySmall,
          ),
          Text(
            'إجمالي المبيعات: ${AppFormatters.formatCurrency(totalSales)}',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.success,
            ),
          ),
        ],
      ),
      trailing: totalDue > 0
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'مستحق',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.error,
                  ),
                ),
                Text(
                  AppFormatters.formatCurrency(totalDue),
                  style: AppTextStyles.titleSmall.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.error,
                  ),
                ),
              ],
            )
          : Icon(
              Icons.check_circle,
              color: AppColors.success,
              size: 20.r,
            ),
    );
  }

  Widget _buildSupplierItem(Map<String, dynamic> supplier) {
    final name = supplier['name'] as String;
    final totalPurchases = (supplier['totalPurchases'] as num).toDouble();
    final totalDue = (supplier['totalDue'] as num).toDouble();
    final purchasesCount = supplier['purchasesCount'] as int;

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: AppColors.info.withValues(alpha: 0.1),
        child: Icon(
          Icons.business,
          color: AppColors.info,
          size: 20.r,
        ),
      ),
      title: Text(
        name,
        style: AppTextStyles.titleSmall.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'عدد المشتريات: $purchasesCount',
            style: AppTextStyles.bodySmall,
          ),
          Text(
            'إجمالي المشتريات: ${AppFormatters.formatCurrency(totalPurchases)}',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.error,
            ),
          ),
        ],
      ),
      trailing: totalDue > 0
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'مستحق',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.warning,
                  ),
                ),
                Text(
                  AppFormatters.formatCurrency(totalDue),
                  style: AppTextStyles.titleSmall.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.warning,
                  ),
                ),
              ],
            )
          : Icon(
              Icons.check_circle,
              color: AppColors.success,
              size: 20.r,
            ),
    );
  }

  /// تصدير التقرير إلى Excel
  Future<void> _exportToExcel() async {
    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // تصدير البيانات
      final filePath = await FinancialReportsExport.exportAccountsToExcel(
        customers: _customersData,
        suppliers: _suppliersData,
        accountsSummary: _accountsSummary,
      );

      // التحقق من أن الويدجت ما زال موجوداً
      if (!mounted) return;

      // إخفاء مؤشر التحميل
      Navigator.of(context).pop();

      // إظهار رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تصدير التقرير بنجاح إلى: $filePath'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      // التحقق من أن الويدجت ما زال موجوداً
      if (!mounted) return;

      // إخفاء مؤشر التحميل
      Navigator.of(context).pop();

      // إظهار رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء التصدير: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }
}
