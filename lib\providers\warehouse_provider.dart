import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/warehouse.dart';
import '../data/repositories/warehouse_repository.dart';
import '../core/utils/app_utils.dart';
import '../core/utils/error_handler.dart';
import 'database_provider.dart';

// Warehouse repository provider
final warehouseRepositoryProvider = Provider<WarehouseRepository>((ref) {
  return WarehouseRepository();
});

// Warehouse management provider
final warehouseManagementProvider = StateNotifierProvider<
    WarehouseManagementNotifier, WarehouseManagementState>((ref) {
  return WarehouseManagementNotifier(ref);
});

// Warehouse management state
class WarehouseManagementState {
  final bool isLoading;
  final String? error;
  final List<Warehouse> warehouses;
  final List<Warehouse> filteredWarehouses;
  final String searchQuery;
  final String? selectedBranch;
  final String? selectedManager;
  final bool? isActiveFilter;
  final String? warehouseTypeFilter;
  final Warehouse? selectedWarehouse;
  final Map<String, dynamic> statistics;
  final String sortField;
  final bool sortAscending;

  const WarehouseManagementState({
    this.isLoading = false,
    this.error,
    this.warehouses = const [],
    this.filteredWarehouses = const [],
    this.searchQuery = '',
    this.selectedBranch,
    this.selectedManager,
    this.isActiveFilter,
    this.warehouseTypeFilter,
    this.selectedWarehouse,
    this.statistics = const {},
    this.sortField = 'name',
    this.sortAscending = true,
  });

  WarehouseManagementState copyWith({
    bool? isLoading,
    String? error,
    List<Warehouse>? warehouses,
    List<Warehouse>? filteredWarehouses,
    String? searchQuery,
    String? selectedBranch,
    String? selectedManager,
    bool? isActiveFilter,
    String? warehouseTypeFilter,
    Warehouse? selectedWarehouse,
    Map<String, dynamic>? statistics,
    String? sortField,
    bool? sortAscending,
  }) {
    return WarehouseManagementState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      warehouses: warehouses ?? this.warehouses,
      filteredWarehouses: filteredWarehouses ?? this.filteredWarehouses,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedBranch: selectedBranch ?? this.selectedBranch,
      selectedManager: selectedManager ?? this.selectedManager,
      isActiveFilter: isActiveFilter ?? this.isActiveFilter,
      warehouseTypeFilter: warehouseTypeFilter ?? this.warehouseTypeFilter,
      selectedWarehouse: selectedWarehouse ?? this.selectedWarehouse,
      statistics: statistics ?? this.statistics,
      sortField: sortField ?? this.sortField,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }
}

// Warehouse management notifier
class WarehouseManagementNotifier
    extends StateNotifier<WarehouseManagementState> {
  final Ref _ref;

  WarehouseManagementNotifier(this._ref)
      : super(const WarehouseManagementState());

  WarehouseRepository get _repository => _ref.read(warehouseRepositoryProvider);
  DatabaseOperations get _dbOperations => _ref.read(databaseOperationsProvider);

  // Load warehouses
  Future<void> loadWarehouses() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final warehouses = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getWarehousesWithDetails();
      });

      final statistics = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getWarehousesStatistics();
      });

      state = state.copyWith(
        isLoading: false,
        warehouses: warehouses,
        filteredWarehouses: _filterAndSortWarehouses(warehouses),
        statistics: statistics,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error loading warehouses', e);
    }
  }

  // Filter and sort warehouses based on current state
  List<Warehouse> _filterAndSortWarehouses(List<Warehouse> warehouses) {
    var filtered = warehouses.where((warehouse) {
      // Search filter
      if (state.searchQuery.isNotEmpty) {
        final query = state.searchQuery.toLowerCase();
        if (!warehouse.name.toLowerCase().contains(query) &&
            !(warehouse.location?.toLowerCase().contains(query) ?? false) &&
            !(warehouse.address?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // Branch filter
      if (state.selectedBranch != null &&
          warehouse.branchId != state.selectedBranch) {
        return false;
      }

      // Manager filter
      if (state.selectedManager != null &&
          warehouse.managerId != state.selectedManager) {
        return false;
      }

      // Active filter
      if (state.isActiveFilter != null &&
          warehouse.isActive != state.isActiveFilter) {
        return false;
      }

      // Warehouse type filter
      if (state.warehouseTypeFilter != null &&
          warehouse.warehouseType != state.warehouseTypeFilter) {
        return false;
      }

      return true;
    }).toList();

    // Sort
    filtered.sort((a, b) {
      int comparison = 0;
      switch (state.sortField) {
        case 'name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'location':
          comparison = (a.location ?? '').compareTo(b.location ?? '');
          break;
        case 'type':
          comparison = (a.warehouseType ?? '').compareTo(b.warehouseType ?? '');
          break;
        case 'capacity':
          comparison = (a.capacity ?? 0).compareTo(b.capacity ?? 0);
          break;
        case 'products':
          comparison = (a.productCount ?? 0).compareTo(b.productCount ?? 0);
          break;
        case 'value':
          comparison = (a.totalValue ?? 0).compareTo(b.totalValue ?? 0);
          break;
        default:
          comparison = a.name.compareTo(b.name);
      }
      return state.sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  // Refresh data
  Future<void> refresh() async {
    await loadWarehouses();
  }

  // Search warehouses
  void searchWarehouses(String query) {
    state = state.copyWith(
      searchQuery: query,
      filteredWarehouses: _filterAndSortWarehouses(state.warehouses),
    );
  }

  // Filter by branch
  void filterByBranch(String? branchId) {
    state = state.copyWith(
      selectedBranch: branchId,
      filteredWarehouses: _filterAndSortWarehouses(state.warehouses),
    );
  }

  // Filter by manager
  void filterByManager(String? managerId) {
    state = state.copyWith(
      selectedManager: managerId,
      filteredWarehouses: _filterAndSortWarehouses(state.warehouses),
    );
  }

  // Filter by active status
  void filterByActiveStatus(bool? isActive) {
    state = state.copyWith(
      isActiveFilter: isActive,
      filteredWarehouses: _filterAndSortWarehouses(state.warehouses),
    );
  }

  // Filter by warehouse type
  void filterByWarehouseType(String? warehouseType) {
    state = state.copyWith(
      warehouseTypeFilter: warehouseType,
      filteredWarehouses: _filterAndSortWarehouses(state.warehouses),
    );
  }

  // Sort warehouses
  void sortWarehouses(String field, bool ascending) {
    state = state.copyWith(
      sortField: field,
      sortAscending: ascending,
      filteredWarehouses: _filterAndSortWarehouses(state.warehouses),
    );
  }

  // Clear filters
  void clearFilters() {
    state = state.copyWith(
      searchQuery: '',
      selectedBranch: null,
      selectedManager: null,
      isActiveFilter: null,
      warehouseTypeFilter: null,
      filteredWarehouses: _filterAndSortWarehouses(state.warehouses),
    );
  }

  // Select warehouse
  void selectWarehouse(Warehouse? warehouse) {
    state = state.copyWith(selectedWarehouse: warehouse);
  }

  // Create warehouse
  Future<void> createWarehouse({
    required String name,
    String? location,
    String? address,
    String? managerId,
    String? phone,
    String? email,
    bool isActive = true,
    bool isDefault = false,
    String? description,
    String? notes,
    double? capacity,
    String warehouseType = 'main',
    String? branchId,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final warehouse = Warehouse.create(
        id: AppUtils.generateId(),
        name: name,
        location: location,
        address: address,
        managerId: managerId,
        phone: phone,
        email: email,
        isActive: isActive,
        isDefault: isDefault,
        description: description,
        notes: notes,
        capacity: capacity,
        warehouseType: warehouseType,
        branchId: branchId,
      );

      await _dbOperations.executeWithReadyCheck(() async {
        await _repository.createWarehouse(warehouse);
      });

      await loadWarehouses();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error creating warehouse', e);
    }
  }

  // Update warehouse
  Future<void> updateWarehouse(
    String id, {
    String? name,
    String? location,
    String? address,
    String? managerId,
    String? phone,
    String? email,
    bool? isActive,
    bool? isDefault,
    String? description,
    String? notes,
    double? capacity,
    String? warehouseType,
    String? branchId,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final existingWarehouse = await _repository.getWarehouseById(id);
      if (existingWarehouse == null) {
        throw Exception('Warehouse not found');
      }

      final updatedWarehouse = existingWarehouse.update(
        name: name,
        location: location,
        address: address,
        managerId: managerId,
        phone: phone,
        email: email,
        isActive: isActive,
        isDefault: isDefault,
        description: description,
        notes: notes,
        capacity: capacity,
        warehouseType: warehouseType,
        branchId: branchId,
      );

      await _repository.updateWarehouse(id, updatedWarehouse);
      await loadWarehouses();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error updating warehouse', e);
    }
  }

  // Delete warehouse
  Future<void> deleteWarehouse(String id) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _dbOperations.executeWithReadyCheck(() async {
        await _repository.deleteWarehouse(id);
      });

      await loadWarehouses();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error deleting warehouse', e);
    }
  }

  // Set warehouse as default
  Future<void> setAsDefault(String warehouseId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _dbOperations.executeWithReadyCheck(() async {
        await _repository.setAsDefault(warehouseId);
      });

      await loadWarehouses();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error setting warehouse as default', e);
    }
  }
}

// Additional providers
final warehouseByIdProvider =
    FutureProvider.family<Warehouse?, String>((ref, id) async {
  final repository = ref.read(warehouseRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getWarehouseById(id);
  });
});

final activeWarehousesProvider = FutureProvider<List<Warehouse>>((ref) async {
  final repository = ref.read(warehouseRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getActiveWarehouses();
  });
});

final defaultWarehouseProvider = FutureProvider<Warehouse?>((ref) async {
  final repository = ref.read(warehouseRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getDefaultWarehouse();
  });
});

final warehousesStatisticsProvider =
    FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.read(warehouseRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getWarehousesStatistics();
  });
});
