import 'dart:io';
import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:tijari_tech/data/models/product.dart';

/// خدمة التعامل مع ملفات Excel
class ExcelService {
  /// تصدير المنتجات إلى Excel
  static Future<void> exportProductsToExcel(List<Product> products) async {
    try {
      // إنشاء ملف Excel جديد
      final excel = Excel.createExcel();
      final sheet = excel['المنتجات'];

      // إضافة رؤوس الأعمدة
      final headers = [
        'رقم المنتج',
        'الباركود',
        'اسم المنتج',
        'سعر الشراء',
        'سعر البيع',
        'الكمية الحالية',
        'الحد الأدنى',
        'الحد الأقصى',
        'الفئة',
        'الوصف',
        'تاريخ الإنشاء',
      ];

      for (int i = 0; i < headers.length; i++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
        cell.value = TextCellValue(headers[i]);
        cell.cellStyle = CellStyle(
          bold: true,
          backgroundColorHex: ExcelColor.blue,
          fontColorHex: ExcelColor.white,
        );
      }

      // إضافة بيانات المنتجات
      for (int i = 0; i < products.length; i++) {
        final product = products[i];
        final rowIndex = i + 1;

        final data = [
          product.id,
          product.barcode ?? '',
          product.getDisplayName(),
          product.costPrice?.toString() ?? '0',
          product.sellingPrice.toString(),
          product.currentStock?.toString() ?? '0',
          product.minStock.toString(),
          product.maxStock.toString(),
          product.categoryId ?? '',
          product.description ?? '',
          product.createdAt.toString().split(' ')[0], // تاريخ فقط
        ];

        for (int j = 0; j < data.length; j++) {
          final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: rowIndex));
          cell.value = TextCellValue(data[j]);
        }
      }

      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'products_${DateTime.now().millisecondsSinceEpoch}.xlsx';
      final filePath = '${directory.path}/$fileName';
      
      final file = File(filePath);
      await file.writeAsBytes(excel.encode()!);

      // مشاركة الملف
      await Share.shareXFiles(
        [XFile(filePath)],
        text: 'تقرير المنتجات',
        subject: 'تصدير المنتجات - ${DateTime.now().toString().split(' ')[0]}',
      );

    } catch (e) {
      throw Exception('فشل في تصدير البيانات: ${e.toString()}');
    }
  }

  /// استيراد المنتجات من Excel
  static Future<List<Map<String, dynamic>>> importProductsFromExcel() async {
    try {
      // اختيار الملف
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) {
        throw Exception('لم يتم اختيار ملف');
      }

      final file = File(result.files.first.path!);
      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);

      // قراءة البيانات من الورقة الأولى
      final sheet = excel.tables.values.first;
      final products = <Map<String, dynamic>>[];

      // تخطي الصف الأول (الرؤوس) والبدء من الصف الثاني
      for (int i = 1; i < sheet.maxRows; i++) {
        final row = sheet.rows[i];
        
        // التأكد من وجود بيانات في الصف
        if (row.isEmpty || row.every((cell) => cell?.value == null)) {
          continue;
        }

        // استخراج البيانات حسب الترتيب المحدد
        final productData = <String, dynamic>{};
        
        // رقم المنتج (العمود الأول)
        if (row.length > 0 && row[0]?.value != null) {
          productData['id'] = row[0]!.value.toString();
        }
        
        // الباركود (العمود الثاني)
        if (row.length > 1 && row[1]?.value != null) {
          productData['barcode'] = row[1]!.value.toString();
        }
        
        // اسم المنتج (العمود الثالث)
        if (row.length > 2 && row[2]?.value != null) {
          productData['name'] = row[2]!.value.toString();
        }
        
        // سعر الشراء (العمود الرابع)
        if (row.length > 3 && row[3]?.value != null) {
          final priceStr = row[3]!.value.toString();
          productData['purchasePrice'] = double.tryParse(priceStr) ?? 0.0;
        }
        
        // سعر البيع (العمود الخامس)
        if (row.length > 4 && row[4]?.value != null) {
          final priceStr = row[4]!.value.toString();
          productData['sellingPrice'] = double.tryParse(priceStr) ?? 0.0;
        }
        
        // الكمية (العمود السادس)
        if (row.length > 5 && row[5]?.value != null) {
          final quantityStr = row[5]!.value.toString();
          productData['currentStock'] = double.tryParse(quantityStr) ?? 0.0;
        }
        
        // رقم المجموعة (العمود السابع)
        if (row.length > 6 && row[6]?.value != null) {
          productData['categoryId'] = row[6]!.value.toString();
        }

        // التأكد من وجود البيانات الأساسية
        if (productData.containsKey('name') && productData['name'].toString().isNotEmpty) {
          products.add(productData);
        }
      }

      return products;

    } catch (e) {
      throw Exception('فشل في استيراد البيانات: ${e.toString()}');
    }
  }

  /// إنشاء قالب Excel للاستيراد
  static Future<void> createImportTemplate() async {
    try {
      final excel = Excel.createExcel();
      final sheet = excel['قالب_الاستيراد'];

      // إضافة رؤوس الأعمدة
      final headers = [
        'رقم المنتج',
        'الباركود',
        'اسم المنتج',
        'سعر الشراء',
        'سعر البيع',
        'الكمية',
        'رقم المجموعة',
      ];

      for (int i = 0; i < headers.length; i++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
        cell.value = TextCellValue(headers[i]);
        cell.cellStyle = CellStyle(
          bold: true,
          backgroundColorHex: ExcelColor.green,
          fontColorHex: ExcelColor.white,
        );
      }

      // إضافة بيانات تجريبية
      final sampleData = [
        ['1', '100001', 'خبز طازج وسط رغيف', '4958', '99999', '1', ''],
        ['2', '100011', 'خبز طازج جاهز رغيف', '7097', '88888', '1', ''],
        ['3', '100021', 'خبز طازج وسط شريحة', '16336', '77777', '', ''],
      ];

      for (int i = 0; i < sampleData.length; i++) {
        final rowData = sampleData[i];
        final rowIndex = i + 1;

        for (int j = 0; j < rowData.length; j++) {
          final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: rowIndex));
          cell.value = TextCellValue(rowData[j]);
        }
      }

      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'import_template.xlsx';
      final filePath = '${directory.path}/$fileName';
      
      final file = File(filePath);
      await file.writeAsBytes(excel.encode()!);

      // مشاركة الملف
      await Share.shareXFiles(
        [XFile(filePath)],
        text: 'قالب استيراد المنتجات',
        subject: 'قالب Excel للاستيراد',
      );

    } catch (e) {
      throw Exception('فشل في إنشاء القالب: ${e.toString()}');
    }
  }

  /// التحقق من صحة بيانات الاستيراد
  static List<String> validateImportData(List<Map<String, dynamic>> data) {
    final errors = <String>[];

    for (int i = 0; i < data.length; i++) {
      final product = data[i];
      final rowNumber = i + 2; // +2 لأن الصف الأول رؤوس والفهرس يبدأ من 0

      // التحقق من اسم المنتج
      if (!product.containsKey('name') || 
          product['name'] == null || 
          product['name'].toString().trim().isEmpty) {
        errors.add('الصف $rowNumber: اسم المنتج مطلوب');
      }

      // التحقق من سعر البيع
      if (!product.containsKey('sellingPrice') || 
          product['sellingPrice'] == null || 
          product['sellingPrice'] <= 0) {
        errors.add('الصف $rowNumber: سعر البيع يجب أن يكون أكبر من الصفر');
      }

      // التحقق من الكمية
      if (product.containsKey('currentStock') && 
          product['currentStock'] != null && 
          product['currentStock'] < 0) {
        errors.add('الصف $rowNumber: الكمية لا يمكن أن تكون سالبة');
      }
    }

    return errors;
  }
}
