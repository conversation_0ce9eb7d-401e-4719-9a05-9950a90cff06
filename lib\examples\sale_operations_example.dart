import '../services/business_service.dart';
import '../data/repositories/sale_repository.dart';
import '../data/repositories/product_repository.dart';
import '../providers/sale_provider.dart';
import '../data/models/sale_item.dart';
import '../core/utils/app_utils.dart';
import '../core/exceptions/app_exceptions.dart';

/// مثال شامل لعمليات إدارة المبيعات
/// يوضح كيفية إنشاء وإدارة المبيعات باستخدام BusinessService و SaleProvider
class SaleOperationsExample {
  final BusinessService _businessService = BusinessService();
  final SaleRepository _saleRepository = SaleRepository();
  final ProductRepository _productRepository = ProductRepository();
  // final CustomerRepository _customerRepository = CustomerRepository();

  /// مثال شامل: إنشاء عملية بيع كاملة
  Future<void> createSaleTransactionExample(dynamic _customerRepository) async {
    try {
      AppUtils.logInfo('=== مثال إنشاء عملية بيع كاملة ===');

      // الحصول على المنتجات المتاحة
      final products = await _productRepository.getActiveProducts();
      if (products.isEmpty) {
        AppUtils.logWarning('لا توجد منتجات متاحة للبيع');
        return;
      }

      // الحصول على العملاء
      final customers = await _customerRepository.getActiveCustomers();
      final customerId = customers.isNotEmpty ? customers.first.id : null;

      // إنشاء عناصر البيع
      final saleItems = <Map<String, dynamic>>[];
      
      // بيع أول منتجين
      for (int i = 0; i < 2 && i < products.length; i++) {
        final product = products[i];
        saleItems.add({
          'productId': product.id,
          'qty': 2.0,
          'unitPrice': product.sellingPrice,
          'unitId': product.baseUnitId,
        });
      }

      // إنشاء عملية البيع
      final saleId = await _businessService.createSaleTransaction(
        customerId: customerId,
        items: saleItems,
        paidAmount: saleItems.fold<double>(0.0, (sum, item) => 
          sum + ((item['qty'] as double) * (item['unitPrice'] as double))), // دفع كامل
        notes: 'عملية بيع تجريبية من المثال',
      );

      AppUtils.logInfo('تم إنشاء عملية البيع بنجاح - ID: $saleId');

      // عرض تفاصيل البيع
      final saleDetails = await _saleRepository.getSaleById(saleId);
      if (saleDetails != null) {
        AppUtils.logInfo('تفاصيل البيع:');
        AppUtils.logInfo('رقم الفاتورة: ${saleDetails['invoice_no']}');
        AppUtils.logInfo('إجمالي المبلغ: ${saleDetails['total_amount']}');
        AppUtils.logInfo('المبلغ المدفوع: ${saleDetails['paid_amount']}');
        AppUtils.logInfo('المبلغ المستحق: ${saleDetails['due_amount']}');
      }
    } catch (e) {
      if (e is InsufficientStockException) {
        AppUtils.logWarning('مخزون غير كافي: ${e.message}');
      } else {
        AppUtils.logError('خطأ في مثال إنشاء عملية البيع', e);
      }
    }
  }

  /// مثال: استخدام SaleManagementNotifier
  Future<void> saleManagementExample() async {
    try {
      AppUtils.logInfo('=== مثال إدارة المبيعات باستخدام Provider ===');

      // الحصول على المنتجات
      final products = await _productRepository.getActiveProducts();
      if (products.isEmpty) {
        AppUtils.logWarning('لا توجد منتجات متاحة');
        return;
      }

      // إنشاء عناصر البيع
      final saleItems = <SaleItem>[];
      final product = products.first;
      
      final saleItem = SaleItem.create(
        id: AppUtils.generateId(),
        saleId: '', // سيتم تعيينه عند الحفظ
        productId: product.id,
        unitId: product.baseUnitId ?? '',
        qty: 1.0,
        unitPrice: product.sellingPrice,
      );
      
      saleItems.add(saleItem);

      // محاكاة استخدام SaleManagementNotifier
      // في التطبيق الحقيقي، ستستخدم Provider
      final saleRepository = SaleRepository();
      
      // حساب الإجمالي
      final total = saleItems.fold<double>(0.0, (sum, item) => sum + item.total);
      
      // إنشاء رقم الفاتورة
      final invoiceNo = await saleRepository.generateInvoiceNumber();
      
      AppUtils.logInfo('رقم الفاتورة المولد: $invoiceNo');
      AppUtils.logInfo('إجمالي المبلغ: $total');
      AppUtils.logInfo('عدد العناصر: ${saleItems.length}');
    } catch (e) {
      AppUtils.logError('خطأ في مثال إدارة المبيعات', e);
    }
  }

  /// مثال: تحديث دفعة في البيع
  Future<void> updateSalePaymentExample() async {
    try {
      AppUtils.logInfo('=== مثال تحديث دفعة في البيع ===');

      // الحصول على المبيعات المعلقة
      final pendingPayments = await _saleRepository.getPendingPayments();
      if (pendingPayments.isEmpty) {
        AppUtils.logWarning('لا توجد مبيعات معلقة الدفع');
        return;
      }

      final sale = pendingPayments.first;
      final saleId = sale['id'] as String;
      final dueAmount = (sale['due_amount'] as num).toDouble();
      
      AppUtils.logInfo('البيع المحدد: ${sale['invoice_no']}');
      AppUtils.logInfo('المبلغ المستحق: $dueAmount');

      // دفع جزئي (50% من المبلغ المستحق)
      final paymentAmount = dueAmount * 0.5;
      
      final success = await _saleRepository.updatePayment(saleId, paymentAmount);
      
      if (success) {
        AppUtils.logInfo('تم تحديث الدفعة بنجاح');
        AppUtils.logInfo('المبلغ المدفوع: $paymentAmount');
        
        // عرض التفاصيل المحدثة
        final updatedSale = await _saleRepository.getSaleById(saleId);
        if (updatedSale != null) {
          AppUtils.logInfo('المبلغ المدفوع الإجمالي: ${updatedSale['paid_amount']}');
          AppUtils.logInfo('المبلغ المستحق الجديد: ${updatedSale['due_amount']}');
        }
      } else {
        AppUtils.logError('فشل في تحديث الدفعة', null);
      }
    } catch (e) {
      AppUtils.logError('خطأ في مثال تحديث الدفعة', e);
    }
  }

  /// مثال: إلغاء بيع
  Future<void> cancelSaleExample() async {
    try {
      AppUtils.logInfo('=== مثال إلغاء بيع ===');

      // إنشاء بيع تجريبي للإلغاء
      final products = await _productRepository.getActiveProducts();
      if (products.isEmpty) {
        AppUtils.logWarning('لا توجد منتجات متاحة');
        return;
      }

      // إنشاء بيع تجريبي
      final saleItems = <Map<String, dynamic>>[
        {
          'productId': products.first.id,
          'qty': 1.0,
          'unitPrice': products.first.sellingPrice,
          'unitId': products.first.baseUnitId,
        }
      ];

      final saleId = await _businessService.createSaleTransaction(
        items: saleItems,
        paidAmount: 0, // بيع آجل
        notes: 'بيع تجريبي للإلغاء',
      );

      AppUtils.logInfo('تم إنشاء بيع تجريبي للإلغاء - ID: $saleId');

      // إلغاء البيع
      const cancellationReason = 'إلغاء تجريبي من المثال';
      final success = await _saleRepository.cancelSale(saleId, cancellationReason);

      if (success) {
        AppUtils.logInfo('تم إلغاء البيع بنجاح');
        AppUtils.logInfo('سبب الإلغاء: $cancellationReason');
      } else {
        AppUtils.logError('فشل في إلغاء البيع', null);
      }
    } catch (e) {
      AppUtils.logError('خطأ في مثال إلغاء البيع', e);
    }
  }

  /// مثال: إحصاءات المبيعات
  Future<void> salesStatisticsExample() async {
    try {
      AppUtils.logInfo('=== مثال إحصاءات المبيعات ===');

      // إحصاءات عامة
      final stats = await _saleRepository.getSalesStatistics();
      
      AppUtils.logInfo('إحصاءات المبيعات:');
      AppUtils.logInfo('إجمالي المبيعات: ${stats['total_sales'] ?? 0}');
      AppUtils.logInfo('عدد الفواتير: ${stats['total_invoices'] ?? 0}');
      AppUtils.logInfo('متوسط قيمة الفاتورة: ${stats['average_invoice_value'] ?? 0}');

      // مبيعات اليوم
      final todaysSales = await _saleRepository.getTodaysSales();
      final todaysTotal = todaysSales.fold<double>(0.0, 
        (sum, sale) => sum + (sale['total_amount'] as num).toDouble());
      
      AppUtils.logInfo('مبيعات اليوم:');
      AppUtils.logInfo('عدد الفواتير: ${todaysSales.length}');
      AppUtils.logInfo('إجمالي المبلغ: $todaysTotal');

      // أفضل العملاء
      final topCustomers = await _saleRepository.getTopCustomers(limit: 5);
      AppUtils.logInfo('أفضل 5 عملاء:');
      for (int i = 0; i < topCustomers.length; i++) {
        final customer = topCustomers[i];
        AppUtils.logInfo('${i + 1}. ${customer['customer_name'] ?? 'عميل غير محدد'} - ${customer['total_purchases']}');
      }

      // المدفوعات المعلقة
      final pendingPayments = await _saleRepository.getPendingPayments();
      final totalDue = pendingPayments.fold<double>(0.0,
        (sum, payment) => sum + (payment['due_amount'] as num).toDouble());
      
      AppUtils.logInfo('المدفوعات المعلقة:');
      AppUtils.logInfo('عدد الفواتير المعلقة: ${pendingPayments.length}');
      AppUtils.logInfo('إجمالي المبلغ المستحق: $totalDue');
    } catch (e) {
      AppUtils.logError('خطأ في مثال إحصاءات المبيعات', e);
    }
  }

  /// تشغيل جميع الأمثلة
  Future<void> runAllExamples() async {
    try {
      AppUtils.logInfo('🚀 بدء تشغيل أمثلة عمليات المبيعات');
      
      await createSaleTransactionExample(_businessService);
      await Future.delayed(const Duration(seconds: 1));
      
      await saleManagementExample();
      await Future.delayed(const Duration(seconds: 1));
      
      await updateSalePaymentExample();
      await Future.delayed(const Duration(seconds: 1));
      
      await cancelSaleExample();
      await Future.delayed(const Duration(seconds: 1));
      
      await salesStatisticsExample();
      
      AppUtils.logInfo('✅ تم الانتهاء من جميع أمثلة عمليات المبيعات');
    } catch (e) {
      AppUtils.logError('خطأ في تشغيل أمثلة عمليات المبيعات', e);
    }
  }
}

/// دالة مساعدة لتشغيل الأمثلة
Future<void> runSaleOperationsExamples() async {
  final example = SaleOperationsExample();
  await example.runAllExamples();
}
