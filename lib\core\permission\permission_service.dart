/// كل الصلاحيات المدعومة
enum Permission {
  inventory,
  inventoryEdit,
  inventoryDelete,
  sales,
  salesEdit,
  salesDelete,
  purchase,
  purchaseEdit,
  purchaseDelete,
  accounts,
  accountsEdit,
  accountsDelete,
  cash,
  cashEdit,
  cashDelete,
  employee,
  employeeEdit,
  employeeDelete,
  reports,
  salesReports,
  purchaseReports,
  inventoryReports,
  financialReports,
  allReports,
  settings,
  pos,
  warehouseManagement,
  stockManagement,
  stockAdjustment,
  stockTransfer,
  priceManagement,
  discountManagement,
  costView,
  backup,
  ai,
}

/// نموذج الصلاحيات
class PermissionsModel {
  final Map<Permission, bool> _map;

  PermissionsModel(Map<Permission, bool> map) : _map = Map.unmodifiable(map);

  bool has(Permission p) => _map[p] ?? false;
}

/// خدمة الصلاحيات
class PermissionService {
  PermissionService._();
  static final _instance = PermissionService._();
  factory PermissionService() => _instance;

  /// يمكن استبدالها لاحقاً بجلب من الخادم
  static PermissionsModel? _permissions;

  /// تحميل الصلاحيات (نادِِها بعد تسجيل الدخول)
  static Future<void> load({String? role}) async {
    // TODO: استبدل بـ API call
    _permissions = _buildPermissionsForRole(role ?? 'admin');
  }

  /// منظور داخلي لبناء الصلاحيات حسب الـ Role
  static PermissionsModel _buildPermissionsForRole(String role) {
    final map = <Permission, bool>{};

    // إعدادات افتراضية لكل دور
    switch (role) {
      case 'admin':
        for (final p in Permission.values) map[p] = true;
        break;
      case 'manager':
        for (final p in Permission.values) map[p] = true;
        map[Permission.backup] = false;
        map[Permission.ai] = false;
        break;
      case 'cashier':
        map[Permission.pos] = true;
        map[Permission.sales] = true;
        map[Permission.salesEdit] = true;
        map[Permission.salesReports] = true;
        map[Permission.inventory] = true;
        map[Permission.inventoryReports] = true;
        break;
      default:
      // موظف عادي
        map[Permission.inventory] = true;
        map[Permission.sales] = true;
        map[Permission.pos] = true;
    }

    return PermissionsModel(map);
  }

  /* -------------------------------------------------- */
  /*  Helpers سريعة تطابق RouteGuard                    */
  /* -------------------------------------------------- */
  static bool hasInventoryAccess() => _has(Permission.inventory);
  static bool hasInventoryEditAccess() => _has(Permission.inventoryEdit);
  static bool hasInventoryDeleteAccess() => _has(Permission.inventoryDelete);
  static bool hasSalesAccess() => _has(Permission.sales);
  static bool hasSalesEditAccess() => _has(Permission.salesEdit);
  static bool hasSalesDeleteAccess() => _has(Permission.salesDelete);
  static bool hasPurchaseAccess() => _has(Permission.purchase);
  static bool hasPurchaseEditAccess() => _has(Permission.purchaseEdit);
  static bool hasPurchaseDeleteAccess() => _has(Permission.purchaseDelete);
  static bool hasAccountsAccess() => _has(Permission.accounts);
  static bool hasAccountsEditAccess() => _has(Permission.accountsEdit);
  static bool hasAccountsDeleteAccess() => _has(Permission.accountsDelete);
  static bool hasCashAccess() => _has(Permission.cash);
  static bool hasCashEditAccess() => _has(Permission.cashEdit);
  static bool hasCashDeleteAccess() => _has(Permission.cashDelete);
  static bool hasEmployeeAccess() => _has(Permission.employee);
  static bool hasEmployeeEditAccess() => _has(Permission.employeeEdit);
  static bool hasEmployeeDeleteAccess() => _has(Permission.employeeDelete);
  static bool hasReportsAccess() => _has(Permission.reports);
  static bool hasSalesReportsAccess() => _has(Permission.salesReports);
  static bool hasPurchaseReportsAccess() => _has(Permission.purchaseReports);
  static bool hasInventoryReportsAccess() => _has(Permission.inventoryReports);
  static bool hasFinancialReportsAccess() => _has(Permission.financialReports);
  static bool hasAllReportsAccess() => _has(Permission.allReports);
  static bool hasAdminAccess() => _has(Permission.settings);
  static bool hasPOSAccess() => _has(Permission.pos);
  static bool hasWarehouseManagementAccess() => _has(Permission.warehouseManagement);
  static bool hasStockManagementAccess() => _has(Permission.stockManagement);
  static bool hasStockAdjustmentAccess() => _has(Permission.stockAdjustment);
  static bool hasStockTransferAccess() => _has(Permission.stockTransfer);
  static bool hasPriceManagementAccess() => _has(Permission.priceManagement);
  static bool hasDiscountManagementAccess() => _has(Permission.discountManagement);
  static bool hasCostViewAccess() => _has(Permission.costView);
  static bool hasBackupAccess() => _has(Permission.backup);
  static bool hasAIAccess() => _has(Permission.ai);

  static bool _has(Permission p) => _permissions?.has(p) ?? false;
}