import '../data/local/dao/chart_of_accounts_dao.dart';
import '../data/local/dao/audit_log_dao.dart';
import '../data/models/chart_of_accounts.dart';
import '../core/utils/app_utils.dart';

/// خدمة إدارة دليل الحسابات - Chart of Accounts Service
/// تدير دليل الحسابات والهيكل الهرمي للحسابات المحاسبية
/// Manages chart of accounts and hierarchical structure of accounting accounts
class ChartOfAccountsService {
  final ChartOfAccountsDao _chartOfAccountsDao;
  final AuditLogDao _auditLogDao;

  ChartOfAccountsService({
    required ChartOfAccountsDao chartOfAccountsDao,
    required AuditLogDao auditLogDao,
  })  : _chartOfAccountsDao = chartOfAccountsDao,
        _auditLogDao = auditLogDao;

  /// إنشاء حساب جديد - Create new account
  /// ينشئ حساب محاسبي جديد مع التحقق من صحة البيانات
  Future<String?> createAccount({
    required String accountCode,
    required String accountName,
    required String accountType,
    String? parentAccountId,
    String? description,
    bool isActive = true,
    String? branchId,
    String? userId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // التحقق من صحة البيانات
      await _validateAccountData(
        accountCode: accountCode,
        accountName: accountName,
        accountType: accountType,
        parentAccountId: parentAccountId,
      );

      // التحقق من عدم تكرار رمز الحساب
      final existingAccount = await _chartOfAccountsDao.getByAccountCode(accountCode);
      if (existingAccount != null) {
        throw Exception('رمز الحساب موجود مسبقاً: $accountCode');
      }

      // إنشاء الحساب الجديد
      final account = ChartOfAccounts.create(
        id: AppUtils.generateId(),
        accountCode: accountCode,
        accountNameAr: accountName,
        accountType: accountType,
        parentId: parentAccountId,
        description: description,
        isActive: isActive,
        branchId: branchId,
        metadata: metadata,
      );
 
      // حفظ الحساب
      final accountId = await _chartOfAccountsDao.insert(account);
      if (accountId == null) {
        throw Exception('فشل في حفظ الحساب الجديد');
      }

      // تسجيل العملية
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: accountId,
        action: 'create',
        description: 'إنشاء حساب محاسبي جديد: $accountName',
        userId: userId ?? 'system',
        category: 'chart_of_accounts',
        severity: 'medium',
        metadata: {
          'account_code': accountCode,
          'account_type': accountType,
          'parent_account_id': parentAccountId,
        },
        branchId: branchId,
      );

      return accountId;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: 'failed',
        action: 'create',
        description: 'فشل في إنشاء حساب محاسبي: $accountName',
        userId: userId ?? 'system',
        category: 'chart_of_accounts',
        severity: 'high',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      rethrow;
    }
  }

  /// التحقق من صحة بيانات الحساب - Validate account data
  Future<void> _validateAccountData({
    required String accountCode,
    required String accountName,
    required String accountType,
    String? parentAccountId,
  }) async {
    // التحقق من رمز الحساب
    if (accountCode.trim().isEmpty) {
      throw Exception('رمز الحساب مطلوب');
    }

    if (accountCode.length > 20) {
      throw Exception('رمز الحساب طويل جداً (الحد الأقصى 20 حرف)');
    }

    // التحقق من اسم الحساب
    if (accountName.trim().isEmpty) {
      throw Exception('اسم الحساب مطلوب');
    }

    if (accountName.length > 200) {
      throw Exception('اسم الحساب طويل جداً (الحد الأقصى 200 حرف)');
    }

    // التحقق من نوع الحساب
    const validAccountTypes = ['asset', 'liability', 'equity', 'revenue', 'expense'];
    if (!validAccountTypes.contains(accountType)) {
      throw Exception('نوع الحساب غير صحيح. الأنواع المسموحة: ${validAccountTypes.join(', ')}');
    }

    // التحقق من الحساب الأب إذا تم تحديده
    if (parentAccountId != null) {
      final parentAccount = await _chartOfAccountsDao.getById(parentAccountId);
      if (parentAccount == null) {
        throw Exception('الحساب الأب غير موجود');
      }

      // التحقق من أن نوع الحساب الأب متوافق
      if (parentAccount.accountType != accountType) {
        throw Exception('نوع الحساب يجب أن يتطابق مع نوع الحساب الأب');
      }
    }
  }

  /// تحديث حساب موجود - Update existing account
  Future<bool> updateAccount({
    required String accountId,
    String? accountCode,
    String? accountName,
    String? accountType,
    String? parentAccountId,
    String? description,
    bool? isActive,
    String? userId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // الحصول على الحساب الحالي
      final currentAccount = await _chartOfAccountsDao.getById(accountId);
      if (currentAccount == null) {
        throw Exception('الحساب غير موجود');
      }

      // التحقق من التحديثات المطلوبة
      if (accountCode != null || accountName != null || accountType != null) {
        await _validateAccountData(
          accountCode: accountCode ?? currentAccount.accountCode,
          accountName: accountName ?? currentAccount.accountName,
          accountType: accountType ?? currentAccount.accountType,
          parentAccountId: parentAccountId ?? currentAccount.parentAccountId,
        );
      }

      // التحقق من عدم تكرار رمز الحساب الجديد
      if (accountCode != null && accountCode != currentAccount.accountCode) {
        final existingAccount = await _chartOfAccountsDao.getByAccountCode(accountCode);
        if (existingAccount != null) {
          throw Exception('رمز الحساب موجود مسبقاً: $accountCode');
        }
      }

      // إنشاء الحساب المحدث
      final updatedAccount = currentAccount.copyWith(
        accountCode: accountCode,
        accountName: accountName,
        accountType: accountType,
        parentAccountId: parentAccountId,
        description: description,
        isActive: isActive,
        metadata: metadata,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

      // حفظ التحديثات
      final success = await _chartOfAccountsDao.update(updatedAccount);

      if (success) {
        // تسجيل العملية
        await _auditLogDao.logOperation(
          tableName: 'chart_of_accounts',
          recordId: accountId,
          action: 'update',
          description: 'تحديث الحساب المحاسبي: ${updatedAccount.accountName}',
          userId: userId ?? 'system',
          category: 'chart_of_accounts',
          severity: 'medium',
          oldValues: currentAccount.toMap().toString(),
          newValues: updatedAccount.toMap().toString(),
          branchId: currentAccount.branchId,
        );
      }

      return success;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: accountId,
        action: 'update',
        description: 'فشل في تحديث الحساب المحاسبي',
        userId: userId ?? 'system',
        category: 'chart_of_accounts',
        severity: 'high',
        isSuccessful: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// حذف حساب (حذف منطقي) - Delete account (soft delete)
  Future<bool> deleteAccount({
    required String accountId,
    required String userId,
    String? reason,
  }) async {
    try {
      // التحقق من وجود الحساب
      final account = await _chartOfAccountsDao.getById(accountId);
      if (account == null) {
        throw Exception('الحساب غير موجود');
      }

      // التحقق من عدم وجود حسابات فرعية
      final childAccounts = await _chartOfAccountsDao.getChildAccounts(accountId);
      if (childAccounts.isNotEmpty) {
        throw Exception('لا يمكن حذف حساب يحتوي على حسابات فرعية');
      }

      // التحقق من عدم وجود قيود محاسبية
      final hasTransactions = await _chartOfAccountsDao.hasTransactions(accountId);
      if (hasTransactions) {
        throw Exception('لا يمكن حذف حساب يحتوي على قيود محاسبية');
      }

      // حذف الحساب
      final success = await _chartOfAccountsDao.delete(accountId);

      if (success) {
        // تسجيل العملية
        await _auditLogDao.logOperation(
          tableName: 'chart_of_accounts',
          recordId: accountId,
          action: 'delete',
          description: 'حذف الحساب المحاسبي: ${account.accountName}${reason != null ? ' - السبب: $reason' : ''}',
          userId: userId,
          category: 'chart_of_accounts',
          severity: 'high',
          metadata: {
            'deletion_reason': reason,
            'account_code': account.accountCode,
            'account_type': account.accountType,
          },
          branchId: account.branchId,
        );
      }

      return success;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: accountId,
        action: 'delete',
        description: 'فشل في حذف الحساب المحاسبي',
        userId: userId,
        category: 'chart_of_accounts',
        severity: 'high',
        isSuccessful: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// الحصول على الهيكل الهرمي للحسابات - Get accounts hierarchy
  Future<List<Map<String, dynamic>>> getAccountsHierarchy({
    String? accountType,
    String? branchId,
    bool activeOnly = true,
  }) async {
    try {
      // الحصول على الحسابات الرئيسية (بدون حساب أب)
      final rootAccounts = await _chartOfAccountsDao.getRootAccounts(accountType);
      
      List<Map<String, dynamic>> hierarchy = [];

      for (var account in rootAccounts) {
        // تصفية حسب الفرع والحالة
        if (branchId != null && account.branchId != branchId) continue;
        if (activeOnly && !account.isActive) continue;

        final accountNode = await _buildAccountNode(account, activeOnly);
        hierarchy.add(accountNode);
      }

      return hierarchy;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: 'hierarchy',
        action: 'get_hierarchy',
        description: 'فشل في الحصول على الهيكل الهرمي للحسابات',
        userId: 'system',
        category: 'chart_of_accounts',
        severity: 'medium',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      return [];
    }
  }

  /// بناء عقدة الحساب مع الحسابات الفرعية - Build account node with children
  Future<Map<String, dynamic>> _buildAccountNode(ChartOfAccounts account, bool activeOnly) async {
    final childAccounts = await _chartOfAccountsDao.getChildAccounts(account.id);
    List<Map<String, dynamic>> children = [];

    for (var child in childAccounts) {
      if (activeOnly && !child.isActive) continue;
      
      final childNode = await _buildAccountNode(child, activeOnly);
      children.add(childNode);
    }

    return {
      'id': account.id,
      'account_code': account.accountCode,
      'account_name': account.accountName,
      'account_type': account.accountType,
      'parent_account_id': account.parentAccountId,
      'balance': account.balance,
      'is_active': account.isActive,
      'description': account.description,
      'level': await _getAccountLevel(account.id),
      'has_children': children.isNotEmpty,
      'children': children,
    };
  }

  /// الحصول على مستوى الحساب في الهيكل الهرمي - Get account level in hierarchy
  Future<int> _getAccountLevel(String accountId) async {
    final account = await _chartOfAccountsDao.getById(accountId);
    if (account == null || account.parentAccountId == null) {
      return 0;
    }

    return 1 + await _getAccountLevel(account.parentAccountId!);
  }

  /// البحث في الحسابات - Search accounts
  Future<List<ChartOfAccounts>> searchAccounts({
    required String query,
    String? accountType,
    String? branchId,
    bool activeOnly = true,
  }) async {
    try {
      List<ChartOfAccounts> results = await _chartOfAccountsDao.searchAccounts(query);

      // تطبيق المرشحات
      if (accountType != null) {
        results = results.where((account) => account.accountType == accountType).toList();
      }

      if (branchId != null) {
        results = results.where((account) => account.branchId == branchId).toList();
      }

      if (activeOnly) {
        results = results.where((account) => account.isActive).toList();
      }

      return results;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: 'search',
        action: 'search_accounts',
        description: 'فشل في البحث في الحسابات',
        userId: 'system',
        category: 'chart_of_accounts',
        severity: 'low',
        isSuccessful: false,
        errorMessage: e.toString(),
        metadata: {
          'search_query': query,
        },
        branchId: branchId,
      );

      return [];
    }
  }

  /// الحصول على الحسابات حسب النوع - Get accounts by type
  Future<List<ChartOfAccounts>> getAccountsByType({
    required String accountType,
    String? branchId,
    bool activeOnly = true,
  }) async {
    try {
      List<ChartOfAccounts> accounts;

      switch (accountType) {
        case 'asset':
          accounts = await _chartOfAccountsDao.getAssetAccounts();
          break;
        case 'liability':
          accounts = await _chartOfAccountsDao.getLiabilityAccounts();
          break;
        case 'equity':
          accounts = await _chartOfAccountsDao.getEquityAccounts();
          break;
        case 'revenue':
          accounts = await _chartOfAccountsDao.getRevenueAccounts();
          break;
        case 'expense':
          accounts = await _chartOfAccountsDao.getExpenseAccounts();
          break;
        default:
          throw Exception('نوع الحساب غير صحيح: $accountType');
      }

      // تطبيق المرشحات
      if (branchId != null) {
        accounts = accounts.where((account) => account.branchId == branchId).toList();
      }

      if (activeOnly) {
        accounts = accounts.where((account) => account.isActive).toList();
      }

      return accounts;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: 'by_type',
        action: 'get_accounts_by_type',
        description: 'فشل في الحصول على الحسابات حسب النوع',
        userId: 'system',
        category: 'chart_of_accounts',
        severity: 'low',
        isSuccessful: false,
        errorMessage: e.toString(),
        metadata: {
          'account_type': accountType,
        },
        branchId: branchId,
      );

      return [];
    }
  }

  /// تفعيل/إلغاء تفعيل حساب - Activate/deactivate account
  Future<bool> toggleAccountStatus({
    required String accountId,
    required String userId,
    String? reason,
  }) async {
    try {
      final account = await _chartOfAccountsDao.getById(accountId);
      if (account == null) {
        throw Exception('الحساب غير موجود');
      }

      final newStatus = !account.isActive;
      final success = await _chartOfAccountsDao.updateAccountStatus(accountId, newStatus);

      if (success) {
        // تسجيل العملية
        await _auditLogDao.logOperation(
          tableName: 'chart_of_accounts',
          recordId: accountId,
          action: newStatus ? 'activate' : 'deactivate',
          description: '${newStatus ? 'تفعيل' : 'إلغاء تفعيل'} الحساب: ${account.accountName}${reason != null ? ' - السبب: $reason' : ''}',
          userId: userId,
          category: 'chart_of_accounts',
          severity: 'medium',
          metadata: {
            'reason': reason,
            'old_status': account.isActive,
            'new_status': newStatus,
          },
          branchId: account.branchId,
        );
      }

      return success;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: accountId,
        action: 'toggle_status',
        description: 'فشل في تغيير حالة الحساب',
        userId: userId,
        category: 'chart_of_accounts',
        severity: 'high',
        isSuccessful: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// إنشاء دليل حسابات افتراضي - Create default chart of accounts
  Future<bool> createDefaultChartOfAccounts({
    String? branchId,
    String? userId,
  }) async {
    try {
      // الحسابات الافتراضية الأساسية
      final defaultAccounts = [
        // الأصول
        {'code': '1000', 'name': 'الأصول', 'type': 'asset', 'parent': null},
        {'code': '1100', 'name': 'الأصول المتداولة', 'type': 'asset', 'parent': '1000'},
        {'code': '1110', 'name': 'النقدية', 'type': 'asset', 'parent': '1100'},
        {'code': '1120', 'name': 'المدينون', 'type': 'asset', 'parent': '1100'},
        {'code': '1130', 'name': 'المخزون', 'type': 'asset', 'parent': '1100'},
        
        // الخصوم
        {'code': '2000', 'name': 'الخصوم', 'type': 'liability', 'parent': null},
        {'code': '2100', 'name': 'الخصوم المتداولة', 'type': 'liability', 'parent': '2000'},
        {'code': '2110', 'name': 'الدائنون', 'type': 'liability', 'parent': '2100'},
        {'code': '2120', 'name': 'المصروفات المستحقة', 'type': 'liability', 'parent': '2100'},
        
        // حقوق الملكية
        {'code': '3000', 'name': 'حقوق الملكية', 'type': 'equity', 'parent': null},
        {'code': '3100', 'name': 'رأس المال', 'type': 'equity', 'parent': '3000'},
        {'code': '3200', 'name': 'الأرباح المحتجزة', 'type': 'equity', 'parent': '3000'},
        
        // الإيرادات
        {'code': '4000', 'name': 'الإيرادات', 'type': 'revenue', 'parent': null},
        {'code': '4100', 'name': 'إيرادات المبيعات', 'type': 'revenue', 'parent': '4000'},
        
        // المصروفات
        {'code': '5000', 'name': 'المصروفات', 'type': 'expense', 'parent': null},
        {'code': '5100', 'name': 'تكلفة البضاعة المباعة', 'type': 'expense', 'parent': '5000'},
        {'code': '5200', 'name': 'المصروفات التشغيلية', 'type': 'expense', 'parent': '5000'},
      ];

      Map<String, String> createdAccounts = {};
      bool allSuccess = true;

      for (var accountData in defaultAccounts) {
        String? parentId;
        if (accountData['parent'] != null) {
          parentId = createdAccounts[accountData['parent']];
        }

        final accountId = await createAccount(
          accountCode: accountData['code'] as String,
          accountName: accountData['name'] as String,
          accountType: accountData['type'] as String,
          parentAccountId: parentId,
          branchId: branchId,
          userId: userId,
          description: 'حساب افتراضي',
        );

        if (accountId != null) {
          createdAccounts[accountData['code'] as String] = accountId;
        } else {
          allSuccess = false;
        }
      }

      // تسجيل العملية
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: 'default_chart',
        action: 'create_default_chart',
        description: 'إنشاء دليل الحسابات الافتراضي',
        userId: userId ?? 'system',
        category: 'chart_of_accounts',
        severity: 'high',
        isSuccessful: allSuccess,
        metadata: {
          'accounts_created': createdAccounts.length,
          'total_accounts': defaultAccounts.length,
        },
        branchId: branchId,
      );

      return allSuccess;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: 'default_chart',
        action: 'create_default_chart',
        description: 'فشل في إنشاء دليل الحسابات الافتراضي',
        userId: userId ?? 'system',
        category: 'chart_of_accounts',
        severity: 'critical',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      return false;
    }
  }
}
