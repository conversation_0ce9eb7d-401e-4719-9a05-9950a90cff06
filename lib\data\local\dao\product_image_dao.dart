import 'package:sqflite/sqflite.dart' as sql;
import 'package:tijari_tech/data/models/product_image.dart';
import 'base_dao.dart';
import '../../../core/utils/app_utils.dart';

class ProductImageDao extends BaseDao<ProductImage> {
  @override
  String get tableName => 'product_images';

  @override
  ProductImage fromMap(Map<String, dynamic> map) => ProductImage.fromMap(map);

  @override
  Map<String, dynamic> toMap(ProductImage entity) => entity.toMap();

  // ------------------------------------------------------------------
  // Product Image-specific queries
  // ------------------------------------------------------------------

  /// استرجاع صور منتج معين
  Future<List<ProductImage>> findByProductId(String productId) async {
    return await findWhere(
      where: 'product_id = ?',
      whereArgs: [productId],
      orderBy: 'sort_order, created_at',
    );
  }

  /// استرجاع الصورة الرئيسية لمنتج
  Future<ProductImage?> findPrimaryImageByProductId(String productId) async {
    final images = await findWhere(
      where: 'product_id = ? AND is_primary = 1',
      whereArgs: [productId],
      limit: 1,
    );
    return images.isNotEmpty ? images.first : null;
  }

  /// تعيين صورة كصورة رئيسية
  Future<bool> setPrimaryImage(String productId, String imageId) async {
    try {
      final db = await database;
      
      return await db.transaction((txn) async {
        // إلغاء تعيين جميع الصور كرئيسية
        await txn.update(
          tableName,
          {'is_primary': 0, 'updated_at': DateTime.now().toIso8601String()},
          where: 'product_id = ? AND deleted_at IS NULL',
          whereArgs: [productId],
        );
        
        // تعيين الصورة المحددة كرئيسية
        final count = await txn.update(
          tableName,
          {'is_primary': 1, 'updated_at': DateTime.now().toIso8601String()},
          where: 'id = ? AND product_id = ? AND deleted_at IS NULL',
          whereArgs: [imageId, productId],
        );
        
        return count > 0;
      });
    } catch (e) {
      AppUtils.logError('Error setting primary image', e);
      return false;
    }
  }

  /// تحديث ترتيب الصور
  Future<bool> updateSortOrder(String imageId, int newSortOrder) async {
    try {
      final db = await database;
      
      final count = await db.update(
        tableName,
        {
          'sort_order': newSortOrder,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ? AND deleted_at IS NULL',
        whereArgs: [imageId],
      );
      
      return count > 0;
    } catch (e) {
      AppUtils.logError('Error updating sort order', e);
      return false;
    }
  }

  /// تحديث ترتيب متعدد للصور
  Future<bool> bulkUpdateSortOrder(Map<String, int> imageOrders) async {
    try {
      final db = await database;
      
      return await db.transaction((txn) async {
        for (final entry in imageOrders.entries) {
          await txn.update(
            tableName,
            {
              'sort_order': entry.value,
              'updated_at': DateTime.now().toIso8601String(),
            },
            where: 'id = ? AND deleted_at IS NULL',
            whereArgs: [entry.key],
          );
        }
        return true;
      });
    } catch (e) {
      AppUtils.logError('Error bulk updating sort order', e);
      return false;
    }
  }

  /// حذف جميع صور منتج معين
  Future<bool> deleteByProductId(String productId) async {
    try {
      final db = await database;
      final now = DateTime.now().toIso8601String();
      
      final count = await db.update(
        tableName,
        {
          'deleted_at': now,
          'updated_at': now,
        },
        where: 'product_id = ? AND deleted_at IS NULL',
        whereArgs: [productId],
      );
      
      AppUtils.logInfo('Soft deleted $count images for product: $productId');
      return count > 0;
    } catch (e) {
      AppUtils.logError('Error deleting images by product ID', e);
      return false;
    }
  }

  /// استرجاع عدد صور منتج معين
  Future<int> countByProductId(String productId) async {
    try {
      return await count(
        where: 'product_id = ?',
        whereArgs: [productId],
      );
    } catch (e) {
      AppUtils.logError('Error counting images by product ID', e);
      return 0;
    }
  }

  /// استرجاع الصور المرتبة حسب التاريخ
  Future<List<ProductImage>> findByProductIdOrderedByDate(
    String productId, {
    bool ascending = false,
  }) async {
    return await findWhere(
      where: 'product_id = ?',
      whereArgs: [productId],
      orderBy: 'created_at',
      ascending: ascending,
    );
  }

  /// البحث في الصور حسب الوصف
  Future<List<ProductImage>> searchByDescription(
    String productId,
    String searchTerm,
  ) async {
    return await findWhere(
      where: 'product_id = ? AND description LIKE ?',
      whereArgs: [productId, '%$searchTerm%'],
      orderBy: 'sort_order, created_at',
    );
  }

  /// استرجاع الصور التي تحتاج مزامنة
  Future<List<ProductImage>> getUnsyncedImages() async {
    return await findWhere(
      where: 'is_synced = 0',
      orderBy: 'updated_at',
    );
  }

  /// تحديث مسار الصورة المحلية
  Future<bool> updateImagePath(String imageId, String newPath) async {
    try {
      final db = await database;
      
      final count = await db.update(
        tableName,
        {
          'image_path': newPath,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ? AND deleted_at IS NULL',
        whereArgs: [imageId],
      );
      
      return count > 0;
    } catch (e) {
      AppUtils.logError('Error updating image path', e);
      return false;
    }
  }

  /// تحديث رابط الصورة
  Future<bool> updateImageUrl(String imageId, String newUrl) async {
    try {
      final db = await database;
      
      final count = await db.update(
        tableName,
        {
          'image_url': newUrl,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ? AND deleted_at IS NULL',
        whereArgs: [imageId],
      );
      
      return count > 0;
    } catch (e) {
      AppUtils.logError('Error updating image URL', e);
      return false;
    }
  }

  /// استرجاع إحصائيات الصور
  Future<Map<String, dynamic>> getImagesStatistics() async {
    final sql = '''
      SELECT 
        COUNT(*) as total_images,
        COUNT(CASE WHEN is_primary = 1 THEN 1 END) as primary_images,
        COUNT(CASE WHEN image_path IS NOT NULL AND image_path != '' THEN 1 END) as local_images,
        COUNT(CASE WHEN image_url IS NOT NULL AND image_url != '' THEN 1 END) as remote_images,
        COUNT(DISTINCT product_id) as products_with_images
      FROM $tableName
      WHERE deleted_at IS NULL
    ''';

    final result = await rawQuery(sql);
    return result.isNotEmpty ? result.first : {};
  }

  /// تنظيف الصور المحذوفة نهائياً
  Future<int> cleanupDeletedImages({int daysOld = 30}) async {
    try {
      final db = await database;
      final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));
      
      final count = await db.delete(
        tableName,
        where: 'deleted_at IS NOT NULL AND deleted_at < ?',
        whereArgs: [cutoffDate.toIso8601String()],
      );
      
      AppUtils.logInfo('Cleaned up $count old deleted images');
      return count;
    } catch (e) {
      AppUtils.logError('Error cleaning up deleted images', e);
      return 0;
    }
  }

  /// إنشاء نسخة احتياطية من الصور
  Future<List<Map<String, dynamic>>> exportImages({String? productId}) async {
    try {
      String whereClause = 'deleted_at IS NULL';
      List<dynamic> whereArgs = [];
      
      if (productId != null) {
        whereClause += ' AND product_id = ?';
        whereArgs.add(productId);
      }
      
      final sql = '''
        SELECT * FROM $tableName 
        WHERE $whereClause 
        ORDER BY product_id, sort_order
      ''';
      
      return await rawQuery(sql, whereArgs.isEmpty ? null : whereArgs);
    } catch (e) {
      AppUtils.logError('Error exporting images', e);
      return [];
    }
  }

  /// استيراد الصور من نسخة احتياطية
  Future<bool> importImages(List<Map<String, dynamic>> imagesData) async {
    try {
      final db = await database;
      
      return await db.transaction((txn) async {
        for (final imageData in imagesData) {
          await txn.insert(tableName, imageData);
        }
        return true;
      });
    } catch (e) {
      AppUtils.logError('Error importing images', e);
      return false;
    }
  }
}
