import 'package:json_annotation/json_annotation.dart';
import '../../core/constants/database_constants.dart';

part 'user.g.dart';

@JsonSerializable()
class User {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final String role;
  final List<String> permissions;
  final bool isActive;
  final String? profileImage;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? lastLoginAt;
  final String? passwordHash;
  final String? branchId;
 final bool isSynced;

  const User({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    required this.role,
    required this.permissions,
    this.isActive = true,
    this.profileImage,
    required this.createdAt,
    this.updatedAt,
    this.lastLoginAt,
    this.passwordHash,
    this.branchId,
    this.isSynced = false,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map[DatabaseConstants.columnUserId] as String,
      name: map[DatabaseConstants.columnUserName] as String,
      email: map[DatabaseConstants.columnUserEmail] as String,
      phone: map[DatabaseConstants.columnUserPhone] as String?,
      role: map[DatabaseConstants.columnUserRole] as String,
      permissions:
          (map[DatabaseConstants.columnUserPermissions] as String).split(','),
      isActive: (map[DatabaseConstants.columnUserIsActive] as int) == 1,
      profileImage: map[DatabaseConstants.columnUserProfileImage] as String?,
      createdAt:
          DateTime.parse(map[DatabaseConstants.columnUserCreatedAt] as String),
      updatedAt: map[DatabaseConstants.columnUserUpdatedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnUserUpdatedAt] as String)
          : null,
      lastLoginAt: map[DatabaseConstants.columnUserLastLoginAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnUserLastLoginAt] as String)
          : null,
      passwordHash: map[DatabaseConstants.columnUserPasswordHash] as String?,
      branchId: map[DatabaseConstants.columnUserBranchId] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnUserId: id,
      DatabaseConstants.columnUserName: name,
      DatabaseConstants.columnUserEmail: email,
      DatabaseConstants.columnUserPhone: phone,
      DatabaseConstants.columnUserRole: role,
      DatabaseConstants.columnUserPermissions: permissions.join(','),
      DatabaseConstants.columnUserIsActive: isActive ? 1 : 0,
      DatabaseConstants.columnUserProfileImage: profileImage,
      DatabaseConstants.columnUserCreatedAt: createdAt.toIso8601String(),
      DatabaseConstants.columnUserUpdatedAt: updatedAt?.toIso8601String(),
      DatabaseConstants.columnUserLastLoginAt: lastLoginAt?.toIso8601String(),
      DatabaseConstants.columnUserPasswordHash: passwordHash,
      DatabaseConstants.columnUserBranchId: branchId,
    };
  }

  User copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? role,
    List<String>? permissions,
    bool? isActive,
    String? profileImage,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
    String? passwordHash,
    String? branchId,
    bool? isSynced,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      permissions: permissions ?? this.permissions,
      isActive: isActive ?? this.isActive,
      profileImage: profileImage ?? this.profileImage,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      passwordHash: passwordHash ?? this.passwordHash,
      branchId: branchId ?? this.branchId,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email, role: $role, isActive: $isActive)';
  }

  // Helper methods
  bool hasPermission(String permission) {
    return permissions.contains(permission) || permissions.contains('all');
  }

  bool get isAdmin => role == 'مدير عام' || hasPermission('admin');
  bool get isManager => role == 'مدير فرع' || hasPermission('manager');
  bool get isAccountant => role == 'محاسب' || hasPermission('accountant');
  bool get isSalesEmployee => role == 'موظف مبيعات' || hasPermission('sales');

  String get displayRole {
    switch (role) {
      case 'مدير عام':
        return 'مدير عام';
      case 'مدير فرع':
        return 'مدير فرع';
      case 'محاسب':
        return 'محاسب';
      case 'موظف مبيعات':
        return 'موظف مبيعات';
      case 'أمين مخزن':
        return 'أمين مخزن';
      default:
        return role;
    }
  }

  String get initials {
    final names = name.split(' ');
    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}';
    }
    return name.isNotEmpty ? name[0] : '';
  }
}

// User roles constants
class UserRoles {
  static const String admin = 'مدير عام';
  static const String manager = 'مدير فرع';
  static const String accountant = 'محاسب';
  static const String salesEmployee = 'موظف مبيعات';
  static const String warehouseKeeper = 'أمين مخزن';

  static List<String> get allRoles => [
        admin,
        manager,
        accountant,
        salesEmployee,
        warehouseKeeper,
      ];
}

// User permissions constants
class UserPermissions {
  // General permissions
  static const String viewAll = 'view_all';
  static const String editAll = 'edit_all';
  static const String deleteAll = 'delete_all';
  static const String admin = 'admin';

  // Sales permissions
  static const String viewSales = 'view_sales';
  static const String addSales = 'add_sales';
  static const String editSales = 'edit_sales';
  static const String deleteSales = 'delete_sales';
  static const String salesReturns = 'sales_returns';

  // Purchase permissions
  static const String viewPurchases = 'view_purchases';
  static const String addPurchases = 'add_purchases';
  static const String editPurchases = 'edit_purchases';
  static const String deletePurchases = 'delete_purchases';
  static const String purchaseReturns = 'purchase_returns';

  // Inventory permissions
  static const String viewInventory = 'view_inventory';
  static const String addProducts = 'add_products';
  static const String editProducts = 'edit_products';
  static const String deleteProducts = 'delete_products';
  static const String stockAdjustment = 'stock_adjustment';

  // Accounts permissions
  static const String viewAccounts = 'view_accounts';
  static const String addCustomers = 'add_customers';
  static const String editCustomers = 'edit_customers';
  static const String deleteCustomers = 'delete_customers';
  static const String addSuppliers = 'add_suppliers';
  static const String editSuppliers = 'edit_suppliers';
  static const String deleteSuppliers = 'delete_suppliers';

  // Cash permissions
  static const String viewCash = 'view_cash';
  static const String addReceipts = 'add_receipts';
  static const String addPayments = 'add_payments';
  static const String editTransactions = 'edit_transactions';
  static const String deleteTransactions = 'delete_transactions';

  // Reports permissions
  static const String viewReports = 'view_reports';
  static const String financialReports = 'financial_reports';
  static const String salesReports = 'sales_reports';
  static const String inventoryReports = 'inventory_reports';

  // Settings permissions
  static const String viewSettings = 'view_settings';
  static const String userManagement = 'user_management';
  static const String systemSettings = 'system_settings';
  static const String backupRestore = 'backup_restore';

  static List<String> get allPermissions => [
        viewAll,
        editAll,
        deleteAll,
        admin,
        viewSales,
        addSales,
        editSales,
        deleteSales,
        salesReturns,
        viewPurchases,
        addPurchases,
        editPurchases,
        deletePurchases,
        purchaseReturns,
        viewInventory,
        addProducts,
        editProducts,
        deleteProducts,
        stockAdjustment,
        viewAccounts,
        addCustomers,
        editCustomers,
        deleteCustomers,
        addSuppliers,
        editSuppliers,
        deleteSuppliers,
        viewCash,
        addReceipts,
        addPayments,
        editTransactions,
        deleteTransactions,
        viewReports,
        financialReports,
        salesReports,
        inventoryReports,
        viewSettings,
        userManagement,
        systemSettings,
        backupRestore,
      ];

  static List<String> getDefaultPermissions(String role) {
    switch (role) {
      case UserRoles.admin:
        return [admin];
      case UserRoles.manager:
        return [
          viewSales,
          addSales,
          editSales,
          salesReturns,
          viewPurchases,
          addPurchases,
          editPurchases,
          purchaseReturns,
          viewInventory,
          addProducts,
          editProducts,
          stockAdjustment,
          viewAccounts,
          addCustomers,
          editCustomers,
          addSuppliers,
          editSuppliers,
          viewCash,
          addReceipts,
          addPayments,
          viewReports,
          salesReports,
          inventoryReports,
        ];
      case UserRoles.accountant:
        return [
          viewSales,
          addSales,
          editSales,
          viewPurchases,
          addPurchases,
          editPurchases,
          viewAccounts,
          addCustomers,
          editCustomers,
          addSuppliers,
          editSuppliers,
          viewCash,
          addReceipts,
          addPayments,
          editTransactions,
          viewReports,
          financialReports,
          salesReports,
        ];
      case UserRoles.salesEmployee:
        return [
          viewSales,
          addSales,
          viewInventory,
          viewAccounts,
          addCustomers,
          viewCash,
          addReceipts,
        ];
      case UserRoles.warehouseKeeper:
        return [
          viewInventory,
          addProducts,
          editProducts,
          stockAdjustment,
          viewPurchases,
          inventoryReports,
        ];
      default:
        return [viewSales, viewInventory];
    }
  }
}
