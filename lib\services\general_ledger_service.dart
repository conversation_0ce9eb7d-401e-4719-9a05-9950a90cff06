import '../data/local/dao/journal_entry_dao.dart';
import '../data/local/dao/chart_of_accounts_dao.dart';
import '../data/local/dao/audit_log_dao.dart';
import '../data/models/journal_entry.dart';
import '../data/models/chart_of_accounts.dart';

/// خدمة دفتر الأستاذ العام - General Ledger Service
/// تدير دفتر الأستاذ وتقارير الأرصدة والحركات المحاسبية
/// Manages general ledger and balance reports and accounting movements
class GeneralLedgerService {
  final JournalEntryDao _journalEntryDao;
  final ChartOfAccountsDao _chartOfAccountsDao;
  final AuditLogDao _auditLogDao;

  GeneralLedgerService({
    required JournalEntryDao journalEntryDao,
    required ChartOfAccountsDao chartOfAccountsDao,
    required AuditLogDao auditLogDao,
  })  : _journalEntryDao = journalEntryDao,
        _chartOfAccountsDao = chartOfAccountsDao,
        _auditLogDao = auditLogDao;

  /// الحصول على دفتر الأستاذ لحساب معين - Get ledger for specific account
  /// يعرض جميع الحركات المؤثرة على الحساب مع الأرصدة الجارية
  Future<Map<String, dynamic>> getAccountLedger({
    required String accountId,
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
  }) async {
    try {
      // الحصول على معلومات الحساب
      final account = await _chartOfAccountsDao.getById(accountId);
      if (account == null) {
        throw Exception('الحساب غير موجود');
      }

      // الحصول على القيود المؤثرة على الحساب
      final debitEntries = await _journalEntryDao.getByDebitAccount(accountId);
      final creditEntries = await _journalEntryDao.getByCreditAccount(accountId);

      // دمج وترتيب القيود حسب التاريخ
      List<Map<String, dynamic>> ledgerEntries = [];
      
      // إضافة القيود المدينة
      for (var entry in debitEntries) {
        if (_isDateInRange(entry.entryDate, startDate, endDate)) {
          ledgerEntries.add({
            'entry': entry,
            'type': 'debit',
            'amount': entry.debitAmount,
            'date': entry.entryDate,
          });
        }
      }

      // إضافة القيود الدائنة
      for (var entry in creditEntries) {
        if (_isDateInRange(entry.entryDate, startDate, endDate)) {
          ledgerEntries.add({
            'entry': entry,
            'type': 'credit',
            'amount': entry.creditAmount,
            'date': entry.entryDate,
          });
        }
      }

      // ترتيب القيود حسب التاريخ
      ledgerEntries.sort((a, b) => a['date'].compareTo(b['date']));

      // حساب الأرصدة الجارية
      double runningBalance = await _getOpeningBalance(accountId, startDate);
      List<Map<String, dynamic>> ledgerWithBalances = [];

      for (var ledgerEntry in ledgerEntries) {
        final entry = ledgerEntry['entry'] as JournalEntry;
        final type = ledgerEntry['type'] as String;
        final amount = ledgerEntry['amount'] as double;

        // تحديث الرصيد الجاري حسب نوع الحساب
        if (account.accountType == 'asset' || account.accountType == 'expense') {
          // الأصول والمصروفات: المدين يزيد، الدائن ينقص
          runningBalance += (type == 'debit') ? amount : -amount;
        } else {
          // الخصوم وحقوق الملكية والإيرادات: الدائن يزيد، المدين ينقص
          runningBalance += (type == 'credit') ? amount : -amount;
        }

        ledgerWithBalances.add({
          'entry_id': entry.id,
          'date': entry.entryDate,
          'description': entry.description,
          'reference': entry.reference,
          'debit_amount': type == 'debit' ? amount : 0.0,
          'credit_amount': type == 'credit' ? amount : 0.0,
          'running_balance': runningBalance,
          'approval_status': entry.approvalStatus,
          'is_reversed': entry.isReversed,
        });
      }

      // تسجيل العملية
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: accountId,
        action: 'get_ledger',
        description: 'عرض دفتر الأستاذ للحساب: ${account.accountName}',
        userId: userId ?? 'system',
        category: 'general_ledger',
        severity: 'low',
        metadata: {
          'entries_count': ledgerWithBalances.length,
          'start_date': startDate?.toIso8601String(),
          'end_date': endDate?.toIso8601String(),
        },
      );

      return {
        'account': account,
        'opening_balance': await _getOpeningBalance(accountId, startDate),
        'closing_balance': runningBalance,
        'entries': ledgerWithBalances,
        'total_debits': ledgerWithBalances.fold<double>(0, (sum, entry) => sum + entry['debit_amount']),
        'total_credits': ledgerWithBalances.fold<double>(0, (sum, entry) => sum + entry['credit_amount']),
        'entries_count': ledgerWithBalances.length,
      };
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: accountId,
        action: 'get_ledger',
        description: 'فشل في عرض دفتر الأستاذ',
        userId: userId ?? 'system',
        category: 'general_ledger',
        severity: 'medium',
        isSuccessful: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// الحصول على الرصيد الافتتاحي - Get opening balance
  Future<double> _getOpeningBalance(String accountId, DateTime? startDate) async {
    if (startDate == null) {
      return 0.0; // إذا لم يتم تحديد تاريخ البداية، الرصيد الافتتاحي صفر
    }

    // حساب الرصيد من بداية السنة المالية حتى تاريخ البداية
    final yearStart = DateTime(startDate.year, 1, 1);
    return await _journalEntryDao.calculateAccountBalanceForPeriod(
      accountId,
      yearStart,
      startDate.subtract(const Duration(days: 1)),
    );
  }

  /// التحقق من أن التاريخ ضمن النطاق المحدد - Check if date is in range
  bool _isDateInRange(DateTime date, DateTime? startDate, DateTime? endDate) {
    if (startDate != null && date.isBefore(startDate)) return false;
    if (endDate != null && date.isAfter(endDate)) return false;
    return true;
  }

  /// الحصول على ميزان المراجعة - Get trial balance
  /// يعرض أرصدة جميع الحسابات في تاريخ معين
  Future<Map<String, dynamic>> getTrialBalance({
    DateTime? asOfDate,
    String? branchId,
    String? userId,
  }) async {
    try {
      final effectiveDate = asOfDate ?? DateTime.now();
      final accounts = await _chartOfAccountsDao.getActiveAccounts();
      
      List<Map<String, dynamic>> trialBalanceEntries = [];
      double totalDebits = 0.0;
      double totalCredits = 0.0;

      for (var account in accounts) {
        // تصفية حسب الفرع إذا تم تحديده
        if (branchId != null && account.branchId != branchId) continue;

        // حساب رصيد الحساب حتى التاريخ المحدد
        final balance = await _journalEntryDao.calculateAccountBalanceAsOf(account.id, effectiveDate);
        
        if (balance != 0) { // عرض الحسابات التي لها أرصدة فقط
          // تحديد ما إذا كان الرصيد مدين أم دائن حسب نوع الحساب
          double debitBalance = 0.0;
          double creditBalance = 0.0;

          if (account.accountType == 'asset' || account.accountType == 'expense') {
            // الأصول والمصروفات: الرصيد الموجب مدين
            if (balance > 0) {
              debitBalance = balance;
              totalDebits += balance;
            } else {
              creditBalance = -balance;
              totalCredits += -balance;
            }
          } else {
            // الخصوم وحقوق الملكية والإيرادات: الرصيد الموجب دائن
            if (balance > 0) {
              creditBalance = balance;
              totalCredits += balance;
            } else {
              debitBalance = -balance;
              totalDebits += -balance;
            }
          }

          trialBalanceEntries.add({
            'account_id': account.id,
            'account_code': account.accountCode,
            'account_name': account.accountName,
            'account_type': account.accountType,
            'debit_balance': debitBalance,
            'credit_balance': creditBalance,
            'balance': balance,
          });
        }
      }

      // ترتيب الحسابات حسب رمز الحساب
      trialBalanceEntries.sort((a, b) => a['account_code'].compareTo(b['account_code']));

      // تسجيل العملية
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: 'trial_balance',
        action: 'get_trial_balance',
        description: 'إنشاء ميزان المراجعة',
        userId: userId ?? 'system',
        category: 'general_ledger',
        severity: 'low',
        metadata: {
          'as_of_date': effectiveDate.toIso8601String(),
          'accounts_count': trialBalanceEntries.length,
          'total_debits': totalDebits,
          'total_credits': totalCredits,
          'is_balanced': (totalDebits - totalCredits).abs() < 0.01,
        },
        branchId: branchId,
      );

      return {
        'as_of_date': effectiveDate,
        'entries': trialBalanceEntries,
        'total_debits': totalDebits,
        'total_credits': totalCredits,
        'difference': totalDebits - totalCredits,
        'is_balanced': (totalDebits - totalCredits).abs() < 0.01,
        'accounts_count': trialBalanceEntries.length,
      };
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: 'trial_balance',
        action: 'get_trial_balance',
        description: 'فشل في إنشاء ميزان المراجعة',
        userId: userId ?? 'system',
        category: 'general_ledger',
        severity: 'high',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      rethrow;
    }
  }

  /// الحصول على تقرير الأرصدة حسب النوع - Get balance report by account type
  Future<Map<String, dynamic>> getBalancesByType({
    DateTime? asOfDate,
    String? branchId,
    String? userId,
  }) async {
    try {
      final effectiveDate = asOfDate ?? DateTime.now();
      final accounts = await _chartOfAccountsDao.getActiveAccounts();
      
      Map<String, List<Map<String, dynamic>>> balancesByType = {
        'asset': [],
        'liability': [],
        'equity': [],
        'revenue': [],
        'expense': [],
      };

      Map<String, double> totalsByType = {
        'asset': 0.0,
        'liability': 0.0,
        'equity': 0.0,
        'revenue': 0.0,
        'expense': 0.0,
      };

      for (var account in accounts) {
        // تصفية حسب الفرع إذا تم تحديده
        if (branchId != null && account.branchId != branchId) continue;

        final balance = await _journalEntryDao.calculateAccountBalanceAsOf(account.id, effectiveDate);
        
        if (balance != 0) {
          final accountData = {
            'account_id': account.id,
            'account_code': account.accountCode,
            'account_name': account.accountName,
            'balance': balance,
          };

          balancesByType[account.accountType]?.add(accountData);
          totalsByType[account.accountType] = (totalsByType[account.accountType] ?? 0.0) + balance;
        }
      }

      // ترتيب الحسابات داخل كل نوع
      for (var type in balancesByType.keys) {
        balancesByType[type]?.sort((a, b) => a['account_code'].compareTo(b['account_code']));
      }

      return {
        'as_of_date': effectiveDate,
        'balances_by_type': balancesByType,
        'totals_by_type': totalsByType,
        'total_assets': totalsByType['asset'] ?? 0.0,
        'total_liabilities': totalsByType['liability'] ?? 0.0,
        'total_equity': totalsByType['equity'] ?? 0.0,
        'total_revenue': totalsByType['revenue'] ?? 0.0,
        'total_expenses': totalsByType['expense'] ?? 0.0,
      };
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: 'balances_by_type',
        action: 'get_balances_by_type',
        description: 'فشل في الحصول على الأرصدة حسب النوع',
        userId: userId ?? 'system',
        category: 'general_ledger',
        severity: 'medium',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      rethrow;
    }
  }

  /// الحصول على تقرير حركة الحسابات - Get account activity report
  Future<Map<String, dynamic>> getAccountActivity({
    required DateTime startDate,
    required DateTime endDate,
    String? accountType,
    String? branchId,
    String? userId,
  }) async {
    try {
      final entries = await _journalEntryDao.getByDateRange(startDate, endDate);
      
      Map<String, Map<String, dynamic>> accountActivity = {};

      for (var entry in entries) {
        // تصفية حسب الفرع إذا تم تحديده
        if (branchId != null && entry.branchId != branchId) continue;

        // معالجة الحساب المدين
        await _processAccountActivity(
          accountActivity,
          entry.debitAccountId,
          entry.debitAmount,
          'debit',
          accountType,
        );

        // معالجة الحساب الدائن
        await _processAccountActivity(
          accountActivity,
          entry.creditAccountId,
          entry.creditAmount,
          'credit',
          accountType,
        );
      }

      // تحويل إلى قائمة وترتيب
      List<Map<String, dynamic>> activityList = accountActivity.values.toList();
      activityList.sort((a, b) => a['account_code'].compareTo(b['account_code']));

      return {
        'start_date': startDate,
        'end_date': endDate,
        'account_activity': activityList,
        'total_accounts': activityList.length,
        'total_debit_amount': activityList.fold<double>(0, (sum, acc) => sum + acc['total_debit']),
        'total_credit_amount': activityList.fold<double>(0, (sum, acc) => sum + acc['total_credit']),
      };
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'journal_entries',
        recordId: 'account_activity',
        action: 'get_account_activity',
        description: 'فشل في الحصول على تقرير حركة الحسابات',
        userId: userId ?? 'system',
        category: 'general_ledger',
        severity: 'medium',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      rethrow;
    }
  }

  /// معالجة نشاط الحساب - Process account activity
  Future<void> _processAccountActivity(
    Map<String, Map<String, dynamic>> accountActivity,
    String accountId,
    double amount,
    String type,
    String? accountTypeFilter,
  ) async {
    final account = await _chartOfAccountsDao.getById(accountId);
    if (account == null) return;

    // تصفية حسب نوع الحساب إذا تم تحديده
    if (accountTypeFilter != null && account.accountType != accountTypeFilter) return;

    if (!accountActivity.containsKey(accountId)) {
      accountActivity[accountId] = {
        'account_id': accountId,
        'account_code': account.accountCode,
        'account_name': account.accountName,
        'account_type': account.accountType,
        'total_debit': 0.0,
        'total_credit': 0.0,
        'transaction_count': 0,
      };
    }

    accountActivity[accountId]![type == 'debit' ? 'total_debit' : 'total_credit'] += amount;
    accountActivity[accountId]!['transaction_count']++;
  }

  /// مزامنة دفتر الأستاذ - Sync general ledger
  /// يعيد حساب جميع أرصدة الحسابات من القيود
  Future<bool> syncGeneralLedger({String? userId}) async {
    try {
      final accounts = await _chartOfAccountsDao.getActiveAccounts();
      bool allSuccess = true;

      for (var account in accounts) {
        final calculatedBalance = await _journalEntryDao.calculateAccountBalance(account.id);
        final updateSuccess = await _chartOfAccountsDao.updateBalance(account.id, calculatedBalance);
        
        if (!updateSuccess) {
          allSuccess = false;
        }
      }

      // تسجيل العملية
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: 'sync_ledger',
        action: 'sync_general_ledger',
        description: 'مزامنة دفتر الأستاذ العام',
        userId: userId ?? 'system',
        category: 'general_ledger',
        severity: 'medium',
        isSuccessful: allSuccess,
        metadata: {
          'accounts_synced': accounts.length,
        },
      );

      return allSuccess;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: 'sync_ledger',
        action: 'sync_general_ledger',
        description: 'فشل في مزامنة دفتر الأستاذ العام',
        userId: userId ?? 'system',
        category: 'general_ledger',
        severity: 'critical',
        isSuccessful: false,
        errorMessage: e.toString(),
      );

      return false;
    }
  }
}
