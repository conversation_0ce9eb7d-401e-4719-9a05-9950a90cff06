import 'package:json_annotation/json_annotation.dart';
import '../../core/constants/database_constants.dart';

part 'settings.g.dart';

/// نموذج الإعدادات - Settings Model
/// يمثل إعدادات النظام والتطبيق
@JsonSerializable()
class Settings {
  final String id;
  final String key;            // مفتاح الإعداد
  final String value;          // قيمة الإعداد
  final String? description;   // وصف الإعداد
  final String category;       // فئة الإعداد (general, accounting, inventory, etc.)
  final String dataType;       // نوع البيانات (string, int, double, bool, json)
  final bool isSystem;         // هل هو إعداد نظام (لا يمكن حذفه)
  final bool isEncrypted;      // هل القيمة مشفرة
  final String? validationRule; // قاعدة التحقق من صحة القيمة
  final String? defaultValue;  // القيمة الافتراضية
  final String? branchId;      // معرف الفرع (null للإعدادات العامة)
  final String? userId;        // معرف المستخدم (للإعدادات الشخصية)
  final DateTime createdAt;    // تاريخ الإنشاء
  final DateTime? updatedAt;   // تاريخ التحديث
  final DateTime? deletedAt;   // تاريخ الحذف (soft delete)
  final bool isSynced;         // هل تم المزامنة

  const Settings({
    required this.id,
    required this.key,
    required this.value,
    this.description,
    this.category = 'general',
    this.dataType = 'string',
    this.isSystem = false,
    this.isEncrypted = false,
    this.validationRule,
    this.defaultValue,
    this.branchId,
    this.userId,
    required this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  /// إنشاء إعداد من JSON
  factory Settings.fromJson(Map<String, dynamic> json) =>
      _$SettingsFromJson(json);

  /// تحويل الإعداد إلى JSON
  Map<String, dynamic> toJson() => _$SettingsToJson(this);

  /// إنشاء إعداد من Map قاعدة البيانات
  factory Settings.fromMap(Map<String, dynamic> map) {
    return Settings(
      id: map[DatabaseConstants.columnSettingsId] as String,
      key: map[DatabaseConstants.columnSettingsKey] as String,
      value: map[DatabaseConstants.columnSettingsValue] as String,
      description: map[DatabaseConstants.columnSettingsDescription] as String?,
      category: map[DatabaseConstants.columnSettingsCategory] as String? ?? 'general',
      dataType: map[DatabaseConstants.columnSettingsDataType] as String? ?? 'string',
      isSystem: (map[DatabaseConstants.columnSettingsIsSystem] as int?) == 1,
      isEncrypted: (map[DatabaseConstants.columnSettingsIsEncrypted] as int?) == 1,
      validationRule: map[DatabaseConstants.columnSettingsValidationRule] as String?,
      defaultValue: map[DatabaseConstants.columnSettingsDefaultValue] as String?,
      branchId: map[DatabaseConstants.columnSettingsBranchId] as String?,
      userId: map[DatabaseConstants.columnSettingsUserId] as String?,
      createdAt: DateTime.parse(map[DatabaseConstants.columnSettingsCreatedAt] as String),
      updatedAt: map[DatabaseConstants.columnSettingsUpdatedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnSettingsUpdatedAt] as String)
          : null,
      deletedAt: map[DatabaseConstants.columnSettingsDeletedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnSettingsDeletedAt] as String)
          : null,
      isSynced: (map[DatabaseConstants.columnSettingsIsSynced] as int) == 1,
    );
  }

  /// تحويل الإعداد إلى Map لقاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnSettingsId: id,
      DatabaseConstants.columnSettingsKey: key,
      DatabaseConstants.columnSettingsValue: value,
      DatabaseConstants.columnSettingsDescription: description,
      DatabaseConstants.columnSettingsCategory: category,
      DatabaseConstants.columnSettingsDataType: dataType,
      DatabaseConstants.columnSettingsIsSystem: isSystem ? 1 : 0,
      DatabaseConstants.columnSettingsIsEncrypted: isEncrypted ? 1 : 0,
      DatabaseConstants.columnSettingsValidationRule: validationRule,
      DatabaseConstants.columnSettingsDefaultValue: defaultValue,
      DatabaseConstants.columnSettingsBranchId: branchId,
      DatabaseConstants.columnSettingsUserId: userId,
      DatabaseConstants.columnSettingsCreatedAt: createdAt.toIso8601String(),
      DatabaseConstants.columnSettingsUpdatedAt: updatedAt?.toIso8601String(),
      DatabaseConstants.columnSettingsDeletedAt: deletedAt?.toIso8601String(),
      DatabaseConstants.columnSettingsIsSynced: isSynced ? 1 : 0,
    };
  }

  /// نسخ الإعداد مع تعديل بعض الخصائص
  Settings copyWith({
    String? id,
    String? key,
    String? value,
    String? description,
    String? category,
    String? dataType,
    bool? isSystem,
    bool? isEncrypted,
    String? validationRule,
    String? defaultValue,
    String? branchId,
    String? userId,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return Settings(
      id: id ?? this.id,
      key: key ?? this.key,
      value: value ?? this.value,
      description: description ?? this.description,
      category: category ?? this.category,
      dataType: dataType ?? this.dataType,
      isSystem: isSystem ?? this.isSystem,
      isEncrypted: isEncrypted ?? this.isEncrypted,
      validationRule: validationRule ?? this.validationRule,
      defaultValue: defaultValue ?? this.defaultValue,
      branchId: branchId ?? this.branchId,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  /// إنشاء إعداد جديد
  factory Settings.create({
    required String id,
    required String key,
    required String value,
    String? description,
    String category = 'general',
    String dataType = 'string',
    bool isSystem = false,
    bool isEncrypted = false,
    String? validationRule,
    String? defaultValue,
    String? branchId,
    String? userId,
  }) {
    final now = DateTime.now();
    return Settings(
      id: id,
      key: key,
      value: value,
      description: description,
      category: category,
      dataType: dataType,
      isSystem: isSystem,
      isEncrypted: isEncrypted,
      validationRule: validationRule,
      defaultValue: defaultValue,
      branchId: branchId,
      userId: userId,
      createdAt: now,
      isSynced: false,
    );
  }

  /// التحقق من صحة بيانات الإعداد
  bool get isValid {
    return id.isNotEmpty &&
        key.isNotEmpty &&
        value.isNotEmpty &&
        category.isNotEmpty &&
        dataType.isNotEmpty;
  }

  /// هل الإعداد محذوف؟
  bool get isDeleted => deletedAt != null;

  /// هل الإعداد عام؟ (ليس خاص بفرع أو مستخدم)
  bool get isGlobal => branchId == null && userId == null;

  /// هل الإعداد خاص بفرع؟
  bool get isBranchSpecific => branchId != null && userId == null;

  /// هل الإعداد خاص بمستخدم؟
  bool get isUserSpecific => userId != null;

  /// تحويل القيمة حسب نوع البيانات
  dynamic get typedValue {
    switch (dataType.toLowerCase()) {
      case 'int':
        return int.tryParse(value) ?? 0;
      case 'double':
        return double.tryParse(value) ?? 0.0;
      case 'bool':
        return value.toLowerCase() == 'true' || value == '1';
      case 'json':
        // يمكن إضافة تحليل JSON هنا إذا لزم الأمر
        return value;
      default:
        return value;
    }
  }

  /// تحديث القيمة
  Settings updateValue(String newValue) {
    return copyWith(
      value: newValue,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  /// إعادة تعيين القيمة إلى الافتراضية
  Settings resetToDefault() {
    if (defaultValue == null) return this;
    return updateValue(defaultValue!);
  }

  /// التحقق من صحة القيمة حسب قاعدة التحقق
  bool isValueValid(String testValue) {
    if (validationRule == null) return true;
    
    // يمكن إضافة منطق التحقق من صحة القيمة هنا
    // مثل التحقق من regex أو نطاق الأرقام
    
    return true; // مؤقتاً
  }

  /// الحصول على القيمة كـ String
  String get stringValue => value;

  /// الحصول على القيمة كـ int
  int get intValue => int.tryParse(value) ?? 0;

  /// الحصول على القيمة كـ double
  double get doubleValue => double.tryParse(value) ?? 0.0;

  /// الحصول على القيمة كـ bool
  bool get boolValue => value.toLowerCase() == 'true' || value == '1';

  @override
  String toString() {
    return 'Settings(id: $id, key: $key, value: $value, category: $category)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Settings && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
