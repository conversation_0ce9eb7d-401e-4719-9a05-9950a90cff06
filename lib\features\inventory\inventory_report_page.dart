import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tijari_tech/core/theme/colors.dart';
import 'package:tijari_tech/core/theme/text_styles.dart';

import 'package:tijari_tech/data/models/product.dart';
import 'package:tijari_tech/providers/stock_management_provider.dart';
import 'package:tijari_tech/widgets/main_layout.dart';

/// صفحة تقرير المخزون
class InventoryReportPage extends ConsumerWidget {
  const InventoryReportPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final stockSummary = ref.watch(stockSummaryProvider);
    final lowStockProducts = ref.watch(lowStockProductsProvider);
    final outOfStockProducts = ref.watch(outOfStockProductsProvider);

    return MainLayout(
      title: 'تقرير المخزون',
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ملخص المخزون
            _buildStockSummary(stockSummary),
            SizedBox(height: 20.h),
            
            // المنتجات النافدة
            _buildOutOfStockSection(outOfStockProducts),
            SizedBox(height: 20.h),
            
            // المنتجات منخفضة المخزون
            _buildLowStockSection(lowStockProducts),
          ],
        ),
      ),
    );
  }

  /// بناء ملخص المخزون
  Widget _buildStockSummary(Map<String, dynamic> summary) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص المخزون',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'إجمالي المعاملات',
                    '${summary['totalTransactions']}',
                    Icons.swap_horiz,
                    AppColors.primary,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildSummaryCard(
                    'تعديلات المخزون',
                    '${summary['totalAdjustments']}',
                    Icons.edit,
                    AppColors.info,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 12.h),
            
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'عمليات الشراء',
                    '${summary['totalPurchases']}',
                    Icons.shopping_cart,
                    AppColors.success,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildSummaryCard(
                    'عمليات البيع',
                    '${summary['totalSales']}',
                    Icons.point_of_sale,
                    AppColors.warning,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة ملخص
  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 32.r,
          ),
          SizedBox(height: 8.h),
          Text(
            value,
            style: AppTextStyles.titleLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء قسم المنتجات النافدة
  Widget _buildOutOfStockSection(List<Product> products) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.warning,
                  color: AppColors.error,
                  size: 24.r,
                ),
                SizedBox(width: 8.w),
                Text(
                  'المنتجات النافدة (${products.length})',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.error,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            
            if (products.isEmpty)
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: AppColors.success,
                      size: 48.r,
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'لا توجد منتجات نافدة',
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: AppColors.success,
                      ),
                    ),
                  ],
                ),
              )
            else
              ...products.map((product) => _buildProductTile(
                product,
                AppColors.error,
                'نفد المخزون',
              )),
          ],
        ),
      ),
    );
  }

  /// بناء قسم المنتجات منخفضة المخزون
  Widget _buildLowStockSection(List<Product> products) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.inventory_2,
                  color: AppColors.warning,
                  size: 24.r,
                ),
                SizedBox(width: 8.w),
                Text(
                  'المنتجات منخفضة المخزون (${products.length})',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.warning,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            
            if (products.isEmpty)
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: AppColors.success,
                      size: 48.r,
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'جميع المنتجات في مستوى آمن',
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: AppColors.success,
                      ),
                    ),
                  ],
                ),
              )
            else
              ...products.map((product) => _buildProductTile(
                product,
                AppColors.warning,
                'مخزون منخفض',
              )),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر المنتج
  Widget _buildProductTile(Product product, Color color, String status) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.h,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              Icons.inventory_2,
              color: color,
              size: 20.r,
            ),
          ),
          SizedBox(width: 12.w),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product.getDisplayName(),
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 4.h),
                Row(
                  children: [
                    Text(
                      'الكمية الحالية: ${product.currentStock?.toStringAsFixed(0) ?? '0'}',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Text(
                      'الحد الأدنى: ${product.minStock.toStringAsFixed(0)}',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: 8.w,
              vertical: 4.h,
            ),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Text(
              status,
              style: AppTextStyles.bodySmall.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
