import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/colors.dart';
import '../../../core/theme/text_styles.dart';

import '../../../providers/warehouse_provider.dart';

class WarehouseFiltersDialog extends ConsumerStatefulWidget {
  const WarehouseFiltersDialog({super.key});

  @override
  ConsumerState<WarehouseFiltersDialog> createState() =>
      _WarehouseFiltersDialogState();
}

class _WarehouseFiltersDialogState
    extends ConsumerState<WarehouseFiltersDialog> {
  String? _selectedBranch;
  String? _selectedManager;
  bool? _isActiveFilter;
  String? _warehouseTypeFilter;
  String _sortField = 'name';
  bool _sortAscending = true;

  @override
  void initState() {
    super.initState();
    final state = ref.read(warehouseManagementProvider);
    _selectedBranch = state.selectedBranch;
    _selectedManager = state.selectedManager;
    _isActiveFilter = state.isActiveFilter;
    _warehouseTypeFilter = state.warehouseTypeFilter;
    _sortField = state.sortField;
    _sortAscending = state.sortAscending;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.r),
                  topRight: Radius.circular(16.r),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.filter_list,
                    color: Colors.white,
                    size: 24.sp,
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Text(
                      'تصفية وترتيب المخازن',
                      style: AppTextStyles.titleLarge.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(20.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Filters section
                    _buildSectionTitle('المرشحات'),
                    SizedBox(height: 16.h),

                    // Active status filter
                    _buildFilterCard(
                      'حالة النشاط',
                      DropdownButtonFormField<bool?>(
                        value: _isActiveFilter,
                        decoration: InputDecoration(
                          hintText: 'اختر حالة النشاط',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 12.w,
                            vertical: 8.h,
                          ),
                        ),
                        items: const [
                          DropdownMenuItem(value: null, child: Text('الكل')),
                          DropdownMenuItem(value: true, child: Text('نشط')),
                          DropdownMenuItem(
                              value: false, child: Text('غير نشط')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _isActiveFilter = value;
                          });
                        },
                      ),
                    ),
                    SizedBox(height: 16.h),

                    // Warehouse type filter
                    _buildFilterCard(
                      'نوع المخزن',
                      DropdownButtonFormField<String?>(
                        value: _warehouseTypeFilter,
                        decoration: InputDecoration(
                          hintText: 'اختر نوع المخزن',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 12.w,
                            vertical: 8.h,
                          ),
                        ),
                        items: const [
                          DropdownMenuItem(value: null, child: Text('الكل')),
                          DropdownMenuItem(value: 'main', child: Text('رئيسي')),
                          DropdownMenuItem(value: 'branch', child: Text('فرع')),
                          DropdownMenuItem(
                              value: 'temporary', child: Text('مؤقت')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _warehouseTypeFilter = value;
                          });
                        },
                      ),
                    ),
                    SizedBox(height: 24.h),

                    // Sorting section
                    _buildSectionTitle('الترتيب'),
                    SizedBox(height: 16.h),

                    // Sort field
                    _buildFilterCard(
                      'ترتيب حسب',
                      DropdownButtonFormField<String>(
                        value: _sortField,
                        decoration: InputDecoration(
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 12.w,
                            vertical: 8.h,
                          ),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'name', child: Text('الاسم')),
                          DropdownMenuItem(
                              value: 'location', child: Text('الموقع')),
                          DropdownMenuItem(value: 'type', child: Text('النوع')),
                          DropdownMenuItem(
                              value: 'capacity', child: Text('السعة')),
                          DropdownMenuItem(
                              value: 'products', child: Text('عدد المنتجات')),
                          DropdownMenuItem(
                              value: 'value', child: Text('القيمة الإجمالية')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _sortField = value!;
                          });
                        },
                      ),
                    ),
                    SizedBox(height: 16.h),

                    // Sort direction
                    _buildFilterCard(
                      'اتجاه الترتيب',
                      Row(
                        children: [
                          Expanded(
                            child: RadioListTile<bool>(
                              title: const Text('تصاعدي'),
                              value: true,
                              groupValue: _sortAscending,
                              onChanged: (value) {
                                setState(() {
                                  _sortAscending = value!;
                                });
                              },
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                          Expanded(
                            child: RadioListTile<bool>(
                              title: const Text('تنازلي'),
                              value: false,
                              groupValue: _sortAscending,
                              onChanged: (value) {
                                setState(() {
                                  _sortAscending = value!;
                                });
                              },
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Action buttons
            Container(
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16.r),
                  bottomRight: Radius.circular(16.r),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _clearFilters,
                      child: const Text('مسح الكل'),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _applyFilters,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                      ),
                      child: const Text(
                        'تطبيق',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: AppTextStyles.titleMedium.copyWith(
        fontWeight: FontWeight.bold,
        color: AppColors.primary,
      ),
    );
  }

  Widget _buildFilterCard(String title, Widget child) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          child,
        ],
      ),
    );
  }

  void _clearFilters() {
    setState(() {
      _selectedBranch = null;
      _selectedManager = null;
      _isActiveFilter = null;
      _warehouseTypeFilter = null;
      _sortField = 'name';
      _sortAscending = true;
    });

    ref.read(warehouseManagementProvider.notifier).clearFilters();
    ref
        .read(warehouseManagementProvider.notifier)
        .sortWarehouses(_sortField, _sortAscending);
    Navigator.of(context).pop();
  }

  void _applyFilters() {
    final notifier = ref.read(warehouseManagementProvider.notifier);

    notifier.filterByBranch(_selectedBranch);
    notifier.filterByManager(_selectedManager);
    notifier.filterByActiveStatus(_isActiveFilter);
    notifier.filterByWarehouseType(_warehouseTypeFilter);
    notifier.sortWarehouses(_sortField, _sortAscending);

    Navigator.of(context).pop();
  }
}
