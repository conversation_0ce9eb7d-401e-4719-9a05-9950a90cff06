import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/colors.dart';
import '../../../core/theme/text_styles.dart';

import '../../../data/models/warehouse.dart';

class WarehouseDetailsDialog extends StatelessWidget {
  final Warehouse warehouse;

  const WarehouseDetailsDialog({
    super.key,
    required this.warehouse,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.r),
                  topRight: Radius.circular(16.r),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8.w),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(
                      _getWarehouseTypeIcon(),
                      color: Colors.white,
                      size: 24.sp,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                warehouse.name,
                                style: AppTextStyles.titleLarge.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            if (warehouse.isDefault)
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 8.w,
                                  vertical: 4.h,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.orange,
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                                child: Text(
                                  'افتراضي',
                                  style: AppTextStyles.bodySmall.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        if (warehouse.location != null)
                          Text(
                            warehouse.location!,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Colors.white70,
                            ),
                          ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(20.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Status and type section
                    Row(
                      children: [
                        Expanded(
                          child: _buildInfoCard(
                            'الحالة',
                            warehouse.isActive ? 'نشط' : 'غير نشط',
                            warehouse.isActive
                                ? Icons.check_circle
                                : Icons.cancel,
                            warehouse.isActive ? Colors.green : Colors.red,
                          ),
                        ),
                        SizedBox(width: 12.w),
                        Expanded(
                          child: _buildInfoCard(
                            'النوع',
                            _getWarehouseTypeText(),
                            _getWarehouseTypeIcon(),
                            _getWarehouseTypeColor(),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 16.h),

                    // Statistics section
                    if (warehouse.productCount != null ||
                        warehouse.totalValue != null)
                      Column(
                        children: [
                          Row(
                            children: [
                              if (warehouse.productCount != null)
                                Expanded(
                                  child: _buildInfoCard(
                                    'عدد المنتجات',
                                    '${warehouse.productCount}',
                                    Icons.inventory,
                                    AppColors.primary,
                                  ),
                                ),
                              if (warehouse.productCount != null &&
                                  warehouse.totalValue != null)
                                SizedBox(width: 12.w),
                              if (warehouse.totalValue != null)
                                Expanded(
                                  child: _buildInfoCard(
                                    'القيمة الإجمالية',
                                    '${warehouse.totalValue!.toStringAsFixed(2)} ر.س',
                                    Icons.monetization_on,
                                    Colors.green,
                                  ),
                                ),
                            ],
                          ),
                          SizedBox(height: 16.h),
                        ],
                      ),

                    // Capacity section
                    if (warehouse.capacity != null)
                      Column(
                        children: [
                          _buildSectionTitle('السعة التخزينية'),
                          SizedBox(height: 12.h),
                          Container(
                            padding: EdgeInsets.all(16.w),
                            decoration: BoxDecoration(
                              color: Colors.grey[50],
                              borderRadius: BorderRadius.circular(12.r),
                              border: Border.all(color: Colors.grey[300]!),
                            ),
                            child: Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'السعة الكلية',
                                      style: AppTextStyles.bodyMedium,
                                    ),
                                    Text(
                                      '${warehouse.capacity!.toStringAsFixed(0)} وحدة',
                                      style: AppTextStyles.bodyMedium.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                if (warehouse.usedCapacity != null) ...[
                                  SizedBox(height: 8.h),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'المستخدم',
                                        style: AppTextStyles.bodyMedium,
                                      ),
                                      Text(
                                        '${warehouse.usedCapacity!.toStringAsFixed(0)} وحدة',
                                        style:
                                            AppTextStyles.bodyMedium.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 12.h),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'نسبة الاستخدام',
                                        style: AppTextStyles.bodyMedium,
                                      ),
                                      Text(
                                        '${warehouse.capacityUtilization.toStringAsFixed(1)}%',
                                        style:
                                            AppTextStyles.bodyMedium.copyWith(
                                          fontWeight: FontWeight.bold,
                                          color: _getCapacityColor(),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 8.h),
                                  LinearProgressIndicator(
                                    value: warehouse.capacityUtilization / 100,
                                    backgroundColor: Colors.grey[300],
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        _getCapacityColor()),
                                  ),
                                ],
                              ],
                            ),
                          ),
                          SizedBox(height: 16.h),
                        ],
                      ),

                    // Contact information section
                    if (warehouse.hasContact || warehouse.address != null)
                      Column(
                        children: [
                          _buildSectionTitle('معلومات الاتصال'),
                          SizedBox(height: 12.h),
                          if (warehouse.address != null)
                            _buildDetailRow(
                              'العنوان',
                              warehouse.address!,
                              Icons.location_on,
                            ),
                          if (warehouse.phone != null)
                            _buildDetailRow(
                              'الهاتف',
                              warehouse.phone!,
                              Icons.phone,
                            ),
                          if (warehouse.email != null)
                            _buildDetailRow(
                              'البريد الإلكتروني',
                              warehouse.email!,
                              Icons.email,
                            ),
                          SizedBox(height: 16.h),
                        ],
                      ),

                    // Additional information section
                    if (warehouse.description != null ||
                        warehouse.notes != null)
                      Column(
                        children: [
                          _buildSectionTitle('معلومات إضافية'),
                          SizedBox(height: 12.h),
                          if (warehouse.description != null)
                            _buildDetailRow(
                              'الوصف',
                              warehouse.description!,
                              Icons.description,
                            ),
                          if (warehouse.notes != null)
                            _buildDetailRow(
                              'ملاحظات',
                              warehouse.notes!,
                              Icons.note,
                            ),
                          SizedBox(height: 16.h),
                        ],
                      ),

                    // Timestamps section
                    _buildSectionTitle('معلومات النظام'),
                    SizedBox(height: 12.h),
                    if (warehouse.createdAt != null)
                      _buildDetailRow(
                        'تاريخ الإنشاء',
                        _formatDateTime(warehouse.createdAt!),
                        Icons.calendar_today,
                      ),
                    if (warehouse.updatedAt != null)
                      _buildDetailRow(
                        'آخر تحديث',
                        _formatDateTime(warehouse.updatedAt!),
                        Icons.update,
                      ),
                  ],
                ),
              ),
            ),

            // Action buttons
            Container(
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16.r),
                  bottomRight: Radius.circular(16.r),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      label: const Text('إغلاق'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: AppTextStyles.titleMedium.copyWith(
        fontWeight: FontWeight.bold,
        color: AppColors.primary,
      ),
    );
  }

  Widget _buildInfoCard(
      String label, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.sp),
          SizedBox(height: 8.h),
          Text(
            value,
            style: AppTextStyles.titleMedium.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 20.sp,
            color: Colors.grey[600],
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  value,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getWarehouseTypeColor() {
    switch (warehouse.warehouseType) {
      case 'main':
        return AppColors.primary;
      case 'branch':
        return Colors.blue;
      case 'temporary':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getWarehouseTypeIcon() {
    switch (warehouse.warehouseType) {
      case 'main':
        return Icons.business;
      case 'branch':
        return Icons.store;
      case 'temporary':
        return Icons.schedule;
      default:
        return Icons.warehouse;
    }
  }

  String _getWarehouseTypeText() {
    switch (warehouse.warehouseType) {
      case 'main':
        return 'رئيسي';
      case 'branch':
        return 'فرع';
      case 'temporary':
        return 'مؤقت';
      default:
        return warehouse.warehouseType ?? 'غير محدد';
    }
  }

  Color _getCapacityColor() {
    final utilization = warehouse.capacityUtilization;
    if (utilization >= 90) return Colors.red;
    if (utilization >= 80) return Colors.orange;
    if (utilization >= 60) return Colors.yellow[700]!;
    return Colors.green;
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
