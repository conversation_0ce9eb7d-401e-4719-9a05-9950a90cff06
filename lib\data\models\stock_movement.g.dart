// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stock_movement.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StockMovement _$StockMovementFromJson(Map<String, dynamic> json) =>
    StockMovement(
      id: json['id'] as String,
      productId: json['productId'] as String,
      warehouseId: json['warehouseId'] as String,
      movementType: json['movementType'] as String,
      quantity: (json['quantity'] as num).toDouble(),
      unitCost: (json['unitCost'] as num).toDouble(),
      totalValue: (json['totalValue'] as num).toDouble(),
      unitId: json['unitId'] as String,
      referenceType: json['referenceType'] as String?,
      referenceId: json['referenceId'] as String?,
      referenceNumber: json['referenceNumber'] as String?,
      fromWarehouseId: json['fromWarehouseId'] as String?,
      toWarehouseId: json['toWarehouseId'] as String?,
      batchNumber: json['batchNumber'] as String?,
      expiryDate: json['expiryDate'] == null
          ? null
          : DateTime.parse(json['expiryDate'] as String),
      notes: json['notes'] as String?,
      reason: json['reason'] as String?,
      createdBy: json['createdBy'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      branchId: json['branchId'] as String?,
      isSynced: json['isSynced'] as bool? ?? false,
      productName: json['productName'] as String?,
      productBarcode: json['productBarcode'] as String?,
      warehouseName: json['warehouseName'] as String?,
      fromWarehouseName: json['fromWarehouseName'] as String?,
      toWarehouseName: json['toWarehouseName'] as String?,
      createdByName: json['createdByName'] as String?,
    );

Map<String, dynamic> _$StockMovementToJson(StockMovement instance) =>
    <String, dynamic>{
      'id': instance.id,
      'productId': instance.productId,
      'warehouseId': instance.warehouseId,
      'movementType': instance.movementType,
      'quantity': instance.quantity,
      'unitCost': instance.unitCost,
      'totalValue': instance.totalValue,
      'unitId': instance.unitId,
      'referenceType': instance.referenceType,
      'referenceId': instance.referenceId,
      'referenceNumber': instance.referenceNumber,
      'fromWarehouseId': instance.fromWarehouseId,
      'toWarehouseId': instance.toWarehouseId,
      'batchNumber': instance.batchNumber,
      'expiryDate': instance.expiryDate?.toIso8601String(),
      'notes': instance.notes,
      'reason': instance.reason,
      'createdBy': instance.createdBy,
      'createdAt': instance.createdAt.toIso8601String(),
      'branchId': instance.branchId,
      'isSynced': instance.isSynced,
      'productName': instance.productName,
      'productBarcode': instance.productBarcode,
      'warehouseName': instance.warehouseName,
      'fromWarehouseName': instance.fromWarehouseName,
      'toWarehouseName': instance.toWarehouseName,
      'createdByName': instance.createdByName,
    };
