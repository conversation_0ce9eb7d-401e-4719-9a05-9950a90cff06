import '../../core/constants/database_constants.dart';

class StockAlert {
  final String id;
  final String productId;
  final String warehouseId;
  final String alertType; // low_stock, expired, near_expiry, out_of_stock
  final String severity; // low, medium, high, critical
  final String title;
  final String message;
  final Map<String, dynamic> data;
  final bool isRead;
  final bool isResolved;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? resolvedAt;
  final String? resolvedBy;
  final DateTime? deletedAt;

  const StockAlert({
    required this.id,
    required this.productId,
    required this.warehouseId,
    required this.alertType,
    required this.severity,
    required this.title,
    required this.message,
    required this.data,
    required this.isRead,
    required this.isResolved,
    required this.createdAt,
    required this.updatedAt,
    this.resolvedAt,
    this.resolvedBy,
    this.deletedAt,
  });

  // Factory constructor for creating new alert
  factory StockAlert.create({
    required String id,
    required String productId,
    required String warehouseId,
    required String alertType,
    required String severity,
    required String title,
    required String message,
    Map<String, dynamic> data = const {},
  }) {
    final now = DateTime.now();
    return StockAlert(
      id: id,
      productId: productId,
      warehouseId: warehouseId,
      alertType: alertType,
      severity: severity,
      title: title,
      message: message,
      data: data,
      isRead: false,
      isResolved: false,
      createdAt: now,
      updatedAt: now,
    );
  }

  // Factory constructor from map
  factory StockAlert.fromMap(Map<String, dynamic> map) {
    return StockAlert(
      id: map['id'] as String,
      productId: map['product_id'] as String,
      warehouseId: map['warehouse_id'] as String,
      alertType: map['alert_type'] as String,
      severity: map['severity'] as String,
      title: map['title'] as String,
      message: map['message'] as String,
      data: map['data'] is String
          ? {} // Handle JSON string if needed
          : Map<String, dynamic>.from(map['data'] ?? {}),
      isRead: (map['is_read'] as int) == 1,
      isResolved: (map['is_resolved'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      resolvedAt: map['resolved_at'] != null
          ? DateTime.parse(map['resolved_at'] as String)
          : null,
      resolvedBy: map['resolved_by'] as String?,
      deletedAt: map['deleted_at'] != null
          ? DateTime.parse(map['deleted_at'] as String)
          : null,
    );
  }

  // Convert to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'product_id': productId,
      'warehouse_id': warehouseId,
      'alert_type': alertType,
      'severity': severity,
      'title': title,
      'message': message,
      'data': data,
      'is_read': isRead ? 1 : 0,
      'is_resolved': isResolved ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'resolved_at': resolvedAt?.toIso8601String(),
      'resolved_by': resolvedBy,
      'deleted_at': deletedAt?.toIso8601String(),
    };
  }

  // Copy with method
  StockAlert copyWith({
    String? id,
    String? productId,
    String? warehouseId,
    String? alertType,
    String? severity,
    String? title,
    String? message,
    Map<String, dynamic>? data,
    bool? isRead,
    bool? isResolved,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? resolvedAt,
    String? resolvedBy,
    DateTime? deletedAt,
  }) {
    return StockAlert(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      warehouseId: warehouseId ?? this.warehouseId,
      alertType: alertType ?? this.alertType,
      severity: severity ?? this.severity,
      title: title ?? this.title,
      message: message ?? this.message,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
      isResolved: isResolved ?? this.isResolved,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      resolvedBy: resolvedBy ?? this.resolvedBy,
      deletedAt: deletedAt ?? this.deletedAt,
    );
  }

  // Mark as read
  StockAlert markAsRead() {
    return copyWith(
      isRead: true,
      updatedAt: DateTime.now(),
    );
  }

  // Mark as resolved
  StockAlert markAsResolved(String resolvedBy) {
    return copyWith(
      isResolved: true,
      resolvedAt: DateTime.now(),
      resolvedBy: resolvedBy,
      updatedAt: DateTime.now(),
    );
  }

  // Mark as deleted
  StockAlert markAsDeleted() {
    return copyWith(
      deletedAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  // Getters
  bool get isDeleted => deletedAt != null;
  bool get isActive => !isResolved && !isDeleted;
  bool get isUnread => !isRead;

  bool get isLowSeverity => severity == 'low';
  bool get isMediumSeverity => severity == 'medium';
  bool get isHighSeverity => severity == 'high';
  bool get isCriticalSeverity => severity == 'critical';

  bool get isLowStockAlert => alertType == 'low_stock';
  bool get isOutOfStockAlert => alertType == 'out_of_stock';
  bool get isExpiredAlert => alertType == 'expired';
  bool get isNearExpiryAlert => alertType == 'near_expiry';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StockAlert && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'StockAlert(id: $id, type: $alertType, severity: $severity, title: $title)';
  }
}

// Alert configuration model
class StockAlertConfig {
  final String id;
  final String productId;
  final String? warehouseId; // null means all warehouses
  final double? lowStockThreshold;
  final int? nearExpiryDays;
  final bool enableLowStockAlert;
  final bool enableOutOfStockAlert;
  final bool enableExpiryAlert;
  final bool enableNearExpiryAlert;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;

  const StockAlertConfig({
    required this.id,
    required this.productId,
    this.warehouseId,
    this.lowStockThreshold,
    this.nearExpiryDays,
    required this.enableLowStockAlert,
    required this.enableOutOfStockAlert,
    required this.enableExpiryAlert,
    required this.enableNearExpiryAlert,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  // Factory constructor for creating new config
  factory StockAlertConfig.create({
    required String id,
    required String productId,
    String? warehouseId,
    double? lowStockThreshold,
    int? nearExpiryDays,
    bool enableLowStockAlert = true,
    bool enableOutOfStockAlert = true,
    bool enableExpiryAlert = true,
    bool enableNearExpiryAlert = true,
  }) {
    final now = DateTime.now();
    return StockAlertConfig(
      id: id,
      productId: productId,
      warehouseId: warehouseId,
      lowStockThreshold: lowStockThreshold,
      nearExpiryDays: nearExpiryDays,
      enableLowStockAlert: enableLowStockAlert,
      enableOutOfStockAlert: enableOutOfStockAlert,
      enableExpiryAlert: enableExpiryAlert,
      enableNearExpiryAlert: enableNearExpiryAlert,
      createdAt: now,
      updatedAt: now,
    );
  }

  // Factory constructor from map
  factory StockAlertConfig.fromMap(Map<String, dynamic> map) {
    return StockAlertConfig(
      id: map['id'] as String,
      productId: map['product_id'] as String,
      warehouseId: map['warehouse_id'] as String?,
      lowStockThreshold: map['low_stock_threshold'] != null
          ? (map['low_stock_threshold'] as num).toDouble()
          : null,
      nearExpiryDays: map['near_expiry_days'] as int?,
      enableLowStockAlert: (map['enable_low_stock_alert'] as int) == 1,
      enableOutOfStockAlert: (map['enable_out_of_stock_alert'] as int) == 1,
      enableExpiryAlert: (map['enable_expiry_alert'] as int) == 1,
      enableNearExpiryAlert: (map['enable_near_expiry_alert'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      deletedAt: map['deleted_at'] != null
          ? DateTime.parse(map['deleted_at'] as String)
          : null,
    );
  }

  // Convert to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'product_id': productId,
      'warehouse_id': warehouseId,
      'low_stock_threshold': lowStockThreshold,
      'near_expiry_days': nearExpiryDays,
      'enable_low_stock_alert': enableLowStockAlert ? 1 : 0,
      'enable_out_of_stock_alert': enableOutOfStockAlert ? 1 : 0,
      'enable_expiry_alert': enableExpiryAlert ? 1 : 0,
      'enable_near_expiry_alert': enableNearExpiryAlert ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'deleted_at': deletedAt?.toIso8601String(),
    };
  }

  // Copy with method
  StockAlertConfig copyWith({
    String? id,
    String? productId,
    String? warehouseId,
    double? lowStockThreshold,
    int? nearExpiryDays,
    bool? enableLowStockAlert,
    bool? enableOutOfStockAlert,
    bool? enableExpiryAlert,
    bool? enableNearExpiryAlert,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
  }) {
    return StockAlertConfig(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      warehouseId: warehouseId ?? this.warehouseId,
      lowStockThreshold: lowStockThreshold ?? this.lowStockThreshold,
      nearExpiryDays: nearExpiryDays ?? this.nearExpiryDays,
      enableLowStockAlert: enableLowStockAlert ?? this.enableLowStockAlert,
      enableOutOfStockAlert:
          enableOutOfStockAlert ?? this.enableOutOfStockAlert,
      enableExpiryAlert: enableExpiryAlert ?? this.enableExpiryAlert,
      enableNearExpiryAlert:
          enableNearExpiryAlert ?? this.enableNearExpiryAlert,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
    );
  }

  // Getters
  bool get isDeleted => deletedAt != null;
  bool get hasLowStockThreshold => lowStockThreshold != null;
  bool get hasNearExpiryDays => nearExpiryDays != null;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StockAlertConfig && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'StockAlertConfig(id: $id, productId: $productId, warehouseId: $warehouseId)';
  }
}
