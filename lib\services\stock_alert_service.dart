import '../data/local/dao/stock_alert_dao.dart';
import '../data/local/dao/stock_dao.dart';
import '../data/local/dao/product_dao.dart';
import '../data/models/stock_alert.dart';
import '../data/models/stock.dart';
import '../data/models/product.dart';
import '../core/utils/app_utils.dart';

class StockAlertService {
  final StockAlertDao _alertDao = StockAlertDao();
  final StockAlertConfigDao _configDao = StockAlertConfigDao();
  final StockDao _stockDao = StockDao();
  final ProductDao _productDao = ProductDao();

  // ------------------------------------------------------------------
  // Alert Generation
  // ------------------------------------------------------------------

  /// فحص وإنشاء تنبيهات المخزون
  Future<void> checkAndGenerateAlerts() async {
    try {
      AppUtils.logInfo('Starting stock alert check...');

      // Check low stock alerts
      await _checkLowStockAlerts();

      // Check out of stock alerts
      await _checkOutOfStockAlerts();

      // Check expiry alerts
      await _checkExpiryAlerts();

      // Check near expiry alerts
      await _checkNearExpiryAlerts();

      AppUtils.logInfo('Stock alert check completed');
    } catch (e) {
      AppUtils.logError('Error checking stock alerts', e);
    }
  }

  /// فحص تنبيهات المخزون المنخفض
  Future<void> _checkLowStockAlerts() async {
    try {
      // Get all stocks with their products and configurations
      final stocks = await _stockDao.getStocksWithDetails();

      for (final stock in stocks) {
        // Skip if stock is zero (will be handled by out of stock check)
        if (stock.quantity <= 0) continue;

        // Get alert configuration for this product
        final config =
            await _getAlertConfig(stock.productId, stock.warehouseId);
        if (config == null || !config.enableLowStockAlert) continue;

        // Check if stock is below threshold
        final threshold =
            config.lowStockThreshold ?? _getDefaultLowStockThreshold();
        if (stock.quantity <= threshold) {
          // Check if alert already exists
          final existingAlerts = await _alertDao.getActiveAlerts(
            productId: stock.productId,
            warehouseId: stock.warehouseId,
            alertType: 'low_stock',
          );

          if (existingAlerts.isEmpty) {
            await _createLowStockAlert(stock, threshold);
          }
        } else {
          // Stock is above threshold, resolve any existing low stock alerts
          await _resolveAlerts(
            productId: stock.productId,
            warehouseId: stock.warehouseId,
            alertType: 'low_stock',
          );
        }
      }
    } catch (e) {
      AppUtils.logError('Error checking low stock alerts', e);
    }
  }

  /// فحص تنبيهات نفاد المخزون
  Future<void> _checkOutOfStockAlerts() async {
    try {
      // Get all stocks with zero quantity
      final outOfStockItems = await _stockDao.getOutOfStockItems();

      for (final stock in outOfStockItems) {
        // Get alert configuration for this product
        final config =
            await _getAlertConfig(stock.productId, stock.warehouseId);
        if (config == null || !config.enableOutOfStockAlert) continue;

        // Check if alert already exists
        final existingAlerts = await _alertDao.getActiveAlerts(
          productId: stock.productId,
          warehouseId: stock.warehouseId,
          alertType: 'out_of_stock',
        );

        if (existingAlerts.isEmpty) {
          await _createOutOfStockAlert(stock);
        }
      }

      // Resolve out of stock alerts for items that are back in stock
      final activeOutOfStockAlerts = await _alertDao.getActiveAlerts(
        alertType: 'out_of_stock',
      );

      for (final alert in activeOutOfStockAlerts) {
        final stock = await _stockDao.getStockByProductAndWarehouse(
          alert.productId,
          alert.warehouseId,
        );

        if (stock != null && stock.quantity > 0) {
          await _alertDao.update(alert.id, alert.markAsResolved('system'));
        }
      }
    } catch (e) {
      AppUtils.logError('Error checking out of stock alerts', e);
    }
  }

  /// فحص تنبيهات انتهاء الصلاحية
  Future<void> _checkExpiryAlerts() async {
    try {
      // Get all products with expiry dates that have passed
      final expiredProducts = await _productDao.getExpiredProducts();

      for (final product in expiredProducts) {
        // Get alert configuration for this product
        final configs = await _configDao.getConfigsForProduct(product.id);

        for (final config in configs) {
          if (!config.enableExpiryAlert) continue;

          final warehouseId = config.warehouseId ?? 'all_warehouses';

          // Check if alert already exists
          final existingAlerts = await _alertDao.getActiveAlerts(
            productId: product.id,
            warehouseId: warehouseId,
            alertType: 'expired',
          );

          if (existingAlerts.isEmpty) {
            await _createExpiredAlert(product, warehouseId);
          }
        }
      }
    } catch (e) {
      AppUtils.logError('Error checking expiry alerts', e);
    }
  }

  /// فحص تنبيهات قرب انتهاء الصلاحية
  Future<void> _checkNearExpiryAlerts() async {
    try {
      // Get all products with expiry dates approaching
      final nearExpiryProducts =
          await _productDao.getNearExpiryProducts(30); // Default 30 days

      for (final product in nearExpiryProducts) {
        // Get alert configuration for this product
        final configs = await _configDao.getConfigsForProduct(product.id);

        for (final config in configs) {
          if (!config.enableNearExpiryAlert) continue;

          final nearExpiryDays = config.nearExpiryDays ?? 30;
          final warehouseId = config.warehouseId ?? 'all_warehouses';

          // Check if product is within near expiry period
          if (product.expiryDate != null) {
            final daysUntilExpiry =
                product.expiryDate!.difference(DateTime.now()).inDays;

            if (daysUntilExpiry <= nearExpiryDays && daysUntilExpiry > 0) {
              // Check if alert already exists
              final existingAlerts = await _alertDao.getActiveAlerts(
                productId: product.id,
                warehouseId: warehouseId,
                alertType: 'near_expiry',
              );

              if (existingAlerts.isEmpty) {
                await _createNearExpiryAlert(
                    product, warehouseId, daysUntilExpiry);
              }
            }
          }
        }
      }
    } catch (e) {
      AppUtils.logError('Error checking near expiry alerts', e);
    }
  }

  // ------------------------------------------------------------------
  // Alert Creation Methods
  // ------------------------------------------------------------------

  Future<void> _createLowStockAlert(Stock stock, double threshold) async {
    final product = await _productDao.findById(stock.productId);
    if (product == null) return;

    final alert = StockAlert.create(
      id: AppUtils.generateId(),
      productId: stock.productId,
      warehouseId: stock.warehouseId,
      alertType: 'low_stock',
      severity: _getLowStockSeverity(stock.quantity, threshold),
      title: 'مخزون منخفض',
      message:
          'المنتج "${product.nameAr}" في المخزن لديه كمية منخفضة (${stock.quantity})',
      data: {
        'current_quantity': stock.quantity,
        'threshold': threshold,
        'product_name': product.nameAr,
      },
    );

    await _alertDao.insert(alert);
    AppUtils.logInfo('Created low stock alert for product: ${product.nameAr}');
  }

  Future<void> _createOutOfStockAlert(Stock stock) async {
    final product = await _productDao.findById(stock.productId);
    if (product == null) return;

    final alert = StockAlert.create(
      id: AppUtils.generateId(),
      productId: stock.productId,
      warehouseId: stock.warehouseId,
      alertType: 'out_of_stock',
      severity: 'critical',
      title: 'نفاد المخزون',
      message: 'المنتج "${product.nameAr}" نفد من المخزن',
      data: {
        'product_name': product.nameAr,
      },
    );

    await _alertDao.insert(alert);
    AppUtils.logInfo(
        'Created out of stock alert for product: ${product.nameAr}');
  }

  Future<void> _createExpiredAlert(Product product, String warehouseId) async {
    final alert = StockAlert.create(
      id: AppUtils.generateId(),
      productId: product.id,
      warehouseId: warehouseId,
      alertType: 'expired',
      severity: 'high',
      title: 'منتج منتهي الصلاحية',
      message: 'المنتج "${product.nameAr}" انتهت صلاحيته',
      data: {
        'product_name': product.nameAr,
        'expiry_date': product.expiryDate?.toIso8601String(),
      },
    );

    await _alertDao.insert(alert);
    AppUtils.logInfo('Created expired alert for product: ${product.nameAr}');
  }

  Future<void> _createNearExpiryAlert(
      Product product, String warehouseId, int daysUntilExpiry) async {
    final alert = StockAlert.create(
      id: AppUtils.generateId(),
      productId: product.id,
      warehouseId: warehouseId,
      alertType: 'near_expiry',
      severity: daysUntilExpiry <= 7 ? 'high' : 'medium',
      title: 'قرب انتهاء الصلاحية',
      message: 'المنتج "${product.nameAr}" سينتهي خلال $daysUntilExpiry يوم',
      data: {
        'product_name': product.nameAr,
        'expiry_date': product.expiryDate?.toIso8601String(),
        'days_until_expiry': daysUntilExpiry,
      },
    );

    await _alertDao.insert(alert);
    AppUtils.logInfo(
        'Created near expiry alert for product: ${product.nameAr}');
  }

  // ------------------------------------------------------------------
  // Helper Methods
  // ------------------------------------------------------------------

  Future<StockAlertConfig?> _getAlertConfig(
      String productId, String warehouseId) async {
    // First try to get specific config for product in warehouse
    var config =
        await _configDao.getConfigForProductInWarehouse(productId, warehouseId);

    // If not found, try to get global config for product
    config ??= await _configDao.getGlobalConfigForProduct(productId);

    return config;
  }

  Future<void> _resolveAlerts({
    required String productId,
    required String warehouseId,
    required String alertType,
  }) async {
    final alerts = await _alertDao.getActiveAlerts(
      productId: productId,
      warehouseId: warehouseId,
      alertType: alertType,
    );

    for (final alert in alerts) {
      await _alertDao.update(alert.id, alert.markAsResolved('system'));
    }
  }

  String _getLowStockSeverity(double currentQuantity, double threshold) {
    final ratio = currentQuantity / threshold;
    if (ratio <= 0.25) return 'critical';
    if (ratio <= 0.5) return 'high';
    if (ratio <= 0.75) return 'medium';
    return 'low';
  }

  double _getDefaultLowStockThreshold() {
    return 10.0; // Default threshold
  }

  // ------------------------------------------------------------------
  // Public API Methods
  // ------------------------------------------------------------------

  /// إنشاء أو تحديث إعدادات التنبيهات للمنتج
  Future<void> setAlertConfig(StockAlertConfig config) async {
    final existingConfig = await _configDao.getConfigForProductInWarehouse(
      config.productId,
      config.warehouseId ?? 'global',
    );

    if (existingConfig != null) {
      await _configDao.update(existingConfig.id, config);
    } else {
      await _configDao.insert(config);
    }
  }

  /// تحديد التنبيه كمقروء
  Future<void> markAlertAsRead(String alertId) async {
    final alert = await _alertDao.findById(alertId);
    if (alert != null) {
      await _alertDao.update(alertId, alert.markAsRead());
    }
  }

  /// حل التنبيه
  Future<void> resolveAlert(String alertId, String resolvedBy) async {
    final alert = await _alertDao.findById(alertId);
    if (alert != null) {
      await _alertDao.update(alertId, alert.markAsResolved(resolvedBy));
    }
  }

  /// استرجاع التنبيهات النشطة
  Future<List<StockAlert>> getActiveAlerts({
    String? warehouseId,
    String? productId,
    String? alertType,
    String? severity,
    int? limit,
  }) async {
    return await _alertDao.getActiveAlerts(
      warehouseId: warehouseId,
      productId: productId,
      alertType: alertType,
      severity: severity,
      limit: limit,
    );
  }

  /// استرجاع إحصائيات التنبيهات
  Future<Map<String, dynamic>> getAlertStatistics({
    String? warehouseId,
    String? productId,
  }) async {
    return await _alertDao.getAlertStatistics(
      warehouseId: warehouseId,
      productId: productId,
    );
  }

  /// تحديد جميع التنبيهات كمقروءة
  Future<void> markAllAlertsAsRead({
    String? warehouseId,
    String? productId,
  }) async {
    await _alertDao.markAllAsRead(
      warehouseId: warehouseId,
      productId: productId,
    );
  }

  /// حل جميع التنبيهات
  Future<void> resolveAllAlerts({
    String? warehouseId,
    String? productId,
    String? alertType,
    required String resolvedBy,
  }) async {
    await _alertDao.resolveAllAlerts(
      warehouseId: warehouseId,
      productId: productId,
      alertType: alertType,
      resolvedBy: resolvedBy,
    );
  }
}
