
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/app_utils.dart';
import '../../core/utils/formatters.dart';
import '../../data/models/customer.dart';
import '../../data/models/product.dart';
import '../../data/models/sale_item.dart';
import '../../providers/sale_provider.dart';
import '../../providers/customer_provider.dart';
import '../../providers/enhanced_product_provider.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/custom_card.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';
import 'widgets/add_item_dialog.dart';

class SaleAddPage extends ConsumerStatefulWidget {
  const SaleAddPage({super.key});

  @override
  ConsumerState<SaleAddPage> createState() => _SaleAddPageState();
}

class _SaleAddPageState extends ConsumerState<SaleAddPage> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  final _paidAmountController = TextEditingController();

  Customer? _selectedCustomer;
  final List<SaleItem> _items = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // تحميل البيانات المطلوبة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }

  void _loadInitialData() {
    // تحميل العملاء والمنتجات
    final customerNotifier = ref.read(customerProvider.notifier);
    final productNotifier =
        ref.read(enhancedProductManagementProvider.notifier);

    // استخدام الطرق الصحيحة لتحميل البيانات
    customerNotifier.refresh();
    productNotifier.refresh();
  }

  @override
  void dispose() {
    _notesController.dispose();
    _paidAmountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'إضافة فاتورة مبيعات',
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildCustomerSection(),
                    SizedBox(height: 20.h),
                    _buildItemsSection(),
                    SizedBox(height: 20.h),
                    _buildPaymentSection(),
                    SizedBox(height: 20.h),
                    _buildNotesSection(),
                  ],
                ),
              ),
            ),
            _buildBottomSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerSection() {
    final customerState = ref.watch(customerProvider);

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'بيانات العميل',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 12.h),
          if (customerState.isLoading)
            const Center(child: CircularProgressIndicator())
          else
            DropdownButtonFormField<Customer>(
              value: _selectedCustomer,
              decoration: const InputDecoration(
                labelText: 'اختر العميل (اختياري)',
                prefixIcon: Icon(Icons.person),
              ),
              items: [
                const DropdownMenuItem<Customer>(
                  value: null,
                  child: Text('بدون عميل (نقدي)'),
                ),
                ...customerState.customers.map((customer) {
                  return DropdownMenuItem<Customer>(
                    value: customer,
                    child: Text(customer.name),
                  );
                }),
              ],
              onChanged: (customer) {
                setState(() {
                  _selectedCustomer = customer;
                });
              },
            ),
          if (_selectedCustomer != null) ...[
            SizedBox(height: 8.h),
            Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'معلومات العميل:',
                    style: AppTextStyles.bodySmall.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                  SizedBox(height: 4.h),
                  if (_selectedCustomer!.phone != null)
                    Text('الهاتف: ${_selectedCustomer!.phone}'),
                  Text(
                      'الرصيد: ${AppFormatters.formatCurrency(_selectedCustomer!.balance)}'),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildItemsSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'عناصر الفاتورة',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              ElevatedButton.icon(
                onPressed: _showAddProductDialog,
                icon: const Icon(Icons.add, size: 16),
                label: const Text('إضافة منتج'),
                style: ElevatedButton.styleFrom(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                  textStyle: TextStyle(fontSize: 12.sp),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          if (_items.isEmpty)
            Container(
              padding: EdgeInsets.all(20.r),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.shopping_cart_outlined,
                    size: 48.r,
                    color: Colors.grey[400],
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'لا توجد منتجات في الفاتورة',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  SizedBox(height: 8.h),
                  ElevatedButton(
                    onPressed: _showAddProductDialog,
                    child: const Text('إضافة منتج'),
                  ),
                ],
              ),
            )
          else
            Column(
              children: [
                ..._items.asMap().entries.map((entry) {
                  final index = entry.key;
                  final item = entry.value;
                  return _buildItemCard(item, index);
                }),
                SizedBox(height: 12.h),
                _buildTotalSection(),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildItemCard(SaleItem item, int index) {
    final productState = ref.watch(enhancedProductManagementProvider);
    final product = productState.products.firstWhere(
      (p) => p.id == item.productId,
      orElse: () => Product.create(
        id: '',
        nameAr: 'منتج غير معروف',
        costPrice: 0,
        sellingPrice: 0,
      ),
    );

    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product.nameAr.isNotEmpty ? product.nameAr : 'منتج غير معروف',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'الكمية: ${item.qty} × ${AppFormatters.formatCurrency(item.unitPrice)}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                AppFormatters.formatCurrency(item.total),
                style: AppTextStyles.titleSmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.success,
                ),
              ),
              SizedBox(height: 4.h),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed: () => _editItem(index),
                    icon: const Icon(Icons.edit, size: 18),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  SizedBox(width: 8.w),
                  IconButton(
                    onPressed: () => _removeItem(index),
                    icon: const Icon(Icons.delete, size: 18, color: Colors.red),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTotalSection() {
    final subtotal = _items.fold<double>(0, (sum, item) => sum + item.total);

    return Container(
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.primary.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'الإجمالي:',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            AppFormatters.formatCurrency(subtotal),
            style: AppTextStyles.titleLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentSection() {
    final total = _items.fold<double>(0, (sum, item) => sum + item.total);

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الدفع',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 12.h),
          CustomTextField(
            controller: _paidAmountController,
            label: 'المبلغ المدفوع',
            prefixIcon: const Icon(Icons.payment),
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value == null || value.isEmpty) return null;
              final amount = double.tryParse(value);
              if (amount == null) return 'يرجى إدخال رقم صحيح';
              if (amount < 0) return 'المبلغ لا يمكن أن يكون سالباً';
              if (amount > total) return 'المبلغ المدفوع أكبر من الإجمالي';
              return null;
            },
            onChanged: (value) {
              setState(() {}); // لتحديث المبلغ المستحق
            },
          ),
          if (_paidAmountController.text.isNotEmpty) ...[
            SizedBox(height: 8.h),
            Container(
              padding: EdgeInsets.all(8.r),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('المبلغ المستحق:'),
                  Text(
                    AppFormatters.formatCurrency(_calculateDueAmount()),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _calculateDueAmount() > 0
                          ? Colors.orange[800]
                          : Colors.green[800],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ملاحظات',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 12.h),
          CustomTextField(
            controller: _notesController,
            label: 'ملاحظات إضافية (اختياري)',
            prefixIcon: const Icon(Icons.note),
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomSection() {
    final total = _items.fold<double>(0, (sum, item) => sum + item.total);

    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الإجمالي النهائي:',
                style: AppTextStyles.titleLarge.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                AppFormatters.formatCurrency(total),
                style: AppTextStyles.titleLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => context.pop(),
                  child: const Text('إلغاء'),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                flex: 2,
                child: CustomButton(
                  text: _isLoading ? 'جاري الحفظ...' : 'حفظ الفاتورة',
                  onPressed: _items.isEmpty || _isLoading ? null : _saveSale,
                  isLoading: _isLoading,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showAddProductDialog() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => const AddItemDialog(),
    );

    if (result != null && result['item'] != null) {
      setState(() {
        _items.add(result['item'] as SaleItem);
      });
    }
  }

  void _editItem(int index) async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => AddItemDialog(
        existingItem: _items[index],
        editIndex: index,
      ),
    );

    if (result != null && result['item'] != null) {
      setState(() {
        _items[index] = result['item'] as SaleItem;
      });
    }
  }

  void _removeItem(int index) {
    setState(() {
      _items.removeAt(index);
    });
  }

  double _calculateDueAmount() {
    final total = _items.fold<double>(0, (sum, item) => sum + item.total);
    final paid = double.tryParse(_paidAmountController.text) ?? 0;
    return total - paid;
  }

  void _saveSale() async {
    if (!_formKey.currentState!.validate()) return;
    if (_items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب إضافة منتج واحد على الأقل')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final paidAmount = double.tryParse(_paidAmountController.text) ?? 0;

      final saleId = await ref.read(saleManagementProvider.notifier).createSale(
            customerId: _selectedCustomer?.id,
            items: _items,
            paid: paidAmount,
            notes: _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
          );

      if (saleId != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ الفاتورة بنجاح')),
        );
        context.go('/sales/view/$saleId');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في حفظ الفاتورة: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

