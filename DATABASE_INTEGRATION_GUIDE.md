# دليل النظام التجاري المحاسبي المتكامل

## 🏢 نظام تجاري تك - النظام المحاسبي الشامل

نظام محاسبي متكامل مبني بتقنية Flutter يوفر جميع الميزات المطلوبة لإدارة الأعمال التجارية بشكل احترافي.

## 📋 الميزات الرئيسية

### 🛍️ إدارة المنتجات والمخزون
- ✅ إضافة وتعديل المنتجات
- ✅ إدارة الفئات والوحدات
- ✅ تتبع المخزون في مستودعات متعددة
- ✅ تسويات المخزون
- ✅ تقارير المنتجات الأكثر مبيعاً والراكدة

### 💰 العمليات التجارية
- ✅ فواتير المبيعات والمشتريات
- ✅ إدارة العملاء والموردين
- ✅ المدفوعات والمستحقات
- ✅ الخصومات والضرائب

### 📊 النظام المحاسبي
- ✅ القيود المحاسبية
- ✅ الحسابات البنكية والصناديق
- ✅ تقارير الأرباح والخسائر
- ✅ الميزانية العمومية

### 🔔 الميزات المتقدمة
- ✅ نظام الإشعارات
- ✅ إدارة المستخدمين والصلاحيات
- ✅ المرفقات والملفات
- ✅ الذكاء الاصطناعي والتنبؤات
- ✅ المزامنة مع الخادم
- ✅ سجل المراجعة والتدقيق

## 🏗️ البنية التقنية

### 📁 هيكل المشروع
```
lib/
├── data/
│   ├── local/
│   │   ├── database.dart          # قاعدة البيانات الرئيسية
│   │   └── dao/                   # طبقة الوصول للبيانات
│   │       ├── base_dao.dart      # الكلاس الأساسي
│   │       ├── product_dao.dart   # DAO المنتجات
│   │       ├── sale_dao.dart      # DAO المبيعات
│   │       └── purchase_dao.dart  # DAO المشتريات
│   ├── models/                    # نماذج البيانات
│   └── repositories/              # طبقة المستودعات
├── services/                      # طبقة الخدمات
├── core/
│   ├── exceptions/               # إدارة الأخطاء
│   └── utils/                    # الأدوات المساعدة
└── examples/                     # أمثلة الاستخدام
```

### 🗄️ قاعدة البيانات

#### الجداول الرئيسية:
- **products** - المنتجات
- **categories** - الفئات
- **units** - الوحدات
- **sales** - المبيعات
- **sale_items** - عناصر المبيعات
- **purchases** - المشتريات
- **purchase_items** - عناصر المشتريات
- **customers** - العملاء
- **suppliers** - الموردين
- **stocks** - المخزون
- **warehouses** - المستودعات

#### الجداول المتقدمة:
- **users** - المستخدمين
- **notifications** - الإشعارات
- **device_sessions** - جلسات الأجهزة
- **attachments** - المرفقات
- **ai_predictions** - توقعات الذكاء الاصطناعي
- **audit_log** - سجل المراجعة
- **settings** - الإعدادات

#### العروض (Views):
- **view_daily_sales_summary** - ملخص المبيعات اليومية
- **view_customer_account_statement** - كشف حساب العميل
- **view_supplier_account_statement** - كشف حساب المورد
- **view_sales_profit_summary** - ملخص الأرباح
- **view_top_selling_products** - الأصناف الأكثر مبيعاً
- **view_slow_moving_products** - الأصناف الراكدة

## 🚀 كيفية الاستخدام

### 1. إضافة منتج جديد

```dart
import 'package:your_app/services/business_service.dart';

final businessService = BusinessService();

// إضافة منتج
final productId = await businessService.addNewProduct(
  nameAr: 'لابتوب ديل XPS 13',
  nameEn: 'Dell XPS 13 Laptop',
  barcode: '*************',
  costPrice: 3500.0,
  sellingPrice: 4200.0,
  minStock: 5,
  maxStock: 50,
);
```

### 2. إنشاء عملية شراء

```dart
// إنشاء عملية شراء
final purchaseId = await businessService.createPurchaseTransaction(
  supplierId: 'supplier-1',
  items: [
    {
      'productId': 'product-1',
      'qty': 10.0,
      'unitPrice': 3500.0,
    },
  ],
  paidAmount: 10000.0,
  notes: 'شراء دفعة جديدة من اللابتوبات',
);
```

### 3. إنشاء عملية بيع

```dart
// إنشاء عملية بيع
final saleId = await businessService.createSaleTransaction(
  customerId: 'customer-1',
  items: [
    {
      'productId': 'product-1',
      'qty': 2.0,
      'unitPrice': 4200.0,
    },
  ],
  paidAmount: 8400.0,
  notes: 'بيع لابتوبين للعميل',
);
```

### 4. الحصول على التقارير

```dart
// تقرير المبيعات اليومية
final salesReport = await businessService.getDailySalesReport();
print('إجمالي المبيعات: ${salesReport['total_sales']} ر.س');

// تقرير المشتريات اليومية
final purchasesReport = await businessService.getDailyPurchasesReport();
print('إجمالي المشتريات: ${purchasesReport['total_purchases']} ر.س');
```

## 🔧 تشغيل الأمثلة

### تشغيل المثال الشامل:

```dart
import 'package:your_app/examples/run_example.dart';

void main() async {
  // تشغيل جميع الأمثلة
  await main();
}
```

### تشغيل أمثلة محددة:

```dart
import 'package:your_app/examples/business_operations_example.dart';

final example = BusinessOperationsExample();

// تشغيل المثال الشامل
await example.runCompleteExample();

// تحديث المخزون
await example.updateStockExample();

// البحث في المنتجات
await example.searchProductsExample();
```

## 📊 التقارير المتاحة

### تقارير المبيعات:
- المبيعات اليومية/الشهرية/السنوية
- أفضل العملاء
- المنتجات الأكثر مبيعاً
- تحليل الأرباح

### تقارير المشتريات:
- المشتريات اليومية/الشهرية/السنوية
- أفضل الموردين
- تحليل التكاليف

### تقارير المخزون:
- المنتجات منخفضة المخزون
- المنتجات الراكدة
- قيمة المخزون
- حركات المخزون

### التقارير المالية:
- الأرباح والخسائر
- التدفق النقدي
- المدفوعات المعلقة
- كشوف الحسابات

## 🛠️ التخصيص والتطوير

### إضافة جدول جديد:

1. **إنشاء النموذج (Model)**:
```dart
class MyModel {
  final String id;
  final String name;
  // ... باقي الخصائص
}
```

2. **إنشاء DAO**:
```dart
class MyModelDao extends BaseDao<MyModel> {
  @override
  String get tableName => 'my_table';
  
  @override
  MyModel fromMap(Map<String, dynamic> map) => MyModel.fromMap(map);
  
  @override
  Map<String, dynamic> toMap(MyModel entity) => entity.toMap();
}
```

3. **إنشاء Repository**:
```dart
class MyModelRepository {
  final MyModelDao _dao = MyModelDao();
  
  Future<String> create(MyModel model) async {
    return await _dao.insert(model);
  }
  
  // ... باقي العمليات
}
```

### إضافة تقرير جديد:

```dart
Future<List<Map<String, dynamic>>> getCustomReport() async {
  final sql = '''
    SELECT 
      column1,
      column2,
      SUM(amount) as total
    FROM my_table
    WHERE condition = ?
    GROUP BY column1, column2
    ORDER BY total DESC
  ''';
  
  return await _dao.rawQuery(sql, [parameter]);
}
```

## 🔒 الأمان والصلاحيات

- نظام مستخدمين متعدد المستويات
- تشفير كلمات المرور
- سجل مراجعة شامل
- صلاحيات مفصلة لكل عملية
- جلسات آمنة للأجهزة

## 📱 المزامنة

- مزامنة تلقائية مع الخادم
- عمل أوفلاين كامل
- حل تعارضات البيانات
- نسخ احتياطية تلقائية

## 🎯 الخطوات التالية

1. **تشغيل الأمثلة** لفهم النظام
2. **تخصيص النماذج** حسب احتياجاتك
3. **إضافة تقارير مخصصة**
4. **تطوير واجهة المستخدم**
5. **إعداد المزامنة مع الخادم**

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +966 11 234 5678
- 🌐 الموقع: www.tijaritech.com

---

**تم تطوير هذا النظام بواسطة فريق تجاري تك المتخصص في حلول الأعمال التقنية**
