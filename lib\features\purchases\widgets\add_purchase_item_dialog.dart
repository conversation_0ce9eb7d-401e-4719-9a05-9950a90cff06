import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/colors.dart';
import '../../../core/theme/text_styles.dart';
import '../../../core/utils/formatters.dart';
import '../../../data/models/product.dart';
import '../../../data/models/purchase_item.dart';
import '../../../widgets/custom_text_field.dart';

class AddPurchaseItemDialog extends StatefulWidget {
  final List<Product> products;
  final PurchaseItem? initialItem;

  const AddPurchaseItemDialog({
    super.key,
    required this.products,
    this.initialItem,
  });

  @override
  State<AddPurchaseItemDialog> createState() => _AddPurchaseItemDialogState();
}

class _AddPurchaseItemDialogState extends State<AddPurchaseItemDialog> {
  final _formKey = GlobalKey<FormState>();
  final _quantityController = TextEditingController();
  final _priceController = TextEditingController();
  final _searchController = TextEditingController();

  String? _selectedProductId;
  List<Product> _filteredProducts = [];

  @override
  void initState() {
    super.initState();
    _filteredProducts = widget.products;

    if (widget.initialItem != null) {
      _selectedProductId = widget.initialItem!.productId;
      _quantityController.text = widget.initialItem!.qty.toString();
      _priceController.text = widget.initialItem!.unitPrice.toString();
    }
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _priceController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _filterProducts(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredProducts = widget.products;
      } else {
        _filteredProducts = widget.products
            .where((product) =>
                product.nameAr.toLowerCase().contains(query.toLowerCase()) ||
                product.barcode?.toLowerCase().contains(query.toLowerCase()) ==
                    true)
            .toList();
      }
    });
  }

  Product? get _selectedProduct {
    if (_selectedProductId == null) return null;
    return widget.products.firstWhere(
      (p) => p.id == _selectedProductId,
      orElse: () => Product.create(
        id: 'unknown',
        nameAr: 'منتج غير موجود',
        categoryId: '',
        baseUnitId: '',
        sellingPrice: 0,
        costPrice: 0,
      ),
    );
  }

  double get _quantity => double.tryParse(_quantityController.text) ?? 0.0;
  double get _unitPrice => double.tryParse(_priceController.text) ?? 0.0;
  double get _totalPrice => _quantity * _unitPrice;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500.w,
        constraints: BoxConstraints(maxHeight: 600.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(16.r),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.r),
                  topRight: Radius.circular(12.r),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.add_shopping_cart,
                    color: AppColors.onPrimary,
                    size: 24.r,
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Text(
                      widget.initialItem != null
                          ? 'تعديل المنتج'
                          : 'إضافة منتج',
                      style: AppTextStyles.titleMedium.copyWith(
                        color: AppColors.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: AppColors.onPrimary,
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: Form(
                key: _formKey,
                child: Padding(
                  padding: EdgeInsets.all(16.r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Product Search
                      CustomTextField(
                        controller: _searchController,
                        label: 'البحث عن منتج',
                        prefixIcon: const Icon(Icons.search),
                        onChanged: _filterProducts,
                      ),
                      SizedBox(height: 16.h),

                      // Product List
                      Text(
                        'اختر المنتج:',
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Expanded(
                        flex: 2,
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.outline),
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: ListView.builder(
                            itemCount: _filteredProducts.length,
                            itemBuilder: (context, index) {
                              final product = _filteredProducts[index];
                              final isSelected =
                                  _selectedProductId == product.id;

                              return ListTile(
                                selected: isSelected,
                                selectedTileColor:
                                    AppColors.primary.withValues(alpha: 0.1),
                                leading: CircleAvatar(
                                  backgroundColor: isSelected
                                      ? AppColors.primary
                                      : AppColors.surface,
                                  child: Icon(
                                    Icons.inventory_2,
                                    color: isSelected
                                        ? AppColors.onPrimary
                                        : AppColors.onSurface,
                                    size: 20.r,
                                  ),
                                ),
                                title: Text(
                                  product.nameAr,
                                  style: AppTextStyles.bodyMedium.copyWith(
                                    fontWeight: isSelected
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                  ),
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    if (product.barcode != null)
                                      Text('الباركود: ${product.barcode}'),
                                    Text(
                                        'سعر الشراء: ${AppFormatters.formatCurrency(product.costPrice)}'),
                                    if (product.currentStock != null)
                                      Text('المخزون: ${product.currentStock}'),
                                  ],
                                ),
                                onTap: () {
                                  setState(() {
                                    _selectedProductId = product.id;
                                    _priceController.text =
                                        product.costPrice.toString();
                                  });
                                },
                              );
                            },
                          ),
                        ),
                      ),
                      SizedBox(height: 16.h),

                      // Quantity and Price
                      Row(
                        children: [
                          Expanded(
                            child: CustomTextField(
                              controller: _quantityController,
                              label: 'الكمية',
                              keyboardType: TextInputType.number,
                              prefixIcon: const Icon(Icons.numbers),
                              onChanged: (value) => setState(() {}),
                              validator: (value) {
                                final qty = double.tryParse(value ?? '');
                                if (qty == null || qty <= 0) {
                                  return 'يرجى إدخال كمية صحيحة';
                                }
                                return null;
                              },
                            ),
                          ),
                          SizedBox(width: 16.w),
                          Expanded(
                            child: CustomTextField(
                              controller: _priceController,
                              label: 'سعر الوحدة',
                              keyboardType: TextInputType.number,
                              prefixIcon: const Icon(Icons.attach_money),
                              onChanged: (value) => setState(() {}),
                              validator: (value) {
                                final price = double.tryParse(value ?? '');
                                if (price == null || price < 0) {
                                  return 'يرجى إدخال سعر صحيح';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 16.h),

                      // Total
                      Container(
                        padding: EdgeInsets.all(12.r),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8.r),
                          border: Border.all(
                              color: AppColors.primary.withValues(alpha: 0.3)),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'الإجمالي:',
                              style: AppTextStyles.bodyLarge.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              AppFormatters.formatCurrency(_totalPrice),
                              style: AppTextStyles.bodyLarge.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppColors.primary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Actions
            Container(
              padding: EdgeInsets.all(16.r),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                      color: AppColors.outline.withValues(alpha: 0.3)),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                  SizedBox(width: 12.w),
                  ElevatedButton(
                    onPressed: _selectedProductId != null ? _saveItem : null,
                    child: Text(widget.initialItem != null ? 'تحديث' : 'إضافة'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _saveItem() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedProductId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار منتج'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final item = PurchaseItem.create(
      purchaseId: '',
      productId: _selectedProductId!,
      qty: _quantity,
      unitPrice: _unitPrice,
      unitId: _selectedProduct?.baseUnitId ?? '',
    );

    Navigator.of(context).pop(item);
  }
}
