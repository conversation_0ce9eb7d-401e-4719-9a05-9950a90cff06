import 'package:flutter/material.dart';
import 'validators.dart';

// Form field configuration
class FormFieldConfig {
  final String key;
  final Validator<String>? validator;
  final String? initialValue;
  final bool isRequired;

  const FormFieldConfig({
    required this.key,
    this.validator,
    this.initialValue,
    this.isRequired = false,
  });
}

// Form validation result
class ValidationResult {
  final bool isValid;
  final Map<String, String> errors;

  const ValidationResult({
    required this.isValid,
    required this.errors,
  });

  ValidationResult.valid() : this(isValid: true, errors: {});
  
  ValidationResult.invalid(Map<String, String> errors) 
      : this(isValid: false, errors: errors);

  String? getError(String key) => errors[key];
  bool hasError(String key) => errors.containsKey(key);
}

// Form validator class
class FormValidator {
  final Map<String, FormFieldConfig> _fields = {};
  final Map<String, String> _values = {};
  final Map<String, String> _errors = {};

  // Add field configuration
  void addField(FormFieldConfig config) {
    _fields[config.key] = config;
    if (config.initialValue != null) {
      _values[config.key] = config.initialValue!;
    }
  }

  // Add multiple fields
  void addFields(List<FormFieldConfig> configs) {
    for (final config in configs) {
      addField(config);
    }
  }

  // Set field value
  void setValue(String key, String? value) {
    if (value != null) {
      _values[key] = value;
    } else {
      _values.remove(key);
    }
    _validateField(key);
  }

  // Get field value
  String? getValue(String key) => _values[key];

  // Get all values
  Map<String, String> getValues() => Map.from(_values);

  // Validate single field
  String? _validateField(String key) {
    final config = _fields[key];
    if (config == null) return null;

    final value = _values[key];
    final error = config.validator?.validate(value);

    if (error != null) {
      _errors[key] = error;
    } else {
      _errors.remove(key);
    }

    return error;
  }

  // Validate all fields
  ValidationResult validate() {
    _errors.clear();

    for (final key in _fields.keys) {
      _validateField(key);
    }

    return ValidationResult(
      isValid: _errors.isEmpty,
      errors: Map.from(_errors),
    );
  }

  // Get field error
  String? getFieldError(String key) => _errors[key];

  // Check if field has error
  bool hasFieldError(String key) => _errors.containsKey(key);

  // Get all errors
  Map<String, String> getErrors() => Map.from(_errors);

  // Clear all errors
  void clearErrors() {
    _errors.clear();
  }

  // Clear specific field error
  void clearFieldError(String key) {
    _errors.remove(key);
  }

  // Check if form is valid
  bool get isValid => _errors.isEmpty;

  // Check if form has any errors
  bool get hasErrors => _errors.isNotEmpty;

  // Reset form
  void reset() {
    _values.clear();
    _errors.clear();
    
    // Set initial values
    for (final config in _fields.values) {
      if (config.initialValue != null) {
        _values[config.key] = config.initialValue!;
      }
    }
  }

  // Create text form field validator
  String? Function(String?) createFieldValidator(String key) {
    return (value) {
      setValue(key, value);
      return getFieldError(key);
    };
  }
}

// Form validation mixin for widgets
mixin FormValidationMixin<T extends StatefulWidget> on State<T> {
  final FormValidator formValidator = FormValidator();

  // Initialize form fields
  void initializeFormFields(List<FormFieldConfig> configs) {
    formValidator.addFields(configs);
  }

  // Validate form
  ValidationResult validateForm() {
    final result = formValidator.validate();
    setState(() {}); // Trigger rebuild to show errors
    return result;
  }

  // Create field validator
  String? Function(String?) createFieldValidator(String key) {
    return formValidator.createFieldValidator(key);
  }

  // Get field error
  String? getFieldError(String key) => formValidator.getFieldError(key);

  // Set field value
  void setFieldValue(String key, String? value) {
    formValidator.setValue(key, value);
    setState(() {}); // Trigger rebuild to update errors
  }

  // Get field value
  String? getFieldValue(String key) => formValidator.getValue(key);

  // Clear field error
  void clearFieldError(String key) {
    formValidator.clearFieldError(key);
    setState(() {});
  }

  // Reset form
  void resetForm() {
    formValidator.reset();
    setState(() {});
  }
}

// Validation utilities
class ValidationUtils {
  // Validate form and show first error
  static bool validateAndShowError(
    BuildContext context,
    FormValidator validator, {
    String? title,
  }) {
    final result = validator.validate();
    
    if (!result.isValid) {
      final firstError = result.errors.values.first;
      _showErrorDialog(context, firstError, title: title);
      return false;
    }
    
    return true;
  }

  // Show error dialog
  static void _showErrorDialog(
    BuildContext context,
    String message, {
    String? title,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title ?? 'خطأ في البيانات'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  // Show validation errors as snackbar
  static void showValidationErrors(
    BuildContext context,
    ValidationResult result,
  ) {
    if (!result.isValid && result.errors.isNotEmpty) {
      final firstError = result.errors.values.first;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(firstError),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  // Create common form field configurations
  static List<FormFieldConfig> createProductFormFields() {
    return [
      FormFieldConfig(
        key: 'nameAr',
        validator: ValidationHelper.requiredText(
          minLength: 2,
          maxLength: 100,
        ),
        isRequired: true,
      ),
      FormFieldConfig(
        key: 'nameEn',
        validator: ValidationHelper.optionalText(
          maxLength: 100,
        ),
      ),
      FormFieldConfig(
        key: 'barcode',
        validator: ValidationHelper.barcode(),
      ),
      FormFieldConfig(
        key: 'reorderPoint',
        validator: ValidationHelper.optionalNumber(
          minValue: 0,
        ),
      ),
    ];
  }

  static List<FormFieldConfig> createCustomerFormFields() {
    return [
      FormFieldConfig(
        key: 'name',
        validator: ValidationHelper.requiredText(
          minLength: 2,
          maxLength: 100,
        ),
        isRequired: true,
      ),
      FormFieldConfig(
        key: 'phone',
        validator: ValidationHelper.optionalPhone(),
      ),
      FormFieldConfig(
        key: 'email',
        validator: ValidationHelper.optionalEmail(),
      ),
    ];
  }

  static List<FormFieldConfig> createSupplierFormFields() {
    return [
      FormFieldConfig(
        key: 'name',
        validator: ValidationHelper.requiredText(
          minLength: 2,
          maxLength: 100,
        ),
        isRequired: true,
      ),
      FormFieldConfig(
        key: 'phone',
        validator: ValidationHelper.optionalPhone(),
      ),
      FormFieldConfig(
        key: 'email',
        validator: ValidationHelper.optionalEmail(),
      ),
    ];
  }

  static List<FormFieldConfig> createBranchFormFields() {
    return [
      FormFieldConfig(
        key: 'name',
        validator: ValidationHelper.requiredText(
          minLength: 2,
          maxLength: 100,
        ),
        isRequired: true,
      ),
      FormFieldConfig(
        key: 'address',
        validator: ValidationHelper.optionalText(
          maxLength: 500,
        ),
      ),
    ];
  }

  static List<FormFieldConfig> createCategoryFormFields() {
    return [
      FormFieldConfig(
        key: 'nameAr',
        validator: ValidationHelper.requiredText(
          minLength: 2,
          maxLength: 100,
        ),
        isRequired: true,
      ),
      FormFieldConfig(
        key: 'nameEn',
        validator: ValidationHelper.optionalText(
          maxLength: 100,
        ),
      ),
    ];
  }

  static List<FormFieldConfig> createUnitFormFields() {
    return [
      FormFieldConfig(
        key: 'name',
        validator: ValidationHelper.requiredText(
          minLength: 1,
          maxLength: 50,
        ),
        isRequired: true,
      ),
      FormFieldConfig(
        key: 'factor',
        validator: ValidationHelper.requiredNumber(
          minValue: 0.001,
          requiredMessage: 'معامل التحويل مطلوب',
          minValueMessage: 'معامل التحويل يجب أن يكون أكبر من صفر',
        ),
        isRequired: true,
      ),
    ];
  }
}
