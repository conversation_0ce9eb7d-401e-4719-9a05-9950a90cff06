import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:tijari_tech/core/utils/extensions.dart';
import 'package:tijari_tech/data/models/unit.dart';
import 'package:tijari_tech/features/units/add_unit_dialog.dart';
import 'package:tijari_tech/features/units/create_uint_dailog.dart';

import '../../../../core/theme/colors.dart';
import '../../../../core/theme/text_styles.dart';
import '../../../providers/unit_provider.dart';
import '../../../router/routes.dart';
import '../../../../widgets/main_layout.dart';

class UnitsManagementPage extends ConsumerStatefulWidget {
  const UnitsManagementPage({super.key});

  @override
  ConsumerState<UnitsManagementPage> createState() =>
      _UnitsManagementPageState();
}

class _UnitsManagementPageState extends ConsumerState<UnitsManagementPage> {
  final _searchController = TextEditingController();
  bool _showInactive = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final unitState = ref.watch(unitManagementProvider);

    return MainLayout(
      title: 'إدارة الوحدات',
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton.extended(
            heroTag: 'addUnit', // أو أي اسم مميز

            onPressed: () => _showAddUnitDialog(context),
            icon: const Icon(Icons.add),
            label: const Text('إضافة وحدة'),
          ),
          SizedBox(height: 8.h),
          FloatingActionButton.extended(
            heroTag: null,
            onPressed: () => _showCreateUnitTreeDialog(context),
            icon: const Icon(Icons.account_tree),
            label: const Text('إنشاء شجرة'),
            backgroundColor: AppColors.secondary,
          ),
        ],
      ),
      child: _buildUnitsTreePanel(context, unitState),
    );
  }

  Widget _buildUnitsTreePanel(BuildContext context, UnitManagementState state) {
    return Container(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTreePanelHeader(context, state),
          SizedBox(height: 13.h),
          Expanded(child: _buildUnitsTree(context, state)),
        ],
      ),
    );
  }

  Widget _buildTreePanelHeader(
      BuildContext context, UnitManagementState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text('شجرة الوحدات',
                style: AppTextStyles.titleLarge
                    .copyWith(fontWeight: FontWeight.bold)),
            const Spacer(),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () =>
                  ref.read(unitManagementProvider.notifier).refresh(),
              tooltip: 'تحديث',
            ),
            IconButton(
              icon: const Icon(Icons.expand_more),
              onPressed: () =>
                  ref.read(unitManagementProvider.notifier).expandAllUnits(),
              tooltip: 'توسيع الكل',
            ),
            IconButton(
              icon: const Icon(Icons.expand_less),
              onPressed: () =>
                  ref.read(unitManagementProvider.notifier).collapseAllUnits(),
              tooltip: 'طي الكل',
            ),
            IconButton(
              icon: const Icon(Icons.add_chart),
              onPressed: () => _showStatisticsDialog(context, state.statistics),
              tooltip: 'إحصائيات',
            ),
          ],
        ),
        SizedBox(height: 16.h),
        TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'البحث في الوحدات...',
            prefixIcon: const Icon(Icons.search),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _searchController.clear();
                      ref.read(unitManagementProvider.notifier).searchUnits('');
                    },
                  )
                : null,
            border:
                OutlineInputBorder(borderRadius: BorderRadius.circular(12.r)),
          ),
          onChanged: (value) =>
              ref.read(unitManagementProvider.notifier).searchUnits(value),
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            FilterChip(
              label: const Text('إظهار غير النشطة'),
              selected: _showInactive,
              onSelected: (selected) {
                setState(() => _showInactive = selected);
                ref
                    .read(unitManagementProvider.notifier)
                    .toggleShowInactive(selected);
              },
            ),
            SizedBox(width: 8.w),
            Chip(
              label: Text('${state.filteredUnits.length} وحدة'),
              backgroundColor: AppColors.primary.withValues(alpha: 0.1),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        // عرض أنواع الوحدات كفلاتر
        SizedBox(
          height: 40.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: state.unitsByType.keys.length + 1,
            itemBuilder: (context, index) {
              if (index == 0) {
                return Padding(
                  padding: EdgeInsets.only(left: 8.w, right: 4.w),
                  child: FilterChip(
                    label: const Text('الكل'),
                    selected: state.selectedUnitType == null,
                    onSelected: (selected) {
                      ref
                          .read(unitManagementProvider.notifier)
                          .filterByUnitType(null);
                    },
                  ),
                );
              }

              final unitType = state.unitsByType.keys.elementAt(index - 1);
              final typeDisplayName = _getUnitTypeDisplayName(unitType);
              final unitCount = state.unitsByType[unitType]?.length ?? 0;

              return Padding(
                padding: EdgeInsets.symmetric(horizontal: 4.w),
                child: FilterChip(
                  label: Text('$typeDisplayName ($unitCount)'),
                  selected: state.selectedUnitType == unitType,
                  onSelected: (selected) {
                    ref.read(unitManagementProvider.notifier).filterByUnitType(
                          selected ? unitType : null,
                        );
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildUnitsTree(BuildContext context, UnitManagementState state) {
    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.error != null) {
      return _errorWidget(state.error!);
    }

    if (state.filteredUnits.isEmpty) {
      return _emptyWidget(state.searchQuery);
    }

    return ListView.builder(
      itemCount: state.filteredUnits.length,
      itemBuilder: (_, index) =>
          _buildUnitTreeItem(context, state, state.filteredUnits[index], 0),
    );
  }

  Widget _buildUnitTreeItem(
      BuildContext context, UnitManagementState state, Unit unit, int level) {
    final isExpanded = state.expandedUnits[unit.id] ?? false;
    final hasChildren = unit.derivedUnits?.isNotEmpty ?? false;
    final branchCount = state.branchUsageCount[unit.id] ?? 0;
    final categoryCount = state.categoryUsageCount[unit.id] ?? 0;

    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(left: (level * 20.0).w),
          child: Card(
            elevation: 1,
            child: ListTile(
              leading: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (hasChildren)
                    IconButton(
                      icon: Icon(
                        isExpanded ? Icons.expand_less : Icons.expand_more,
                        size: 20.r,
                        color: Colors.grey[600],
                      ),
                      onPressed: () => ref
                          .read(unitManagementProvider.notifier)
                          .toggleUnitExpansion(unit.id),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    )
                  else
                    SizedBox(width: 20.r),
                  Container(
                    width: 8.w,
                    height: 8.w,
                    decoration: BoxDecoration(
                      color: unit.isBaseUnitType
                          ? AppColors.primary
                          : AppColors.secondary,
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),
              title: Text(unit.name, style: AppTextStyles.bodyMedium),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    unit.symbol ??
                        (unit.isBaseUnit
                            ? 'وحدة أساسية'
                            : 'وحدة فرعية (${unit.factor}x)'),
                    style: AppTextStyles.bodySmall,
                  ),
                  if (branchCount > 0 ||
                      categoryCount > 0 ||
                      (unit.usageCount ?? 0) > 0)
                    Row(
                      children: [
                        if (unit.usageCount != null && unit.usageCount! > 0)
                          _buildUsageChip(
                              'منتجات', unit.usageCount!, Colors.blue),
                        if (branchCount > 0)
                          _buildUsageChip('فروع', branchCount, Colors.green),
                        if (categoryCount > 0)
                          _buildUsageChip(
                              'تصنيفات', categoryCount, Colors.orange),
                      ],
                    ),
                ],
              ),
              trailing: _popupMenu(context, unit),
              onTap: () => _openDetailsPage(context, unit),
            ),
          ),
        ),
        if (isExpanded && hasChildren)
          ...unit.derivedUnits!.map(
              (child) => _buildUnitTreeItem(context, state, child, level + 1)),
      ],
    );
  }

  Widget _buildUsageChip(String label, int count, Color color) {
    return Container(
      margin: EdgeInsets.only(right: 4.w, top: 2.h),
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        '$label: $count',
        style: AppTextStyles.bodySmall.copyWith(
          color: color,
          fontSize: 10.sp,
        ),
      ),
    );
  }

  Widget _popupMenu(BuildContext context, Unit unit) {
    return PopupMenuButton<String>(
      onSelected: (value) => _handleUnitAction(context, unit, value),
      itemBuilder: (_) => [
        const PopupMenuItem(value: 'edit', child: Text('تعديل')),
        if (unit.isBaseUnitType)
          const PopupMenuItem(
              value: 'add_derived', child: Text('إضافة وحدة مشتقة')),
        const PopupMenuItem(value: 'toggle_active', child: Text('تفعيل/تعطيل')),
        const PopupMenuItem(
            value: 'delete',
            child: Text('حذف', style: TextStyle(color: Colors.red))),
      ],
    );
  }

  void _openDetailsPage(BuildContext context, Unit unit) {
    // Navigator.of(context).push(
    //   MaterialPageRoute(builder: (_) {
    //     UnitDetailsPage(unit: unit);
    //   }),
    // );
  }

  Widget _errorWidget(String error) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64.r, color: AppColors.error),
            SizedBox(height: 16.h),
            Text('حدث خطأ', style: AppTextStyles.titleMedium),
            Text(
              error,
              style: AppTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () =>
                  ref.read(unitManagementProvider.notifier).refresh(),
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );

  Widget _emptyWidget(String searchQuery) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.straighten, size: 64.r, color: Colors.grey[400]),
            SizedBox(height: 16.h),
            Text(
              searchQuery.isNotEmpty ? 'لا توجد نتائج' : 'لا توجد وحدات',
              style: AppTextStyles.titleMedium,
            ),
            Text(
              searchQuery.isNotEmpty
                  ? 'جرب البحث بكلمات أخرى'
                  : 'ابدأ بإضافة وحدة جديدة',
              style: AppTextStyles.bodyMedium.copyWith(color: Colors.grey[500]),
            ),
            if (searchQuery.isEmpty) ...[
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => _showAddUnitDialog(context),
                child: const Text('إضافة وحدة'),
              ),
              SizedBox(height: 8.h),
              OutlinedButton(
                onPressed: () => _showCreateUnitTreeDialog(context),
                child: const Text('إنشاء شجرة وحدات'),
              ),
              SizedBox(height: 8.h),
              TextButton(
                onPressed: () => ref
                    .read(unitManagementProvider.notifier)
                    .createDefaultUnits(),
                child: const Text('إنشاء وحدات افتراضية'),
              ),
            ],
          ],
        ),
      );

  void _handleUnitAction(BuildContext context, Unit unit, String action) {
    switch (action) {
      case 'edit':
        _showEditUnitDialog(context, unit);
        break;
      case 'add_derived':
        _showAddDerivedUnitDialog(context, unit);
        break;
      case 'toggle_active':
        _toggleUnitActive(context, unit);
        break;
      case 'delete':
        _showDeleteConfirmation(context, unit);
        break;
    }
  }

  void _showAddUnitDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (_) => const AddEditUnitDialog(),
    );
  }

  void _showEditUnitDialog(BuildContext context, Unit unit) {
    showDialog(
      context: context,
      builder: (_) => AddEditUnitDialog(unit: unit),
    );
  }

  void _showAddDerivedUnitDialog(BuildContext context, Unit baseUnit) {
    showDialog(
      context: context,
      builder: (_) => AddEditUnitDialog(baseUnit: baseUnit),
    );
  }

  void _showCreateUnitTreeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (_) => const CreateUnitTreeDialog(),
    );
  }

  void _toggleUnitActive(BuildContext context, Unit unit) {
    ref.read(unitManagementProvider.notifier).updateUnit(
          id: unit.id,
          isActive: !unit.isActiveUnit,
        );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text('تم ${unit.isActiveUnit ? 'تعطيل' : 'تفعيل'} الوحدة بنجاح'),
        backgroundColor: unit.isActiveUnit ? Colors.orange : Colors.green,
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, Unit unit) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل أنت متأكد من حذف وحدة "${unit.name}"؟'),
            if (unit.usageCount != null && unit.usageCount! > 0) ...[
              SizedBox(height: 8.h),
              Text(
                'هذه الوحدة مستخدمة في ${unit.usageCount} منتج',
                style: TextStyle(color: Colors.red[700]),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(unitManagementProvider.notifier).deleteUnit(unit.id);
              context.showSuccessSnackBar('تم حذف الوحدة بنجاح');
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showStatisticsDialog(
      BuildContext context, Map<String, dynamic> statistics) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('إحصائيات الوحدات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _StatisticRow(
                'إجمالي الوحدات', '${statistics['total_units'] ?? 0}'),
            _StatisticRow(
                'الوحدات النشطة', '${statistics['active_units'] ?? 0}'),
            _StatisticRow(
                'الوحدات الأساسية', '${statistics['base_units'] ?? 0}'),
            _StatisticRow(
                'أنواع الوحدات', '${statistics['unit_types_count'] ?? 0}'),
            _StatisticRow('المنتجات المستخدمة',
                '${statistics['total_products_using_units'] ?? 0}'),
            _StatisticRow('الفروع', '${statistics['total_branches'] ?? 0}'),
            _StatisticRow(
                'التصنيفات', '${statistics['total_categories'] ?? 0}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  String _getUnitTypeDisplayName(String unitType) {
    switch (unitType) {
      case 'weight':
        return 'وزن';
      case 'length':
        return 'طول';
      case 'volume':
        return 'حجم';
      case 'area':
        return 'مساحة';
      case 'count':
        return 'عدد';
      case 'time':
        return 'وقت';
      case 'other':
        return 'أخرى';
      default:
        return unitType;
    }
  }
}

class _StatisticRow extends StatelessWidget {
  final String label;
  final String value;

  const _StatisticRow(this.label, this.value);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
          Text(value),
        ],
      ),
    );
  }
}
