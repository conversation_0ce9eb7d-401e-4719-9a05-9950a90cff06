import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/colors.dart';
import '../../../core/theme/text_styles.dart';
import '../../../data/models/product.dart';
import '../../../data/models/stock.dart';
import '../../../providers/enhanced_product_provider.dart';
import '../../../providers/stock_provider.dart';
import '../../../widgets/custom_text_field.dart';

class StockAdjustmentItemDialog extends ConsumerStatefulWidget {
  final String warehouseId;
  final Map<String, dynamic>? existingItem;

  const StockAdjustmentItemDialog({
    super.key,
    required this.warehouseId,
    this.existingItem,
  });

  @override
  ConsumerState<StockAdjustmentItemDialog> createState() =>
      _StockAdjustmentItemDialogState();
}

class _StockAdjustmentItemDialogState
    extends ConsumerState<StockAdjustmentItemDialog> {
  final _formKey = GlobalKey<FormState>();
  final _searchController = TextEditingController();
  final _adjustmentQtyController = TextEditingController();
  final _notesController = TextEditingController();

  Product? _selectedProduct;
  Stock? _currentStock;
  String _adjustmentType = 'increase';
  String _reason = 'تالف';
  bool _isLoading = false;

  final List<String> _adjustmentReasons = [
    'تالف',
    'فاقد',
    'هدية',
    'عينة',
    'انتهاء صلاحية',
    'خطأ جرد',
    'تصحيح',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    if (widget.existingItem != null) {
      final item = widget.existingItem!;
      _adjustmentType = item['type'] ?? 'increase';
      _reason = item['reason'] ?? 'تالف';
      _adjustmentQtyController.text = item['adjustmentQty']?.toString() ?? '';
      _notesController.text = item['notes'] ?? '';
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _adjustmentQtyController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600.w,
        constraints: BoxConstraints(maxHeight: 700.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(24.r),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildProductSelection(),
                        SizedBox(height: 16.h),
                        if (_selectedProduct != null) ...[
                          _buildCurrentStockInfo(),
                          SizedBox(height: 16.h),
                          _buildAdjustmentDetails(),
                          SizedBox(height: 16.h),
                          _buildCalculatedResult(),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(24.r),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.inventory_2_outlined,
            color: AppColors.primary,
            size: 24.r,
          ),
          SizedBox(width: 12.w),
          Text(
            widget.existingItem != null
                ? 'تعديل صنف التسوية'
                : 'إضافة صنف للتسوية',
            style: AppTextStyles.titleLarge.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close),
          ),
        ],
      ),
    );
  }

  Widget _buildProductSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'اختيار المنتج',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 8.h),
        CustomTextField(
          controller: _searchController,
          label: 'البحث عن منتج',
          hint: 'ادخل اسم المنتج أو الباركود...',
          prefixIcon:const Icon(Icons.search),
          onChanged: _searchProducts,
          suffixIcon: _selectedProduct != null
              ? IconButton(
                  onPressed: _clearSelection,
                  icon: const Icon(Icons.clear),
                )
              : null,
        ),
        if (_selectedProduct != null) ...[
          SizedBox(height: 8.h),
          _buildSelectedProductCard(),
        ] else ...[
          SizedBox(height: 8.h),
          _buildProductSearchResults(),
        ],
      ],
    );
  }

  Widget _buildSelectedProductCard() {
    return Card(
      color: AppColors.success.withValues(alpha: 0.1),
      child: Padding(
        padding: EdgeInsets.all(12.r),
        child: Row(
          children: [
            Icon(
              Icons.check_circle,
              color: AppColors.success,
              size: 20.r,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _selectedProduct!.nameAr,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (_selectedProduct!.nameAr.isNotEmpty)
                    Text(
                      'الباركود: ${_selectedProduct!.barcode}',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductSearchResults() {
    final productState = ref.watch(enhancedProductManagementProvider);

    if (productState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (productState.filteredProducts.isEmpty) {
      return Container(
        padding: EdgeInsets.all(16.r),
        child: Text(
          'لا توجد منتجات',
          style: AppTextStyles.bodyMedium.copyWith(
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      );
    }

    return Container(
      height: 200.h,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: ListView.builder(
        itemCount: productState.filteredProducts.take(10).length,
        itemBuilder: (context, index) {
          final product = productState.filteredProducts[index];
          return ListTile(
            title: Text(product.nameAr),
            subtitle: product.nameAr.isNotEmpty
                ? Text('الباركود: ${product.barcode}')
                : null,
            onTap: () => _selectProduct(product),
          );
        },
      ),
    );
  }

  Widget _buildCurrentStockInfo() {
    if (_currentStock == null) {
      return Card(
        color: AppColors.warning.withValues(alpha: 0.1),
        child: Padding(
          padding: EdgeInsets.all(12.r),
          child: Row(
            children: [
              Icon(
                Icons.warning_outlined,
                color: AppColors.warning,
                size: 20.r,
              ),
              SizedBox(width: 12.w),
              Text(
                'لا يوجد مخزون لهذا المنتج في المخزن المحدد',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.warning,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      color: AppColors.info.withValues(alpha: 0.1),
      child: Padding(
        padding: EdgeInsets.all(12.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات المخزون الحالي',
              style: AppTextStyles.titleSmall.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.info,
              ),
            ),
            SizedBox(height: 8.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('الكمية الحالية:'),
                Text(
                  '${_currentStock!.quantity}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            if (_currentStock!.reservedQuantity > 0) ...[
              SizedBox(height: 4.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('الكمية المحجوزة:'),
                  Text(
                    '${_currentStock!.reservedQuantity}',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.warning,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
            SizedBox(height: 4.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('الكمية المتاحة:'),
                Text(
                  '${_currentStock!.quantity - _currentStock!.reservedQuantity}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.success,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdjustmentDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تفاصيل التسوية',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 12.h),

        // Adjustment Type
        Row(
          children: [
            Expanded(
              child: RadioListTile<String>(
                title: const Text('زيادة'),
                value: 'increase',
                groupValue: _adjustmentType,
                onChanged: (value) {
                  setState(() {
                    _adjustmentType = value!;
                  });
                },
                activeColor: AppColors.success,
              ),
            ),
            Expanded(
              child: RadioListTile<String>(
                title: const Text('نقص'),
                value: 'decrease',
                groupValue: _adjustmentType,
                onChanged: (value) {
                  setState(() {
                    _adjustmentType = value!;
                  });
                },
                activeColor: AppColors.error,
              ),
            ),
          ],
        ),

        SizedBox(height: 16.h),

        // Quantity and Reason
        Row(
          children: [
            Expanded(
              flex: 2,
              child: TextFormField(
                controller: _adjustmentQtyController,
                decoration: const InputDecoration(
                  labelText: 'الكمية',
                  hintText: '0.00',
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الكمية مطلوبة';
                  }
                  final qty = double.tryParse(value);
                  if (qty == null || qty <= 0) {
                    return 'الكمية يجب أن تكون أكبر من صفر';
                  }
                  return null;
                },
                onChanged: (value) => setState(() {}),
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              flex: 3,
              child: DropdownButtonFormField<String>(
                value: _reason,
                decoration: const InputDecoration(
                  labelText: 'السبب',
                ),
                items: _adjustmentReasons.map((reason) {
                  return DropdownMenuItem(
                    value: reason,
                    child: Text(reason),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _reason = value!;
                  });
                },
              ),
            ),
          ],
        ),

        SizedBox(height: 16.h),

        // Notes
        TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'ملاحظات (اختياري)',
            hintText: 'أدخل ملاحظات إضافية...',
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildCalculatedResult() {
    final adjustmentQty = double.tryParse(_adjustmentQtyController.text) ?? 0;
    final currentQty = _currentStock?.quantity ?? 0;
    final newQty = _adjustmentType == 'increase'
        ? currentQty + adjustmentQty
        : currentQty - adjustmentQty;

    return Card(
      color: _adjustmentType == 'increase'
          ? AppColors.success.withValues(alpha: 0.1)
          : AppColors.error.withValues(alpha: 0.1),
      child: Padding(
        padding: EdgeInsets.all(12.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نتيجة التسوية',
              style: AppTextStyles.titleSmall.copyWith(
                fontWeight: FontWeight.w600,
                color: _adjustmentType == 'increase'
                    ? AppColors.success
                    : AppColors.error,
              ),
            ),
            SizedBox(height: 8.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('الكمية الحالية:'),
                Text('$currentQty'),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('${_adjustmentType == 'increase' ? 'زيادة' : 'نقص'}:'),
                Text(
                  '${_adjustmentType == 'increase' ? '+' : '-'}$adjustmentQty',
                  style: TextStyle(
                    color: _adjustmentType == 'increase'
                        ? AppColors.success
                        : AppColors.error,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الكمية الجديدة:',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '$newQty',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: newQty < 0 ? AppColors.error : AppColors.success,
                  ),
                ),
              ],
            ),
            if (newQty < 0)
              Padding(
                padding: EdgeInsets.only(top: 8.h),
                child: Text(
                  'تحذير: الكمية الجديدة ستكون سالبة!',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.error,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: EdgeInsets.all(24.r),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(12.r),
          bottomRight: Radius.circular(12.r),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: ElevatedButton(
              onPressed: _selectedProduct != null &&
                      _adjustmentQtyController.text.isNotEmpty &&
                      !_isLoading
                  ? _saveItem
                  : null,
              child: _isLoading
                  ? SizedBox(
                      height: 20.h,
                      width: 20.w,
                      child: const CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Text(widget.existingItem != null ? 'تحديث' : 'إضافة'),
            ),
          ),
        ],
      ),
    );
  }

  void _searchProducts(String query) {
    if (query.isNotEmpty) {
      ref
          .read(enhancedProductManagementProvider.notifier)
          .searchProducts(query);
    }
  }

  void _selectProduct(Product product) async {
    setState(() {
      _selectedProduct = product;
      _searchController.text = product.nameAr;
      _isLoading = true;
    });

    try {
      // Load current stock for this product in the selected warehouse
      final stocks = await ref.read(stockRepositoryProvider).getAllStocks(
            productId: product.id,
            warehouseId: widget.warehouseId,
          );

      setState(() {
        _currentStock = stocks.isNotEmpty ? stocks.first : null;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _currentStock = null;
        _isLoading = false;
      });
    }
  }

  void _clearSelection() {
    setState(() {
      _selectedProduct = null;
      _currentStock = null;
      _searchController.clear();
    });
  }

  void _saveItem() {
    if (_formKey.currentState!.validate() && _selectedProduct != null) {
      final adjustmentQty = double.parse(_adjustmentQtyController.text);
      final currentQty = _currentStock?.quantity ?? 0;
      final newQty = _adjustmentType == 'increase'
          ? currentQty + adjustmentQty
          : currentQty - adjustmentQty;

      final item = {
        'id': widget.existingItem?['id'] ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        'productId': _selectedProduct!.id,
        'name': _selectedProduct!.nameAr,
        'barcode': _selectedProduct!.barcode,
        'currentQty': currentQty,
        'adjustmentQty': adjustmentQty,
        'newQty': newQty,
        'type': _adjustmentType,
        'reason': _reason,
        'notes': _notesController.text.trim(),
      };

      Navigator.of(context).pop(item);
    }
  }
}
