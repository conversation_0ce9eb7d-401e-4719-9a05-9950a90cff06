import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:tijari_tech/core/utils/extensions.dart';
import 'package:tijari_tech/core/utils/formatters.dart';
import 'package:tijari_tech/shared/widgets/empty_state_widget.dart';
import 'package:tijari_tech/shared/widgets/loading_widget.dart';
import 'package:tijari_tech/widgets/error_widget.dart';

import '../../data/models/cash_box.dart';
import '../../providers/cash_box_provider.dart';

import '../../core/utils/app_utils.dart';
import '../../router/routes.dart';

class CashBoxesPage extends ConsumerStatefulWidget {
  const CashBoxesPage({super.key});

  @override
  ConsumerState<CashBoxesPage> createState() => _CashBoxesPageState();
}

class _CashBoxesPageState extends ConsumerState<CashBoxesPage> {
  final TextEditingController _searchController = TextEditingController();
  bool _showDeleted = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cashBoxesAsync = ref.watch(cashBoxesProvider);
    final searchResultsAsync = ref.watch(searchCashBoxesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الصناديق'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddCashBoxDialog(),
            tooltip: 'إضافة صندوق جديد',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'toggle_deleted') {
                setState(() {
                  _showDeleted = !_showDeleted;
                });
              } else if (value == 'statistics') {
                _showStatisticsDialog();
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'toggle_deleted',
                child: Row(
                  children: [
                    Icon(
                        _showDeleted ? Icons.visibility_off : Icons.visibility),
                    const SizedBox(width: 8),
                    Text(_showDeleted ? 'إخفاء المحذوفة' : 'عرض المحذوفة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'statistics',
                child: Row(
                  children: [
                    Icon(Icons.analytics),
                    SizedBox(width: 8),
                    Text('الإحصائيات'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث في الصناديق...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          ref
                              .read(searchCashBoxesProvider.notifier)
                              .clearSearch();
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onChanged: (value) {
                if (value.isNotEmpty) {
                  ref
                      .read(searchCashBoxesProvider.notifier)
                      .searchCashBoxes(value);
                } else {
                  ref.read(searchCashBoxesProvider.notifier).clearSearch();
                }
              },
            ),
          ),

          // Cash boxes list
          Expanded(
            child: _searchController.text.isNotEmpty
                ? _buildSearchResults(searchResultsAsync)
                : _buildCashBoxesList(cashBoxesAsync),
          ),
        ],
      ),
    );
  }

  Widget _buildCashBoxesList(AsyncValue<List<CashBox>> cashBoxesAsync) {
    return cashBoxesAsync.when(
      data: (cashBoxes) {
        final filteredCashBoxes = _showDeleted
            ? cashBoxes
            : cashBoxes.where((cb) => !cb.isDeleted).toList();

        if (filteredCashBoxes.isEmpty) {
          return EmptyStateWidget(
            icon: Icons.account_balance_wallet,
            title: 'لا توجد صناديق',
            subtitle: _showDeleted
                ? 'لا توجد صناديق محذوفة'
                : 'لم يتم إنشاء أي صناديق بعد',
            actionText: 'إضافة صندوق جديد',
            onActionPressed: () => _showAddCashBoxDialog(),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(cashBoxesProvider);
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredCashBoxes.length,
            itemBuilder: (context, index) {
              final cashBox = filteredCashBoxes[index];
              return _buildCashBoxCard(cashBox);
            },
          ),
        );
      },
      loading: () => const LoadingWidget(),
      error: (error, stackTrace) => CustomErrorWidget(
        message: error.toString(),
        onRetry: () => ref.invalidate(cashBoxesProvider),
      ),
    );
  }

  Widget _buildSearchResults(AsyncValue<List<CashBox>> searchResultsAsync) {
    return searchResultsAsync.when(
      data: (cashBoxes) {
        if (cashBoxes.isEmpty && _searchController.text.isNotEmpty) {
          return const EmptyStateWidget(
            icon: Icons.search_off,
            title: 'لا توجد نتائج',
            subtitle: 'لم يتم العثور على صناديق تطابق البحث',
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: cashBoxes.length,
          itemBuilder: (context, index) {
            final cashBox = cashBoxes[index];
            return _buildCashBoxCard(cashBox);
          },
        );
      },
      loading: () => const LoadingWidget(),
      error: (error, stackTrace) => CustomErrorWidget(
        message: error.toString(),
        onRetry: () => ref
            .read(searchCashBoxesProvider.notifier)
            .searchCashBoxes(_searchController.text),
      ),
    );
  }

  Widget _buildCashBoxCard(CashBox cashBox) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getStatusColor(cashBox),
          child: Icon(
            Icons.account_balance_wallet,
            color: Colors.white,
          ),
        ),
        title: Text(
          cashBox.name,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            decoration: cashBox.isDeleted ? TextDecoration.lineThrough : null,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(cashBox.formattedBalance),
            Text(
              cashBox.statusText,
              style: TextStyle(
                color: _getStatusColor(cashBox),
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleCashBoxAction(value, cashBox),
          itemBuilder: (context) => _buildCashBoxMenuItems(cashBox),
        ),
        onTap: () => _showCashBoxDetails(cashBox),
      ),
    );
  }

  List<PopupMenuEntry<String>> _buildCashBoxMenuItems(CashBox cashBox) {
    final items = <PopupMenuEntry<String>>[];

    if (!cashBox.isDeleted) {
      items.addAll([
        const PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit),
              SizedBox(width: 8),
              Text('تعديل'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'toggle_active',
          child: Row(
            children: [
              Icon(Icons.toggle_on),
              SizedBox(width: 8),
              Text('تغيير الحالة'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'update_balance',
          child: Row(
            children: [
              Icon(Icons.account_balance),
              SizedBox(width: 8),
              Text('تحديث الرصيد'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, color: Colors.red),
              SizedBox(width: 8),
              Text('حذف', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ]);
    } else {
      items.add(
        const PopupMenuItem(
          value: 'restore',
          child: Row(
            children: [
              Icon(Icons.restore, color: Colors.green),
              SizedBox(width: 8),
              Text('استعادة', style: TextStyle(color: Colors.green)),
            ],
          ),
        ),
      );
    }

    return items;
  }

  Color _getStatusColor(CashBox cashBox) {
    if (cashBox.isDeleted) return Colors.grey;
    if (!cashBox.isActive) return Colors.orange;
    return Colors.green;
  }

  void _handleCashBoxAction(String action, CashBox cashBox) {
    switch (action) {
      case 'edit':
        _showEditCashBoxDialog(cashBox);
        break;
      case 'toggle_active':
        _toggleCashBoxActive(cashBox);
        break;
      case 'update_balance':
        _showUpdateBalanceDialog(cashBox);
        break;
      case 'delete':
        _deleteCashBox(cashBox);
        break;
      case 'restore':
        _restoreCashBox(cashBox);
        break;
    }
  }

  void _showCashBoxDetails(CashBox cashBox) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(cashBox.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('الرصيد', cashBox.formattedBalance),
            _buildDetailRow('الحالة', cashBox.statusText),
            _buildDetailRow('تاريخ الإنشاء',
                AppFormatters.formatDate(cashBox.createdAt as DateTime)),
            if (cashBox.updatedAt != null)
              _buildDetailRow('آخر تحديث',
                  AppFormatters.formatDate(cashBox.updatedAt as DateTime)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String? value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value ?? 'غير محدد')),
        ],
      ),
    );
  }

  void _showAddCashBoxDialog() {
    // TODO: Navigate to add cash box page
    context.go(AppRoutes.cashBoxAdd);
  }

  void _showEditCashBoxDialog(CashBox cashBox) {
    // TODO: Navigate to edit cash box page
    context.go('${AppRoutes.cashBoxEdit}?id=${cashBox.id}');
  }

  void _showUpdateBalanceDialog(CashBox cashBox) {
    context.showInfoSnackBar('سيتم تنفيذ هذه الميزة قريباً');
    // TODO: Implement update balance dialog
    // AppUtils.showSnackBar(context, 'سيتم تنفيذ هذه الميزة قريباً');
  }

  void _toggleCashBoxActive(CashBox cashBox) async {
    final success = await ref
        .read(cashBoxesProvider.notifier)
        .toggleActive(cashBox.id, !cashBox.isActive);

    if (success) {
      context.showSuccessSnackBar(
          'تم ${cashBox.isActive ? 'إلغاء تفعيل' : 'تفعيل'} الصندوق بنجاح');
    } else {
      context.showErrorSnackBar('فشل في تغيير حالة الصندوق');
    }
  }

  void _deleteCashBox(CashBox cashBox) async {
    final confirmed = await context.showConfirmDialog(
      content:
          'هل أنت متأكد من حذف الصندوق "${cashBox.name}"؟\nلا يمكن حذف صندوق يحتوي على رصيد.',
      title: 'حذف الصندوق',
      cancelText: "إلغاء",
      confirmText: "حذف",
    );

    if (confirmed == true) {
      final success =
          await ref.read(cashBoxesProvider.notifier).deleteCashBox(cashBox.id);

      if (success) {
        context.showSuccessSnackBar('تم حذف الصندوق بنجاح');
      } else {
        context.showSuccessSnackBar('فشل في حذف الصندوق');
      }
    }
  }

  void _restoreCashBox(CashBox cashBox) async {
    final success =
        await ref.read(cashBoxesProvider.notifier).restoreCashBox(cashBox.id);

    if (success) {
      context.showSuccessSnackBar('تم استعادة الصندوق بنجاح');
    } else {
      context.showSuccessSnackBar('فشل في استعادة الصندوق');
    }
  }

  void _showStatisticsDialog() async {
    final statistics = await ref.read(cashBoxStatisticsProvider.future);

    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إحصائيات الصناديق'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatisticRow(
                'إجمالي الصناديق', '${statistics['total_count']}'),
            _buildStatisticRow(
                'الصناديق النشطة', '${statistics['active_count']}'),
            _buildStatisticRow(
                'الصناديق غير النشطة', '${statistics['inactive_count']}'),
            _buildStatisticRow(
                'الصناديق المحذوفة', '${statistics['deleted_count']}'),
            const Divider(),
            _buildStatisticRow('إجمالي الأرصدة',
                '${(statistics['total_balance'] as double).toStringAsFixed(2)} ر.س'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }
}
