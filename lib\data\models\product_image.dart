import '../../core/constants/database_constants.dart';

class ProductImage {
    final String id;
    final String productId;
    final String imageUrl;
    final String? imagePath; // للصور المحلية
    final bool isPrimary;
    final int sortOrder;
    final String? description;
    final DateTime? createdAt;
    final DateTime? updatedAt;
    final DateTime? deletedAt;
    final bool isSynced;

  const ProductImage({
    required this.id,
    required this.productId,
    required this.imageUrl,
    this.imagePath,
    this.isPrimary = false,
    this.sortOrder = 0,
    this.description,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  // Factory constructor for creating from map
  factory ProductImage.fromMap(Map<String, dynamic> map) {
    return ProductImage(
      id: map['id'] as String,
      productId: map['product_id'] as String,
      imageUrl: map['image_url'] as String,
      imagePath: map['image_path'] as String?,
      isPrimary: (map['is_primary'] as int? ?? 0) == 1,
      sortOrder: map['sort_order'] as int? ?? 0,
      description: map['description'] as String?,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'] as String)
          : null,
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
      deletedAt: map['deleted_at'] != null
          ? DateTime.parse(map['deleted_at'] as String)
          : null,
      isSynced: (map['is_synced'] as int? ?? 0) == 1,
    );
  }

  // Convert to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'product_id': productId,
      'image_url': imageUrl,
      'image_path': imagePath,
      'is_primary': isPrimary ? 1 : 0,
      'sort_order': sortOrder,
      'description': description,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'deleted_at': deletedAt?.toIso8601String(),
      'is_synced': isSynced ? 1 : 0,
    };
  }

  // Map serialization
  factory ProductImage.fromJson(Map<String, dynamic> json) =>
      ProductImage.fromMap(json);
  Map<String, dynamic> toJson() => toMap();

  // Copy with method
  ProductImage copyWith({
    String? id,
    String? productId,
    String? imageUrl,
    String? imagePath,
    bool? isPrimary,
    int? sortOrder,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return ProductImage(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      imageUrl: imageUrl ?? this.imageUrl,
      imagePath: imagePath ?? this.imagePath,
      isPrimary: isPrimary ?? this.isPrimary,
      sortOrder: sortOrder ?? this.sortOrder,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  // Equality and hashCode
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductImage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // String representation
  @override
  String toString() {
    return 'ProductImage(id: $id, productId: $productId, isPrimary: $isPrimary, sortOrder: $sortOrder)';
  }

  // Helper methods
  bool get isDeleted => deletedAt != null;
  bool get isActive => !isDeleted;

  // Create a new image with current timestamp
  static ProductImage create({
    required String id,
    required String productId,
    required String imageUrl,
    String? imagePath,
    bool isPrimary = false,
    int sortOrder = 0,
    String? description,
  }) {
    final now = DateTime.now();
    return ProductImage(
      id: id,
      productId: productId,
      imageUrl: imageUrl,
      imagePath: imagePath,
      isPrimary: isPrimary,
      sortOrder: sortOrder,
      description: description,
      createdAt: now,
      updatedAt: now,
      isSynced: false,
    );
  }

  // Update image with new timestamp
  ProductImage update({
    String? imageUrl,
    String? imagePath,
    bool? isPrimary,
    int? sortOrder,
    String? description,
  }) {
    return copyWith(
      imageUrl: imageUrl ?? this.imageUrl,
      imagePath: imagePath ?? this.imagePath,
      isPrimary: isPrimary ?? this.isPrimary,
      sortOrder: sortOrder ?? this.sortOrder,
      description: description ?? this.description,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as deleted
  ProductImage markAsDeleted() {
    return copyWith(
      deletedAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as synced
  ProductImage markAsSynced() {
    return copyWith(isSynced: true);
  }
}
