import 'package:sqflite/sqflite.dart';

/// تحسينات الأداء لقاعدة البيانات
/// 
/// يحتوي هذا الملف على:
/// - فهارس محسنة للاستعلامات السريعة
/// - استعلامات محسنة للمخزون والمنتجات
/// - تحسينات الذاكرة والتخزين المؤقت
/// - استراتيجيات التحميل المتقدمة
/// 
/// تم تطوير هذه التحسينات لضمان أداء سريع ومستقر

// class DatabaseOptimizations {
  
  /// إنشاء الفهارس المحسنة للأداء
   Future<void> createOptimizedIndexes(Database db) async {
    // فهارس جدول المنتجات
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_products_category_active 
      ON products(category_id, is_active)
    ''');
    
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_products_barcode 
      ON products(barcode)
    ''');
    
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_products_name_ar 
      ON products(name_ar)
    ''');
    
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_products_stock_levels 
      ON products(current_stock, min_stock, max_stock)
    ''');
    
    // فهارس جدول المخزون
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_stocks_product_warehouse 
      ON stocks(product_id, warehouse_id)
    ''');
    
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_stocks_warehouse_active 
      ON stocks(warehouse_id, is_active)
    ''');
    
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_stocks_low_stock 
      ON stocks(quantity, reorder_point) 
      WHERE quantity <= reorder_point
    ''');
    
    // فهارس جدول معاملات المخزون
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_stock_transactions_product_date 
      ON stock_transactions(product_id, created_at DESC)
    ''');
    
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_stock_transactions_warehouse_date 
      ON stock_transactions(warehouse_id, created_at DESC)
    ''');
    
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_stock_transactions_type_date 
      ON stock_transactions(transaction_type, created_at DESC)
    ''');
    
    // فهارس جدول حركات المخزون
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_stock_movements_product_date 
      ON stock_movements(product_id, created_at DESC)
    ''');
    
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_stock_movements_warehouse_date 
      ON stock_movements(warehouse_id, created_at DESC)
    ''');
    
    // فهارس جدول المخازن
    await db.execute('''
      CREATE INDEX IF NOT EXISTS idx_warehouses_active_default 
      ON warehouses(is_active, is_default)
    ''');
  }
  
  /// استعلامات محسنة للمخزون
   class OptimizedStockQueries {
    
    /// الحصول على المنتجات مع معلومات المخزون (محسن)
    static Future<List<Map<String, dynamic>>> getProductsWithStock(
      Database db, {
      String? categoryId,
      String? warehouseId,
      bool? lowStockOnly,
      int? limit,
      int? offset,
    }) async {
      final whereConditions = <String>[];
      final whereArgs = <dynamic>[];
      
      // بناء شروط الاستعلام ديناميكياً
      whereConditions.add('p.is_active = 1');
      
      if (categoryId != null) {
        whereConditions.add('p.category_id = ?');
        whereArgs.add(categoryId);
      }
      
      if (warehouseId != null) {
        whereConditions.add('s.warehouse_id = ?');
        whereArgs.add(warehouseId);
      }
      
      if (lowStockOnly == true) {
        whereConditions.add('s.quantity <= s.reorder_point');
      }
      
      final whereClause = whereConditions.isNotEmpty 
        ? 'WHERE ${whereConditions.join(' AND ')}'
        : '';
      
      final limitClause = limit != null 
        ? 'LIMIT $limit ${offset != null ? 'OFFSET $offset' : ''}'
        : '';
      
      return await db.rawQuery('''
        SELECT 
          p.id,
          p.name_ar,
          p.name_en,
          p.barcode,
          p.cost_price,
          p.selling_price,
          p.current_stock,
          p.min_stock,
          p.max_stock,
          p.reorder_point,
          s.quantity as warehouse_stock,
          s.warehouse_id,
          w.name as warehouse_name,
          c.name_ar as category_name,
          u.name_ar as unit_name,
          CASE 
            WHEN s.quantity <= 0 THEN 'out_of_stock'
            WHEN s.quantity <= s.reorder_point THEN 'low_stock'
            WHEN s.quantity > s.max_stock THEN 'over_stock'
            ELSE 'normal'
          END as stock_status
        FROM products p
        LEFT JOIN stocks s ON p.id = s.product_id
        LEFT JOIN warehouses w ON s.warehouse_id = w.id
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN units u ON p.base_unit_id = u.id
        $whereClause
        ORDER BY p.name_ar ASC
        $limitClause
      ''', whereArgs);
    }
    
    /// الحصول على إحصائيات المخزون السريعة (محسن)
    static Future<Map<String, dynamic>> getStockStatistics(
      Database db, {
      String? warehouseId,
    }) async {
      final whereCondition = warehouseId != null 
        ? 'WHERE s.warehouse_id = ?'
        : '';
      final whereArgs = warehouseId != null ? [warehouseId] : <dynamic>[];
      
      final result = await db.rawQuery('''
        SELECT 
          COUNT(DISTINCT p.id) as total_products,
          COUNT(DISTINCT CASE WHEN s.quantity > 0 THEN p.id END) as in_stock_products,
          COUNT(DISTINCT CASE WHEN s.quantity <= 0 THEN p.id END) as out_of_stock_products,
          COUNT(DISTINCT CASE WHEN s.quantity <= s.reorder_point AND s.quantity > 0 THEN p.id END) as low_stock_products,
          COUNT(DISTINCT CASE WHEN s.quantity > s.max_stock THEN p.id END) as over_stock_products,
          COALESCE(SUM(s.quantity * p.cost_price), 0) as total_stock_value,
          COALESCE(SUM(s.quantity), 0) as total_quantity
        FROM products p
        LEFT JOIN stocks s ON p.id = s.product_id
        $whereCondition
      ''', whereArgs);
      
      return result.first;
    }
    
    /// الحصول على المعاملات الأخيرة (محسن)
    static Future<List<Map<String, dynamic>>> getRecentTransactions(
      Database db, {
      String? productId,
      String? warehouseId,
      String? transactionType,
      int limit = 50,
    }) async {
      final whereConditions = <String>[];
      final whereArgs = <dynamic>[];
      
      if (productId != null) {
        whereConditions.add('st.product_id = ?');
        whereArgs.add(productId);
      }
      
      if (warehouseId != null) {
        whereConditions.add('st.warehouse_id = ?');
        whereArgs.add(warehouseId);
      }
      
      if (transactionType != null) {
        whereConditions.add('st.transaction_type = ?');
        whereArgs.add(transactionType);
      }
      
      final whereClause = whereConditions.isNotEmpty 
        ? 'WHERE ${whereConditions.join(' AND ')}'
        : '';
      
      return await db.rawQuery('''
        SELECT 
          st.id,
          st.product_id,
          st.warehouse_id,
          st.transaction_type,
          st.operation_type,
          st.quantity,
          st.reason,
          st.notes,
          st.created_at,
          p.name_ar as product_name,
          w.name as warehouse_name
        FROM stock_transactions st
        LEFT JOIN products p ON st.product_id = p.id
        LEFT JOIN warehouses w ON st.warehouse_id = w.id
        $whereClause
        ORDER BY st.created_at DESC
        LIMIT $limit
      ''', whereArgs);
    }
    
    /// البحث السريع في المنتجات (محسن)
    static Future<List<Map<String, dynamic>>> searchProducts(
      Database db,
      String searchTerm, {
      int limit = 20,
    }) async {
      final searchPattern = '%$searchTerm%';
      
      return await db.rawQuery('''
        SELECT 
          p.id,
          p.name_ar,
          p.name_en,
          p.barcode,
          p.current_stock,
          p.selling_price,
          c.name_ar as category_name,
          -- ترتيب النتائج حسب الصلة
          CASE 
            WHEN p.barcode = ? THEN 1
            WHEN p.name_ar LIKE ? THEN 2
            WHEN p.name_en LIKE ? THEN 3
            ELSE 4
          END as relevance_score
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.is_active = 1 
          AND (
            p.barcode LIKE ? OR
            p.name_ar LIKE ? OR
            p.name_en LIKE ?
          )
        ORDER BY relevance_score ASC, p.name_ar ASC
        LIMIT $limit
      ''', [
        searchTerm, // للباركود المطابق تماماً
        searchPattern, // للاسم العربي
        searchPattern, // للاسم الإنجليزي
        searchPattern, // للباركود (جزئي)
        searchPattern, // للاسم العربي (جزئي)
        searchPattern, // للاسم الإنجليزي (جزئي)
      ]);
    }
  }
  
  /// تحسينات الذاكرة والتخزين المؤقت
   class CacheOptimizations {
    static final Map<String, dynamic> _cache = {};
    static final Map<String, DateTime> _cacheTimestamps = {};
    static const Duration _cacheExpiry = Duration(minutes: 5);
    
    /// الحصول من التخزين المؤقت أو تنفيذ الاستعلام
    static Future<T> getOrCache<T>(
      String key,
      Future<T> Function() query,
    ) async {
      final now = DateTime.now();
      
      // التحقق من وجود البيانات في التخزين المؤقت
      if (_cache.containsKey(key) && _cacheTimestamps.containsKey(key)) {
        final cacheTime = _cacheTimestamps[key]!;
        if (now.difference(cacheTime) < _cacheExpiry) {
          return _cache[key] as T;
        }
      }
      
      // تنفيذ الاستعلام وحفظ النتيجة
      final result = await query();
      _cache[key] = result;
      _cacheTimestamps[key] = now;
      
      return result;
    }
    
    /// مسح التخزين المؤقت
    static void clearCache([String? key]) {
      if (key != null) {
        _cache.remove(key);
        _cacheTimestamps.remove(key);
      } else {
        _cache.clear();
        _cacheTimestamps.clear();
      }
    }
    
    /// مسح التخزين المؤقت المنتهي الصلاحية
    static void clearExpiredCache() {
      final now = DateTime.now();
      final expiredKeys = <String>[];
      
      _cacheTimestamps.forEach((key, timestamp) {
        if (now.difference(timestamp) >= _cacheExpiry) {
          expiredKeys.add(key);
        }
      });
      
      for (final key in expiredKeys) {
        _cache.remove(key);
        _cacheTimestamps.remove(key);
      }
    }
  }
  
  /// تحسينات الاستعلامات المجمعة
   class BatchOptimizations {
    
    /// تحديث مخزون متعدد المنتجات في معاملة واحدة
    static Future<void> batchUpdateStock(
      Database db,
      List<Map<String, dynamic>> updates,
    ) async {
      await db.transaction((txn) async {
        for (final update in updates) {
          await txn.update(
            'products',
            {'current_stock': update['newStock']},
            where: 'id = ?',
            whereArgs: [update['productId']],
          );
          
          // تسجيل المعاملة
          await txn.insert('stock_transactions', {
            'id': update['transactionId'],
            'product_id': update['productId'],
            'warehouse_id': update['warehouseId'],
            'transaction_type': update['transactionType'],
            'operation_type': update['operationType'],
            'quantity': update['quantity'],
            'reason': update['reason'],
            'notes': update['notes'],
            'created_at': DateTime.now().toIso8601String(),
          });
        }
      });
    }
    
    /// تحميل بيانات متعددة في استعلام واحد
    static Future<Map<String, dynamic>> loadDashboardData(Database db) async {
      // تنفيذ عدة استعلامات في معاملة واحدة للحصول على جميع البيانات
      final results = <String, dynamic>{};
      
      await db.transaction((txn) async {
        // إحصائيات المنتجات
        final productStats = await txn.rawQuery('''
          SELECT 
            COUNT(*) as total_products,
            COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_products,
            COUNT(CASE WHEN current_stock <= min_stock THEN 1 END) as low_stock_products,
            COUNT(CASE WHEN current_stock <= 0 THEN 1 END) as out_of_stock_products
          FROM products
        ''');
        results['productStats'] = productStats.first;
        
        // إحصائيات المخازن
        final warehouseStats = await txn.rawQuery('''
          SELECT 
            COUNT(*) as total_warehouses,
            COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_warehouses
          FROM warehouses
        ''');
        results['warehouseStats'] = warehouseStats.first;
        
        // المعاملات الأخيرة
        final recentTransactions = await txn.rawQuery('''
          SELECT 
            st.*,
            p.name_ar as product_name,
            w.name as warehouse_name
          FROM stock_transactions st
          LEFT JOIN products p ON st.product_id = p.id
          LEFT JOIN warehouses w ON st.warehouse_id = w.id
          ORDER BY st.created_at DESC
          LIMIT 10
        ''');
        results['recentTransactions'] = recentTransactions;
        
        // المنتجات منخفضة المخزون
        final lowStockProducts = await txn.rawQuery('''
          SELECT 
            p.*,
            c.name_ar as category_name
          FROM products p
          LEFT JOIN categories c ON p.category_id = c.id
          WHERE p.current_stock <= p.min_stock
            AND p.is_active = 1
          ORDER BY (p.current_stock / p.min_stock) ASC
          LIMIT 10
        ''');
        results['lowStockProducts'] = lowStockProducts;
      });
      
      return results;
    }
  }
  
  /// تحسينات الأداء العامة
   class GeneralOptimizations {
    
    /// تحسين إعدادات قاعدة البيانات
    static Future<void> optimizeDatabaseSettings(Database db) async {
      // تحسين إعدادات SQLite للأداء
      await db.execute('PRAGMA journal_mode = WAL');
      await db.execute('PRAGMA synchronous = NORMAL');
      await db.execute('PRAGMA cache_size = 10000');
      await db.execute('PRAGMA temp_store = MEMORY');
      await db.execute('PRAGMA mmap_size = 268435456'); // 256MB
    }
    
    /// تحليل وتحسين الجداول
    static Future<void> analyzeAndOptimize(Database db) async {
      // تحليل الجداول لتحسين الاستعلامات
      await db.execute('ANALYZE');
      
      // إعادة بناء الفهارس إذا لزم الأمر
      await db.execute('REINDEX');
    }
    
    /// تنظيف قاعدة البيانات
    static Future<void> cleanupDatabase(Database db) async {
      // حذف المعاملات القديمة (أكثر من سنة)
      final cutoffDate = DateTime.now().subtract(const Duration(days: 365));
      await db.delete(
        'stock_transactions',
        where: 'created_at < ?',
        whereArgs: [cutoffDate.toIso8601String()],
      );
      
      // ضغط قاعدة البيانات
      await db.execute('VACUUM');
    }
  }
