import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../widgets/main_layout.dart';
import '../../data/models/stock_alert.dart';
import '../../providers/stock_alert_provider.dart';

class StockAlertsPage extends ConsumerStatefulWidget {
  const StockAlertsPage({super.key});

  @override
  ConsumerState<StockAlertsPage> createState() => _StockAlertsPageState();
}

class _StockAlertsPageState extends ConsumerState<StockAlertsPage> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(stockAlertManagementProvider.notifier).loadAlerts();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(stockAlertManagementProvider);

    return MainLayout(
      title: 'تنبيهات المخزون',
      actions: [
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: _showFiltersDialog,
        ),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: () => ref
              .read(stockAlertManagementProvider.notifier)
              .loadAlerts(),
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'mark_all_read':
                _markAllAsRead();
                break;
              case 'resolve_all':
                _resolveAllAlerts();
                break;
              case 'generate_alerts':
                _generateAlerts();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'mark_all_read',
              child: Text('تحديد الكل كمقروء'),
            ),
            const PopupMenuItem(
              value: 'resolve_all',
              child: Text('حل جميع التنبيهات'),
            ),
            const PopupMenuItem(
              value: 'generate_alerts',
              child: Text('إنشاء تنبيهات جديدة'),
            ),
          ],
        ),
      ],
      child: Column(
        children: [
          // Search bar
          _buildSearchBar(),

          // Active filters
          _buildActiveFilters(),

          // Statistics
          _buildStatistics(),

          // Alerts list
          Expanded(
            child: _buildAlertsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: EdgeInsets.all(16.r),
      child: TextFormField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث في التنبيهات...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    ref
                        .read(stockAlertManagementProvider.notifier)
                        .search('');
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
        ),
        onChanged: (value) => ref
            .read(stockAlertManagementProvider.notifier)
            .search(value),
      ),
    );
  }

  Widget _buildActiveFilters() {
    final state = ref.watch(stockAlertManagementProvider);
    final filters = <Widget>[];

    if (state.selectedWarehouse != null) {
      filters.add(_buildFilterChip(
        'مخزن محدد',
        () => ref
            .read(stockAlertManagementProvider.notifier)
            .filterByWarehouse(null),
      ));
    }

    if (state.selectedProduct != null) {
      filters.add(_buildFilterChip(
        'منتج محدد',
        () => ref
            .read(stockAlertManagementProvider.notifier)
            .filterByProduct(null),
      ));
    }

    if (state.selectedAlertType != null) {
      filters.add(_buildFilterChip(
        'نوع التنبيه: ${_getAlertTypeLabel(state.selectedAlertType!)}',
        () => ref
            .read(stockAlertManagementProvider.notifier)
            .filterByAlertType(null),
      ));
    }

    if (state.selectedSeverity != null) {
      filters.add(_buildFilterChip(
        'الأهمية: ${_getSeverityLabel(state.selectedSeverity!)}',
        () => ref
            .read(stockAlertManagementProvider.notifier)
            .filterBySeverity(null),
      ));
    }

    if (state.showOnlyUnread == true) {
      filters.add(_buildFilterChip(
        'غير مقروء فقط',
        () => ref
            .read(stockAlertManagementProvider.notifier)
            .filterByReadStatus(null),
      ));
    }

    if (state.showOnlyActive == true) {
      filters.add(_buildFilterChip(
        'نشط فقط',
        () => ref
            .read(stockAlertManagementProvider.notifier)
            .filterByActiveStatus(null),
      ));
    }

    if (filters.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الفلاتر النشطة:',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Wrap(
            spacing: 8.w,
            runSpacing: 4.h,
            children: filters,
          ),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, VoidCallback onDeleted) {
    return Chip(
      label: Text(label),
      deleteIcon: const Icon(Icons.close, size: 18),
      onDeleted: onDeleted,
      backgroundColor: AppColors.primary.withValues(alpha: 0.1),
      deleteIconColor: AppColors.primary,
    );
  }

  Widget _buildStatistics() {
    final state = ref.watch(stockAlertManagementProvider);
    final stats = state.statistics;

    if (stats.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.all(16.r),
      child: Card(
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'إحصائيات التنبيهات',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 12.h),
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'إجمالي التنبيهات',
                      '${stats['total_alerts'] ?? 0}',
                      Icons.notifications,
                      AppColors.primary,
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'غير مقروء',
                      '${stats['unread_alerts'] ?? 0}',
                      Icons.mark_email_unread,
                      AppColors.warning,
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'حرج',
                      '${stats['critical_alerts'] ?? 0}',
                      Icons.priority_high,
                      AppColors.error,
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'نشط',
                      '${stats['active_alerts'] ?? 0}',
                      Icons.check_circle,
                      AppColors.success,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24.r),
        SizedBox(height: 4.h),
        Text(
          value,
          style: AppTextStyles.titleMedium.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildAlertsList() {
    final state = ref.watch(stockAlertManagementProvider);

    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64.r,
              color: AppColors.error,
            ),
            SizedBox(height: 16.h),
            Text(
              'حدث خطأ في تحميل البيانات',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.error,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              state.error!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: () => ref
                  .read(stockAlertManagementProvider.notifier)
                  .loadAlerts(),
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (state.filteredAlerts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.notifications_none,
              size: 64.r,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16.h),
            Text(
              'لا توجد تنبيهات',
              style: AppTextStyles.titleMedium.copyWith(
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'لم يتم العثور على أي تنبيهات تطابق المعايير المحددة',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      itemCount: state.filteredAlerts.length,
      itemBuilder: (context, index) {
        final alert = state.filteredAlerts[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 8.h),
          child: _buildAlertCard(alert),
        );
      },
    );
  }

  Widget _buildAlertCard(StockAlert alert) {
    return Card(
      elevation: alert.isUnread ? 4 : 2,
      color: alert.isUnread ? Colors.white : Colors.grey[50],
      child: InkWell(
        onTap: () => _showAlertDetails(alert),
        borderRadius: BorderRadius.circular(8.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  _buildSeverityIcon(alert.severity),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      alert.title,
                      style: AppTextStyles.titleSmall.copyWith(
                        fontWeight: alert.isUnread ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ),
                  if (alert.isUnread)
                    Container(
                      width: 8.r,
                      height: 8.r,
                      decoration: const BoxDecoration(
                        color: AppColors.primary,
                        shape: BoxShape.circle,
                      ),
                    ),
                  SizedBox(width: 8.w),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'mark_read':
                          _markAlertAsRead(alert.id);
                          break;
                        case 'resolve':
                          _resolveAlert(alert.id);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      if (alert.isUnread)
                        const PopupMenuItem(
                          value: 'mark_read',
                          child: Text('تحديد كمقروء'),
                        ),
                      if (!alert.isResolved)
                        const PopupMenuItem(
                          value: 'resolve',
                          child: Text('حل التنبيه'),
                        ),
                    ],
                  ),
                ],
              ),
              SizedBox(height: 8.h),
              Text(
                alert.message,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.grey[700],
                ),
              ),
              SizedBox(height: 8.h),
              Row(
                children: [
                  _buildAlertTypeChip(alert.alertType),
                  SizedBox(width: 8.w),
                  Text(
                    DateFormat('yyyy/MM/dd HH:mm').format(alert.createdAt),
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSeverityIcon(String severity) {
    IconData icon;
    Color color;

    switch (severity) {
      case 'critical':
        icon = Icons.error;
        color = AppColors.error;
        break;
      case 'high':
        icon = Icons.warning;
        color = Colors.orange;
        break;
      case 'medium':
        icon = Icons.info;
        color = AppColors.warning;
        break;
      case 'low':
      default:
        icon = Icons.info_outline;
        color = AppColors.primary;
        break;
    }

    return Icon(icon, color: color, size: 20.r);
  }

  Widget _buildAlertTypeChip(String alertType) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: _getAlertTypeColor(alertType).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Text(
        _getAlertTypeLabel(alertType),
        style: AppTextStyles.bodySmall.copyWith(
          color: _getAlertTypeColor(alertType),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Color _getAlertTypeColor(String alertType) {
    switch (alertType) {
      case 'out_of_stock':
        return AppColors.error;
      case 'low_stock':
        return AppColors.warning;
      case 'expired':
        return Colors.red;
      case 'near_expiry':
        return Colors.orange;
      default:
        return AppColors.primary;
    }
  }

  String _getAlertTypeLabel(String alertType) {
    switch (alertType) {
      case 'out_of_stock':
        return 'نفاد المخزون';
      case 'low_stock':
        return 'مخزون منخفض';
      case 'expired':
        return 'منتهي الصلاحية';
      case 'near_expiry':
        return 'قرب انتهاء الصلاحية';
      default:
        return alertType;
    }
  }

  String _getSeverityLabel(String severity) {
    switch (severity) {
      case 'critical':
        return 'حرج';
      case 'high':
        return 'عالي';
      case 'medium':
        return 'متوسط';
      case 'low':
        return 'منخفض';
      default:
        return severity;
    }
  }

  void _showFiltersDialog() {
    // TODO: Implement filters dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير نافذة الفلاتر قريباً')),
    );
  }

  void _showAlertDetails(StockAlert alert) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(alert.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(alert.message),
            SizedBox(height: 16.h),
            Text('النوع: ${_getAlertTypeLabel(alert.alertType)}'),
            Text('الأهمية: ${_getSeverityLabel(alert.severity)}'),
            Text('التاريخ: ${DateFormat('yyyy/MM/dd HH:mm').format(alert.createdAt)}'),
            if (alert.isResolved)
              Text('تم الحل في: ${DateFormat('yyyy/MM/dd HH:mm').format(alert.resolvedAt!)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          if (alert.isUnread)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _markAlertAsRead(alert.id);
              },
              child: const Text('تحديد كمقروء'),
            ),
          if (!alert.isResolved)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _resolveAlert(alert.id);
              },
              child: const Text('حل التنبيه'),
            ),
        ],
      ),
    );
  }

  void _markAlertAsRead(String alertId) async {
    final success = await ref
        .read(stockAlertManagementProvider.notifier)
        .markAlertAsRead(alertId);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'تم تحديد التنبيه كمقروء' : 'فشل في تحديث التنبيه'),
          backgroundColor: success ? AppColors.success : AppColors.error,
        ),
      );
    }
  }

  void _resolveAlert(String alertId) async {
    final success = await ref
        .read(stockAlertManagementProvider.notifier)
        .resolveAlert(alertId, 'current_user'); // TODO: Get from auth service

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'تم حل التنبيه' : 'فشل في حل التنبيه'),
          backgroundColor: success ? AppColors.success : AppColors.error,
        ),
      );
    }
  }

  void _markAllAsRead() async {
    final success = await ref
        .read(stockAlertManagementProvider.notifier)
        .markAllAsRead();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'تم تحديد جميع التنبيهات كمقروءة' : 'فشل في تحديث التنبيهات'),
          backgroundColor: success ? AppColors.success : AppColors.error,
        ),
      );
    }
  }

  void _resolveAllAlerts() async {
    final success = await ref
        .read(stockAlertManagementProvider.notifier)
        .resolveAllAlerts('current_user'); // TODO: Get from auth service

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'تم حل جميع التنبيهات' : 'فشل في حل التنبيهات'),
          backgroundColor: success ? AppColors.success : AppColors.error,
        ),
      );
    }
  }

  void _generateAlerts() async {
    final success = await ref
        .read(stockAlertManagementProvider.notifier)
        .generateAlerts();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'تم إنشاء التنبيهات الجديدة' : 'فشل في إنشاء التنبيهات'),
          backgroundColor: success ? AppColors.success : AppColors.error,
        ),
      );
    }
  }
}
