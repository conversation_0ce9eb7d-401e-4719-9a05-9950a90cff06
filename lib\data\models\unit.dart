import 'package:json_annotation/json_annotation.dart';
import '../../core/constants/database_constants.dart';

part 'unit.g.dart';

@JsonSerializable()
class Unit {
  final String id;
  final String name;
  final String? symbol;
  final String? abbreviation;
  final double factor; // معامل التحويل إلى الوحدة الأساسية
  final String? baseUnitId; // الوحدة الأساسية
  final String unitType; // نوع الوحدة (وزن، حجم، طول، كمية)
  final bool isBaseUnit;
  final bool isActive;
  final String? description;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final bool isSynced;
  
  // Related data (not stored in main table)
  final Unit? baseUnit;
  final List<Unit>? derivedUnits;
  final int? usageCount; // عدد المنتجات التي تستخدم هذه الوحدة
  final int? branchCount; // عدد الفروع التي تستخدم هذه الوحدة
  final int? categoryCount; // عدد التصنيفات التي تستخدم هذه الوحدة

  const Unit({
    required this.id,
    required this.name,
    this.symbol,
    this.abbreviation,
    required this.factor,
    this.baseUnitId,
    this.unitType = 'quantity',
    this.isBaseUnit = false,
    this.isActive = true,
    this.description,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
    this.baseUnit,
    this.derivedUnits,
    this.usageCount,
    this.branchCount,
    this.categoryCount,
  });

  // Factory constructor for creating a new Unit instance from a map
  factory Unit.fromMap(Map<String, dynamic> map) {
    return Unit(
      id: map[DatabaseConstants.columnUnitId] as String,
      name: map[DatabaseConstants.columnUnitName] as String,
      symbol: map[DatabaseConstants.columnUnitSymbol] as String?,
      abbreviation: map[DatabaseConstants.columnUnitAbbreviation] as String?,
      factor: (map[DatabaseConstants.columnUnitFactor] as num).toDouble(),
      baseUnitId: map[DatabaseConstants.columnUnitBaseUnitId] as String?,
      unitType: map[DatabaseConstants.columnUnitType] as String? ?? 'quantity',
      isBaseUnit: (map[DatabaseConstants.columnUnitIsBaseUnit] as int? ?? 0) == 1,
      isActive: (map[DatabaseConstants.columnUnitIsActive] as int? ?? 1) == 1,
      description: map[DatabaseConstants.columnUnitDescription] as String?,
      createdAt: map[DatabaseConstants.columnUnitCreatedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnUnitCreatedAt] as String)
          : null,
      updatedAt: map[DatabaseConstants.columnUnitUpdatedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnUnitUpdatedAt] as String)
          : null,
      deletedAt: map[DatabaseConstants.columnUnitDeletedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnUnitDeletedAt] as String)
          : null,
      isSynced: (map[DatabaseConstants.columnUnitIsSynced] as int? ?? 0) == 1,
      usageCount: map[DatabaseConstants.columnUnitUsageCount] as int?,
      branchCount: map[DatabaseConstants.columnUnitBranchCount] as int?,
      categoryCount: map[DatabaseConstants.columnUnitCategoryCount] as int?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnUnitId: id,
      DatabaseConstants.columnUnitName: name,
      DatabaseConstants.columnUnitSymbol: symbol,
      DatabaseConstants.columnUnitAbbreviation: abbreviation,
      DatabaseConstants.columnUnitFactor: factor,
      DatabaseConstants.columnUnitBaseUnitId: baseUnitId,
      DatabaseConstants.columnUnitType: unitType,
      DatabaseConstants.columnUnitIsBaseUnit: isBaseUnit ? 1 : 0,
      DatabaseConstants.columnUnitIsActive: isActive ? 1 : 0,
      DatabaseConstants.columnUnitDescription: description,
      DatabaseConstants.columnUnitCreatedAt: createdAt?.toIso8601String(),
      DatabaseConstants.columnUnitUpdatedAt: updatedAt?.toIso8601String(),
      DatabaseConstants.columnUnitDeletedAt: deletedAt?.toIso8601String(),
      DatabaseConstants.columnUnitIsSynced: isSynced ? 1 : 0,
    };
  }

  // JSON serialization
  factory Unit.fromJson(Map<String, dynamic> json) => _$UnitFromJson(json);
  Map<String, dynamic> toJson() => _$UnitToJson(this);

  // Copy with method for creating modified copies
  Unit copyWith({
    String? id,
    String? name,
    String? symbol,
    String? abbreviation,
    double? factor,
    String? baseUnitId,
    String? unitType,
    bool? isBaseUnit,
    bool? isActive,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
    Unit? baseUnit,
    List<Unit>? derivedUnits,
    int? usageCount,
    int? branchCount,
    int? categoryCount,
  }) {
    return Unit(
      id: id ?? this.id,
      name: name ?? this.name,
      symbol: symbol ?? this.symbol,
      abbreviation: abbreviation ?? this.abbreviation,
      factor: factor ?? this.factor,
      baseUnitId: baseUnitId ?? this.baseUnitId,
      unitType: unitType ?? this.unitType,
      isBaseUnit: isBaseUnit ?? this.isBaseUnit,
      isActive: isActive ?? this.isActive,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
      baseUnit: baseUnit ?? this.baseUnit,
      derivedUnits: derivedUnits ?? this.derivedUnits,
      usageCount: usageCount ?? this.usageCount,
      branchCount: branchCount ?? this.branchCount,
      categoryCount: categoryCount ?? this.categoryCount,
    );
  }

  // Equality and hashCode
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Unit && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // String representation
  @override
  String toString() {
    return 'Unit(id: $id, name: $name, factor: $factor, type: $unitType, isSynced: $isSynced)';
  }

  // Helper methods
  bool get isDeleted => deletedAt != null;
  bool get isActiveUnit => !isDeleted && isActive;
  bool get isBaseUnitType => isBaseUnit || baseUnitId == null;
  bool get hasSymbol => symbol != null && symbol!.isNotEmpty;
  bool get hasAbbreviation => abbreviation != null && abbreviation!.isNotEmpty;
  bool get hasBaseUnit => baseUnitId != null && baseUnitId!.isNotEmpty;
  bool get hasDerivedUnits => derivedUnits != null && derivedUnits!.isNotEmpty;
  
  // Get display name with symbol/abbreviation
  String get displayName {
    if (isBaseUnitType) {
      return hasSymbol ? '$name ($symbol)' : name;
    } else {
      return hasSymbol ? '$name ($symbol) x$factor' : '$name x$factor';
    }
  }
  
  

  // Get short display name
  String get shortName {
    if (hasAbbreviation) return abbreviation!;
    if (hasSymbol) return symbol!;
    return name;
  }

  // Get type display name in Arabic
  String get typeDisplayName {
    switch (unitType) {
      case 'weight':
        return 'وزن';
      case 'length':
        return 'طول';
      case 'volume':
        return 'حجم';
      case 'area':
        return 'مساحة';
      case 'count':
        return 'عدد';
      case 'time':
        return 'وقت';
      case 'other':
        return 'أخرى';
      default:
        return unitType;
    }
  }

  // Convert quantity from this unit to base unit
  double toBaseUnit(double quantity) {
    return quantity * factor;
  }

  // Convert quantity from base unit to this unit
  double fromBaseUnit(double baseQuantity) {
    return baseQuantity / factor;
  }

  // Convert quantity from this unit to another unit
  double? convertTo(double quantity, Unit targetUnit) {
    if (!canConvertTo(targetUnit)) return null;
    
    // إذا كانت الوحدتان من نفس الشجرة
    if (baseUnitId == targetUnit.baseUnitId || 
        (isBaseUnitType && targetUnit.baseUnitId == id) || 
        (targetUnit.isBaseUnitType && baseUnitId == targetUnit.id)) {
      final baseQuantity = toBaseUnit(quantity);
      return targetUnit.fromBaseUnit(baseQuantity);
    }
    
    // إذا كانت الوحدتان من نفس النوع ولكن من أشجار مختلفة
    if (unitType == targetUnit.unitType) {
      // تحويل إلى الوحدة الأساسية المشتركة
      final baseQuantity = toBaseUnit(quantity);
      // تحويل من الوحدة الأساسية إلى الوحدة المستهدفة
      return targetUnit.fromBaseUnit(baseQuantity);
    }
    
    return null;
  }

  // Check if conversion is possible between units
  bool canConvertTo(Unit targetUnit) {
    return unitType == targetUnit.unitType;
  }

  // Get conversion factor to another unit
  double? getConversionFactor(Unit targetUnit) {
    if (!canConvertTo(targetUnit)) return null;
    return factor / targetUnit.factor;
  }

  // Format quantity with unit
  String formatQuantity(double quantity, {int decimals = 2}) {
    final formattedQuantity = quantity.toStringAsFixed(decimals);
    return '$formattedQuantity $shortName';
  }

  // Format quantity with full unit name
  String formatQuantityWithFullName(double quantity, {int decimals = 2}) {
    final formattedQuantity = quantity.toStringAsFixed(decimals);
    return '$formattedQuantity $displayName';
  }

  // Create a new unit with current timestamp
  static Unit create({
    required String id,
    required String name,
    String? symbol,
    String? abbreviation,
    required double factor,
    String? baseUnitId,
    String unitType = 'quantity',
    bool isBaseUnit = false,
    bool isActive = true,
    String? description,
  }) {
    final now = DateTime.now();
    return Unit(
      id: id,
      name: name,
      symbol: symbol,
      abbreviation: abbreviation,
      factor: factor,
      baseUnitId: baseUnitId,
      unitType: unitType,
      isBaseUnit: isBaseUnit,
      isActive: isActive,
      description: description,
      createdAt: now,
      updatedAt: now,
      isSynced: false,
    );
  }

  // Update unit with new timestamp
  Unit update({
    String? name,
    String? symbol,
    String? abbreviation,
    double? factor,
    String? baseUnitId,
    String? unitType,
    bool? isBaseUnit,
    bool? isActive,
    String? description,
  }) {
    return copyWith(
      name: name ?? this.name,
      symbol: symbol ?? this.symbol,
      abbreviation: abbreviation ?? this.abbreviation,
      factor: factor ?? this.factor,
      baseUnitId: baseUnitId ?? this.baseUnitId,
      unitType: unitType ?? this.unitType,
      isBaseUnit: isBaseUnit ?? this.isBaseUnit,
      isActive: isActive ?? this.isActive,
      description: description ?? this.description,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as deleted
  Unit markAsDeleted() {
    return copyWith(
      deletedAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as synced
  Unit markAsSynced() {
    return copyWith(isSynced: true);
  }

  // Mark as active/inactive
  Unit markAsActive({required bool active}) {
    return copyWith(
      isActive: active,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Common unit factory methods - CORRECTED
  static Unit piece({required String id}) => Unit.create(
        id: id,
        name: 'قطعة',
        symbol: 'قطعة',
        abbreviation: 'pc',
        factor: 1.0,
        unitType: 'count',
        isBaseUnit: true,
      );

  static Unit box({required String id, required double piecesPerBox}) => Unit.create(
        id: id,
        name: 'كرتون',
        symbol: 'كرتون',
        abbreviation: 'box',
        factor: piecesPerBox,
        unitType: 'count',
        isBaseUnit: false,
      );

  static Unit pack({required String id, required double piecesPerPack}) => Unit.create(
        id: id,
        name: 'باكت',
        symbol: 'باكت',
        abbreviation: 'pack',
        factor: piecesPerPack,
        unitType: 'count',
        isBaseUnit: false,
      );

  static Unit kilogram({required String id}) => Unit.create(
        id: id,
        name: 'كيلوجرام',
        symbol: 'كجم',
        abbreviation: 'kg',
        factor: 1000.0,
        unitType: 'weight',
        isBaseUnit: false,
      );

  static Unit gram({required String id}) => Unit.create(
        id: id,
        name: 'جرام',
        symbol: 'جم',
        abbreviation: 'g',
        factor: 1.0,
        unitType: 'weight',
        isBaseUnit: true,
      );

  static Unit ton({required String id}) => Unit.create(
        id: id,
        name: 'طن',
        symbol: 'طن',
        abbreviation: 't',
        factor: 1000000.0,
        unitType: 'weight',
        isBaseUnit: false,
      );

  static Unit liter({required String id}) => Unit.create(
        id: id,
        name: 'لتر',
        symbol: 'لتر',
        abbreviation: 'L',
        factor: 1000.0,
        unitType: 'volume',
        isBaseUnit: false,
      );

  static Unit milliliter({required String id}) => Unit.create(
        id: id,
        name: 'مليلتر',
        symbol: 'مل',
        abbreviation: 'mL',
        factor: 1.0,
        unitType: 'volume',
        isBaseUnit: true,
      );

  static Unit meter({required String id}) => Unit.create(
        id: id,
        name: 'متر',
        symbol: 'م',
        abbreviation: 'm',
        factor: 100.0,
        unitType: 'length',
        isBaseUnit: false,
      );

  static Unit centimeter({required String id}) => Unit.create(
        id: id,
        name: 'سنتيمتر',
        symbol: 'سم',
        abbreviation: 'cm',
        factor: 1.0,
        unitType: 'length',
        isBaseUnit: true,
      );

  static Unit squareMeter({required String id}) => Unit.create(
        id: id,
        name: 'متر مربع',
        symbol: 'م²',
        abbreviation: 'm²',
        factor: 10000.0,
        unitType: 'area',
        isBaseUnit: false,
      );

  static Unit squareCentimeter({required String id}) => Unit.create(
        id: id,
        name: 'سنتيمتر مربع',
        symbol: 'سم²',
        abbreviation: 'cm²',
        factor: 1.0,
        unitType: 'area',
        isBaseUnit: true,
      );

  // Create default units for the system
  static List<Unit> createDefaultUnits() {
    return [
      // Weight units
      gram(id: 'unit_gram'),
      kilogram(id: 'unit_kg'),
      ton(id: 'unit_ton'),
      
      // Volume units
      milliliter(id: 'unit_ml'),
      liter(id: 'unit_liter'),
      
      // Length units
      centimeter(id: 'unit_cm'),
      meter(id: 'unit_meter'),
      
      // Area units
      squareCentimeter(id: 'unit_cm2'),
      squareMeter(id: 'unit_m2'),
      
      // Count units
      piece(id: 'unit_piece'),
    ];
  }

  // Create default unit trees for the system
  static Map<String, List<Unit>> createDefaultUnitTrees() {
    return {
      'weight': [
        gram(id: 'unit_gram'),
        kilogram(id: 'unit_kg'),
        ton(id: 'unit_ton'),
      ],
      'volume': [
        milliliter(id: 'unit_ml'),
        liter(id: 'unit_liter'),
      ],
      'length': [
        centimeter(id: 'unit_cm'),
        meter(id: 'unit_meter'),
      ],
      'area': [
        squareCentimeter(id: 'unit_cm2'),
        squareMeter(id: 'unit_m2'),
      ],
      'count': [
        piece(id: 'unit_piece'),
      ],
    };
  }

  // Create derived unit with base unit relationship
  static Unit createDerivedUnit({
    required String id,
    required String name,
    String? symbol,
    String? abbreviation,
    required String baseUnitId,
    required double factor,
    String? description,
    bool isActive = true,
  }) {
    return Unit.create(
      id: id,
      name: name,
      symbol: symbol,
      abbreviation: abbreviation,
      factor: factor,
      baseUnitId: baseUnitId,
      unitType: 'quantity', // سيتم تحديثه لاحقاً
      isBaseUnit: false,
      isActive: isActive,
      description: description,
    );
  }
}