import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../services/inventory_integration_service.dart';
import '../data/models/sale.dart';
import '../data/models/sale_item.dart';
import '../data/models/purchase.dart';
import '../data/models/purchase_item.dart';
import '../core/utils/app_utils.dart';
import '../core/utils/error_handler.dart';

// Inventory integration service provider
final inventoryIntegrationServiceProvider = Provider<InventoryIntegrationService>((ref) {
  return InventoryIntegrationService();
});

// Inventory integration provider
final inventoryIntegrationProvider = StateNotifierProvider<
    InventoryIntegrationNotifier, InventoryIntegrationState>((ref) {
  return InventoryIntegrationNotifier(ref);
});

// Inventory integration state
class InventoryIntegrationState {
  final bool isProcessing;
  final String? error;
  final String? successMessage;
  final Map<String, dynamic>? lastValidation;

  const InventoryIntegrationState({
    this.isProcessing = false,
    this.error,
    this.successMessage,
    this.lastValidation,
  });

  InventoryIntegrationState copyWith({
    bool? isProcessing,
    String? error,
    String? successMessage,
    Map<String, dynamic>? lastValidation,
  }) {
    return InventoryIntegrationState(
      isProcessing: isProcessing ?? this.isProcessing,
      error: error,
      successMessage: successMessage,
      lastValidation: lastValidation ?? this.lastValidation,
    );
  }
}

// Inventory integration notifier
class InventoryIntegrationNotifier extends StateNotifier<InventoryIntegrationState> {
  final Ref _ref;

  InventoryIntegrationNotifier(this._ref) : super(const InventoryIntegrationState());

  InventoryIntegrationService get _service => _ref.read(inventoryIntegrationServiceProvider);

  // ------------------------------------------------------------------
  // Sales Integration
  // ------------------------------------------------------------------

  /// معالجة مبيعة وتحديث المخزون
  Future<bool> processSale({
    required Sale sale,
    required List<SaleItem> saleItems,
    required String userId,
    String? warehouseId,
  }) async {
    try {
      state = state.copyWith(isProcessing: true, error: null, successMessage: null);

      final success = await _service.processSale(
        sale: sale,
        saleItems: saleItems,
        userId: userId,
        warehouseId: warehouseId,
      );

      if (success) {
        state = state.copyWith(
          isProcessing: false,
          successMessage: 'تم معالجة المبيعة وتحديث المخزون بنجاح',
        );
      } else {
        state = state.copyWith(
          isProcessing: false,
          error: 'فشل في معالجة المبيعة',
        );
      }

      return success;
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error processing sale', e);
      return false;
    }
  }

  /// إلغاء مبيعة وإرجاع المخزون
  Future<bool> cancelSale({
    required Sale sale,
    required List<SaleItem> saleItems,
    required String userId,
    String? reason,
  }) async {
    try {
      state = state.copyWith(isProcessing: true, error: null, successMessage: null);

      final success = await _service.cancelSale(
        sale: sale,
        saleItems: saleItems,
        userId: userId,
        reason: reason,
      );

      if (success) {
        state = state.copyWith(
          isProcessing: false,
          successMessage: 'تم إلغاء المبيعة وإرجاع المخزون بنجاح',
        );
      } else {
        state = state.copyWith(
          isProcessing: false,
          error: 'فشل في إلغاء المبيعة',
        );
      }

      return success;
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error cancelling sale', e);
      return false;
    }
  }

  // ------------------------------------------------------------------
  // Purchase Integration
  // ------------------------------------------------------------------

  /// معالجة مشتريات وتحديث المخزون
  Future<bool> processPurchase({
    required Purchase purchase,
    required List<PurchaseItem> purchaseItems,
    required String userId,
    String? warehouseId,
  }) async {
    try {
      state = state.copyWith(isProcessing: true, error: null, successMessage: null);

      final success = await _service.processPurchase(
        purchase: purchase,
        purchaseItems: purchaseItems,
        userId: userId,
        warehouseId: warehouseId,
      );

      if (success) {
        state = state.copyWith(
          isProcessing: false,
          successMessage: 'تم معالجة المشتريات وتحديث المخزون بنجاح',
        );
      } else {
        state = state.copyWith(
          isProcessing: false,
          error: 'فشل في معالجة المشتريات',
        );
      }

      return success;
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error processing purchase', e);
      return false;
    }
  }

  /// إلغاء مشتريات وتقليل المخزون
  Future<bool> cancelPurchase({
    required Purchase purchase,
    required List<PurchaseItem> purchaseItems,
    required String userId,
    String? reason,
  }) async {
    try {
      state = state.copyWith(isProcessing: true, error: null, successMessage: null);

      final success = await _service.cancelPurchase(
        purchase: purchase,
        purchaseItems: purchaseItems,
        userId: userId,
        reason: reason,
      );

      if (success) {
        state = state.copyWith(
          isProcessing: false,
          successMessage: 'تم إلغاء المشتريات وتعديل المخزون بنجاح',
        );
      } else {
        state = state.copyWith(
          isProcessing: false,
          error: 'فشل في إلغاء المشتريات',
        );
      }

      return success;
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error cancelling purchase', e);
      return false;
    }
  }

  // ------------------------------------------------------------------
  // Stock Validation
  // ------------------------------------------------------------------

  /// التحقق من توفر المخزون للمبيعة
  Future<Map<String, dynamic>?> validateStockForSale({
    required List<SaleItem> saleItems,
    String? warehouseId,
  }) async {
    try {
      state = state.copyWith(isProcessing: true, error: null);

      final validation = await _service.validateStockForSale(
        saleItems: saleItems,
        warehouseId: warehouseId,
      );

      state = state.copyWith(
        isProcessing: false,
        lastValidation: validation,
      );

      return validation;
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error validating stock for sale', e);
      return null;
    }
  }

  /// حجز المخزون للمبيعة
  Future<bool> reserveStockForSale({
    required List<SaleItem> saleItems,
    required String saleId,
    String? warehouseId,
  }) async {
    try {
      state = state.copyWith(isProcessing: true, error: null, successMessage: null);

      final success = await _service.reserveStockForSale(
        saleItems: saleItems,
        saleId: saleId,
        warehouseId: warehouseId,
      );

      if (success) {
        state = state.copyWith(
          isProcessing: false,
          successMessage: 'تم حجز المخزون بنجاح',
        );
      } else {
        state = state.copyWith(
          isProcessing: false,
          error: 'فشل في حجز المخزون',
        );
      }

      return success;
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error reserving stock for sale', e);
      return false;
    }
  }

  /// إلغاء حجز المخزون
  Future<bool> releaseReservedStock({
    required List<SaleItem> saleItems,
    required String saleId,
    String? warehouseId,
  }) async {
    try {
      state = state.copyWith(isProcessing: true, error: null, successMessage: null);

      final success = await _service.releaseReservedStock(
        saleItems: saleItems,
        saleId: saleId,
        warehouseId: warehouseId,
      );

      if (success) {
        state = state.copyWith(
          isProcessing: false,
          successMessage: 'تم إلغاء حجز المخزون بنجاح',
        );
      } else {
        state = state.copyWith(
          isProcessing: false,
          error: 'فشل في إلغاء حجز المخزون',
        );
      }

      return success;
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error releasing reserved stock', e);
      return false;
    }
  }

  // ------------------------------------------------------------------
  // Utility Methods
  // ------------------------------------------------------------------

  /// مسح الرسائل
  void clearMessages() {
    state = state.copyWith(
      error: null,
      successMessage: null,
    );
  }

  /// مسح آخر تحقق
  void clearLastValidation() {
    state = state.copyWith(lastValidation: null);
  }
}

// Additional providers for specific operations
final stockValidationProvider = FutureProvider.family<Map<String, dynamic>, Map<String, dynamic>>((ref, params) async {
  final service = ref.read(inventoryIntegrationServiceProvider);
  
  final saleItems = params['saleItems'] as List<SaleItem>;
  final warehouseId = params['warehouseId'] as String?;

  return await service.validateStockForSale(
    saleItems: saleItems,
    warehouseId: warehouseId,
  );
});
