import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../router/routes.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/responsive_wrapper.dart';

class EmployeesPage extends ConsumerWidget {
  const EmployeesPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MainLayout(
      title: 'الموظفين',
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go(AppRoutes.employeeAdd),
        child: const Icon(Icons.person_add),
      ),
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Employee Stats
            _buildEmployeeStats(context),
            SizedBox(height: 24.h),
            
            // Quick Actions
            _buildQuickActions(context),
            SizedBox(height: 24.h),
            
            // Employee List
            _buildEmployeeList(context),
          ],
        ),
      ),
    );
  }

  Widget _buildEmployeeStats(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات الموظفين',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 4,
          desktopColumns: 4,
          children: [
            _buildStatCard(
              context,
              title: 'إجمالي الموظفين',
              value: '24',
              icon: Icons.people_outline,
              color: AppColors.primary,
            ),
            _buildStatCard(
              context,
              title: 'الموظفين النشطين',
              value: '22',
              icon: Icons.person_outline,
              color: AppColors.success,
            ),
            _buildStatCard(
              context,
              title: 'إجمالي الرواتب',
              value: '85,500 ر.س',
              icon: Icons.account_balance_wallet_outlined,
              color: AppColors.info,
            ),
            _buildStatCard(
              context,
              title: 'رواتب هذا الشهر',
              value: '78,200 ر.س',
              icon: Icons.payment_outlined,
              color: AppColors.warning,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32.r,
              color: color,
            ),
            SizedBox(height: 8.h),
            Text(
              value,
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              title,
              style: AppTextStyles.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 3,
          desktopColumns: 4,
          children: [
            _buildActionCard(
              context,
              title: 'إضافة موظف',
              subtitle: 'تسجيل موظف جديد',
              icon: Icons.person_add_outlined,
              color: AppColors.primary,
              onTap: () => context.go(AppRoutes.employeeAdd),
            ),
            _buildActionCard(
              context,
              title: 'قائمة الموظفين',
              subtitle: 'عرض جميع الموظفين',
              icon: Icons.people_outline,
              color: AppColors.secondary,
              onTap: () => context.go(AppRoutes.employeeList),
            ),
            _buildActionCard(
              context,
              title: 'إدارة الرواتب',
              subtitle: 'حساب ودفع الرواتب',
              icon: Icons.account_balance_wallet_outlined,
              color: AppColors.success,
              onTap: () => context.go(AppRoutes.salaries),
            ),
            _buildActionCard(
              context,
              title: 'تقرير الحضور',
              subtitle: 'تقرير حضور الموظفين',
              icon: Icons.access_time_outlined,
              color: AppColors.info,
              onTap: () {
                // TODO: Navigate to attendance report
              },
            ),
            _buildActionCard(
              context,
              title: 'الإجازات',
              subtitle: 'إدارة إجازات الموظفين',
              icon: Icons.event_available_outlined,
              color: AppColors.warning,
              onTap: () {
                // TODO: Navigate to leaves
              },
            ),
            _buildActionCard(
              context,
              title: 'تقييم الأداء',
              subtitle: 'تقييم أداء الموظفين',
              icon: Icons.star_outline,
              color: AppColors.accent,
              onTap: () {
                // TODO: Navigate to performance
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32.r,
                color: color,
              ),
              SizedBox(height: 8.h),
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 4.h),
              Text(
                subtitle,
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmployeeList(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'الموظفين الأخيرين',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => context.go(AppRoutes.employeeList),
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Card(
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 5,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final employees = [
                {
                  'name': 'أحمد محمد علي',
                  'position': 'مدير المبيعات',
                  'salary': '8,500',
                  'status': 'نشط',
                },
                {
                  'name': 'فاطمة أحمد',
                  'position': 'محاسبة',
                  'salary': '6,200',
                  'status': 'نشط',
                },
                {
                  'name': 'محمد عبدالله',
                  'position': 'مندوب مبيعات',
                  'salary': '4,800',
                  'status': 'نشط',
                },
                {
                  'name': 'نورا سالم',
                  'position': 'أمين مخزن',
                  'salary': '3,500',
                  'status': 'إجازة',
                },
                {
                  'name': 'خالد العتيبي',
                  'position': 'سائق',
                  'salary': '3,200',
                  'status': 'نشط',
                },
              ];
              
              final employee = employees[index];
              
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                  child: Text(
                    employee['name']!.substring(0, 1),
                    style: AppTextStyles.titleMedium.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                title: Text(
                  employee['name']!,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      employee['position']!,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      'الراتب: ${employee['salary']} ر.س',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.success,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                trailing: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: employee['status'] == 'نشط' 
                        ? AppColors.success 
                        : AppColors.warning,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    employee['status']!,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                onTap: () {
                  // TODO: Navigate to employee details
                },
              );
            },
          ),
        ),
      ],
    );
  }
}
