// import 'package:flutter_test/flutter_test.dart';
// import 'package:tijari_tech/data/models/stock_movement.dart';
// import 'package:tijari_tech/data/models/sale_item.dart';
// import 'package:tijari_tech/data/models/purchase_item.dart';
// import 'package:tijari_tech/core/exceptions/app_exceptions.dart';
// import 'package:tijari_tech/core/utils/app_utils.dart';

// void main() {
//   group('Inventory Management Tests', () {
//     group('StockMovement Model Tests', () {
//       test('should create StockMovement with quantity parameter', () {
//         final stockMovement = StockMovement(
//           id: 'test-id',
//           productId: 'product-1',
//           warehouseId: 'warehouse-1',
//           movementType: 'in',
//           quantity: 10.0,
//           unitCost: 5.0,
//           totalValue: 50.0,
//           referenceType: 'purchase',
//           referenceId: 'purchase-1',
//           notes: 'Test movement',
//           createdBy: 'test-user',
//           createdAt: DateTime.now(),
//           isSynced: false,
//         );

//         expect(stockMovement.quantity, equals(10.0));
//         expect(stockMovement.movementType, equals('in'));
//         expect(stockMovement.productId, equals('product-1'));
//       });

//       test('should serialize to JSON correctly', () {
//         final stockMovement = StockMovement(
//           id: 'test-id',
//           productId: 'product-1',
//           warehouseId: 'warehouse-1',
//           movementType: 'in',
//           quantity: 10.0,
//           unitCost: 5.0,
//           totalValue: 50.0,
//           referenceType: 'purchase',
//           referenceId: 'purchase-1',
//           notes: 'Test movement',
//           createdBy: 'test-user',
//           createdAt: DateTime.now(),
//           isSynced: false,
//         );

//         final json = stockMovement.toJson();

//         expect(json['quantity'], equals(10.0));
//         expect(json['qty'], equals(10.0)); // Backward compatibility
//         expect(json['movement_type'], equals('in'));
//         expect(json['product_id'], equals('product-1'));
//       });

//       test('should deserialize from JSON correctly', () {
//         final json = {
//           'id': 'test-id',
//           'product_id': 'product-1',
//           'warehouse_id': 'warehouse-1',
//           'movement_type': 'in',
//           'quantity': 10.0,
//           'qty': 10.0,
//           'unit_cost': 5.0,
//           'total_value': 50.0,
//           'reference_type': 'purchase',
//           'reference_id': 'purchase-1',
//           'notes': 'Test movement',
//           'created_by': 'test-user',
//           'created_at': DateTime.now().toIso8601String(),
//           'is_synced': 0,
//         };

//         final stockMovement = StockMovement.fromJson(json);

//         expect(stockMovement.quantity, equals(10.0));
//         expect(stockMovement.movementType, equals('in'));
//         expect(stockMovement.productId, equals('product-1'));
//       });
//     });

//     group('SaleItem Model Tests', () {
//       test('should create SaleItem with qty parameter', () {
//         final saleItem = SaleItem.create(
//           id: 'item-1',
//           saleId: 'sale-1',
//           productId: 'product-1',
//           unitId: 'unit-1',
//           qty: 5.0,
//           unitPrice: 10.0,
//         );

//         expect(saleItem.qty, equals(5.0));
//         expect(saleItem.total, equals(50.0));
//         expect(saleItem.productId, equals('product-1'));
//       });

//       test('should update quantity correctly', () {
//         final saleItem = SaleItem.create(
//           id: 'item-1',
//           saleId: 'sale-1',
//           productId: 'product-1',
//           unitId: 'unit-1',
//           qty: 5.0,
//           unitPrice: 10.0,
//         );

//         final updatedItem = saleItem.updateQuantity(8.0);

//         expect(updatedItem.qty, equals(8.0));
//         expect(updatedItem.total, equals(80.0));
//         expect(updatedItem.productId, equals('product-1'));
//       });
//     });

//     group('PurchaseItem Model Tests', () {
//       test('should create PurchaseItem with qty parameter', () {
//         final purchaseItem = PurchaseItem.create(
//           purchaseId: 'purchase-1',
//           productId: 'product-1',
//           unitId: 'unit-1',
//           qty: 20.0,
//           unitPrice: 8.0,
//         );

//         expect(purchaseItem.qty, equals(20.0));
//         expect(purchaseItem.totalPrice, equals(160.0));
//         expect(purchaseItem.productId, equals('product-1'));
//       });

//       test('should update quantity correctly', () {
//         final purchaseItem = PurchaseItem.create(
//           purchaseId: 'purchase-1',
//           productId: 'product-1',
//           unitId: 'unit-1',
//           qty: 20.0,
//           unitPrice: 8.0,
//         );

//         final updatedItem = purchaseItem.updateQty(25.0);

//         expect(updatedItem.qty, equals(25.0));
//         expect(updatedItem.totalPrice, equals(200.0));
//         expect(updatedItem.productId, equals('product-1'));
//       });
//     });

//     group('Validation Tests', () {
//       test('should throw ValidationException for invalid data', () {
//         expect(
//           () => throw ValidationException('Test validation error'),
//           throwsA(isA<ValidationException>()),
//         );
//       });

//       test('should handle validation messages correctly', () {
//         final exception = ValidationException('Invalid quantity');
//         expect(exception.message, equals('Invalid quantity'));
//         expect(exception.toString(), contains('ValidationException'));
//       });
//     });

//     group('Utility Tests', () {
//       test('should generate unique IDs', () {
//         final id1 = AppUtils.generateId();
//         final id2 = AppUtils.generateId();

//         expect(id1, isNotEmpty);
//         expect(id2, isNotEmpty);
//         expect(id1, isNot(equals(id2)));
//       });

//       test('should log messages correctly', () {
//         // Test logging functionality
//         expect(() => AppUtils.logInfo('Test message'), returnsNormally);
//         expect(() => AppUtils.logError('Test error', 'error details'),
//             returnsNormally);
//       });
//     });

//     group('Integration Tests', () {
//       test('should handle stock movement creation workflow', () {
//         // Test the complete workflow of creating a stock movement
//         final movement = StockMovement(
//           id: AppUtils.generateId(),
//           productId: 'product-1',
//           warehouseId: 'warehouse-1',
//           movementType: 'in',
//           quantity: 15.0,
//           unitCost: 7.0,
//           totalValue: 105.0,
//           referenceType: 'purchase',
//           referenceId: 'purchase-1',
//           notes: 'Integration test movement',
//           createdBy: 'test-user',
//           createdAt: DateTime.now(),
//           isSynced: false,
//         );

//         // Verify the movement was created correctly
//         expect(movement.id, isNotEmpty);
//         expect(movement.quantity, equals(15.0));
//         expect(movement.totalValue, equals(105.0));

//         // Test JSON serialization/deserialization
//         final json = movement.toJson();
//         final recreatedMovement = StockMovement.fromJson(json);

//         expect(recreatedMovement.quantity, equals(movement.quantity));
//         expect(recreatedMovement.movementType, equals(movement.movementType));
//         expect(recreatedMovement.productId, equals(movement.productId));
//       });

//       test('should handle sale item workflow', () {
//         // Create a sale item
//         final saleItem = SaleItem.create(
//           id: AppUtils.generateId(),
//           saleId: 'sale-1',
//           productId: 'product-1',
//           unitId: 'unit-1',
//           qty: 3.0,
//           unitPrice: 15.0,
//         );

//         expect(saleItem.qty, equals(3.0));
//         expect(saleItem.total, equals(45.0));

//         // Update the quantity
//         final updatedItem = saleItem.updateQuantity(5.0);
//         expect(updatedItem.qty, equals(5.0));
//         expect(updatedItem.total, equals(75.0));

//         // Update the price
//         final priceUpdatedItem = updatedItem.update(unitPrice: 20.0);
//         expect(priceUpdatedItem.unitPrice, equals(20.0));
//         expect(priceUpdatedItem.total, equals(100.0));
//       });
//     });
//   });
// }
