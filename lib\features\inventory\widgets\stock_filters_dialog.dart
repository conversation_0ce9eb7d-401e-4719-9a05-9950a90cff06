import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/colors.dart';
import '../../../core/theme/text_styles.dart';
import '../../../data/models/stock.dart';
import '../../../providers/stock_provider.dart';
import '../../../providers/warehouse_provider.dart';
import '../../../providers/enhanced_product_provider.dart';

/// حوار تصفية المخزون
///
/// يوفر واجهة لتصفية المخزون حسب:
/// - المخزن
/// - المنتج
/// - حالة المخزون
///
/// Ref: تم تطوير هذا الحوار لتحسين تجربة المستخدم في البحث والتصفية

class StockFiltersDialog extends ConsumerStatefulWidget {
  const StockFiltersDialog({super.key});

  @override
  ConsumerState<StockFiltersDialog> createState() => _StockFiltersDialogState();
}

class _StockFiltersDialogState extends ConsumerState<StockFiltersDialog> {
  String? selectedWarehouse;
  String? selectedProduct;
  StockStatus? selectedStatus;

  @override
  void initState() {
    super.initState();
    final state = ref.read(stockManagementProvider);
    selectedWarehouse = state.selectedWarehouse;
    selectedProduct = state.selectedProduct;
    selectedStatus = state.statusFilter;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        padding: EdgeInsets.all(24.w),
        constraints: BoxConstraints(
          maxWidth: 400.w,
          maxHeight: 600.h,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.filter_list,
                  color: AppColors.primary,
                  size: 24.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  'تصفية المخزون',
                  style: AppTextStyles.titleLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            SizedBox(height: 24.h),

            // Warehouse filter
            Text(
              'المخزن',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            Consumer(
              builder: (context, ref, child) {
                final warehousesAsync = ref.watch(activeWarehousesProvider);

                return warehousesAsync.when(
                  data: (warehouses) => DropdownButtonFormField<String>(
                    value: selectedWarehouse,
                    decoration: InputDecoration(
                      hintText: 'اختر المخزن',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 12.w,
                        vertical: 8.h,
                      ),
                    ),
                    items: [
                      const DropdownMenuItem<String>(
                        value: null,
                        child: Text('جميع المخازن'),
                      ),
                      ...warehouses.map((warehouse) => DropdownMenuItem<String>(
                            value: warehouse.id,
                            child: Text(warehouse.name),
                          )),
                    ],
                    onChanged: (value) {
                      setState(() {
                        selectedWarehouse = value;
                      });
                    },
                  ),
                  loading: () => const CircularProgressIndicator(),
                  error: (error, stack) => Text('خطأ في تحميل المخازن'),
                );
              },
            ),

            SizedBox(height: 16.h),

            // Product filter
            Text(
              'المنتج',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            Consumer(
              builder: (context, ref, child) {
                final products = ref.watch(enhancedProductManagementProvider
                    .select((state) => state.products));

                return DropdownButtonFormField<String>(
                  value: selectedProduct,
                  decoration: InputDecoration(
                    hintText: 'اختر المنتج',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12.w,
                      vertical: 8.h,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem<String>(
                      value: null,
                      child: Text('جميع المنتجات'),
                    ),
                    ...products.map((product) => DropdownMenuItem<String>(
                          value: product.id,
                          child: Text(product.nameAr),
                        )),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedProduct = value;
                    });
                  },
                );
              },
            ),

            SizedBox(height: 16.h),

            // Status filter
            Text(
              'حالة المخزون',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            DropdownButtonFormField<StockStatus>(
              value: selectedStatus,
              decoration: InputDecoration(
                hintText: 'اختر الحالة',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 8.h,
                ),
              ),
              items: [
                const DropdownMenuItem<StockStatus>(
                  value: null,
                  child: Text('جميع الحالات'),
                ),
                const DropdownMenuItem<StockStatus>(
                  value: StockStatus.inStock,
                  child: Text('متوفر'),
                ),
                const DropdownMenuItem<StockStatus>(
                  value: StockStatus.lowStock,
                  child: Text('منخفض'),
                ),
                const DropdownMenuItem<StockStatus>(
                  value: StockStatus.outOfStock,
                  child: Text('نفد'),
                ),
                const DropdownMenuItem<StockStatus>(
                  value: StockStatus.overStock,
                  child: Text('زائد'),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  selectedStatus = value;
                });
              },
            ),

            SizedBox(height: 32.h),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      ref.read(stockManagementProvider.notifier).clearFilters();
                      Navigator.of(context).pop();
                    },
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    child: Text(
                      'مسح الكل',
                      style: AppTextStyles.bodyMedium,
                    ),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      ref.read(stockManagementProvider.notifier)
                        ..filterByWarehouse(selectedWarehouse)
                        ..filterByProduct(selectedProduct)
                        ..filterByStatus(selectedStatus);
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    child: Text(
                      'تطبيق',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
