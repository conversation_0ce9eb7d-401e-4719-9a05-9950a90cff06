/* ------------------------------------------------------------------ */
/* ---------------- حوار إنشاء شجرة الوحدات ----------------------- */
/* ------------------------------------------------------------------ */
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tijari_tech/core/theme/colors.dart';
import 'package:tijari_tech/core/theme/text_styles.dart';
import 'package:tijari_tech/core/uint_type.dart';
import 'package:tijari_tech/core/utils/extensions.dart';
import 'package:tijari_tech/providers/unit_provider.dart';

class CreateUnitTreeDialog extends ConsumerStatefulWidget {
  const CreateUnitTreeDialog({super.key});

  @override
  ConsumerState<CreateUnitTreeDialog> createState() =>
      _CreateUnitTreeDialogState();
}

class _CreateUnitTreeDialogState extends ConsumerState<CreateUnitTreeDialog> {
  final _formKey = GlobalKey<FormState>();
  final _baseNameController = TextEditingController();
  final _baseSymbolController = TextEditingController();
  final _baseAbbreviationController = TextEditingController();
  final _descriptionController = TextEditingController();

  UnitType _selectedType = UnitType.count;
  bool _isLoading = false;

  final List<Map<String, dynamic>> _derivedUnits = [
    {
      'name': '',
      'symbol': '',
      'abbreviation': '',
      'factor': 1.0,
      'description': ''
    },
  ];

  @override
  void dispose() {
    _baseNameController.dispose();
    _baseSymbolController.dispose();
    _baseAbbreviationController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600.w,
        height: 700.h,
        padding: EdgeInsets.all(24.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.account_tree,
                  color: AppColors.secondary,
                  size: 24.r,
                ),
                SizedBox(width: 8.w),
                Text(
                  'إنشاء شجرة وحدات',
                  style: AppTextStyles.titleLarge
                      .copyWith(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            // Form
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الوحدة الأساسية',
                        style: AppTextStyles.titleMedium
                            .copyWith(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 12.h),
                      TextFormField(
                        controller: _baseNameController,
                        decoration: const InputDecoration(
                          labelText: 'اسم الوحدة الأساسية (عربي) *',
                          border: OutlineInputBorder(),
                        ),
                        validator: (v) => v == null || v.trim().isEmpty
                            ? 'اسم الوحدة مطلوب'
                            : null,
                      ),
                  
                      SizedBox(height: 12.h),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _baseSymbolController,
                              decoration: const InputDecoration(
                                labelText: 'الرمز *',
                                border: OutlineInputBorder(),
                              ),
                              validator: (v) => v == null || v.trim().isEmpty
                                  ? 'الرمز مطلوب'
                                  : null,
                            ),
                          ),
                          SizedBox(width: 12.w),
                          Expanded(
                            child: TextFormField(
                              controller: _baseAbbreviationController,
                              decoration: const InputDecoration(
                                labelText: 'الاختصار',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 12.h),
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'الوصف',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 2,
                      ),
                      SizedBox(height: 12.h),
                      DropdownButtonFormField<UnitType>(
                        value: _selectedType,
                        decoration: const InputDecoration(
                          labelText: 'نوع الوحدة *',
                          border: OutlineInputBorder(),
                        ),
                        items: UnitType.values
                            .map((t) => DropdownMenuItem(
                                  value: t,
                                  child: Text(_getUnitTypeDisplayName(t)),
                                ))
                            .toList(),
                        onChanged: (v) => setState(() => _selectedType = v!),
                      ),
                      SizedBox(height: 24.h),

                      // الوحدات المشتقة
                      Row(
                        children: [
                          Text(
                            'الوحدات المشتقة',
                            style: AppTextStyles.titleMedium
                                .copyWith(fontWeight: FontWeight.bold),
                          ),
                          const Spacer(),
                          IconButton(
                            icon: const Icon(Icons.add),
                            onPressed: _addDerivedUnit,
                            tooltip: 'إضافة وحدة مشتقة',
                          ),
                        ],
                      ),
                      SizedBox(height: 12.h),
                      ..._derivedUnits.asMap().entries.map(
                            (entry) =>
                                _buildDerivedUnitCard(entry.key, entry.value),
                          ),
                    ],
                  ),
                ),
              ),
            ),
            // Actions
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed:
                        _isLoading ? null : () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _submit,
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2))
                        : const Text('إنشاء الشجرة'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDerivedUnitCard(int index, Map<String, dynamic> unit) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      child: Padding(
        padding: EdgeInsets.all(12.r),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'الوحدة المشتقة ${index + 1}',
                    style: AppTextStyles.bodyMedium
                        .copyWith(fontWeight: FontWeight.bold),
                  ),
                ),
                if (_derivedUnits.length > 1)
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red),
                    onPressed: () => _removeDerivedUnit(index),
                  ),
              ],
            ),
            SizedBox(height: 12.h),
            TextFormField(
              initialValue: unit['name'],
              decoration: const InputDecoration(
                labelText: 'اسم الوحدة *',
                border: OutlineInputBorder(),
              ),
              validator: (v) =>
                  v == null || v.trim().isEmpty ? 'اسم الوحدة مطلوب' : null,
              onChanged: (value) => unit['name'] = value,
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    initialValue: unit['symbol'],
                    decoration: const InputDecoration(
                      labelText: 'الرمز *',
                      border: OutlineInputBorder(),
                    ),
                    validator: (v) =>
                        v == null || v.trim().isEmpty ? 'الرمز مطلوب' : null,
                    onChanged: (value) => unit['symbol'] = value,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: TextFormField(
                    initialValue: unit['abbreviation'],
                    decoration: const InputDecoration(
                      labelText: 'الاختصار',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) => unit['abbreviation'] = value,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            TextFormField(
              initialValue: unit['factor'].toString(),
              decoration: const InputDecoration(
                labelText: 'معامل التحويل *',
                border: OutlineInputBorder(),
              ),
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              validator: (v) {
                if (v == null || v.trim().isEmpty) {
                  return 'معامل التحويل مطلوب';
                }
                final val = double.tryParse(v);
                return val == null || val <= 0
                    ? 'أدخل رقمًا صحيحًا أكبر من صفر'
                    : null;
              },
              onChanged: (value) =>
                  unit['factor'] = double.tryParse(value) ?? 1.0,
            ),
            SizedBox(height: 12.h),
            TextFormField(
              initialValue: unit['description'],
              decoration: const InputDecoration(
                labelText: 'الوصف',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
              onChanged: (value) => unit['description'] = value,
            ),
          ],
        ),
      ),
    );
  }

  void _addDerivedUnit() {
    setState(() {
      _derivedUnits.add({
        'name': '',
        'symbol': '',
        'abbreviation': '',
        'factor': 1.0,
        'description': '',
      });
    });
  }

  void _removeDerivedUnit(int index) {
    setState(() {
      _derivedUnits.removeAt(index);
    });
  }

  String _getUnitTypeDisplayName(UnitType type) {
    switch (type) {
      case UnitType.weight:
        return 'وزن';
      case UnitType.length:
        return 'طول';
      case UnitType.volume:
        return 'حجم';
      case UnitType.area:
        return 'مساحة';
      case UnitType.count:
        return 'عدد';
      case UnitType.time:
        return 'وقت';
      case UnitType.other:
        return 'أخرى';
    }
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final notifier = ref.read(unitManagementProvider.notifier);

      // تحضير بيانات الوحدات المشتقة
      final derivedUnitsData = _derivedUnits
          .where((unit) => unit['name'].toString().trim().isNotEmpty)
          .map((unit) => {
                'name': unit['name'].toString().trim(),
                'symbol': unit['symbol'].toString().trim(),
                'abbreviation': unit['abbreviation'].toString().trim(),
                'factor': unit['factor'] as double,
                'description': unit['description'].toString().trim(),
              })
          .toList();

      final success = await notifier.createUnitTree(
        baseName: _baseNameController.text.trim(),
        baseSymbol: _baseSymbolController.text.trim(),
        baseAbbreviation: _baseAbbreviationController.text.trim(),
        unitType: _selectedType.toString().split('.').last,
        derivedUnitsData: derivedUnitsData,
        description: _descriptionController.text.trim(),
      );

      if (mounted) {
        if (success) {
          Navigator.of(context).pop();
          context.showSuccessSnackBar('تم إنشاء شجرة الوحدات بنجاح');
        } else {
          context.showErrorSnackBar('فشل في إنشاء شجرة الوحدات');
        }
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar('حدث خطأ: ${e.toString()}');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
