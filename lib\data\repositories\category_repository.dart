import 'package:tijari_tech/core/constants/database_constants.dart';

import '../local/dao/category_dao.dart';
import '../models/category.dart';
import '../../core/utils/app_utils.dart';
import '../../core/exceptions/app_exceptions.dart';

class CategoryRepository {
  final CategoryDao _categoryDao = CategoryDao();

  // ------------------------------------------------------------------
  // CRUD Operations
  // ------------------------------------------------------------------

  /// إنشاء فئة جديدة
  Future<String> createCategory(Category category) async {
    try {
      await _validateCategory(category);

      return await _categoryDao.insert(category);
    } catch (e) {
      AppUtils.logError('Error creating category', e);
      rethrow;
    }
  }

  /// استرجاع جميع الفئات
  Future<List<Category>> getAllCategories({
    String? orderBy,
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    try {
      return await _categoryDao.findAll(
        orderBy: orderBy ?? DatabaseConstants.columnCategoryNameAr,
        ascending: ascending,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      AppUtils.logError('Error getting all categories', e);
      throw RepositoryException('Failed to get categories');
    }
  }

  /// استرجاع الفئات النشطة فقط
  Future<List<Category>> getActiveCategories({
    String? orderBy,
    bool ascending = true,
  }) async {
    try {
      return await _categoryDao.findWhere(
        where: '${DatabaseConstants.columnCategoryDeletedAt} IS NULL',
        orderBy: orderBy ?? DatabaseConstants.columnCategoryNameAr,
        ascending: ascending,
      );
    } catch (e) {
      AppUtils.logError('Error getting active categories', e);
      return [];
    }
  }

  /// استرجاع فئة حسب ID
  Future<Category?> getCategoryById(String id) async {
    try {
      return await _categoryDao.findById(id);
    } catch (e) {
      AppUtils.logError('Error getting category by ID', e);
      return null;
    }
  }

  /// استرجاع الفئات الجذرية (بدون أب)
  Future<List<Category>> getRootCategories() async {
    try {
      return await _categoryDao.getRootCategories();
    } catch (e) {
      AppUtils.logError('Error getting root categories', e);
      return [];
    }
  }

  /// استرجاع الفئات الفرعية لفئة معينة
  Future<List<Category>> getSubcategories(String parentId) async {
    try {
      return await _categoryDao.getSubcategories(parentId);
    } catch (e) {
      AppUtils.logError('Error getting subcategories', e);
      return [];
    }
  }

  /// تحديث فئة
  Future<bool> updateCategory(String id, Category category) async {
    try {
      await _validateCategory(category);
      return await _categoryDao.update(id, category);
    } catch (e) {
      AppUtils.logError('Error updating category', e);
      return false;
    }
  }

  /// حذف فئة (soft delete)
  Future<bool> deleteCategory(String id) async {
    try {
      return await _categoryDao.delete(id);
    } catch (e) {
      AppUtils.logError('Error deleting category', e);
      return false;
    }
  }

  /// استرجاع هرم الفئات
  Future<List<Map<String, dynamic>>> getCategoryHierarchy() async {
    try {
      return await _categoryDao.getCategoryHierarchy();
    } catch (e) {
      AppUtils.logError('Error getting category hierarchy', e);
      return [];
    }
  }

  // ------------------------------------------------------------------
  // Validation
  // ------------------------------------------------------------------

  /// التحقق من صحة بيانات الفئة
  Future<void> _validateCategory(Category category) async {
    if (category.nameAr.trim().isEmpty) {
      throw ValidationException('Category Arabic name is required');
    }

    if (category.nameEn!.trim().isEmpty) {
      throw ValidationException('Category English name is required');
    }

    // التحقق من عدم تكرار الاسم
    final existingCategories = await _categoryDao.findWhere(
      where: '''
        (${DatabaseConstants.columnCategoryNameAr} = ? OR 
         ${DatabaseConstants.columnCategoryNameEn} = ?) AND
        ${DatabaseConstants.columnCategoryId} != ? AND
        ${DatabaseConstants.columnCategoryDeletedAt} IS NULL
      ''',
      whereArgs: [category.nameAr, category.nameEn, category.id],
    );

    if (existingCategories.isNotEmpty) {
      throw ValidationException('Category name already exists');
    }
  }

  // ------------------------------------------------------------------
  // Enhanced Operations (merged from EnhancedCategoryRepository)
  // ------------------------------------------------------------------

  /// استرجاع البنية الشجرية للفئات
  Future<List<Category>> getCategoriesTree() async {
    try {
      return await _categoryDao.getCategoriesTree();
    } catch (e) {
      AppUtils.logError('Error getting categories tree', e);
      throw RepositoryException('Failed to get categories tree');
    }
  }

  /// استرجاع الفئات مع عدد المنتجات
  Future<List<Category>> getCategoriesWithProductCount() async {
    try {
      return await _categoryDao.getCategoriesWithProductCount();
    } catch (e) {
      AppUtils.logError('Error getting categories with product count', e);
      throw RepositoryException('Failed to get categories with product count');
    }
  }

  /// البحث المتقدم في الفئات
  Future<List<Category>> searchCategories({
    String? searchTerm,
    String? parentId,
    bool? isActive,
    String? orderBy,
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    try {
      return await _categoryDao.advancedSearch(
        searchTerm: searchTerm,
        parentId: parentId,
        isActive: isActive,
        orderBy: orderBy,
        ascending: ascending,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      AppUtils.logError('Error searching categories', e);
      throw RepositoryException('Failed to search categories');
    }
  }

  /// استرجاع إحصائيات الفئات
  Future<Map<String, dynamic>> getCategoriesStatistics() async {
    try {
      return await _categoryDao.getCategoriesStatistics();
    } catch (e) {
      AppUtils.logError('Error getting categories statistics', e);
      throw RepositoryException('Failed to get categories statistics');
    }
  }

  /// استرجاع الفئات الأكثر استخداماً
  Future<List<Category>> getMostUsedCategories({int limit = 10}) async {
    try {
      return await _categoryDao.advancedSearch(
        isActive: true,
        orderBy: 'product_count',
        ascending: false,
        limit: limit,
      );
    } catch (e) {
      AppUtils.logError('Error getting most used categories', e);
      throw RepositoryException('Failed to get most used categories');
    }
  }

  /// نقل فئة إلى فئة أب جديدة
  Future<bool> moveCategory(String categoryId, String? newParentId) async {
    try {
      // التحقق من عدم إنشاء حلقة مفرغة
      if (newParentId != null) {
        final category = await _categoryDao.findById(categoryId);
        if (category == null) {
          throw ValidationException('Category not found');
        }

        // التحقق من أن الفئة الجديدة ليست فرعية للفئة المراد نقلها
        final newParent = await _categoryDao.findById(newParentId);
        if (newParent != null && newParent.isDescendantOf(category)) {
          throw ValidationException(
              'Cannot move category to its own descendant');
        }
      }

      final category = await _categoryDao.findById(categoryId);
      if (category == null) return false;

      final updatedCategory = category.copyWith(
        parentId: newParentId,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

      return await _categoryDao.update(categoryId, updatedCategory);
    } catch (e) {
      AppUtils.logError('Error moving category', e);
      rethrow;
    }
  }

  /// تحديث ترتيب الفئات
  Future<bool> updateCategoriesOrder(Map<String, int> categoryOrders) async {
    try {
      for (final entry in categoryOrders.entries) {
        final categoryId = entry.key;
        final sortOrder = entry.value;

        final category = await _categoryDao.findById(categoryId);
        if (category != null) {
          final updatedCategory = category.copyWith(
            sortOrder: sortOrder,
            updatedAt: DateTime.now(),
            isSynced: false,
          );
          await _categoryDao.update(categoryId, updatedCategory);
        }
      }

      AppUtils.logInfo('Categories order updated successfully');
      return true;
    } catch (e) {
      AppUtils.logError('Error updating categories order', e);
      return false;
    }
  }

  /// تطبيق معدل ضريبة على فئة وفئاتها الفرعية
  Future<bool> applyTaxRateToCategory(String categoryId, double taxRate) async {
    try {
      final category = await _categoryDao.findById(categoryId);
      if (category == null) return false;

      // تحديث الفئة الحالية
      final updatedCategory = category.copyWith(
        taxRate: taxRate,
        updatedAt: DateTime.now(),
        isSynced: false,
      );
      await _categoryDao.update(categoryId, updatedCategory);

      // تحديث الفئات الفرعية
      final subcategories = await _categoryDao.getSubcategories(categoryId);
      for (final subcategory in subcategories) {
        await applyTaxRateToCategory(subcategory.id, taxRate);
      }

      AppUtils.logInfo('Tax rate applied to category and subcategories');
      return true;
    } catch (e) {
      AppUtils.logError('Error applying tax rate to category', e);
      return false;
    }
  }

  /// استرجاع فئة مع التفاصيل الكاملة
  Future<Category?> getEnhancedCategoryWithDetails(String id) async {
    try {
      return await _categoryDao.findById(id);
    } catch (e) {
      AppUtils.logError('Error getting enhanced category with details', e);
      return null;
    }
  }
}
