/// نظام التوجيه المتكامل للتطبيق
/// 
/// يحتوي على جميع الملفات المطلوبة لإدارة التنقل والتوجيه في التطبيق
/// 
/// الملفات المتضمنة:
/// - [app_router.dart]: إعد<PERSON> GoRouter الرئيسي
/// - [routes.dart]: تعريف جميع المسارات
/// - [navigation_service.dart]: خدمة التنقل المركزية
/// - [route_guard.dart]: حماية المسارات والصلاحيات
/// - [sidebar_navigation.dart]: إدارة التنقل في الشريط الجانبي

// Core routing files
export 'app_router.dart';
export 'routes.dart';

// Navigation services
export 'navigation_service.dart';
export 'route_guard.dart';
export 'sidebar_navigation.dart';

/// مثال على الاستخدام:
/// 
/// ```dart
/// // في main.dart
/// import 'package:tijari_tech/router/index.dart';
/// 
/// class MyApp extends ConsumerWidget {
///   @override
///   Widget build(BuildContext context, WidgetRef ref) {
///     final router = ref.watch(appRouterProvider);
///     
///     return MaterialApp.router(
///       routerConfig: router,
///       title: 'تجاري تك',
///     );
///   }
/// }
/// 
/// // في أي صفحة للتنقل
/// import 'package:tijari_tech/router/index.dart';
/// 
/// // التنقل إلى صفحة المنتجات
/// NavigationService.goToProducts(context);
/// 
/// // التنقل إلى تعديل منتج
/// NavigationService.goToProductEdit(context, productId);
/// 
/// // التحقق من الصلاحيات
/// if (RouteGuard.canEdit('inventory')) {
///   // عرض زر التعديل
/// }
/// 
/// // في الشريط الجانبي
/// final navigationItems = SidebarNavigation.getFilteredNavigationItems();
/// ```

/// الميزات الرئيسية:
/// 
/// 1. **التوجيه المتقدم**: استخدام GoRouter لإدارة التنقل
/// 2. **الحماية والصلاحيات**: نظام شامل لحماية المسارات
/// 3. **التنقل المركزي**: خدمة موحدة لجميع عمليات التنقل
/// 4. **الشريط الجانبي الذكي**: عرض العناصر حسب الصلاحيات
/// 5. **سهولة الصيانة**: تنظيم واضح ومنطقي للملفات

/// المسارات المتاحة:
/// 
/// **المخزون:**
/// - `/inventory` - صفحة المخزون الرئيسية
/// - `/inventory/products` - المنتجات
/// - `/inventory/categories` - التصنيفات
/// - `/inventory/units` - وحدات القياس
/// - `/inventory/warehouses` - المخازن
/// - `/inventory/stock-management` - إدارة المخزون
/// - `/inventory/stock-movements` - حركة المخزون
/// - `/inventory/stock-adjustment` - تسوية المخزون
/// - `/inventory/stock-transfer` - نقل المخزون
/// 
/// **المبيعات:**
/// - `/sales` - صفحة المبيعات الرئيسية
/// - `/sales/pos` - نقطة البيع
/// - `/sales/add` - إضافة مبيعة
/// - `/sales/list` - قائمة المبيعات
/// - `/sales/return` - مرتجعات المبيعات
/// 
/// **المشتريات:**
/// - `/purchases` - صفحة المشتريات الرئيسية
/// - `/purchases/add` - إضافة مشتريات
/// - `/purchases/list` - قائمة المشتريات
/// - `/purchases/return` - مرتجعات المشتريات
/// 
/// **الحسابات:**
/// - `/accounts` - صفحة الحسابات الرئيسية
/// - `/accounts/customers` - العملاء
/// - `/accounts/suppliers` - الموردين
/// 
/// **التقارير:**
/// - `/reports` - صفحة التقارير الرئيسية
/// - `/reports/sales` - تقرير المبيعات
/// - `/reports/purchases` - تقرير المشتريات
/// - `/reports/inventory` - تقارير المخزون
/// - `/reports/profit-loss` - الأرباح والخسائر

/// نصائح للاستخدام:
/// 
/// 1. **استخدم NavigationService**: بدلاً من context.go() مباشرة
/// 2. **تحقق من الصلاحيات**: قبل عرض الأزرار أو الروابط
/// 3. **استخدم المسارات المحددة**: من ملف Routes بدلاً من كتابة النصوص
/// 4. **اختبر التنقل**: تأكد من عمل جميع المسارات بشكل صحيح
/// 5. **حدث الصلاحيات**: عند إضافة مسارات جديدة
