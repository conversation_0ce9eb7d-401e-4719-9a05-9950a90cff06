import 'package:json_annotation/json_annotation.dart';
import '../../core/constants/database_constants.dart';

part 'branch.g.dart';

@JsonSerializable()
class Branch {
  final String id;
  final String name;
  final String? address;
  final String? phone;
  final String? email;
  final String? managerId;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final bool isSynced;

  const Branch({
    required this.id,
    required this.name,
    this.address,
    this.phone,
    this.email,
    this.managerId,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  // Factory constructor for creating a new Branch instance from a map
  factory Branch.fromMap(Map<String, dynamic> map) {
    return Branch(
      id: map[DatabaseConstants.columnBranchId] as String,
      name: map[DatabaseConstants.columnBranchName] as String,
      address: map[DatabaseConstants.columnBranchAddress] as String?,
      phone: map[DatabaseConstants.columnBranchPhone] as String?,
      email: map[DatabaseConstants.columnBranchEmail] as String?,
      managerId: map[DatabaseConstants.columnBranchManagerId] as String?,
      isActive: (map[DatabaseConstants.columnBranchIsActive] as int? ?? 1) == 1,
      createdAt: map[DatabaseConstants.columnBranchCreatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnBranchCreatedAt] as String)
          : null,
      updatedAt: map[DatabaseConstants.columnBranchUpdatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnBranchUpdatedAt] as String)
          : null,
      deletedAt: map[DatabaseConstants.columnBranchDeletedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnBranchDeletedAt] as String)
          : null,
      isSynced: (map[DatabaseConstants.columnBranchIsSynced] as int? ?? 0) == 1,
    );
  }

  // Convert Branch instance to a map
  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnBranchId: id,
      DatabaseConstants.columnBranchName: name,
      DatabaseConstants.columnBranchAddress: address,
      DatabaseConstants.columnBranchPhone: phone,
      DatabaseConstants.columnBranchEmail: email,
      DatabaseConstants.columnBranchManagerId: managerId,
      DatabaseConstants.columnBranchIsActive: isActive ? 1 : 0,
      DatabaseConstants.columnBranchCreatedAt: createdAt?.toIso8601String(),
      DatabaseConstants.columnBranchUpdatedAt: updatedAt?.toIso8601String(),
      DatabaseConstants.columnBranchDeletedAt: deletedAt?.toIso8601String(),
      DatabaseConstants.columnBranchIsSynced: isSynced ? 1 : 0,
    };
  }

  // JSON serialization
  factory Branch.fromJson(Map<String, dynamic> json) => _$BranchFromJson(json);
  Map<String, dynamic> toJson() => _$BranchToJson(this);

  // Copy with method for creating modified copies
  Branch copyWith({
    String? id,
    String? name,
    String? address,
    String? phone,
    String? email,
    String? managerId,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return Branch(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      managerId: managerId ?? this.managerId,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  // Equality and hashCode
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Branch && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // String representation
  @override
  String toString() {
    return 'Branch(id: $id, name: $name, address: $address, isSynced: $isSynced)';
  }

  // Helper methods
  bool get isDeleted => deletedAt != null;

  // Create a new branch with current timestamp
  static Branch create({
    required String id,
    required String name,
    String? address,
    String? phone,
    String? email,
    String? managerId,
  }) {
    final now = DateTime.now();
    return Branch(
      id: id,
      name: name,
      address: address,
      phone: phone,
      email: email,
      managerId: managerId,
      isActive: true,
      createdAt: now,
      updatedAt: now,
      isSynced: false,
    );
  }

  // Update branch with new timestamp
  Branch update({
    String? name,
    String? address,
    String? phone,
    String? email,
    String? managerId,
  }) {
    return copyWith(
      name: name ?? this.name,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      managerId: managerId ?? this.managerId,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as deleted
  Branch markAsDeleted() {
    return copyWith(
      deletedAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as synced
  Branch markAsSynced() {
    return copyWith(isSynced: true);
  }
}
