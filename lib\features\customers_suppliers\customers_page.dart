// customers_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:tijari_tech/data/models/customer.dart';
import 'package:tijari_tech/providers/customer_provider.dart';
import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../shared/widgets/loading_widget.dart';
import '../../shared/widgets/error_display_widget.dart';
import '../../shared/widgets/empty_state_widget.dart';
import '../../widgets/main_layout.dart';

/// صفحة العملاء
/// Customers Page
class CustomersPage extends ConsumerStatefulWidget {
  const CustomersPage({super.key});

  @override
  ConsumerState<CustomersPage> createState() => _CustomersPageState();
}

class _CustomersPageState extends ConsumerState<CustomersPage> {
  final _searchController = TextEditingController();
  final _scrollController = ScrollController();
  CustomerBalanceStatus? _selectedBalanceFilter;

  @override
  void initState() {
    super.initState();
    // تحديث البيانات عند بدء الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(customerProvider.notifier).refresh();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final customerState = ref.watch(customerProvider);
    final customerNotifier = ref.read(customerProvider.notifier);

    return MainLayout(
      title: 'العملاء',
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToAddCustomer(),
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.person_add, color: Colors.white),
      ),
      child: Column(
        children: [
          // شريط البحث والفلاتر
          _buildSearchAndFilters(customerNotifier),
          // الإحصائيات السريعة
          _buildQuickStats(customerState),
          // قائمة العملاء
          Expanded(
            child: _buildCustomersList(customerState, customerNotifier),
          ),
        ],
      ),
    );
  }

  /// بناء شريط البحث والفلاتر
  Widget _buildSearchAndFilters(CustomerNotifier notifier) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في العملاء...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        notifier.setSearchQuery('');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: const BorderSide(color: AppColors.primary),
              ),
            ),
            onChanged: (value) {
              notifier.setSearchQuery(value);
            },
          ),
          SizedBox(height: 12.h),
          // فلاتر حالة الرصيد
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip(
                  label: 'الكل',
                  isSelected: _selectedBalanceFilter == null,
                  onTap: () {
                    setState(() => _selectedBalanceFilter = null);
                    notifier.setBalanceFilter(null);
                  },
                ),
                SizedBox(width: 8.w),
                _buildFilterChip(
                  label: 'مدين',
                  isSelected:
                      _selectedBalanceFilter == CustomerBalanceStatus.debt,
                  onTap: () {
                    setState(() =>
                        _selectedBalanceFilter = CustomerBalanceStatus.debt);
                    notifier.setBalanceFilter(CustomerBalanceStatus.debt);
                  },
                ),
                SizedBox(width: 8.w),
                _buildFilterChip(
                  label: 'دائن',
                  isSelected:
                      _selectedBalanceFilter == CustomerBalanceStatus.credit,
                  onTap: () {
                    setState(() =>
                        _selectedBalanceFilter = CustomerBalanceStatus.credit);
                    notifier.setBalanceFilter(CustomerBalanceStatus.credit);
                  },
                ),
                SizedBox(width: 8.w),
                _buildFilterChip(
                  label: 'متوازن',
                  isSelected:
                      _selectedBalanceFilter == CustomerBalanceStatus.balanced,
                  onTap: () {
                    setState(() => _selectedBalanceFilter =
                        CustomerBalanceStatus.balanced);
                    notifier.setBalanceFilter(CustomerBalanceStatus.balanced);
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رقاقة الفلتر
  Widget _buildFilterChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey.shade300,
          ),
        ),
        child: Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: isSelected ? Colors.white : Colors.grey.shade700,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  /// بناء الإحصائيات السريعة
  Widget _buildQuickStats(CustomerState state) {
    // استخدام الإحصائيات المتاحة أو حسابها يدوياً في حالة عدم توفرها
    final activeCount =
        state.statistics?['active_count'] ?? state.activeCustomersCount;
    final debtCount =
        state.statistics?['debt_count'] ?? state.debtCustomersCount;
    final creditCount =
        state.statistics?['credit_count'] ?? state.creditCustomersCount;

    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              title: 'العدد الكلي',
              value: activeCount.toString(),
              icon: Icons.people,
              color: AppColors.primary,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildStatCard(
              title: 'مدين',
              value: debtCount.toString(),
              icon: Icons.trending_up,
              color: Colors.red,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildStatCard(
              title: 'دائن',
              value: creditCount.toString(),
              icon: Icons.trending_down,
              color: Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء كارت الإحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.r),
          SizedBox(height: 8.h),
          Text(
            value,
            style: AppTextStyles.titleMedium.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة العملاء
  Widget _buildCustomersList(CustomerState state, CustomerNotifier notifier) {
    if (state.isLoading) {
      return const LoadingWidget();
    }

    if (state.error != null) {
      return ErrorDisplayWidget(
        error: state.error!,
        onRetry: () => notifier.refresh(),
      );
    }

    final filteredCustomers = state.filteredCustomers;

    if (filteredCustomers.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.people_outline,
        title: 'لا توجد عملاء',
        subtitle: state.searchQuery.isNotEmpty
            ? 'لم يتم العثور على نتائج للبحث "${state.searchQuery}"'
            : 'ابدأ بإضافة عميل جديد',
        actionText: 'إضافة عميل',
        onActionPressed: () => _navigateToAddCustomer(),
      );
    }

    return RefreshIndicator(
      onRefresh: () => notifier.refresh(),
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.all(16.w),
        itemCount: filteredCustomers.length,
        itemBuilder: (context, index) {
          final customer = filteredCustomers[index];
          return _buildCustomerCard(customer, notifier);
        },
      ),
    );
  }

  /// بناء كارت العميل
  Widget _buildCustomerCard(Customer customer, CustomerNotifier notifier) {
    final currencyFormat = NumberFormat.currency(
      locale: 'ar_SA',
      symbol: 'ر.س',
      decimalDigits: 2,
    );

    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: () => _navigateToEditCustomer(customer),
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف الأول: الاسم والرصيد
              Row(
                children: [
                  Expanded(
                    child: Text(
                      customer.name,
                      style: AppTextStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: _getBalanceColor(customer.balanceStatus)
                          .withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      currencyFormat.format(customer.absoluteBalance),
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: _getBalanceColor(customer.balanceStatus),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.h),
              // الصف الثاني: معلومات الاتصال
              if (customer.hasPhone || customer.hasEmail) ...[
                Row(
                  children: [
                    if (customer.hasPhone) ...[
                      Icon(Icons.phone,
                          size: 16.r, color: Colors.grey.shade600),
                      SizedBox(width: 4.w),
                      Text(
                        customer.phone!,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                    if (customer.hasPhone && customer.hasEmail)
                      SizedBox(width: 16.w),
                    if (customer.hasEmail) ...[
                      Icon(Icons.email,
                          size: 16.r, color: Colors.grey.shade600),
                      SizedBox(width: 4.w),
                      Expanded(
                        child: Text(
                          customer.email!,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: Colors.grey.shade600,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ],
                ),
                SizedBox(height: 8.h),
              ],
              // الصف الثالث: حالة الرصيد والإجراءات
              Row(
                children: [
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: _getBalanceColor(customer.balanceStatus)
                          .withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6.r),
                    ),
                    child: Text(
                      customer.balanceStatus.displayName,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: _getBalanceColor(customer.balanceStatus),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const Spacer(),
                  // أزرار الإجراءات
                  PopupMenuButton<String>(
                    onSelected: (value) =>
                        _handleCustomerAction(value, customer, notifier),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 18),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'balance',
                        child: Row(
                          children: [
                            Icon(Icons.account_balance_wallet, size: 18),
                            SizedBox(width: 8),
                            Text('تعديل الرصيد'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 18, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                    child: Icon(
                      Icons.more_vert,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// الحصول على لون حالة الرصيد
  Color _getBalanceColor(CustomerBalanceStatus status) {
    switch (status) {
      case CustomerBalanceStatus.debt:
        return Colors.red;
      case CustomerBalanceStatus.credit:
        return Colors.green;
      case CustomerBalanceStatus.balanced:
        return Colors.blue;
    }
  }

  /// التعامل مع إجراءات العميل
  void _handleCustomerAction(
      String action, Customer customer, CustomerNotifier notifier) {
    switch (action) {
      case 'edit':
        _navigateToEditCustomer(customer);
        break;
      case 'balance':
        _showBalanceDialog(customer, notifier);
        break;
      case 'delete':
        _showDeleteConfirmation(customer, notifier);
        break;
    }
  }

  /// عرض حوار تعديل الرصيد
  void _showBalanceDialog(Customer customer, CustomerNotifier notifier) {
    final balanceController = TextEditingController(
      text: customer.balance.toString(),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تعديل رصيد ${customer.name}'),
        content: TextField(
          controller: balanceController,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'الرصيد الجديد',
            suffixText: 'ر.س',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final newBalance = double.tryParse(balanceController.text) ?? 0.0;
              notifier.updateCustomerBalance(customer.id, newBalance);
              Navigator.pop(context);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  /// عرض تأكيد الحذف
  void _showDeleteConfirmation(Customer customer, CustomerNotifier notifier) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف ${customer.name}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              notifier.deleteCustomer(customer.id);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  /// الانتقال لصفحة إضافة عميل
  void _navigateToAddCustomer() {
    context.go('/accounts/customers/add');
  }

  /// الانتقال لصفحة تعديل عميل
  void _navigateToEditCustomer(Customer customer) {
    context.go('/accounts/customers/edit/${customer.id}');
  }
}
