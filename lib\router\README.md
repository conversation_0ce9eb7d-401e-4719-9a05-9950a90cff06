# نظام التوجيه المتكامل - تجاري تك

نظام توجيه شامل ومتقدم لتطبيق تجاري تك، مبني على GoRouter مع دعم كامل للحماية والصلاحيات.

## 📁 هيكل الملفات

```
lib/router/
├── app_router.dart              # إعداد GoRouter الرئيسي
├── routes.dart                  # تعريف جميع المسارات
├── navigation_service.dart      # خدمة التنقل المركزية
├── route_guard.dart            # حماية المسارات والصلاحيات
├── sidebar_navigation.dart     # إدارة التنقل في الشريط الجانبي
├── index.dart                  # تجميع جميع الملفات
└── README.md                   # هذا الملف
```

## 🚀 الميزات الرئيسية

### 1. التوجيه المتقدم
- استخدام GoRouter لإدارة التنقل
- دعم المسارات المتداخلة
- معالجة الأخطاء والصفحات غير الموجودة
- دعم المعاملات في المسارات

### 2. الحماية والصلاحيات
- نظام شامل لحماية المسارات
- التحقق من تسجيل الدخول
- التحقق من الصلاحيات لكل مسار
- إعادة توجيه تلقائية للمستخدمين غير المخولين

### 3. التنقل المركزي
- خدمة موحدة لجميع عمليات التنقل
- دوال مخصصة لكل مسار
- سهولة الاستخدام والصيانة

### 4. الشريط الجانبي الذكي
- عرض العناصر حسب الصلاحيات
- تنظيم هرمي للمسارات
- دعم الأيقونات والعناوين

## 📋 المسارات المتاحة

### المخزون
- `/inventory` - الصفحة الرئيسية
- `/inventory/products` - المنتجات
- `/inventory/categories` - التصنيفات
- `/inventory/units` - وحدات القياس
- `/inventory/warehouses` - المخازن
- `/inventory/stock-management` - إدارة المخزون
- `/inventory/stock-movements` - حركة المخزون
- `/inventory/stock-adjustment` - تسوية المخزون
- `/inventory/stock-transfer` - نقل المخزون

### المبيعات
- `/sales` - الصفحة الرئيسية
- `/sales/pos` - نقطة البيع
- `/sales/add` - إضافة مبيعة
- `/sales/list` - قائمة المبيعات
- `/sales/return` - مرتجعات المبيعات

### المشتريات
- `/purchases` - الصفحة الرئيسية
- `/purchases/add` - إضافة مشتريات
- `/purchases/list` - قائمة المشتريات
- `/purchases/return` - مرتجعات المشتريات

### الحسابات
- `/accounts` - الصفحة الرئيسية
- `/accounts/customers` - العملاء
- `/accounts/suppliers` - الموردين

### التقارير
- `/reports` - الصفحة الرئيسية
- `/reports/sales` - تقرير المبيعات
- `/reports/purchases` - تقرير المشتريات
- `/reports/inventory` - تقارير المخزون
- `/reports/profit-loss` - الأرباح والخسائر

## 💻 الاستخدام

### 1. إعداد التطبيق

```dart
import 'package:tijari_tech/router/index.dart';

class MyApp extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(appRouterProvider);
    
    return MaterialApp.router(
      routerConfig: router,
      title: 'تجاري تك',
    );
  }
}
```

### 2. التنقل بين الصفحات

```dart
import 'package:tijari_tech/router/index.dart';

// التنقل إلى صفحة المنتجات
NavigationService.goToProducts(context);

// التنقل إلى تعديل منتج
NavigationService.goToProductEdit(context, productId);

// التنقل إلى إضافة مخزن
NavigationService.goToWarehouseAdd(context);

// العودة للصفحة السابقة
NavigationService.goBack(context);
```

### 3. التحقق من الصلاحيات

```dart
import 'package:tijari_tech/router/index.dart';

// التحقق من صلاحية التعديل
if (RouteGuard.canEdit('inventory')) {
  // عرض زر التعديل
}

// التحقق من صلاحية الحذف
if (RouteGuard.canDelete('sales')) {
  // عرض زر الحذف
}

// التحقق من صلاحية إدارة المخازن
if (RouteGuard.canManageWarehouses()) {
  // عرض خيارات إدارة المخازن
}
```

### 4. الشريط الجانبي

```dart
import 'package:tijari_tech/router/index.dart';

class SidebarWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final navigationItems = SidebarNavigation.getFilteredNavigationItems();
    
    return ListView.builder(
      itemCount: navigationItems.length,
      itemBuilder: (context, index) {
        final item = navigationItems[index];
        return ListTile(
          leading: Icon(item.icon),
          title: Text(item.title),
          onTap: () => context.go(item.route),
        );
      },
    );
  }
}
```

## 🔧 إضافة مسارات جديدة

### 1. تحديث routes.dart

```dart
// إضافة المسار الجديد
static const String newFeature = '/new-feature';

// إضافة للقائمة المناسبة
static const List<String> inventoryRoutes = [
  // ... المسارات الموجودة
  newFeature,
];
```

### 2. تحديث app_router.dart

```dart
// إضافة المسار في GoRouter
GoRoute(
  path: '/new-feature',
  name: 'new-feature',
  builder: (context, state) => const NewFeaturePage(),
),
```

### 3. تحديث navigation_service.dart

```dart
// إضافة دالة التنقل
static void goToNewFeature(BuildContext context) {
  context.go(Routes.newFeature);
}
```

### 4. تحديث route_guard.dart

```dart
// إضافة التحقق من الصلاحيات
static bool canAccessNewFeature() {
  return PermissionService.hasNewFeatureAccess();
}
```

### 5. تحديث sidebar_navigation.dart

```dart
// إضافة عنصر التنقل
const NavigationItem(
  title: 'الميزة الجديدة',
  icon: Icons.new_releases,
  route: Routes.newFeature,
  permission: 'new_feature_access',
),
```

## 🛡️ نظام الصلاحيات

### أنواع الصلاحيات

- **عرض**: `view` - عرض البيانات
- **إضافة**: `create` - إنشاء بيانات جديدة
- **تعديل**: `edit` - تعديل البيانات الموجودة
- **حذف**: `delete` - حذف البيانات
- **إدارة**: `manage` - صلاحيات إدارية شاملة

### الوحدات المحمية

- `inventory` - المخزون
- `sales` - المبيعات
- `purchases` - المشتريات
- `accounts` - الحسابات
- `cash` - الخزينة
- `employees` - الموظفين
- `reports` - التقارير
- `settings` - الإعدادات

## 📝 أفضل الممارسات

### 1. استخدم NavigationService
```dart
// ✅ صحيح
NavigationService.goToProducts(context);

// ❌ خطأ
context.go('/inventory/products');
```

### 2. تحقق من الصلاحيات
```dart
// ✅ صحيح
if (RouteGuard.canEdit('inventory')) {
  return EditButton();
}

// ❌ خطأ - عدم التحقق من الصلاحيات
return EditButton();
```

### 3. استخدم المسارات المحددة
```dart
// ✅ صحيح
NavigationService.goToProductEdit(context, Routes.productEdit);

// ❌ خطأ
context.go('/inventory/products/edit');
```

### 4. معالجة الأخطاء
```dart
// ✅ صحيح
try {
  NavigationService.goToProducts(context);
} catch (e) {
  // معالجة الخطأ
}
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **المسار غير موجود**
   - تأكد من إضافة المسار في app_router.dart
   - تحقق من صحة كتابة المسار

2. **عدم وجود صلاحية**
   - تحقق من إعدادات الصلاحيات
   - تأكد من تسجيل الدخول

3. **الصفحة لا تظهر**
   - تحقق من import الصفحة
   - تأكد من صحة builder function

4. **مشاكل التنقل**
   - استخدم NavigationService بدلاً من context.go
   - تحقق من صحة المعاملات

## 📚 مراجع إضافية

- [GoRouter Documentation](https://pub.dev/packages/go_router)
- [Flutter Navigation](https://docs.flutter.dev/development/ui/navigation)
- [Riverpod State Management](https://riverpod.dev/)

---

تم تطوير هذا النظام لتطبيق تجاري تك بواسطة فريق التطوير.
