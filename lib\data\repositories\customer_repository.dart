import '../../core/utils/app_utils.dart';
import '../local/dao/customer_dao.dart';
import '../models/customer.dart';

class CustomerRepository {
  final CustomerDao _customerDao;

  CustomerRepository(this._customerDao);

  // ------------------------------------------------------------------
  // Basic Operations
  // ------------------------------------------------------------------

  Future<String> createCustomer(Customer customer) async {
    try {
      AppUtils.logInfo('Creating customer: ${customer.name}');

      await _validateCustomer(customer);
      await _checkDuplicateName(customer.name, excludeId: customer.id);

      final customerId = await _customerDao.insert(customer);
      AppUtils.logInfo('Customer created successfully - ID: $customerId');
      return customerId;
    } catch (e) {
      AppUtils.logError('Error creating customer', e);
      rethrow;
    }
  }

  Future<void> updateCustomer(Customer customer) async {
    try {
      AppUtils.logInfo('Updating customer: ${customer.name}');

      await _validateCustomer(customer);
      final existingCustomer = await _customerDao.findById(customer.id);
      if (existingCustomer == null) {
        throw Exception('Customer not found');
      }

      await _checkDuplicateName(customer.name, excludeId: customer.id);
      await _customerDao.update(customer);

      AppUtils.logInfo('Customer updated successfully - ID: ${customer.id}');
    } catch (e) {
      AppUtils.logError('Error updating customer', e);
      rethrow;
    }
  }

  Future<void> deleteCustomer(String id) async {
    try {
      AppUtils.logInfo('Deleting customer - ID: $id');

      final customer = await _customerDao.findById(id);
      if (customer == null) {
        throw Exception('Customer not found');
      }

      await _checkCanDelete(id);
      await _customerDao.delete(id);

      AppUtils.logInfo('Customer deleted successfully - ID: $id');
    } catch (e) {
      AppUtils.logError('Error deleting customer', e);
      rethrow;
    }
  }

  Future<void> restoreCustomer(String id) async {
    try {
      AppUtils.logInfo('Restoring customer - ID: $id');

      final customer = await _customerDao.findById(id);
      if (customer == null) {
        throw Exception('Customer not found');
      }

      if (!customer.isDeleted) {
        throw Exception('Customer is not deleted');
      }

      final restoredCustomer = customer.copyWith(
        deletedAt: null,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

      await _customerDao.update(restoredCustomer);
      AppUtils.logInfo('Customer restored successfully - ID: $id');
    } catch (e) {
      AppUtils.logError('Error restoring customer', e);
      rethrow;
    }
  }

  Future<void> deleteCustomerForever(String id) async {
    try {
      AppUtils.logInfo('Permanently deleting customer - ID: $id');
      await _checkCanDeleteForever(id);
      await _customerDao.delete(id);
      AppUtils.logInfo('Customer permanently deleted - ID: $id');
    } catch (e) {
      AppUtils.logError('Error permanently deleting customer', e);
      rethrow;
    }
  }

  // ------------------------------------------------------------------
  // Query Operations
  // ------------------------------------------------------------------

  Future<Customer?> getCustomerById(String id) async {
    try {
      return await _customerDao.findById(id);
    } catch (e) {
      AppUtils.logError('Error retrieving customer', e);
      rethrow;
    }
  }

  Future<List<Customer>> getAllCustomers({bool includeDeleted = false}) async {
    try {
      return await _customerDao.findAll(includeDeleted: includeDeleted);
    } catch (e) {
      AppUtils.logError('Error retrieving customers', e);
      rethrow;
    }
  }

  Future<List<Customer>> searchCustomers(String query, {bool includeDeleted = false}) async {
    try {
      if (query.trim().isEmpty) {
        return await getAllCustomers(includeDeleted: includeDeleted);
      }
      return await _customerDao.search(query.trim(), includeDeleted: includeDeleted);
    } catch (e) {
      AppUtils.logError('Error searching customers', e);
      rethrow;
    }
  }

  Future<List<Customer>> getCustomersByBalanceStatus(
    CustomerBalanceStatus status, {
    bool includeDeleted = false,
  }) async {
    try {
      return await _customerDao.findByBalanceStatus(status, includeDeleted: includeDeleted);
    } catch (e) {
      AppUtils.logError('Error retrieving customers by balance status', e);
      rethrow;
    }
  }

  Future<List<Customer>> getDebtCustomers() async {
    return await getCustomersByBalanceStatus(CustomerBalanceStatus.debt);
  }

  Future<List<Customer>> getCreditCustomers() async {
    return await getCustomersByBalanceStatus(CustomerBalanceStatus.credit);
  }

  Future<List<Customer>> getBalancedCustomers() async {
    return await getCustomersByBalanceStatus(CustomerBalanceStatus.balanced);
  }

  // ------------------------------------------------------------------
  // Balance Operations
  // ------------------------------------------------------------------

  Future<void> updateCustomerBalance(String id, double newBalance) async {
    try {
      AppUtils.logInfo('Updating customer balance - ID: $id, New balance: $newBalance');

      final customer = await _customerDao.findById(id);
      if (customer == null) {
        throw Exception('Customer not found');
      }

      final updatedCustomer = customer.copyWith(
        balance: newBalance,
        updatedAt: DateTime.now(),
        isSynced: false,
      );
      
      await _customerDao.update(updatedCustomer);
      AppUtils.logInfo('Customer balance updated successfully');
    } catch (e) {
      AppUtils.logError('Error updating customer balance', e);
      rethrow;
    }
  }

  Future<void> addToCustomerBalance(String id, double amount) async {
    try {
      AppUtils.logInfo('Adding to customer balance - ID: $id, Amount: $amount');

      final customer = await _customerDao.findById(id);
      if (customer == null) {
        throw Exception('Customer not found');
      }

      final updatedCustomer = customer.addToBalance(amount);
      await _customerDao.update(updatedCustomer);

      AppUtils.logInfo('Amount added to customer balance successfully');
    } catch (e) {
      AppUtils.logError('Error adding to customer balance', e);
      rethrow;
    }
  }

  Future<void> subtractFromCustomerBalance(String id, double amount) async {
    try {
      AppUtils.logInfo('Subtracting from customer balance - ID: $id, Amount: $amount');

      final customer = await _customerDao.findById(id);
      if (customer == null) {
        throw Exception('Customer not found');
      }

      final updatedCustomer = customer.subtractFromBalance(amount);
      await _customerDao.update(updatedCustomer);

      AppUtils.logInfo('Amount subtracted from customer balance successfully');
    } catch (e) {
      AppUtils.logError('Error subtracting from customer balance', e);
      rethrow;
    }
  }

  Future<void> clearCustomerBalance(String id) async {
    try {
      AppUtils.logInfo('Clearing customer balance - ID: $id');

      final customer = await _customerDao.findById(id);
      if (customer == null) {
        throw Exception('Customer not found');
      }

      final updatedCustomer = customer.clearBalance();
      await _customerDao.update(updatedCustomer);

      AppUtils.logInfo('Customer balance cleared successfully');
    } catch (e) {
      AppUtils.logError('Error clearing customer balance', e);
      rethrow;
    }
  }

  // ------------------------------------------------------------------
  // Statistics
  // ------------------------------------------------------------------

  Future<Map<String, dynamic>> getCustomerStatistics() async {
    try {
      return await _customerDao.getCustomerStats();
    } catch (e) {
      AppUtils.logError('Error retrieving customer statistics', e);
      rethrow;
    }
  }

  // ------------------------------------------------------------------
  // Synchronization
  // ------------------------------------------------------------------

  Future<List<Customer>> getUnsyncedCustomers() async {
    try {
      return await _customerDao.getUnsyncedCustomers();
    } catch (e) {
      AppUtils.logError('Error retrieving unsynced customers', e);
      rethrow;
    }
  }

  Future<void> markCustomerAsSynced(String id) async {
    try {
      await _customerDao.markAsSynced(id);
    } catch (e) {
      AppUtils.logError('Error marking customer as synced', e);
      rethrow;
    }
  }

  // ------------------------------------------------------------------
  // Private Validation Methods
  // ------------------------------------------------------------------

  Future<void> _validateCustomer(Customer customer) async {
    if (customer.name.trim().isEmpty) {
      throw Exception('Customer name is required');
    }

    if (customer.name.trim().length < 2) {
      throw Exception('Customer name must be more than one character');
    }

    if (customer.phone != null && customer.phone!.isNotEmpty) {
      if (!RegExp(r'^[0-9+\-\s()]+$').hasMatch(customer.phone!)) {
        throw Exception('Invalid phone number');
      }
    }

    if (customer.email != null && customer.email!.isNotEmpty) {
      if (!RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(customer.email!)) {
        throw Exception('Invalid email address');
      }
    }
  }

  Future<void> _checkDuplicateName(String name, {String? excludeId}) async {
    try {
      AppUtils.logInfo('Checking for duplicate name: $name');
      final customers = await _customerDao.findAll();
      final duplicateCustomer = customers
          .where((customer) =>
              customer.name.toLowerCase() == name.toLowerCase() &&
              customer.id != excludeId)
          .firstOrNull;

      if (duplicateCustomer != null) {
        throw Exception('Another customer with the same name exists');
      }
      AppUtils.logInfo('No duplicate name found');
    } catch (e) {
      AppUtils.logError('Error checking for duplicate name', e);
      AppUtils.logInfo('Ignoring duplicate check error and proceeding');
    }
  }

  Future<void> _checkCanDelete(String id) async {
    final customer = await _customerDao.findById(id);
    if (customer != null && customer.balance != 0) {
      throw Exception('Cannot delete customer with non-zero balance');
    }
  }

  Future<void> _checkCanDeleteForever(String id) async {
    await _checkCanDelete(id);
    final customer = await _customerDao.findById(id);
    if (customer != null && !customer.isDeleted) {
      throw Exception('Customer must be deleted first before permanent deletion');
    }
  }
}