import '../data/local/dao/settings_dao.dart';
import '../data/local/dao/audit_log_dao.dart';
import '../data/models/settings.dart';
import '../core/utils/app_utils.dart';

/// خدمة إدارة الإعدادات - Settings Service
/// تدير إعدادات النظام والتكوين العام والخاص بالفروع والمستخدمين
/// Manages system settings and general, branch, and user-specific configurations
class SettingsService {
  final SettingsDao _settingsDao;
  final AuditLogDao _auditLogDao;

  SettingsService({
    required SettingsDao settingsDao,
    required AuditLogDao auditLogDao,
  })  : _settingsDao = settingsDao,
        _auditLogDao = auditLogDao;

  /// إنشاء إعداد جديد - Create new setting
  /// ينشئ إعداد جديد مع التحقق من صحة البيانات
  Future<String?> createSetting({
    required String key,
    required String value,
    required String dataType,
    String? description,
    String? category,
    String? scope,
    String? branchId,
    String? userId,
    bool isEncrypted = false,
    String? createdByUserId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // التحقق من صحة البيانات
      _validateSettingData(
        key: key,
        value: value,
        dataType: dataType,
        scope: scope,
      );

      // التحقق من عدم تكرار المفتاح في نفس النطاق
      final existingSetting = await _settingsDao.getByKey(key, scope: scope, branchId: branchId, userId: userId);
      if (existingSetting != null) {
        throw Exception('المفتاح موجود مسبقاً في هذا النطاق: $key');
      }

      // تشفير القيمة إذا كان مطلوباً
      String finalValue = value;
      if (isEncrypted) {
        finalValue = _encryptValue(value);
      }

      // إنشاء الإعداد الجديد
      final setting = Settings.create(
        id: AppUtils.generateId(),
        key: key,
        value: finalValue,
        dataType: dataType,
        description: description,
        category: category ?? 'general',
        scope: scope ?? 'global',
        branchId: branchId,
        userId: userId,
        isEncrypted: isEncrypted,
        metadata: metadata,
      );

      // حفظ الإعداد
      final settingId = await _settingsDao.insert(setting);
      if (settingId == null) {
        throw Exception('فشل في حفظ الإعداد الجديد');
      }

      // تسجيل العملية
      await _auditLogDao.logOperation(
        tableName: 'settings',
        recordId: settingId,
        action: 'create',
        description: 'إنشاء إعداد جديد: $key',
        userId: createdByUserId ?? 'system',
        category: 'settings_management',
        severity: 'low',
        metadata: {
          'setting_key': key,
          'category': category,
          'scope': scope,
          'data_type': dataType,
          'is_encrypted': isEncrypted,
        },
        branchId: branchId,
      );

      return settingId;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'settings',
        recordId: 'failed',
        action: 'create',
        description: 'فشل في إنشاء إعداد جديد: $key',
        userId: createdByUserId ?? 'system',
        category: 'settings_management',
        severity: 'medium',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      rethrow;
    }
  }

  /// التحقق من صحة بيانات الإعداد - Validate setting data
  void _validateSettingData({
    required String key,
    required String value,
    required String dataType,
    String? scope,
  }) {
    // التحقق من المفتاح
    if (key.trim().isEmpty) {
      throw Exception('مفتاح الإعداد مطلوب');
    }

    if (key.length > 100) {
      throw Exception('مفتاح الإعداد طويل جداً (الحد الأقصى 100 حرف)');
    }

    // التحقق من القيمة
    if (value.trim().isEmpty) {
      throw Exception('قيمة الإعداد مطلوبة');
    }

    // التحقق من نوع البيانات
    const validDataTypes = ['string', 'integer', 'double', 'boolean', 'json'];
    if (!validDataTypes.contains(dataType)) {
      throw Exception('نوع البيانات غير صحيح. الأنواع المسموحة: ${validDataTypes.join(', ')}');
    }

    // التحقق من صحة القيمة حسب نوع البيانات
    _validateValueByDataType(value, dataType);

    // التحقق من النطاق
    if (scope != null) {
      const validScopes = ['global', 'branch', 'user'];
      if (!validScopes.contains(scope)) {
        throw Exception('نطاق الإعداد غير صحيح. النطاقات المسموحة: ${validScopes.join(', ')}');
      }
    }
  }

  /// التحقق من صحة القيمة حسب نوع البيانات - Validate value by data type
  void _validateValueByDataType(String value, String dataType) {
    switch (dataType) {
      case 'integer':
        if (int.tryParse(value) == null) {
          throw Exception('القيمة يجب أن تكون رقم صحيح');
        }
        break;
      case 'double':
        if (double.tryParse(value) == null) {
          throw Exception('القيمة يجب أن تكون رقم عشري');
        }
        break;
      case 'boolean':
        if (value.toLowerCase() != 'true' && value.toLowerCase() != 'false') {
          throw Exception('القيمة يجب أن تكون true أو false');
        }
        break;
      case 'json':
        try {
          // محاولة تحليل JSON للتحقق من صحته
          // في التطبيق الحقيقي، استخدم مكتبة JSON
          if (!value.startsWith('{') && !value.startsWith('[')) {
            throw Exception('القيمة يجب أن تكون JSON صحيح');
          }
        } catch (e) {
          throw Exception('القيمة يجب أن تكون JSON صحيح');
        }
        break;
    }
  }

  /// تشفير القيمة - Encrypt value
  String _encryptValue(String value) {
    // في التطبيق الحقيقي، استخدم مكتبة تشفير قوية
    // هنا نستخدم تشفير بسيط للتطوير
    return 'ENC:${value.split('').reversed.join()}';
  }

  /// فك تشفير القيمة - Decrypt value
  String _decryptValue(String encryptedValue) {
    if (encryptedValue.startsWith('ENC:')) {
      return encryptedValue.substring(4).split('').reversed.join();
    }
    return encryptedValue;
  }

  /// الحصول على إعداد بالمفتاح - Get setting by key
  Future<dynamic> getSetting({
    required String key,
    String? scope,
    String? branchId,
    String? userId,
    dynamic defaultValue,
  }) async {
    try {
      final setting = await _settingsDao.getByKey(key, scope: scope, branchId: branchId, userId: userId);
      
      if (setting == null) {
        return defaultValue;
      }

      // فك التشفير إذا كان مطلوباً
      String value = setting.value;
      if (setting.isEncrypted) {
        value = _decryptValue(value);
      }

      // تحويل القيمة حسب نوع البيانات
      return _convertValueByDataType(value, setting.dataType);
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'settings',
        recordId: key,
        action: 'get_setting',
        description: 'فشل في الحصول على الإعداد',
        userId: userId ?? 'system',
        category: 'settings_management',
        severity: 'low',
        isSuccessful: false,
        errorMessage: e.toString(),
        metadata: {
          'setting_key': key,
          'scope': scope,
        },
        branchId: branchId,
      );

      return defaultValue;
    }
  }

  /// تحويل القيمة حسب نوع البيانات - Convert value by data type
  dynamic _convertValueByDataType(String value, String dataType) {
    switch (dataType) {
      case 'integer':
        return int.tryParse(value) ?? 0;
      case 'double':
        return double.tryParse(value) ?? 0.0;
      case 'boolean':
        return value.toLowerCase() == 'true';
      case 'json':
        // في التطبيق الحقيقي، استخدم مكتبة JSON
        return value;
      case 'string':
      default:
        return value;
    }
  }

  /// تحديث إعداد موجود - Update existing setting
  Future<bool> updateSetting({
    required String key,
    required String value,
    String? scope,
    String? branchId,
    String? userId,
    String? updatedByUserId,
  }) async {
    try {
      // الحصول على الإعداد الحالي
      final currentSetting = await _settingsDao.getByKey(key, scope: scope, branchId: branchId, userId: userId);
      if (currentSetting == null) {
        throw Exception('الإعداد غير موجود: $key');
      }

      // التحقق من صحة القيمة الجديدة
      _validateValueByDataType(value, currentSetting.dataType);

      // تشفير القيمة إذا كان مطلوباً
      String finalValue = value;
      if (currentSetting.isEncrypted) {
        finalValue = _encryptValue(value);
      }

      // تحديث الإعداد
      final success = await _settingsDao.updateValue(currentSetting.id, finalValue);

      if (success) {
        // تسجيل العملية
        await _auditLogDao.logOperation(
          tableName: 'settings',
          recordId: currentSetting.id,
          action: 'update',
          description: 'تحديث الإعداد: $key',
          userId: updatedByUserId ?? 'system',
          category: 'settings_management',
          severity: 'low',
          oldValues: currentSetting.isEncrypted ? '[مشفر]' : currentSetting.value,
          newValues: currentSetting.isEncrypted ? '[مشفر]' : value,
          metadata: {
            'setting_key': key,
            'scope': scope,
          },
          branchId: branchId,
        );
      }

      return success;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'settings',
        recordId: key,
        action: 'update',
        description: 'فشل في تحديث الإعداد',
        userId: updatedByUserId ?? 'system',
        category: 'settings_management',
        severity: 'medium',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      rethrow;
    }
  }

  /// حذف إعداد - Delete setting
  Future<bool> deleteSetting({
    required String key,
    String? scope,
    String? branchId,
    String? userId,
    required String deletedByUserId,
    String? reason,
  }) async {
    try {
      // الحصول على الإعداد
      final setting = await _settingsDao.getByKey(key, scope: scope, branchId: branchId, userId: userId);
      if (setting == null) {
        throw Exception('الإعداد غير موجود: $key');
      }

      // حذف الإعداد
      final success = await _settingsDao.delete(setting.id);

      if (success) {
        // تسجيل العملية
        await _auditLogDao.logOperation(
          tableName: 'settings',
          recordId: setting.id,
          action: 'delete',
          description: 'حذف الإعداد: $key${reason != null ? ' - السبب: $reason' : ''}',
          userId: deletedByUserId,
          category: 'settings_management',
          severity: 'medium',
          metadata: {
            'setting_key': key,
            'scope': scope,
            'deletion_reason': reason,
          },
          branchId: branchId,
        );
      }

      return success;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'settings',
        recordId: key,
        action: 'delete',
        description: 'فشل في حذف الإعداد',
        userId: deletedByUserId,
        category: 'settings_management',
        severity: 'medium',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      rethrow;
    }
  }

  /// الحصول على الإعدادات حسب الفئة - Get settings by category
  Future<List<Settings>> getSettingsByCategory({
    required String category,
    String? scope,
    String? branchId,
    String? userId,
  }) async {
    try {
      return await _settingsDao.getByCategory(category, scope: scope, branchId: branchId, userId: userId);
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'settings',
        recordId: 'by_category',
        action: 'get_settings_by_category',
        description: 'فشل في الحصول على الإعدادات حسب الفئة',
        userId: userId ?? 'system',
        category: 'settings_management',
        severity: 'low',
        isSuccessful: false,
        errorMessage: e.toString(),
        metadata: {
          'category': category,
          'scope': scope,
        },
        branchId: branchId,
      );

      return [];
    }
  }

  /// الحصول على جميع الإعدادات حسب النطاق - Get all settings by scope
  Future<Map<String, dynamic>> getAllSettingsByScope({
    String? scope,
    String? branchId,
    String? userId,
  }) async {
    try {
      final settings = await _settingsDao.getByScope(scope ?? 'global', branchId: branchId, userId: userId);
      Map<String, dynamic> settingsMap = {};

      for (var setting in settings) {
        String value = setting.value;
        if (setting.isEncrypted) {
          value = _decryptValue(value);
        }

        settingsMap[setting.key] = _convertValueByDataType(value, setting.dataType);
      }

      return settingsMap;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'settings',
        recordId: 'by_scope',
        action: 'get_all_settings_by_scope',
        description: 'فشل في الحصول على جميع الإعدادات حسب النطاق',
        userId: userId ?? 'system',
        category: 'settings_management',
        severity: 'low',
        isSuccessful: false,
        errorMessage: e.toString(),
        metadata: {
          'scope': scope,
        },
        branchId: branchId,
      );

      return {};
    }
  }

  /// تحميل الإعدادات الافتراضية - Load default settings
  Future<bool> loadDefaultSettings({
    String? branchId,
    String? userId,
  }) async {
    try {
      // الإعدادات الافتراضية للنظام
      final defaultSettings = [
        {'key': 'app_name', 'value': 'نظام إدارة المحاسبة والمخزون', 'type': 'string', 'category': 'general'},
        {'key': 'app_version', 'value': '1.0.0', 'type': 'string', 'category': 'general'},
        {'key': 'currency', 'value': 'SAR', 'type': 'string', 'category': 'accounting'},
        {'key': 'decimal_places', 'value': '2', 'type': 'integer', 'category': 'accounting'},
        {'key': 'tax_rate', 'value': '15.0', 'type': 'double', 'category': 'accounting'},
        {'key': 'auto_backup', 'value': 'true', 'type': 'boolean', 'category': 'system'},
        {'key': 'backup_interval_hours', 'value': '24', 'type': 'integer', 'category': 'system'},
        {'key': 'max_login_attempts', 'value': '3', 'type': 'integer', 'category': 'security'},
        {'key': 'session_timeout_minutes', 'value': '30', 'type': 'integer', 'category': 'security'},
        {'key': 'require_approval_for_entries', 'value': 'false', 'type': 'boolean', 'category': 'accounting'},
        {'key': 'enable_audit_log', 'value': 'true', 'type': 'boolean', 'category': 'system'},
        {'key': 'date_format', 'value': 'dd/MM/yyyy', 'type': 'string', 'category': 'general'},
        {'key': 'time_format', 'value': '24h', 'type': 'string', 'category': 'general'},
        {'key': 'language', 'value': 'ar', 'type': 'string', 'category': 'general'},
      ];

      bool allSuccess = true;

      for (var settingData in defaultSettings) {
        try {
          // التحقق من عدم وجود الإعداد مسبقاً
          final existingSetting = await _settingsDao.getByKey(
            settingData['key'] as String,
            scope: 'global',
            branchId: branchId,
            userId: userId,
          );

          if (existingSetting == null) {
            final settingId = await createSetting(
              key: settingData['key'] as String,
              value: settingData['value'] as String,
              dataType: settingData['type'] as String,
              category: settingData['category'] as String,
              scope: 'global',
              branchId: branchId,
              userId: userId,
              createdByUserId: 'system',
              description: 'إعداد افتراضي للنظام',
            );

            if (settingId == null) {
              allSuccess = false;
            }
          }
        } catch (e) {
          allSuccess = false;
        }
      }

      // تسجيل العملية
      await _auditLogDao.logOperation(
        tableName: 'settings',
        recordId: 'default_settings',
        action: 'load_defaults',
        description: 'تحميل الإعدادات الافتراضية',
        userId: 'system',
        category: 'settings_management',
        severity: 'medium',
        isSuccessful: allSuccess,
        metadata: {
          'settings_count': defaultSettings.length,
        },
        branchId: branchId,
      );

      return allSuccess;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'settings',
        recordId: 'default_settings',
        action: 'load_defaults',
        description: 'فشل في تحميل الإعدادات الافتراضية',
        userId: 'system',
        category: 'settings_management',
        severity: 'critical',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      return false;
    }
  }

  /// تصدير الإعدادات - Export settings
  Future<Map<String, dynamic>> exportSettings({
    String? scope,
    String? branchId,
    String? userId,
    String? exportedByUserId,
  }) async {
    try {
      final settings = await _settingsDao.getByScope(scope ?? 'global', branchId: branchId, userId: userId);
      
      Map<String, dynamic> exportData = {
        'export_date': DateTime.now().toIso8601String(),
        'scope': scope ?? 'global',
        'branch_id': branchId,
        'user_id': userId,
        'settings_count': settings.length,
        'settings': {},
      };

      for (var setting in settings) {
        // لا نصدر الإعدادات المشفرة لأسباب أمنية
        if (!setting.isEncrypted) {
          exportData['settings'][setting.key] = {
            'value': setting.value,
            'data_type': setting.dataType,
            'category': setting.category,
            'description': setting.description,
          };
        }
      }

      // تسجيل العملية
      await _auditLogDao.logOperation(
        tableName: 'settings',
        recordId: 'export',
        action: 'export_settings',
        description: 'تصدير الإعدادات',
        userId: exportedByUserId ?? 'system',
        category: 'settings_management',
        severity: 'medium',
        metadata: {
          'scope': scope,
          'settings_exported': exportData['settings_count'],
        },
        branchId: branchId,
      );

      return exportData;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'settings',
        recordId: 'export',
        action: 'export_settings',
        description: 'فشل في تصدير الإعدادات',
        userId: exportedByUserId ?? 'system',
        category: 'settings_management',
        severity: 'medium',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      rethrow;
    }
  }
}
