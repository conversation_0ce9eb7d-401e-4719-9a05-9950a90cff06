import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/extensions.dart';
import '../../providers/sale_provider.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/responsive_wrapper.dart';

class POSPage extends ConsumerWidget {
  const POSPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final posState = ref.watch(posProvider);

    return MainLayout(
      title: 'نقطة البيع',
      showBottomNavigation: false,
      child: ResponsiveBuilder(
        builder: (context, constraints) {
          if (constraints.maxWidth >= 1024) {
            return _buildDesktopLayout(context, ref, posState);
          } else if (constraints.maxWidth >= 768) {
            return _buildTabletLayout(context, ref, posState);
          } else {
            return _buildMobileLayout(context, ref, posState);
          }
        },
      ),
    );
  }

  Widget _buildDesktopLayout(BuildContext context, WidgetRef ref, POSState posState) {
    return Row(
      children: [
        // Left side - Product selection
        Expanded(
          flex: 2,
          child: _buildProductSelection(context, ref),
        ),
        
        // Right side - Cart and checkout
        SizedBox(
          width: 400.w,
          child: _buildCartAndCheckout(context, ref, posState),
        ),
      ],
    );
  }

  Widget _buildTabletLayout(BuildContext context, WidgetRef ref, POSState posState) {
    return Row(
      children: [
        // Left side - Product selection
        Expanded(
          flex: 3,
          child: _buildProductSelection(context, ref),
        ),
        
        // Right side - Cart and checkout
        SizedBox(
          width: 350.w,
          child: _buildCartAndCheckout(context, ref, posState),
        ),
      ],
    );
  }

  Widget _buildMobileLayout(BuildContext context, WidgetRef ref, POSState posState) {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          TabBar(
            tabs: [
              Tab(
                icon: const Icon(Icons.inventory_2_outlined),
                text: 'المنتجات',
              ),
              Tab(
                icon: const Icon(Icons.shopping_cart_outlined),
                text: 'السلة (${posState.items.length})',
              ),
            ],
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildProductSelection(context, ref),
                _buildCartAndCheckout(context, ref, posState),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductSelection(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        // Search bar
        Padding(
          padding: EdgeInsets.all(16.r),
          child: TextField(
            decoration: InputDecoration(
              hintText: 'البحث عن منتج أو مسح الباركود...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: IconButton(
                icon: const Icon(Icons.qr_code_scanner),
                onPressed: () {
                  // TODO: Implement barcode scanner
                },
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            onChanged: (value) {
              // TODO: Implement product search
            },
          ),
        ),
        
        // Product grid
        Expanded(
          child: GridView.builder(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: context.isMobile ? 2 : context.isTablet ? 3 : 4,
              crossAxisSpacing: 16.w,
              mainAxisSpacing: 16.h,
              childAspectRatio: 0.8,
            ),
            itemCount: 20, // TODO: Replace with actual products
            itemBuilder: (context, index) {
              return _buildProductCard(context, ref, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProductCard(BuildContext context, WidgetRef ref, int index) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () {
          // TODO: Add product to cart
          _showAddToCartDialog(context, ref, index);
        },
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(12.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product image placeholder
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    Icons.inventory_2_outlined,
                    size: 40.r,
                    color: Colors.grey[400],
                  ),
                ),
              ),
              SizedBox(height: 8.h),
              
              // Product name
              Text(
                'منتج رقم ${index + 1}',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 4.h),
              
              // Product price
              Text(
                '${(index + 1) * 25.0} ر.س',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 4.h),
              
              // Stock status
              Container(
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: index % 3 == 0 ? AppColors.success : AppColors.warning,
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Text(
                  index % 3 == 0 ? 'متوفر' : 'منخفض',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCartAndCheckout(BuildContext context, WidgetRef ref, POSState posState) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          left: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Column(
        children: [
          // Cart header
          Container(
            padding: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.shopping_cart_outlined, size: 24.r),
                SizedBox(width: 8.w),
                Text(
                  'السلة (${posState.items.length})',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (posState.items.isNotEmpty)
                  IconButton(
                    icon: const Icon(Icons.clear_all),
                    onPressed: () {
                      ref.read(posProvider.notifier).clearCart();
                    },
                  ),
              ],
            ),
          ),
          
          // Cart items
          Expanded(
            child: posState.items.isEmpty
                ? _buildEmptyCart(context)
                : _buildCartItems(context, ref, posState),
          ),
          
          // Checkout section
          if (posState.items.isNotEmpty)
            _buildCheckoutSection(context, ref, posState),
        ],
      ),
    );
  }

  Widget _buildEmptyCart(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 64.r,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16.h),
          Text(
            'السلة فارغة',
            style: AppTextStyles.titleMedium.copyWith(
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'اختر المنتجات لإضافتها للسلة',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCartItems(BuildContext context, WidgetRef ref, POSState posState) {
    return ListView.separated(
      padding: EdgeInsets.all(16.r),
      itemCount: posState.items.length,
      separatorBuilder: (context, index) => SizedBox(height: 8.h),
      itemBuilder: (context, index) {
        final item = posState.items[index];
        return _buildCartItem(context, ref, item);
      },
    );
  }

  Widget _buildCartItem(BuildContext context, WidgetRef ref, item) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(12.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'منتج رقم ${item.productId}', // TODO: Get actual product name
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, size: 20),
                  onPressed: () {
                    ref.read(posProvider.notifier).removeItem(item.id);
                  },
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Row(
              children: [
                // Quantity controls
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.remove, size: 16),
                        onPressed: () {
                          if (item.qty > 1) {
                            ref.read(posProvider.notifier).updateItemQuantity(
                              item.id, 
                              item.qty - 1,
                            );
                          }
                        },
                      ),
                      Text(
                        item.qty.toString(),
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.add, size: 16),
                        onPressed: () {
                          ref.read(posProvider.notifier).updateItemQuantity(
                            item.id, 
                            item.qty + 1,
                          );
                        },
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                Text(
                  item.total.toCurrency,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCheckoutSection(BuildContext context, WidgetRef ref, POSState posState) {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Column(
        children: [
          // Totals
          _buildTotalRow('المجموع الفرعي:', posState.subtotal.toCurrency),
          _buildTotalRow('الضريبة:', posState.taxAmount.toCurrency),
          _buildTotalRow('الخصم:', posState.discountAmount.toCurrency),
          const Divider(),
          _buildTotalRow(
            'الإجمالي:', 
            posState.total.toCurrency, 
            isTotal: true,
          ),
          SizedBox(height: 16.h),
          
          // Checkout button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                _showCheckoutDialog(context, ref, posState);
              },
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 16.h),
              ),
              child: Text(
                'إتمام البيع',
                style: AppTextStyles.buttonText,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: isTotal 
                ? AppTextStyles.titleMedium.copyWith(fontWeight: FontWeight.bold)
                : AppTextStyles.bodyMedium,
          ),
          Text(
            value,
            style: isTotal 
                ? AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  )
                : AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }

  void _showAddToCartDialog(BuildContext context, WidgetRef ref, int productIndex) {
    // TODO: Implement add to cart dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة للسلة'),
        content: const Text('سيتم إضافة المنتج للسلة'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Add product to cart
              Navigator.of(context).pop();
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  void _showCheckoutDialog(BuildContext context, WidgetRef ref, POSState posState) {
    // TODO: Implement checkout dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إتمام البيع'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('الإجمالي: ${posState.total.toCurrency}'),
            // TODO: Add payment method selection and amount input
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              // TODO: Complete sale
              final saleId = await ref.read(posProvider.notifier).completeSale();
              if (saleId != null) {
                Navigator.of(context).pop();
                context.showSuccessSnackBar('تم إتمام البيع بنجاح');
              }
            },
            child: const Text('إتمام'),
          ),
        ],
      ),
    );
  }
}
