import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../utils/app_utils.dart';
import '../constants/app_constants.dart';

// Notification service provider
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService();
});

// Notification types
enum NotificationType {
  info,
  success,
  warning,
  error,
  lowStock,
  newSale,
  newPurchase,
  paymentDue,
}

// Notification model
class AppNotification {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final DateTime timestamp;
  final bool isRead;
  final Map<String, dynamic>? data;

  const AppNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    this.isRead = false,
    this.data,
  });

  AppNotification copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    DateTime? timestamp,
    bool? isRead,
    Map<String, dynamic>? data,
  }) {
    return AppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      data: data ?? this.data,
    );
  }

  // Get icon for notification type
  IconData get icon {
    switch (type) {
      case NotificationType.info:
        return Icons.info_outline;
      case NotificationType.success:
        return Icons.check_circle_outline;
      case NotificationType.warning:
        return Icons.warning_amber_outlined;
      case NotificationType.error:
        return Icons.error_outline;
      case NotificationType.lowStock:
        return Icons.inventory_2_outlined;
      case NotificationType.newSale:
        return Icons.point_of_sale;
      case NotificationType.newPurchase:
        return Icons.shopping_cart_outlined;
      case NotificationType.paymentDue:
        return Icons.payment_outlined;
    }
  }

  // Get color for notification type
  Color get color {
    switch (type) {
      case NotificationType.info:
        return Colors.blue;
      case NotificationType.success:
        return Colors.green;
      case NotificationType.warning:
        return Colors.orange;
      case NotificationType.error:
        return Colors.red;
      case NotificationType.lowStock:
        return Colors.orange;
      case NotificationType.newSale:
        return Colors.green;
      case NotificationType.newPurchase:
        return Colors.blue;
      case NotificationType.paymentDue:
        return Colors.red;
    }
  }
}

// Notification service
class NotificationService {
  final List<AppNotification> _notifications = [];
  final List<VoidCallback> _listeners = [];

  // Get all notifications
  List<AppNotification> get notifications => List.unmodifiable(_notifications);

  // Get unread notifications count
  int get unreadCount => _notifications.where((n) => !n.isRead).length;

  // Get unread notifications
  List<AppNotification> get unreadNotifications => 
      _notifications.where((n) => !n.isRead).toList();

  // Add listener
  void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  // Remove listener
  void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  // Notify listeners
  void _notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }

  // Add notification
  void addNotification({
    required String title,
    required String message,
    required NotificationType type,
    Map<String, dynamic>? data,
  }) {
    final notification = AppNotification(
      id: AppUtils.generateId(),
      title: title,
      message: message,
      type: type,
      timestamp: DateTime.now(),
      data: data,
    );

    _notifications.insert(0, notification);

    // Keep only the last N notifications
    if (_notifications.length > AppConstants.maxNotifications) {
      _notifications.removeRange(AppConstants.maxNotifications, _notifications.length);
    }

    _notifyListeners();
    AppUtils.logInfo('Notification added: $title');
  }

  // Mark notification as read
  void markAsRead(String id) {
    final index = _notifications.indexWhere((n) => n.id == id);
    if (index >= 0) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      _notifyListeners();
    }
  }

  // Mark all notifications as read
  void markAllAsRead() {
    for (int i = 0; i < _notifications.length; i++) {
      if (!_notifications[i].isRead) {
        _notifications[i] = _notifications[i].copyWith(isRead: true);
      }
    }
    _notifyListeners();
  }

  // Remove notification
  void removeNotification(String id) {
    _notifications.removeWhere((n) => n.id == id);
    _notifyListeners();
  }

  // Clear all notifications
  void clearAll() {
    _notifications.clear();
    _notifyListeners();
  }

  // Show snackbar notification
  void showSnackBar(
    BuildContext context, {
    required String message,
    NotificationType type = NotificationType.info,
    Duration duration = const Duration(seconds: 3),
  }) {
    Color backgroundColor;
    switch (type) {
      case NotificationType.success:
        backgroundColor = Colors.green;
        break;
      case NotificationType.warning:
        backgroundColor = Colors.orange;
        break;
      case NotificationType.error:
        backgroundColor = Colors.red;
        break;
      default:
        backgroundColor = Colors.blue;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'إغلاق',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  // Predefined notification methods
  void showSuccess(String message, {Map<String, dynamic>? data}) {
    addNotification(
      title: 'نجح',
      message: message,
      type: NotificationType.success,
      data: data,
    );
  }

  void showError(String message, {Map<String, dynamic>? data}) {
    addNotification(
      title: 'خطأ',
      message: message,
      type: NotificationType.error,
      data: data,
    );
  }

  void showWarning(String message, {Map<String, dynamic>? data}) {
    addNotification(
      title: 'تحذير',
      message: message,
      type: NotificationType.warning,
      data: data,
    );
  }

  void showInfo(String message, {Map<String, dynamic>? data}) {
    addNotification(
      title: 'معلومات',
      message: message,
      type: NotificationType.info,
      data: data,
    );
  }

  void showLowStock(String productName, double currentStock, {Map<String, dynamic>? data}) {
    addNotification(
      title: 'مخزون منخفض',
      message: 'المنتج "$productName" وصل لمستوى منخفض: $currentStock',
      type: NotificationType.lowStock,
      data: data,
    );
  }

  void showNewSale(String invoiceNo, double amount, {Map<String, dynamic>? data}) {
    addNotification(
      title: 'مبيعات جديدة',
      message: 'فاتورة جديدة $invoiceNo بقيمة ${amount.toStringAsFixed(2)} ر.س',
      type: NotificationType.newSale,
      data: data,
    );
  }

  void showNewPurchase(String invoiceNo, double amount, {Map<String, dynamic>? data}) {
    addNotification(
      title: 'مشتريات جديدة',
      message: 'فاتورة شراء جديدة $invoiceNo بقيمة ${amount.toStringAsFixed(2)} ر.س',
      type: NotificationType.newPurchase,
      data: data,
    );
  }

  void showPaymentDue(String customerName, double amount, {Map<String, dynamic>? data}) {
    addNotification(
      title: 'دفعة مستحقة',
      message: 'العميل "$customerName" عليه مبلغ ${amount.toStringAsFixed(2)} ر.س',
      type: NotificationType.paymentDue,
      data: data,
    );
  }

  // Get notifications by type
  List<AppNotification> getNotificationsByType(NotificationType type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  // Get notifications from today
  List<AppNotification> getTodayNotifications() {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    return _notifications.where((n) => 
      n.timestamp.isAfter(startOfDay) && n.timestamp.isBefore(endOfDay)
    ).toList();
  }

  // Get notifications from this week
  List<AppNotification> getWeekNotifications() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final startOfWeekDay = DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day);

    return _notifications.where((n) => n.timestamp.isAfter(startOfWeekDay)).toList();
  }
}
