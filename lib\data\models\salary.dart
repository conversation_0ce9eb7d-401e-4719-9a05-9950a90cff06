import 'package:json_annotation/json_annotation.dart';
import '../../core/constants/database_constants.dart';

part 'salary.g.dart';

/// نموذج الراتب - Salary Model
/// يمثل بيانات رواتب الموظفين في النظام
@JsonSerializable()
class Salary {
  final String id;
  final String employeeId;     // معرف الموظف
  final double basicSalary;    // الراتب الأساسي
  final double allowances;     // البدلات
  final double deductions;     // الخصومات
  final double overtime;       // العمل الإضافي
  final double bonus;          // المكافآت
  final double netSalary;      // صافي الراتب
  final DateTime payrollDate;  // تاريخ الراتب (شهر/سنة)
  final DateTime? paidDate;    // تاريخ الدفع الفعلي
  final String status;         // حالة الراتب (pending, paid, cancelled)
  final String? notes;         // ملاحظات
  final String? paymentMethod; // طريقة الدفع
  final String? referenceNumber; // رقم المرجع
  final String createdBy;      // من أنشأ السجل
  final DateTime createdAt;    // تاريخ الإنشاء
  final DateTime? updatedAt;   // تاريخ التحديث
  final DateTime? deletedAt;   // تاريخ الحذف (soft delete)
  final bool isSynced;         // هل تم المزامنة

  const Salary({
    required this.id,
    required this.employeeId,
    required this.basicSalary,
    this.allowances = 0.0,
    this.deductions = 0.0,
    this.overtime = 0.0,
    this.bonus = 0.0,
    required this.netSalary,
    required this.payrollDate,
    this.paidDate,
    this.status = 'pending',
    this.notes,
    this.paymentMethod,
    this.referenceNumber,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  /// إنشاء راتب من JSON
  factory Salary.fromJson(Map<String, dynamic> json) =>
      _$SalaryFromJson(json);

  /// تحويل الراتب إلى JSON
  Map<String, dynamic> toJson() => _$SalaryToJson(this);

  /// إنشاء راتب من Map قاعدة البيانات
  factory Salary.fromMap(Map<String, dynamic> map) {
    return Salary(
      id: map[DatabaseConstants.columnSalaryId] as String,
      employeeId: map[DatabaseConstants.columnSalaryEmployeeId] as String,
      basicSalary: (map[DatabaseConstants.columnSalaryBasicSalary] as num).toDouble(),
      allowances: (map[DatabaseConstants.columnSalaryAllowances] as num?)?.toDouble() ?? 0.0,
      deductions: (map[DatabaseConstants.columnSalaryDeductions] as num?)?.toDouble() ?? 0.0,
      overtime: (map[DatabaseConstants.columnSalaryOvertime] as num?)?.toDouble() ?? 0.0,
      bonus: (map[DatabaseConstants.columnSalaryBonus] as num?)?.toDouble() ?? 0.0,
      netSalary: (map[DatabaseConstants.columnSalaryNetSalary] as num).toDouble(),
      payrollDate: DateTime.parse(map[DatabaseConstants.columnSalaryPayrollDate] as String),
      paidDate: map[DatabaseConstants.columnSalaryPaidDate] != null
          ? DateTime.parse(map[DatabaseConstants.columnSalaryPaidDate] as String)
          : null,
      status: map[DatabaseConstants.columnSalaryStatus] as String? ?? 'pending',
      notes: map[DatabaseConstants.columnSalaryNotes] as String?,
      paymentMethod: map[DatabaseConstants.columnSalaryPaymentMethod] as String?,
      referenceNumber: map[DatabaseConstants.columnSalaryReferenceNumber] as String?,
      createdBy: map[DatabaseConstants.columnSalaryCreatedBy] as String,
      createdAt: DateTime.parse(map[DatabaseConstants.columnSalaryCreatedAt] as String),
      updatedAt: map[DatabaseConstants.columnSalaryUpdatedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnSalaryUpdatedAt] as String)
          : null,
      deletedAt: map[DatabaseConstants.columnSalaryDeletedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnSalaryDeletedAt] as String)
          : null,
      isSynced: (map[DatabaseConstants.columnSalaryIsSynced] as int) == 1,
    );
  }

  /// تحويل الراتب إلى Map لقاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnSalaryId: id,
      DatabaseConstants.columnSalaryEmployeeId: employeeId,
      DatabaseConstants.columnSalaryBasicSalary: basicSalary,
      DatabaseConstants.columnSalaryAllowances: allowances,
      DatabaseConstants.columnSalaryDeductions: deductions,
      DatabaseConstants.columnSalaryOvertime: overtime,
      DatabaseConstants.columnSalaryBonus: bonus,
      DatabaseConstants.columnSalaryNetSalary: netSalary,
      DatabaseConstants.columnSalaryPayrollDate: payrollDate.toIso8601String(),
      DatabaseConstants.columnSalaryPaidDate: paidDate?.toIso8601String(),
      DatabaseConstants.columnSalaryStatus: status,
      DatabaseConstants.columnSalaryNotes: notes,
      DatabaseConstants.columnSalaryPaymentMethod: paymentMethod,
      DatabaseConstants.columnSalaryReferenceNumber: referenceNumber,
      DatabaseConstants.columnSalaryCreatedBy: createdBy,
      DatabaseConstants.columnSalaryCreatedAt: createdAt.toIso8601String(),
      DatabaseConstants.columnSalaryUpdatedAt: updatedAt?.toIso8601String(),
      DatabaseConstants.columnSalaryDeletedAt: deletedAt?.toIso8601String(),
      DatabaseConstants.columnSalaryIsSynced: isSynced ? 1 : 0,
    };
  }

  /// نسخ الراتب مع تعديل بعض الخصائص
  Salary copyWith({
    String? id,
    String? employeeId,
    double? basicSalary,
    double? allowances,
    double? deductions,
    double? overtime,
    double? bonus,
    double? netSalary,
    DateTime? payrollDate,
    DateTime? paidDate,
    String? status,
    String? notes,
    String? paymentMethod,
    String? referenceNumber,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return Salary(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      basicSalary: basicSalary ?? this.basicSalary,
      allowances: allowances ?? this.allowances,
      deductions: deductions ?? this.deductions,
      overtime: overtime ?? this.overtime,
      bonus: bonus ?? this.bonus,
      netSalary: netSalary ?? this.netSalary,
      payrollDate: payrollDate ?? this.payrollDate,
      paidDate: paidDate ?? this.paidDate,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  /// حساب صافي الراتب تلقائياً
  static double calculateNetSalary({
    required double basicSalary,
    double allowances = 0.0,
    double deductions = 0.0,
    double overtime = 0.0,
    double bonus = 0.0,
  }) {
    return basicSalary + allowances + overtime + bonus - deductions;
  }

  /// إنشاء راتب جديد مع حساب صافي الراتب تلقائياً
  factory Salary.create({
    required String id,
    required String employeeId,
    required double basicSalary,
    double allowances = 0.0,
    double deductions = 0.0,
    double overtime = 0.0,
    double bonus = 0.0,
    DateTime? payrollDate,
    String? notes,
    String? paymentMethod,
    String? referenceNumber,
    required String createdBy,
  }) {
    final now = DateTime.now();
    final netSalary = calculateNetSalary(
      basicSalary: basicSalary,
      allowances: allowances,
      deductions: deductions,
      overtime: overtime,
      bonus: bonus,
    );

    return Salary(
      id: id,
      employeeId: employeeId,
      basicSalary: basicSalary,
      allowances: allowances,
      deductions: deductions,
      overtime: overtime,
      bonus: bonus,
      netSalary: netSalary,
      payrollDate: payrollDate ?? now,
      createdBy: createdBy,
      createdAt: now,
      notes: notes,
      paymentMethod: paymentMethod,
      referenceNumber: referenceNumber,
      isSynced: false,
    );
  }

  /// التحقق من صحة بيانات الراتب
  bool get isValid {
    return id.isNotEmpty &&
        employeeId.isNotEmpty &&
        basicSalary >= 0 &&
        netSalary >= 0 &&
        createdBy.isNotEmpty;
  }

  /// هل الراتب محذوف؟
  bool get isDeleted => deletedAt != null;

  /// هل الراتب مدفوع؟
  bool get isPaid => status == 'paid' && paidDate != null;

  /// هل الراتب معلق؟
  bool get isPending => status == 'pending';

  /// هل الراتب ملغي؟
  bool get isCancelled => status == 'cancelled';

  /// إجمالي الإضافات
  double get totalAdditions => allowances + overtime + bonus;

  /// إجمالي الخصومات
  double get totalDeductions => deductions;

  /// تحديد الراتب كمدفوع
  Salary markAsPaid({
    DateTime? paidDate,
    String? paymentMethod,
    String? referenceNumber,
  }) {
    return copyWith(
      status: 'paid',
      paidDate: paidDate ?? DateTime.now(),
      paymentMethod: paymentMethod ?? this.paymentMethod,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  /// إلغاء الراتب
  Salary cancel({String? reason}) {
    return copyWith(
      status: 'cancelled',
      notes: reason != null ? '$notes\nسبب الإلغاء: $reason' : notes,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  @override
  String toString() {
    return 'Salary(id: $id, employeeId: $employeeId, netSalary: $netSalary, payrollDate: $payrollDate, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Salary && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
