import 'package:flutter/material.dart';
import 'lib/core/services/service_locator.dart';
import 'lib/core/bootstrap/bootstrap_system.dart';
import 'lib/services/services.dart';

/// اختبار سريع لطبقة الخدمات
/// Quick test for Services Layer
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    print('🚀 بدء اختبار طبقة الخدمات...');
    
    // تهيئة النظام
    await ServiceLocator.init();
    await BootstrapSystem.bootstrapSystem();
    
    print('✅ تم تهيئة النظام بنجاح');
    
    // اختبار الخدمات
    await testAccountingService();
    await testUserService();
    await testSettingsService();
    
    print('🎉 تم اختبار جميع الخدمات بنجاح!');
    
  } catch (e) {
    print('❌ خطأ في الاختبار: $e');
  }
}

/// اختبار خدمة المحاسبة
Future<void> testAccountingService() async {
  print('\n📊 اختبار خدمة المحاسبة...');
  
  final accountingService = ServiceLocator.get<AccountingService>();
  
  // إنشاء قيد تجريبي
  final result = await accountingService.createSalesEntry(
    amount: 1000.0,
    description: 'مبيعات تجريبية',
    customerId: 'test-customer',
    invoiceNumber: 'INV-001',
  );
  
  if (result.isSuccess) {
    print('✅ تم إنشاء قيد المبيعات بنجاح');
  } else {
    print('❌ فشل في إنشاء قيد المبيعات: ${result.message}');
  }
}

/// اختبار خدمة المستخدمين
Future<void> testUserService() async {
  print('\n👤 اختبار خدمة المستخدمين...');
  
  final userService = ServiceLocator.get<UserService>();
  
  // محاولة تسجيل الدخول بالمستخدم الافتراضي
  final loginResult = await userService.login('admin', 'admin123');
  
  if (loginResult.isSuccess) {
    print('✅ تم تسجيل الدخول بنجاح');
    print('المستخدم: ${loginResult.data?.name}');
  } else {
    print('❌ فشل في تسجيل الدخول: ${loginResult.message}');
  }
}

/// اختبار خدمة الإعدادات
Future<void> testSettingsService() async {
  print('\n⚙️ اختبار خدمة الإعدادات...');
  
  final settingsService = ServiceLocator.get<SettingsService>();
  
  // الحصول على إعداد
  final companyName = await settingsService.getSetting('company_name');
  print('اسم الشركة: $companyName');
  
  // تحديث إعداد
  final updateResult = await settingsService.updateSetting(
    'company_name', 
    'شركة تجاري تك للتكنولوجيا'
  );
  
  if (updateResult.isSuccess) {
    print('✅ تم تحديث اسم الشركة بنجاح');
  } else {
    print('❌ فشل في تحديث اسم الشركة: ${updateResult.message}');
  }
}
