// نموذج معاملات المخزون وحالة إدارة المخزون

/// نموذج معاملة المخزون
class StockTransaction {
  final String id;
  final String productId;
  final String type; // 'adjustment', 'purchase', 'sale', 'return'
  final String operation; // 'add', 'subtract', 'set'
  final double quantity;
  final double previousStock;
  final double newStock;
  final String reason;
  final String? notes;
  final String? userId;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const StockTransaction({
    required this.id,
    required this.productId,
    required this.type,
    required this.operation,
    required this.quantity,
    required this.previousStock,
    required this.newStock,
    required this.reason,
    this.notes,
    this.userId,
    required this.createdAt,
    this.updatedAt,
  });

  factory StockTransaction.fromJson(Map<String, dynamic> json) {
    return StockTransaction(
      id: json['id'] as String,
      productId: json['productId'] as String,
      type: json['type'] as String,
      operation: json['operation'] as String,
      quantity: (json['quantity'] as num).toDouble(),
      previousStock: (json['previousStock'] as num).toDouble(),
      newStock: (json['newStock'] as num).toDouble(),
      reason: json['reason'] as String,
      notes: json['notes'] as String?,
      userId: json['userId'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'productId': productId,
      'type': type,
      'operation': operation,
      'quantity': quantity,
      'previousStock': previousStock,
      'newStock': newStock,
      'reason': reason,
      'notes': notes,
      'userId': userId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }
}

/// حالة إدارة المخزون
class StockManagementState {
  final List<StockTransaction> transactions;
  final bool isLoading;
  final String? error;

  const StockManagementState({
    this.transactions = const [],
    this.isLoading = false,
    this.error,
  });

  StockManagementState copyWith({
    List<StockTransaction>? transactions,
    bool? isLoading,
    String? error,
  }) {
    return StockManagementState(
      transactions: transactions ?? this.transactions,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

/// نوع عملية المخزون
enum StockOperationType {
  add('add', 'إضافة'),
  subtract('subtract', 'خصم'),
  set('set', 'تعيين');

  const StockOperationType(this.value, this.displayName);

  final String value;
  final String displayName;
}

/// نوع معاملة المخزون
enum StockTransactionType {
  adjustment('adjustment', 'تعديل مخزون'),
  purchase('purchase', 'شراء'),
  sale('sale', 'بيع'),
  returnItem('return', 'مرتجع');

  const StockTransactionType(this.value, this.displayName);

  final String value;
  final String displayName;
}
