import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:tijari_tech/core/utils/formatters.dart';
import '../../core/theme/app_theme.dart';
import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../data/models/sale.dart';
import '../../data/models/purchase.dart';
import '../../data/repositories/sale_repository.dart';
import '../../data/repositories/purchase_repository.dart';
import '../../widgets/custom_card.dart';
import '../../services/financial_reports_export.dart';

/// صفحة تقارير المبيعات والمشتريات التفصيلية
class SalesPurchasesReportsPage extends ConsumerStatefulWidget {
  const SalesPurchasesReportsPage({super.key});

  @override
  ConsumerState<SalesPurchasesReportsPage> createState() =>
      _SalesPurchasesReportsPageState();
}

class _SalesPurchasesReportsPageState
    extends ConsumerState<SalesPurchasesReportsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  DateTimeRange? _dateRange;
  String _selectedPeriod = 'thisMonth';
  bool _isLoading = false;

  // بيانات التقارير
  List<Map<String, dynamic>> _sales = [];
  List<Map<String, dynamic>> _purchases = [];
  Map<String, dynamic> _salesStats = {};
  Map<String, dynamic> _purchasesStats = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _initializeDateRange();
    _loadReportsData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _initializeDateRange() {
    final now = DateTime.now();
    switch (_selectedPeriod) {
      case 'today':
        _dateRange = DateTimeRange(
          start: DateTime(now.year, now.month, now.day),
          end: DateTime(now.year, now.month, now.day, 23, 59, 59),
        );
        break;
      case 'thisWeek':
        final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
        _dateRange = DateTimeRange(
          start: DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day),
          end: DateTime(now.year, now.month, now.day, 23, 59, 59),
        );
        break;
      case 'thisMonth':
        _dateRange = DateTimeRange(
          start: DateTime(now.year, now.month, 1),
          end: DateTime(now.year, now.month + 1, 0, 23, 59, 59),
        );
        break;
      case 'thisYear':
        _dateRange = DateTimeRange(
          start: DateTime(now.year, 1, 1),
          end: DateTime(now.year, 12, 31, 23, 59, 59),
        );
        break;
    }
  }

  Future<void> _loadReportsData() async {
    if (_dateRange == null) return;

    setState(() => _isLoading = true);

    try {
      // تحميل بيانات المبيعات
      final saleRepository = SaleRepository();
      final allSales = await saleRepository.getAllSales();
      _sales = allSales.where((sale) {
        final saleDateStr = sale['sale_date'] as String?;
        if (saleDateStr == null) return false;
        final saleDate = DateTime.parse(saleDateStr);
        return saleDate
                .isAfter(_dateRange!.start.subtract(const Duration(days: 1))) &&
            saleDate.isBefore(_dateRange!.end.add(const Duration(days: 1)));
      }).toList();

      // تحميل بيانات المشتريات
      final purchaseRepository = PurchaseRepository();
      final allPurchases = await purchaseRepository.getAllPurchases();
      _purchases = allPurchases.where((purchase) {
        final purchaseDateStr = purchase['purchase_date'] as String?;
        if (purchaseDateStr == null) return false;
        final purchaseDate = DateTime.parse(purchaseDateStr);
        return purchaseDate
                .isAfter(_dateRange!.start.subtract(const Duration(days: 1))) &&
            purchaseDate.isBefore(_dateRange!.end.add(const Duration(days: 1)));
      }).toList();

      // حساب الإحصائيات
      _calculateSalesStats();
      _calculatePurchasesStats();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل البيانات: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _calculateSalesStats() {
    final totalSales = _sales.length;
    final totalAmount = _sales.fold<double>(0, (sum, sale) {
      final total = (sale['total'] as num?)?.toDouble() ?? 0.0;
      return sum + total;
    });
    final totalPaid = _sales.fold<double>(0, (sum, sale) {
      final paid = (sale['paid'] as num?)?.toDouble() ?? 0.0;
      return sum + paid;
    });
    final totalDue = _sales.fold<double>(0, (sum, sale) {
      final due = (sale['due'] as num?)?.toDouble() ?? 0.0;
      return sum + due;
    });

    final paidSales = _sales.where((sale) {
      final total = (sale['total'] as num?)?.toDouble() ?? 0.0;
      final paid = (sale['paid'] as num?)?.toDouble() ?? 0.0;
      return paid >= total;
    }).length;

    final partiallyPaidSales = _sales.where((sale) {
      final total = (sale['total'] as num?)?.toDouble() ?? 0.0;
      final paid = (sale['paid'] as num?)?.toDouble() ?? 0.0;
      return paid > 0 && paid < total;
    }).length;

    final unpaidSales = _sales.where((sale) {
      final paid = (sale['paid'] as num?)?.toDouble() ?? 0.0;
      return paid <= 0;
    }).length;

    _salesStats = {
      'totalSales': totalSales,
      'totalAmount': totalAmount,
      'totalPaid': totalPaid,
      'totalDue': totalDue,
      'paidSales': paidSales,
      'partiallyPaidSales': partiallyPaidSales,
      'unpaidSales': unpaidSales,
      'averageAmount': totalSales > 0 ? totalAmount / totalSales : 0,
      'collectionRate': totalAmount > 0 ? (totalPaid / totalAmount) * 100 : 0,
    };
  }

  void _calculatePurchasesStats() {
    final totalPurchases = _purchases.length;
    final totalAmount = _purchases.fold<double>(0, (sum, purchase) {
      final total = (purchase['total_amount'] as num?)?.toDouble() ?? 0.0;
      return sum + total;
    });
    final totalPaid = _purchases.fold<double>(0, (sum, purchase) {
      final paid = (purchase['paid_amount'] as num?)?.toDouble() ?? 0.0;
      return sum + paid;
    });
    final totalDue = _purchases.fold<double>(0, (sum, purchase) {
      final due = (purchase['due_amount'] as num?)?.toDouble() ?? 0.0;
      return sum + due;
    });

    _purchasesStats = {
      'totalPurchases': totalPurchases,
      'totalAmount': totalAmount,
      'totalPaid': totalPaid,
      'totalDue': totalDue,
      'averageAmount': totalPurchases > 0 ? totalAmount / totalPurchases : 0,
      'paymentRate': totalAmount > 0 ? (totalPaid / totalAmount) * 100 : 0,
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقارير المبيعات والمشتريات'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportToExcel,
            tooltip: 'تصدير إلى Excel',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.onPrimary,
          unselectedLabelColor: AppColors.onPrimary.withOpacity(0.7),
          indicatorColor: AppColors.onPrimary,
          tabs: const [
            Tab(text: 'المبيعات', icon: Icon(Icons.trending_up)),
            Tab(text: 'المشتريات', icon: Icon(Icons.trending_down)),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildFiltersSection(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildSalesReportTab(),
                _buildPurchasesReportTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return CustomCard(
      margin: EdgeInsets.all(16.r),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'فلترة التقارير',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: _buildPeriodSelector(),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildCustomDateRange(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return DropdownButtonFormField<String>(
      value: _selectedPeriod,
      decoration: const InputDecoration(
        labelText: 'الفترة الزمنية',
        border: OutlineInputBorder(),
      ),
      items: const [
        DropdownMenuItem(value: 'today', child: Text('اليوم')),
        DropdownMenuItem(value: 'thisWeek', child: Text('هذا الأسبوع')),
        DropdownMenuItem(value: 'thisMonth', child: Text('هذا الشهر')),
        DropdownMenuItem(value: 'thisYear', child: Text('هذا العام')),
        DropdownMenuItem(value: 'custom', child: Text('فترة مخصصة')),
      ],
      onChanged: (value) {
        setState(() {
          _selectedPeriod = value!;
          if (value != 'custom') {
            _initializeDateRange();
            _loadReportsData();
          }
        });
      },
    );
  }

  Widget _buildCustomDateRange() {
    return OutlinedButton.icon(
      onPressed: _selectedPeriod == 'custom' ? _selectDateRange : null,
      icon: const Icon(Icons.date_range),
      label: Text(
        _dateRange != null
            ? '${DateFormat('dd/MM/yyyy').format(_dateRange!.start)} - ${DateFormat('dd/MM/yyyy').format(_dateRange!.end)}'
            : 'اختر التاريخ',
      ),
    );
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _dateRange,
      locale: const Locale('ar'),
    );

    if (picked != null) {
      setState(() {
        _dateRange = picked;
      });
      _loadReportsData();
    }
  }

  /// تصدير التقرير إلى Excel
  Future<void> _exportToExcel() async {
    if (_dateRange == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى تحديد فترة زمنية أولاً')),
      );
      return;
    }

    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // تصدير البيانات
      final filePath = await FinancialReportsExport.exportSalesPurchasesToExcel(
        sales: _sales,
        purchases: _purchases,
        salesStats: _salesStats,
        purchasesStats: _purchasesStats,
        dateRange: _dateRange!,
      );

      // إخفاء مؤشر التحميل
      Navigator.of(context).pop();

      // إظهار رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تصدير التقرير بنجاح إلى: $filePath'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      // إخفاء مؤشر التحميل
      Navigator.of(context).pop();

      // إظهار رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء التصدير: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Widget _buildSalesReportTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        children: [
          _buildSalesStatsCards(),
          SizedBox(height: 16.h),
          _buildSalesChart(),
          SizedBox(height: 16.h),
          _buildSalesTable(),
        ],
      ),
    );
  }

  Widget _buildPurchasesReportTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        children: [
          _buildPurchasesStatsCards(),
          SizedBox(height: 16.h),
          _buildPurchasesChart(),
          SizedBox(height: 16.h),
          _buildPurchasesTable(),
        ],
      ),
    );
  }

  Widget _buildSalesStatsCards() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 12.w,
      mainAxisSpacing: 12.h,
      childAspectRatio: 0.9,
      children: [
        _buildStatCard(
          'إجمالي المبيعات',
          '${_salesStats['totalSales'] ?? 0}',
          Icons.receipt_long,
          AppColors.primary,
        ),
        _buildStatCard(
          'إجمالي المبلغ',
          AppFormatters.formatCurrency(_salesStats['totalAmount'] ?? 0),
          Icons.attach_money,
          AppColors.success,
        ),
        _buildStatCard(
          'المبلغ المحصل',
          AppFormatters.formatCurrency(_salesStats['totalPaid'] ?? 0),
          Icons.payment,
          AppColors.info,
        ),
        _buildStatCard(
          'المبلغ المستحق',
          AppFormatters.formatCurrency(_salesStats['totalDue'] ?? 0),
          Icons.pending_actions,
          AppColors.warning,
        ),
      ],
    );
  }

  Widget _buildPurchasesStatsCards() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 12.w,
      mainAxisSpacing: 12.h,
      childAspectRatio: 0.9,
      children: [
        _buildStatCard(
          'إجمالي المشتريات',
          '${_purchasesStats['totalPurchases'] ?? 0}',
          Icons.shopping_cart,
          AppColors.primary,
        ),
        _buildStatCard(
          'إجمالي المبلغ',
          AppFormatters.formatCurrency(_purchasesStats['totalAmount'] ?? 0),
          Icons.attach_money,
          AppColors.error,
        ),
        _buildStatCard(
          'المبلغ المدفوع',
          AppFormatters.formatCurrency(_purchasesStats['totalPaid'] ?? 0),
          Icons.payment,
          AppColors.info,
        ),
        _buildStatCard(
          'المبلغ المستحق',
          AppFormatters.formatCurrency(_purchasesStats['totalDue'] ?? 0),
          Icons.pending_actions,
          AppColors.warning,
        ),
      ],
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32.r),
            SizedBox(height: 8.h),
            Text(
              title,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 4.h),
            Text(
              value,
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesChart() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تحليل المبيعات',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: _buildAnalysisItem(
                    'متوسط قيمة الفاتورة',
                    AppFormatters.formatCurrency(
                        (_salesStats['averageAmount'] as num?)?.toDouble() ??
                            0),
                    Icons.analytics,
                    AppColors.primary,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildAnalysisItem(
                    'معدل التحصيل',
                    '${(_salesStats['collectionRate'] ?? 0).toStringAsFixed(1)}%',
                    Icons.trending_up,
                    AppColors.success,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            _buildPaymentStatusChart(),
          ],
        ),
      ),
    );
  }

  Widget _buildPurchasesChart() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تحليل المشتريات',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: _buildAnalysisItem(
                    'متوسط قيمة الفاتورة',
                    AppFormatters.formatCurrency(
                        (_purchasesStats['averageAmount'] as num?)
                                ?.toDouble() ??
                            0.0),
                    Icons.analytics,
                    AppColors.primary,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildAnalysisItem(
                    'معدل الدفع',
                    '${(_purchasesStats['paymentRate'] ?? 0).toStringAsFixed(1)}%',
                    Icons.trending_down,
                    AppColors.error,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalysisItem(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.r),
          SizedBox(height: 8.h),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 4.h),
          Text(
            value,
            style: AppTextStyles.titleSmall.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentStatusChart() {
    final paidSales = _salesStats['paidSales'] ?? 0;
    final partiallyPaidSales = _salesStats['partiallyPaidSales'] ?? 0;
    final unpaidSales = _salesStats['unpaidSales'] ?? 0;
    final totalSales = _salesStats['totalSales'] ?? 1;

    return Column(
      children: [
        Text(
          'توزيع حالات الدفع',
          style: AppTextStyles.titleSmall.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildStatusItem(
                'مدفوع بالكامل',
                paidSales,
                totalSales,
                AppColors.success,
              ),
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: _buildStatusItem(
                'مدفوع جزئياً',
                partiallyPaidSales,
                totalSales,
                AppColors.warning,
              ),
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: _buildStatusItem(
                'غير مدفوع',
                unpaidSales,
                totalSales,
                AppColors.error,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatusItem(String label, int count, int total, Color color) {
    final percentage = total > 0 ? (count / total) * 100 : 0.0;

    return Container(
      padding: EdgeInsets.all(8.r),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6.r),
      ),
      child: Column(
        children: [
          Text(
            count.toString(),
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: AppTextStyles.bodySmall,
            textAlign: TextAlign.center,
          ),
          Text(
            '${percentage.toStringAsFixed(1)}%',
            style: AppTextStyles.bodySmall.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSalesTable() {
    if (_sales.isEmpty) {
      return CustomCard(
        child: Padding(
          padding: EdgeInsets.all(32.r),
          child: Column(
            children: [
              Icon(
                Icons.receipt_long_outlined,
                size: 48.r,
                color: AppColors.onSurface.withOpacity(0.5),
              ),
              SizedBox(height: 16.h),
              Text(
                'لا توجد مبيعات في الفترة المحددة',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(16.r),
            child: Text(
              'تفاصيل المبيعات',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const Divider(height: 1),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _sales.length > 10 ? 10 : _sales.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final sale = _sales[index];
              return _buildSaleItem(sale);
            },
          ),
          if (_sales.length > 10)
            Padding(
              padding: EdgeInsets.all(16.r),
              child: Center(
                child: TextButton(
                  onPressed: () {
                    // عرض جميع المبيعات
                  },
                  child: Text('عرض جميع المبيعات (${_sales.length})'),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSaleItem(Map<String, dynamic> sale) {
    final invoiceNo = sale['invoice_no'] as String? ?? 'غير محدد';
    final customerName = sale['customer_name'] as String? ?? 'عميل غير محدد';
    final total = (sale['total'] as num?)?.toDouble() ?? 0.0;
    final paid = (sale['paid'] as num?)?.toDouble() ?? 0.0;
    final due = (sale['due'] as num?)?.toDouble() ?? 0.0;
    final saleDateStr = sale['sale_date'] as String?;
    final saleDate =
        saleDateStr != null ? DateTime.parse(saleDateStr) : DateTime.now();

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: AppColors.primary.withValues(alpha: 0.1),
        child: Icon(
          Icons.receipt,
          color: AppColors.primary,
          size: 20.r,
        ),
      ),
      title: Text(
        'فاتورة رقم: $invoiceNo',
        style: AppTextStyles.titleSmall.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'العميل: $customerName',
            style: AppTextStyles.bodySmall,
          ),
          Text(
            'التاريخ: ${AppFormatters.formatDate(saleDate)}',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
      trailing: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            AppFormatters.formatCurrency(total),
            style: AppTextStyles.titleSmall.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          if (due > 0)
            Text(
              'مستحق: ${AppFormatters.formatCurrency(due)}',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.error,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPurchasesTable() {
    if (_purchases.isEmpty) {
      return CustomCard(
        child: Padding(
          padding: EdgeInsets.all(32.r),
          child: Column(
            children: [
              Icon(
                Icons.shopping_cart_outlined,
                size: 48.r,
                color: AppColors.onSurface.withValues(alpha: 0.5),
              ),
              SizedBox(height: 16.h),
              Text(
                'لا توجد مشتريات في الفترة المحددة',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(16.r),
            child: Text(
              'تفاصيل المشتريات',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const Divider(height: 1),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _purchases.length > 10 ? 10 : _purchases.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final purchase = _purchases[index];
              return _buildPurchaseItem(purchase);
            },
          ),
          if (_purchases.length > 10)
            Padding(
              padding: EdgeInsets.all(16.r),
              child: Center(
                child: TextButton(
                  onPressed: () {
                    // عرض جميع المشتريات
                  },
                  child: Text('عرض جميع المشتريات (${_purchases.length})'),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPurchaseItem(Map<String, dynamic> purchase) {
    final invoiceNo = purchase['invoice_no'] as String? ?? 'غير محدد';
    final supplierName =
        purchase['supplier_name'] as String? ?? 'مورد غير محدد';
    final totalAmount = (purchase['total_amount'] as num?)?.toDouble() ?? 0.0;
    final paidAmount = (purchase['paid_amount'] as num?)?.toDouble() ?? 0.0;
    final dueAmount = (purchase['due_amount'] as num?)?.toDouble() ?? 0.0;
    final purchaseDateStr = purchase['purchase_date'] as String?;
    final purchaseDate = purchaseDateStr != null
        ? DateTime.parse(purchaseDateStr)
        : DateTime.now();

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: AppColors.error.withValues(alpha: 0.1),
        child: Icon(
          Icons.shopping_cart,
          color: AppColors.error,
          size: 20.r,
        ),
      ),
      title: Text(
        'فاتورة رقم: $invoiceNo',
        style: AppTextStyles.titleSmall.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المورد: $supplierName',
            style: AppTextStyles.bodySmall,
          ),
          Text(
            'التاريخ: ${AppFormatters.formatDate(purchaseDate)}',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
      trailing: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            AppFormatters.formatCurrency(totalAmount),
            style: AppTextStyles.titleSmall.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.error,
            ),
          ),
          if (dueAmount > 0)
            Text(
              'مستحق: ${AppFormatters.formatCurrency(dueAmount)}',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.warning,
              ),
            ),
        ],
      ),
    );
  }
}
