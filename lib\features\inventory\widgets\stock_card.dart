import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/colors.dart';
import '../../../core/theme/text_styles.dart';
import '../../../data/models/stock.dart';

class StockCard extends StatelessWidget {
  final Stock stock;
  final VoidCallback? onTap;
  final VoidCallback? onAdjust;
  final VoidCallback? onTransfer;

  const StockCard({
    super.key,
    required this.stock,
    this.onTap,
    this.onAdjust,
    this.onTransfer,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row
              Row(
                children: [
                  // Product info
                  Expanded(
                    child: Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(8.w),
                          decoration: BoxDecoration(
                            color: _getStatusColor().withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Icon(
                            _getStatusIcon(),
                            color: _getStatusColor(),
                            size: 20.sp,
                          ),
                        ),
                        SizedBox(width: 12.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                stock.productName ?? 'منتج غير محدد',
                                style: AppTextStyles.titleMedium.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              if (stock.productBarcode != null)
                                Text(
                                  stock.productBarcode!,
                                  style: AppTextStyles.bodySmall.copyWith(
                                    color: Colors.grey[600],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Status badge
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      _getStatusText(),
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 10.sp,
                      ),
                    ),
                  ),

                  // Actions menu
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'adjust':
                          onAdjust?.call();
                          break;
                        case 'transfer':
                          onTransfer?.call();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'adjust',
                        child: Row(
                          children: [
                            Icon(Icons.tune, size: 16),
                            SizedBox(width: 8),
                            Text('تعديل الكمية'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'transfer',
                        child: Row(
                          children: [
                            Icon(Icons.swap_horiz, size: 16),
                            SizedBox(width: 8),
                            Text('نقل المخزون'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // Warehouse info
              if (stock.warehouseName != null)
                Row(
                  children: [
                    Icon(
                      Icons.warehouse,
                      size: 16.sp,
                      color: Colors.grey[600],
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      stock.warehouseName!,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),

              SizedBox(height: 12.h),

              // Stock details
              Row(
                children: [
                  Expanded(
                    child: _buildDetailItem(
                      'الكمية الكلية',
                      '${stock.quantity.toStringAsFixed(2)} ${stock.unitName ?? ''}',
                      Icons.inventory,
                    ),
                  ),
                  Expanded(
                    child: _buildDetailItem(
                      'الكمية المتاحة',
                      '${stock.actualAvailableQuantity.toStringAsFixed(2)} ${stock.unitName ?? ''}',
                      Icons.check_circle,
                    ),
                  ),
                ],
              ),

              if (stock.reservedQuantity > 0) SizedBox(height: 8.h),

              if (stock.reservedQuantity > 0)
                Row(
                  children: [
                    Expanded(
                      child: _buildDetailItem(
                        'الكمية المحجوزة',
                        '${stock.reservedQuantity.toStringAsFixed(2)} ${stock.unitName ?? ''}',
                        Icons.lock,
                      ),
                    ),
                    Expanded(
                      child: _buildDetailItem(
                        'القيمة الإجمالية',
                        '${stock.stockValue.toStringAsFixed(2)} ر.س',
                        Icons.monetization_on,
                      ),
                    ),
                  ],
                ),

              // Reorder point warning
              if (stock.reorderPoint > 0 &&
                  stock.quantity <= stock.reorderPoint)
                Column(
                  children: [
                    SizedBox(height: 12.h),
                    Container(
                      padding: EdgeInsets.all(8.w),
                      decoration: BoxDecoration(
                        color: Colors.orange[50],
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(color: Colors.orange[300]!),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.warning,
                            color: Colors.orange,
                            size: 16.sp,
                          ),
                          SizedBox(width: 8.w),
                          Expanded(
                            child: Text(
                              'المخزون أقل من نقطة إعادة الطلب (${stock.reorderPoint.toStringAsFixed(0)})',
                              style: AppTextStyles.bodySmall.copyWith(
                                color: Colors.orange[700],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

              // Expiry date warning
              if (stock.expiryDate != null && _isNearExpiry())
                Column(
                  children: [
                    SizedBox(height: 8.h),
                    Container(
                      padding: EdgeInsets.all(8.w),
                      decoration: BoxDecoration(
                        color: Colors.red[50],
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(color: Colors.red[300]!),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.schedule,
                            color: Colors.red,
                            size: 16.sp,
                          ),
                          SizedBox(width: 8.w),
                          Expanded(
                            child: Text(
                              _getExpiryWarningText(),
                              style: AppTextStyles.bodySmall.copyWith(
                                color: Colors.red[700],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.sp,
          color: Colors.grey[600],
        ),
        SizedBox(width: 4.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getStatusColor() {
    switch (stock.stockStatus) {
      case StockStatus.inStock:
        return Colors.green;
      case StockStatus.lowStock:
        return Colors.orange;
      case StockStatus.outOfStock:
        return Colors.red;
      case StockStatus.overStock:
        return Colors.blue;
    }
  }

  IconData _getStatusIcon() {
    switch (stock.stockStatus) {
      case StockStatus.inStock:
        return Icons.check_circle;
      case StockStatus.lowStock:
        return Icons.warning;
      case StockStatus.outOfStock:
        return Icons.error;
      case StockStatus.overStock:
        return Icons.trending_up;
    }
  }

  String _getStatusText() {
    switch (stock.stockStatus) {
      case StockStatus.inStock:
        return 'متوفر';
      case StockStatus.lowStock:
        return 'منخفض';
      case StockStatus.outOfStock:
        return 'نفد';
      case StockStatus.overStock:
        return 'زائد';
    }
  }

  bool _isNearExpiry() {
    if (stock.expiryDate == null) return false;

    final now = DateTime.now();
    final expiryDate = stock.expiryDate!;
    final daysUntilExpiry = expiryDate.difference(now).inDays;

    return daysUntilExpiry <= 30; // Consider near expiry if within 30 days
  }

  String _getExpiryWarningText() {
    if (stock.expiryDate == null) return '';

    final now = DateTime.now();
    final expiryDate = stock.expiryDate!;
    final daysUntilExpiry = expiryDate.difference(now).inDays;

    if (daysUntilExpiry < 0) {
      return 'منتهي الصلاحية منذ ${(-daysUntilExpiry)} يوم';
    } else if (daysUntilExpiry == 0) {
      return 'ينتهي اليوم';
    } else if (daysUntilExpiry <= 7) {
      return 'ينتهي خلال $daysUntilExpiry أيام';
    } else {
      return 'ينتهي خلال $daysUntilExpiry يوم';
    }
  }
}
