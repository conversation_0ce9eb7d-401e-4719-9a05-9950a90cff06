import 'package:tijari_tech/core/constants/database_constants.dart';
import 'base_dao.dart';
import '../../models/cash_box.dart';
import '../../../core/utils/app_utils.dart';

class CashBoxDao extends BaseDao<CashBox> {
  @override
  String get tableName => DatabaseConstants.tableCashBoxes;

  @override
  CashBox fromMap(Map<String, dynamic> map) => CashBox.fromMap(map);

  @override
  Map<String, dynamic> toMap(CashBox entity) => entity.toMap();

  // ------------------------------------------------------------------
  // CashBox-specific queries
  // ------------------------------------------------------------------

  /// استرجاع الصناديق النشطة فقط
  Future<List<CashBox>> getActiveCashBoxes() async {
    return await findWhere(
      where: '${DatabaseConstants.columnCashBoxDeletedAt} IS NULL AND ${DatabaseConstants.columnCashBoxIsActive} = 1',
      orderBy: DatabaseConstants.columnCashBoxName,
    );
  }

  /// استرجاع جميع الصناديق (بما في ذلك المحذوفة)
  Future<List<CashBox>> getAllCashBoxes() async {
    return await findAll(orderBy: DatabaseConstants.columnCashBoxName);
  }

  /// استرجاع صندوق حسب الاسم
  Future<CashBox?> findByName(String name) async {
    try {
      final cashBoxes = await findWhere(
        where: '${DatabaseConstants.columnCashBoxName} = ? AND ${DatabaseConstants.columnCashBoxDeletedAt} IS NULL',
        whereArgs: [name],
        limit: 1,
      );
      return cashBoxes.isNotEmpty ? cashBoxes.first : null;
    } catch (e) {
      AppUtils.logError('Error finding cash box by name', e);
      rethrow;
    }
  }

  /// استرجاع الصندوق الافتراضي
  Future<CashBox?> getDefaultCashBox() async {
    try {
      final cashBoxes = await findWhere(
        where: '${DatabaseConstants.columnCashBoxDeletedAt} IS NULL AND ${DatabaseConstants.columnCashBoxIsActive} = 1',
        orderBy: DatabaseConstants.columnCashBoxCreatedAt,
        limit: 1,
      );
      return cashBoxes.isNotEmpty ? cashBoxes.first : null;
    } catch (e) {
      AppUtils.logError('Error getting default cash box', e);
      rethrow;
    }
  }

  /// استرجاع الصناديق حسب الفرع
  Future<List<CashBox>> getCashBoxesByBranch(String branchId) async {
    return await findWhere(
      where: '${DatabaseConstants.columnCashBoxBranchId} = ? AND ${DatabaseConstants.columnCashBoxDeletedAt} IS NULL',
      whereArgs: [branchId],
      orderBy: DatabaseConstants.columnCashBoxName,
    );
  }

  /// تحديث رصيد الصندوق
  Future<void> updateBalance(String cashBoxId, double newBalance) async {
    try {
      final db = await database;
      await db.update(
        tableName,
        {
          DatabaseConstants.columnCashBoxBalance: newBalance,
          DatabaseConstants.columnCashBoxUpdatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnCashBoxIsSynced: 0,
        },
        where: '${DatabaseConstants.columnCashBoxId} = ?',
        whereArgs: [cashBoxId],
      );
      AppUtils.logInfo('Cash box balance updated: $cashBoxId -> $newBalance');
    } catch (e) {
      AppUtils.logError('Error updating cash box balance', e);
      rethrow;
    }
  }

  /// إضافة مبلغ إلى رصيد الصندوق
  Future<void> addToBalance(String cashBoxId, double amount) async {
    try {
      final db = await database;
      await db.rawUpdate('''
        UPDATE $tableName 
        SET ${DatabaseConstants.columnCashBoxBalance} = ${DatabaseConstants.columnCashBoxBalance} + ?,
            ${DatabaseConstants.columnCashBoxUpdatedAt} = ?,
            ${DatabaseConstants.columnCashBoxIsSynced} = 0
        WHERE ${DatabaseConstants.columnCashBoxId} = ?
      ''', [amount, DateTime.now().toIso8601String(), cashBoxId]);
      
      AppUtils.logInfo('Amount added to cash box: $cashBoxId + $amount');
    } catch (e) {
      AppUtils.logError('Error adding to cash box balance', e);
      rethrow;
    }
  }

  /// طرح مبلغ من رصيد الصندوق
  Future<void> subtractFromBalance(String cashBoxId, double amount) async {
    try {
      final db = await database;
      
      // التحقق من وجود رصيد كافي
      final cashBox = await findById(cashBoxId);
      if (cashBox == null) {
        throw Exception('Cash box not found');
      }
      
      if (cashBox.balance < amount) {
        throw Exception('Insufficient balance in cash box');
      }
      
      await db.rawUpdate('''
        UPDATE $tableName 
        SET ${DatabaseConstants.columnCashBoxBalance} = ${DatabaseConstants.columnCashBoxBalance} - ?,
            ${DatabaseConstants.columnCashBoxUpdatedAt} = ?,
            ${DatabaseConstants.columnCashBoxIsSynced} = 0
        WHERE ${DatabaseConstants.columnCashBoxId} = ?
      ''', [amount, DateTime.now().toIso8601String(), cashBoxId]);
      
      AppUtils.logInfo('Amount subtracted from cash box: $cashBoxId - $amount');
    } catch (e) {
      AppUtils.logError('Error subtracting from cash box balance', e);
      rethrow;
    }
  }

  /// الحصول على إجمالي أرصدة الصناديق النشطة
  Future<double> getTotalActiveBalance() async {
    try {
      final db = await database;
      final result = await db.rawQuery('''
        SELECT SUM(${DatabaseConstants.columnCashBoxBalance}) as total
        FROM $tableName 
        WHERE ${DatabaseConstants.columnCashBoxDeletedAt} IS NULL 
          AND ${DatabaseConstants.columnCashBoxIsActive} = 1
      ''');
      
      return (result.first['total'] as num?)?.toDouble() ?? 0.0;
    } catch (e) {
      AppUtils.logError('Error getting total active balance', e);
      rethrow;
    }
  }

  /// البحث في الصناديق
  Future<List<CashBox>> searchCashBoxes(String query) async {
    return await findWhere(
      where: '''
        ${DatabaseConstants.columnCashBoxDeletedAt} IS NULL AND 
        ${DatabaseConstants.columnCashBoxName} LIKE ?
      ''',
      whereArgs: ['%$query%'],
      orderBy: DatabaseConstants.columnCashBoxName,
    );
  }

  /// تفعيل/إلغاء تفعيل الصندوق
  Future<void> toggleActive(String cashBoxId, bool isActive) async {
    try {
      final db = await database;
      await db.update(
        tableName,
        {
          DatabaseConstants.columnCashBoxIsActive: isActive ? 1 : 0,
          DatabaseConstants.columnCashBoxUpdatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnCashBoxIsSynced: 0,
        },
        where: '${DatabaseConstants.columnCashBoxId} = ?',
        whereArgs: [cashBoxId],
      );
      AppUtils.logInfo('Cash box active status updated: $cashBoxId -> $isActive');
    } catch (e) {
      AppUtils.logError('Error toggling cash box active status', e);
      rethrow;
    }
  }

  /// حذف ناعم للصندوق
  Future<void> softDelete(String cashBoxId) async {
    try {
      final db = await database;
      await db.update(
        tableName,
        {
          DatabaseConstants.columnCashBoxDeletedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnCashBoxUpdatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnCashBoxIsSynced: 0,
        },
        where: '${DatabaseConstants.columnCashBoxId} = ?',
        whereArgs: [cashBoxId],
      );
      AppUtils.logInfo('Cash box soft deleted: $cashBoxId');
    } catch (e) {
      AppUtils.logError('Error soft deleting cash box', e);
      rethrow;
    }
  }

  /// استعادة الصندوق المحذوف
  Future<void> restore(String cashBoxId) async {
    try {
      final db = await database;
      await db.update(
        tableName,
        {
          DatabaseConstants.columnCashBoxDeletedAt: null,
          DatabaseConstants.columnCashBoxUpdatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnCashBoxIsSynced: 0,
        },
        where: '${DatabaseConstants.columnCashBoxId} = ?',
        whereArgs: [cashBoxId],
      );
      AppUtils.logInfo('Cash box restored: $cashBoxId');
    } catch (e) {
      AppUtils.logError('Error restoring cash box', e);
      rethrow;
    }
  }

  /// الحصول على إحصائيات الصناديق
  Future<Map<String, dynamic>> getCashBoxStatistics() async {
    try {
      final db = await database;
      
      final totalResult = await db.rawQuery('''
        SELECT 
          COUNT(*) as total_count,
          SUM(CASE WHEN ${DatabaseConstants.columnCashBoxIsActive} = 1 AND ${DatabaseConstants.columnCashBoxDeletedAt} IS NULL THEN 1 ELSE 0 END) as active_count,
          SUM(CASE WHEN ${DatabaseConstants.columnCashBoxIsActive} = 0 AND ${DatabaseConstants.columnCashBoxDeletedAt} IS NULL THEN 1 ELSE 0 END) as inactive_count,
          SUM(CASE WHEN ${DatabaseConstants.columnCashBoxDeletedAt} IS NOT NULL THEN 1 ELSE 0 END) as deleted_count,
          SUM(CASE WHEN ${DatabaseConstants.columnCashBoxIsActive} = 1 AND ${DatabaseConstants.columnCashBoxDeletedAt} IS NULL THEN ${DatabaseConstants.columnCashBoxBalance} ELSE 0 END) as total_balance
        FROM $tableName
      ''');
      
      final result = totalResult.first;
      
      return {
        'total_count': result['total_count'] ?? 0,
        'active_count': result['active_count'] ?? 0,
        'inactive_count': result['inactive_count'] ?? 0,
        'deleted_count': result['deleted_count'] ?? 0,
        'total_balance': (result['total_balance'] as num?)?.toDouble() ?? 0.0,
      };
    } catch (e) {
      AppUtils.logError('Error getting cash box statistics', e);
      rethrow;
    }
  }
}
