// Base exception class
abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const AppException(this.message, {this.code, this.originalError});

  @override
  String toString() => 'AppException: $message';
}

// Validation exceptions
class ValidationException extends AppException {
  const ValidationException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);

  @override
  String toString() => 'ValidationException: $message';
}

// Repository exceptions
class RepositoryException extends AppException {
  const RepositoryException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);

  @override
  String toString() => 'RepositoryException: $message';
}

// Database exceptions
class DatabaseException extends AppException {
  const DatabaseException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);

  @override
  String toString() => 'DatabaseException: $message';
}

// Network exceptions
class NetworkException extends AppException {
  const NetworkException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);

  @override
  String toString() => 'NetworkException: $message';
}

// Authentication exceptions
class AuthenticationException extends AppException {
  const AuthenticationException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);

  @override
  String toString() => 'AuthenticationException: $message';
}

// Authorization exceptions
class AuthorizationException extends AppException {
  const AuthorizationException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);

  @override
  String toString() => 'AuthorizationException: $message';
}

// Business logic exceptions
class BusinessLogicException extends AppException {
  const BusinessLogicException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);

  @override
  String toString() => 'BusinessLogicException: $message';
}

// File operation exceptions
class FileException extends AppException {
  const FileException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);

  @override
  String toString() => 'FileException: $message';
}

// Sync exceptions
class SyncException extends AppException {
  const SyncException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);

  @override
  String toString() => 'SyncException: $message';
}

// Configuration exceptions
class ConfigurationException extends AppException {
  const ConfigurationException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);

  @override
  String toString() => 'ConfigurationException: $message';
}

// Specific business exceptions
class InsufficientStockException extends BusinessLogicException {
  final String productId;
  final String productName;
  final double requestedQuantity;
  final double availableQuantity;

  const InsufficientStockException({
    required this.productId,
    required this.productName,
    required this.requestedQuantity,
    required this.availableQuantity,
  }) : super(
          'Insufficient stock for $productName. Requested: $requestedQuantity, Available: $availableQuantity',
          code: 'INSUFFICIENT_STOCK',
        );
}

class DuplicateRecordException extends ValidationException {
  final String field;
  final String value;

  const DuplicateRecordException({
    required this.field,
    required this.value,
  }) : super(
          'Duplicate value "$value" for field "$field"',
          code: 'DUPLICATE_RECORD',
        );
}

class RecordNotFoundException extends RepositoryException {
  final String recordType;
  final String recordId;

  const RecordNotFoundException({
    required this.recordType,
    required this.recordId,
  }) : super(
          '$recordType with ID "$recordId" not found',
          code: 'RECORD_NOT_FOUND',
        );
}

class InvalidOperationException extends BusinessLogicException {
  final String operation;
  final String reason;

  const InvalidOperationException({
    required this.operation,
    required this.reason,
  }) : super(
          'Invalid operation "$operation": $reason',
          code: 'INVALID_OPERATION',
        );
}

class PermissionDeniedException extends AuthorizationException {
  final String action;
  final String resource;

  const PermissionDeniedException({
    required this.action,
    required this.resource,
  }) : super(
          'Permission denied for action "$action" on resource "$resource"',
          code: 'PERMISSION_DENIED',
        );
}

// Exception handler utility
class ExceptionHandler {
  static String getDisplayMessage(Exception exception) {
    if (exception is AppException) {
      return exception.message;
    }
    
    // Handle common Flutter/Dart exceptions
    if (exception is FormatException) {
      return 'تنسيق البيانات غير صحيح';
    }
    
    if (exception is ArgumentError) {
      return 'خطأ في المعاملات المرسلة';
    }
    
    if (exception is StateError) {
      return 'خطأ في حالة التطبيق';
    }
    
    if (exception is TypeError) {
      return 'خطأ في نوع البيانات';
    }
    
    // Default message for unknown exceptions
    return 'حدث خطأ غير متوقع';
  }

  static String getErrorCode(Exception exception) {
    if (exception is AppException && exception.code != null) {
      return exception.code!;
    }
    
    return 'UNKNOWN_ERROR';
  }

  static bool isRetryable(Exception exception) {
    if (exception is NetworkException) {
      return true;
    }
    
    if (exception is SyncException) {
      return true;
    }
    
    if (exception is DatabaseException) {
      // Some database errors might be retryable
      return exception.code != 'CONSTRAINT_VIOLATION';
    }
    
    return false;
  }

  static Map<String, dynamic> toMap(Exception exception) {
    return {
      'type': exception.runtimeType.toString(),
      'message': getDisplayMessage(exception),
      'code': getErrorCode(exception),
      'retryable': isRetryable(exception),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}

// Exception logging utility
class ExceptionLogger {
  static void log(Exception exception, {String? context, Map<String, dynamic>? additionalData}) {
    final errorData = {
      'exception': ExceptionHandler.toMap(exception),
      'context': context,
      'additionalData': additionalData,
    };
    
    // Log to console in debug mode
    print('Exception logged: ${errorData.toString()}');
    
    // In production, you might want to send this to a logging service
    // like Firebase Crashlytics, Sentry, etc.
  }
}
