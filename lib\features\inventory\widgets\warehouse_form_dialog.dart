import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tijari_tech/core/theme/colors.dart';
import 'package:tijari_tech/core/theme/text_styles.dart';
import 'package:tijari_tech/widgets/custom_text_field.dart';
import '../../../shared/widgets/loading_overlay.dart';

import '../../../data/models/warehouse.dart';
import '../../../providers/warehouse_provider.dart';

class WarehouseFormDialog extends ConsumerStatefulWidget {
  final Warehouse? warehouse;

  const WarehouseFormDialog({
    super.key,
    this.warehouse,
  });

  @override
  ConsumerState<WarehouseFormDialog> createState() =>
      _WarehouseFormDialogState();
}

class _WarehouseFormDialogState extends ConsumerState<WarehouseFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _locationController = TextEditingController();
  final _addressController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _notesController = TextEditingController();
  final _capacityController = TextEditingController();

  bool _isActive = true;
  bool _isDefault = false;
  String _warehouseType = 'main';
  String? _selectedBranchId;
  String? _selectedManagerId;

  bool get _isEditing => widget.warehouse != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _initializeFields();
    }
  }

  void _initializeFields() {
    final warehouse = widget.warehouse!;
    _nameController.text = warehouse.name;
    _locationController.text = warehouse.location ?? '';
    _addressController.text = warehouse.address ?? '';
    _phoneController.text = warehouse.phone ?? '';
    _emailController.text = warehouse.email ?? '';
    _descriptionController.text = warehouse.description ?? '';
    _notesController.text = warehouse.notes ?? '';
    _capacityController.text = warehouse.capacity?.toString() ?? '';
    _isActive = warehouse.isActive;
    _isDefault = warehouse.isDefault;
    _warehouseType = warehouse.warehouseType ?? 'main';
    _selectedBranchId = warehouse.branchId;
    _selectedManagerId = warehouse.managerId;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _locationController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _descriptionController.dispose();
    _notesController.dispose();
    _capacityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(warehouseManagementProvider);

    return LoadingOverlay(
      isLoading: state.isLoading,
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: EdgeInsets.all(20.w),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16.r),
                    topRight: Radius.circular(16.r),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _isEditing ? Icons.edit : Icons.add,
                      color: Colors.white,
                      size: 24.sp,
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Text(
                        _isEditing ? 'تعديل المخزن' : 'إضافة مخزن جديد',
                        style: AppTextStyles.titleLarge.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close, color: Colors.white),
                    ),
                  ],
                ),
              ),

              // Form content
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(20.w),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Basic information section
                        _buildSectionTitle('المعلومات الأساسية'),
                        SizedBox(height: 16.h),

                        CustomTextField(
                          controller: _nameController,
                          label: 'اسم المخزن',
                          hint: 'أدخل اسم المخزن',
                          prefixIcon:Icon( Icons.warehouse_outlined),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'اسم المخزن مطلوب';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: 16.h),

                        Row(
                          children: [
                            Expanded(
                              child: CustomTextField(
                                controller: _locationController,
                                label: 'الموقع',
                                hint: 'أدخل موقع المخزن',
                                prefixIcon: Icon( Icons.location_on),
                              ),
                            ),
                            SizedBox(width: 12.w),
                            Expanded(
                              child: DropdownButtonFormField<String>(
                                value: _warehouseType,
                                decoration: InputDecoration(
                                  labelText: 'نوع المخزن',
                                  prefixIcon: const Icon(Icons.category),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8.r),
                                  ),
                                ),
                                items: const [
                                  DropdownMenuItem(
                                      value: 'main', child: Text('رئيسي')),
                                  DropdownMenuItem(
                                      value: 'branch', child: Text('فرع')),
                                  DropdownMenuItem(
                                      value: 'temporary', child: Text('مؤقت')),
                                ],
                                onChanged: (value) {
                                  setState(() {
                                    _warehouseType = value!;
                                  });
                                },
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 16.h),

                        CustomTextField(
                          controller: _addressController,
                          label: 'العنوان',
                          hint: 'أدخل عنوان المخزن',
                          prefixIcon:Icon(  Icons.home),
                          maxLines: 2,
                        ),
                        SizedBox(height: 16.h),

                        // Contact information section
                        _buildSectionTitle('معلومات الاتصال'),
                        SizedBox(height: 16.h),

                        Row(
                          children: [
                            Expanded(
                              child: CustomTextField(
                                controller: _phoneController,
                                label: 'رقم الهاتف',
                                hint: 'أدخل رقم الهاتف',
                                prefixIcon:Icon(  Icons.phone ) ,
                                keyboardType: TextInputType.phone,
                              ),
                            ),
                            SizedBox(width: 12.w),
                            Expanded(
                              child: CustomTextField(
                                controller: _emailController,
                                label: 'البريد الإلكتروني',
                                hint: 'أدخل البريد الإلكتروني',
                                prefixIcon:Icon(  Icons.email) ,
                                keyboardType: TextInputType.emailAddress,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 16.h),

                        // Capacity and settings section
                        _buildSectionTitle('الإعدادات'),
                        SizedBox(height: 16.h),

                        CustomTextField(
                          controller: _capacityController,
                          label: 'السعة التخزينية',
                          hint: 'أدخل السعة بالوحدات',
                          prefixIcon: Icon( Icons.storage),
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value != null && value.isNotEmpty) {
                              final capacity = double.tryParse(value);
                              if (capacity == null || capacity <= 0) {
                                return 'السعة يجب أن تكون رقم موجب';
                              }
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: 16.h),

                        // Status switches
                        Row(
                          children: [
                            Expanded(
                              child: SwitchListTile(
                                title: const Text('مخزن نشط'),
                                subtitle:
                                    const Text('تفعيل/إلغاء تفعيل المخزن'),
                                value: _isActive,
                                onChanged: (value) {
                                  setState(() {
                                    _isActive = value;
                                  });
                                },
                              ),
                            ),
                            Expanded(
                              child: SwitchListTile(
                                title: const Text('مخزن افتراضي'),
                                subtitle: const Text('تعيين كمخزن افتراضي'),
                                value: _isDefault,
                                onChanged: (value) {
                                  setState(() {
                                    _isDefault = value;
                                  });
                                },
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 16.h),

                        // Additional information section
                        _buildSectionTitle('معلومات إضافية'),
                        SizedBox(height: 16.h),

                        CustomTextField(
                          controller: _descriptionController,
                          label: 'الوصف',
                          hint: 'أدخل وصف المخزن',
                          prefixIcon: Icon( Icons.description),
                          maxLines: 3,
                        ),
                        SizedBox(height: 16.h),

                        CustomTextField(
                          controller: _notesController,
                          label: 'ملاحظات',
                          hint: 'أدخل أي ملاحظات إضافية',
                          prefixIcon:Icon(  Icons.note),
                          maxLines: 3,
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Action buttons
              Container(
                padding: EdgeInsets.all(20.w),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(16.r),
                    bottomRight: Radius.circular(16.r),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('إلغاء'),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _saveWarehouse,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                        ),
                        child: Text(
                          _isEditing ? 'تحديث' : 'إضافة',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: AppTextStyles.titleMedium.copyWith(
        fontWeight: FontWeight.bold,
        color: AppColors.primary,
      ),
    );
  }

  void _saveWarehouse() async {
    if (!_formKey.currentState!.validate()) return;

    final capacity = _capacityController.text.isNotEmpty
        ? double.tryParse(_capacityController.text)
        : null;

    if (_isEditing) {
      await ref.read(warehouseManagementProvider.notifier).updateWarehouse(
            widget.warehouse!.id,
            name: _nameController.text.trim(),
            location: _locationController.text.trim().isEmpty
                ? null
                : _locationController.text.trim(),
            address: _addressController.text.trim().isEmpty
                ? null
                : _addressController.text.trim(),
            phone: _phoneController.text.trim().isEmpty
                ? null
                : _phoneController.text.trim(),
            email: _emailController.text.trim().isEmpty
                ? null
                : _emailController.text.trim(),
            isActive: _isActive,
            isDefault: _isDefault,
            description: _descriptionController.text.trim().isEmpty
                ? null
                : _descriptionController.text.trim(),
            notes: _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
            capacity: capacity,
            warehouseType: _warehouseType,
            managerId: _selectedManagerId,
            branchId: _selectedBranchId,
          );
    } else {
      await ref.read(warehouseManagementProvider.notifier).createWarehouse(
            name: _nameController.text.trim(),
            location: _locationController.text.trim().isEmpty
                ? null
                : _locationController.text.trim(),
            address: _addressController.text.trim().isEmpty
                ? null
                : _addressController.text.trim(),
            phone: _phoneController.text.trim().isEmpty
                ? null
                : _phoneController.text.trim(),
            email: _emailController.text.trim().isEmpty
                ? null
                : _emailController.text.trim(),
            isActive: _isActive,
            isDefault: _isDefault,
            description: _descriptionController.text.trim().isEmpty
                ? null
                : _descriptionController.text.trim(),
            notes: _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
            capacity: capacity,
            warehouseType: _warehouseType,
            managerId: _selectedManagerId,
            branchId: _selectedBranchId,
          );
    }

    final state = ref.read(warehouseManagementProvider);
    if (!state.isLoading && state.error == null) {
      Navigator.of(context).pop();
    }
  }
}
