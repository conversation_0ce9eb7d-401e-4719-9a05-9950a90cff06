import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/extensions.dart';
import '../../providers/enhanced_product_provider.dart';

class BarcodeScannerWidget extends ConsumerStatefulWidget {
  final Function(String barcode)? onBarcodeScanned;
  final bool showProductInfo;
  final bool allowManualEntry;

  const BarcodeScannerWidget({
    super.key,
    this.onBarcodeScanned,
    this.showProductInfo = true,
    this.allowManualEntry = true,
  });

  @override
  ConsumerState<BarcodeScannerWidget> createState() =>
      _BarcodeScannerWidgetState();
}

class _BarcodeScannerWidgetState extends ConsumerState<BarcodeScannerWidget> {
  MobileScannerController? _controller;
  final _manualBarcodeController = TextEditingController();
  bool _isScanning = true;
  String? _lastScannedBarcode;
  bool _flashOn = false;

  @override
  void initState() {
    super.initState();
    _initializeScanner();
  }

  @override
  void dispose() {
    _controller?.dispose();
    _manualBarcodeController.dispose();
    super.dispose();
  }

  void _initializeScanner() {
    _controller = MobileScannerController(
      detectionSpeed: DetectionSpeed.noDuplicates,
      facing: CameraFacing.back,
      torchEnabled: false,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 400.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          // Header with controls
          _buildHeader(),

          // Scanner or manual entry
          Expanded(
            child: _isScanning ? _buildScanner() : _buildManualEntry(),
          ),

          // Footer with last scanned info
          if (_lastScannedBarcode != null) _buildFooter(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: Row(
        children: [
          Icon(
            _isScanning ? Icons.qr_code_scanner : Icons.keyboard,
            color: AppColors.primary,
          ),
          SizedBox(width: 8.w),
          Text(
            _isScanning ? 'مسح الباركود' : 'إدخال يدوي',
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),

          // Flash toggle (only for scanner)
          if (_isScanning) ...[
            IconButton(
              onPressed: _toggleFlash,
              icon: Icon(
                _flashOn ? Icons.flash_on : Icons.flash_off,
                color: _flashOn ? Colors.yellow : Colors.grey,
              ),
              tooltip: _flashOn ? 'إطفاء الفلاش' : 'تشغيل الفلاش',
            ),
            SizedBox(width: 8.w),
          ],

          // Switch between scanner and manual entry
          if (widget.allowManualEntry)
            IconButton(
              onPressed: _toggleInputMethod,
              icon: Icon(
                _isScanning ? Icons.keyboard : Icons.qr_code_scanner,
                color: AppColors.primary,
              ),
              tooltip: _isScanning ? 'إدخال يدوي' : 'مسح الباركود',
            ),
        ],
      ),
    );
  }

  Widget _buildScanner() {
    return ClipRRect(
      borderRadius: BorderRadius.only(
        bottomLeft: Radius.circular(_lastScannedBarcode != null ? 0 : 16.r),
        bottomRight: Radius.circular(_lastScannedBarcode != null ? 0 : 16.r),
      ),
      child: Stack(
        children: [
          MobileScanner(
            controller: _controller,
            onDetect: _onBarcodeDetected,
          ),

          // Scanning overlay
          _buildScanningOverlay(),

          // Instructions
          Positioned(
            bottom: 20.h,
            left: 20.w,
            right: 20.w,
            child: Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                'وجه الكاميرا نحو الباركود',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScanningOverlay() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
      ),
      child: Center(
        child: Container(
          width: 250.w,
          height: 150.h,
          decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.primary,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Stack(
            children: [
              // Corner indicators
              ...List.generate(4, (index) {
                final isTop = index < 2;
                final isLeft = index % 2 == 0;

                return Positioned(
                  top: isTop ? 0 : null,
                  bottom: isTop ? null : 0,
                  left: isLeft ? 0 : null,
                  right: isLeft ? null : 0,
                  child: Container(
                    width: 20.w,
                    height: 20.h,
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(isTop && isLeft ? 12.r : 0),
                        topRight: Radius.circular(isTop && !isLeft ? 12.r : 0),
                        bottomLeft:
                            Radius.circular(!isTop && isLeft ? 12.r : 0),
                        bottomRight:
                            Radius.circular(!isTop && !isLeft ? 12.r : 0),
                      ),
                    ),
                  ),
                );
              }),

              // Scanning line animation
              AnimatedContainer(
                duration: const Duration(seconds: 2),
                curve: Curves.easeInOut,
                child: Container(
                  width: double.infinity,
                  height: 2.h,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.transparent,
                        AppColors.primary,
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildManualEntry() {
    return Padding(
      padding: EdgeInsets.all(16.r),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.qr_code,
            size: 64.r,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16.h),
          Text(
            'أدخل رقم الباركود يدوياً',
            style: AppTextStyles.titleMedium.copyWith(
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 24.h),
          TextField(
            controller: _manualBarcodeController,
            decoration: InputDecoration(
              labelText: 'رقم الباركود',
              hintText: 'أدخل رقم الباركود هنا',
              prefixIcon: const Icon(Icons.qr_code),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            keyboardType: TextInputType.number,
            textInputAction: TextInputAction.done,
            onSubmitted: _onManualBarcodeEntered,
          ),
          SizedBox(height: 16.h),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () =>
                  _onManualBarcodeEntered(_manualBarcodeController.text),
              child: const Text('تأكيد'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(16.r),
          bottomRight: Radius.circular(16.r),
        ),
        border: Border(
          top: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 20.r,
              ),
              SizedBox(width: 8.w),
              Text(
                'تم مسح الباركود',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),

          Container(
            padding: EdgeInsets.all(12.r),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.qr_code,
                  color: Colors.grey[600],
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    _lastScannedBarcode!,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () {
                    Clipboard.setData(
                        ClipboardData(text: _lastScannedBarcode!));
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('تم نسخ الباركود')),
                    );
                  },
                  icon: const Icon(Icons.copy, size: 16),
                  tooltip: 'نسخ',
                ),
              ],
            ),
          ),

          // Product info if enabled
          if (widget.showProductInfo) _buildProductInfo(),
        ],
      ),
    );
  }

  Widget _buildProductInfo() {
    return FutureBuilder(
      future: _findProductByBarcode(_lastScannedBarcode!),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Padding(
            padding: EdgeInsets.only(top: 8.h),
            child: const LinearProgressIndicator(),
          );
        }

        if (snapshot.hasData && snapshot.data != null) {
          final product = snapshot.data!;
          return Container(
            margin: EdgeInsets.only(top: 8.h),
            padding: EdgeInsets.all(12.r),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
              border:
                  Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'معلومات المنتج',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  product.getDisplayName(),
                  style: AppTextStyles.bodyMedium,
                ),
                if (product.categoryName != null)
                  Text(
                    'الفئة: ${product.categoryName}',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                Text(
                  'السعر: ${product.sellingPrice.toStringAsFixed(2)} ر.س',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          );
        }

        return Container(
          margin: EdgeInsets.only(top: 8.h),
          padding: EdgeInsets.all(12.r),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              Icon(
                Icons.warning_amber,
                color: Colors.orange,
                size: 16.r,
              ),
              SizedBox(width: 8.w),
              Text(
                'لم يتم العثور على منتج بهذا الباركود',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.orange[700],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _onBarcodeDetected(BarcodeCapture capture) {
    final barcode = capture.barcodes.first.rawValue;
    if (barcode != null && barcode != _lastScannedBarcode) {
      setState(() {
        _lastScannedBarcode = barcode;
      });

      widget.onBarcodeScanned?.call(barcode);

      // Vibrate and play sound
      _provideFeedback();
    }
  }

  void _onManualBarcodeEntered(String barcode) {
    if (barcode.trim().isNotEmpty) {
      setState(() {
        _lastScannedBarcode = barcode.trim();
      });

      widget.onBarcodeScanned?.call(barcode.trim());
      _manualBarcodeController.clear();

      // Switch back to scanner
      setState(() {
        _isScanning = true;
      });
    }
  }

  void _toggleInputMethod() {
    setState(() {
      _isScanning = !_isScanning;
    });
  }

  void _toggleFlash() {
    setState(() {
      _flashOn = !_flashOn;
    });
    _controller?.toggleTorch();
  }

  void _provideFeedback() {
    // TODO: Add haptic feedback and sound
    // HapticFeedback.lightImpact();
    // SystemSound.play(SystemSoundType.click);
  }

  Future<dynamic> _findProductByBarcode(String barcode) async {
    try {
      final repository = ref.read(enhancedProductRepositoryProvider);
      return await repository.getProductByBarcode(barcode);
    } catch (e) {
      return null;
    }
  }
}
