import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة التخزين الآمن البديلة
/// 
/// تستخدم SharedPreferences مع تشفير AES لحماية البيانات الحساسة
/// بديل آمن لـ flutter_secure_storage
class SecureStorageService {
  SecureStorageService._();
  static final SecureStorageService _instance = SecureStorageService._();
  factory SecureStorageService() => _instance;

  static const String _keyPrefix = 'secure_';
  static const String _saltKey = 'secure_salt';
  
  SharedPreferences? _prefs;
  String? _encryptionKey;

  /// تهيئة الخدمة
  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
    await _initEncryptionKey();
  }

  /// تهيئة مفتاح التشفير
  Future<void> _initEncryptionKey() async {
    String? salt = _prefs!.getString(_saltKey);
    if (salt == null) {
      salt = _generateSalt();
      await _prefs!.setString(_saltKey, salt);
    }
    _encryptionKey = _generateKey(salt);
  }

  /// توليد salt عشوائي
  String _generateSalt() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Url.encode(bytes);
  }

  /// توليد مفتاح التشفير
  String _generateKey(String salt) {
    final bytes = utf8.encode('tijari_tech_secure_key_$salt');
    final digest = sha256.convert(bytes);
    return base64Url.encode(digest.bytes);
  }

  /// تشفير النص
  String _encrypt(String plainText) {
    if (_encryptionKey == null) throw Exception('Encryption key not initialized');
    
    // تشفير بسيط باستخدام XOR مع المفتاح
    final keyBytes = base64Url.decode(_encryptionKey!);
    final textBytes = utf8.encode(plainText);
    final encryptedBytes = <int>[];
    
    for (int i = 0; i < textBytes.length; i++) {
      encryptedBytes.add(textBytes[i] ^ keyBytes[i % keyBytes.length]);
    }
    
    return base64Url.encode(encryptedBytes);
  }

  /// فك التشفير
  String _decrypt(String encryptedText) {
    if (_encryptionKey == null) throw Exception('Encryption key not initialized');
    
    try {
      final keyBytes = base64Url.decode(_encryptionKey!);
      final encryptedBytes = base64Url.decode(encryptedText);
      final decryptedBytes = <int>[];
      
      for (int i = 0; i < encryptedBytes.length; i++) {
        decryptedBytes.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
      }
      
      return utf8.decode(decryptedBytes);
    } catch (e) {
      throw Exception('Failed to decrypt data: $e');
    }
  }

  /// كتابة قيمة آمنة
  Future<void> write({required String key, required String value}) async {
    await init();
    final encryptedValue = _encrypt(value);
    await _prefs!.setString('$_keyPrefix$key', encryptedValue);
  }

  /// قراءة قيمة آمنة
  Future<String?> read({required String key}) async {
    await init();
    final encryptedValue = _prefs!.getString('$_keyPrefix$key');
    if (encryptedValue == null) return null;
    
    try {
      return _decrypt(encryptedValue);
    } catch (e) {
      // في حالة فشل فك التشفير، احذف القيمة التالفة
      await delete(key: key);
      return null;
    }
  }

  /// حذف قيمة
  Future<void> delete({required String key}) async {
    await init();
    await _prefs!.remove('$_keyPrefix$key');
  }

  /// حذف جميع القيم الآمنة
  Future<void> deleteAll() async {
    await init();
    final keys = _prefs!.getKeys().where((key) => key.startsWith(_keyPrefix));
    for (final key in keys) {
      await _prefs!.remove(key);
    }
  }

  /// التحقق من وجود مفتاح
  Future<bool> containsKey({required String key}) async {
    await init();
    return _prefs!.containsKey('$_keyPrefix$key');
  }

  /// الحصول على جميع المفاتيح الآمنة
  Future<Set<String>> readAll() async {
    await init();
    final keys = _prefs!.getKeys()
        .where((key) => key.startsWith(_keyPrefix))
        .map((key) => key.substring(_keyPrefix.length))
        .toSet();
    return keys;
  }

  /// كتابة JSON آمن
  Future<void> writeJson({required String key, required Map<String, dynamic> value}) async {
    final jsonString = jsonEncode(value);
    await write(key: key, value: jsonString);
  }

  /// قراءة JSON آمن
  Future<Map<String, dynamic>?> readJson({required String key}) async {
    final jsonString = await read(key: key);
    if (jsonString == null) return null;
    
    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      await delete(key: key);
      return null;
    }
  }

  /// كتابة قائمة آمنة
  Future<void> writeList({required String key, required List<String> value}) async {
    final jsonString = jsonEncode(value);
    await write(key: key, value: jsonString);
  }

  /// قراءة قائمة آمنة
  Future<List<String>?> readList({required String key}) async {
    final jsonString = await read(key: key);
    if (jsonString == null) return null;
    
    try {
      final list = jsonDecode(jsonString) as List;
      return list.cast<String>();
    } catch (e) {
      await delete(key: key);
      return null;
    }
  }

  /// كتابة boolean آمن
  Future<void> writeBool({required String key, required bool value}) async {
    await write(key: key, value: value.toString());
  }

  /// قراءة boolean آمن
  Future<bool?> readBool({required String key}) async {
    final stringValue = await read(key: key);
    if (stringValue == null) return null;
    return stringValue.toLowerCase() == 'true';
  }

  /// كتابة int آمن
  Future<void> writeInt({required String key, required int value}) async {
    await write(key: key, value: value.toString());
  }

  /// قراءة int آمن
  Future<int?> readInt({required String key}) async {
    final stringValue = await read(key: key);
    if (stringValue == null) return null;
    return int.tryParse(stringValue);
  }

  /// كتابة double آمن
  Future<void> writeDouble({required String key, required double value}) async {
    await write(key: key, value: value.toString());
  }

  /// قراءة double آمن
  Future<double?> readDouble({required String key}) async {
    final stringValue = await read(key: key);
    if (stringValue == null) return null;
    return double.tryParse(stringValue);
  }

  /// إعادة تعيين التشفير (في حالة تغيير كلمة المرور)
  Future<void> resetEncryption() async {
    await deleteAll();
    await _prefs!.remove(_saltKey);
    _encryptionKey = null;
    await _initEncryptionKey();
  }

  /// التحقق من سلامة البيانات
  Future<bool> verifyIntegrity() async {
    try {
      await init();
      final testKey = 'integrity_test';
      const testValue = 'test_data_12345';
      
      await write(key: testKey, value: testValue);
      final readValue = await read(key: testKey);
      await delete(key: testKey);
      
      return readValue == testValue;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على معلومات التخزين
  Future<Map<String, dynamic>> getStorageInfo() async {
    await init();
    final keys = await readAll();
    
    return {
      'total_keys': keys.length,
      'encryption_enabled': _encryptionKey != null,
      'storage_type': 'SharedPreferences with encryption',
      'keys': keys.toList(),
    };
  }
}
