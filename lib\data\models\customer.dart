// lib/data/models/customer.dart
import 'package:json_annotation/json_annotation.dart';
import '../../core/constants/database_constants.dart';

part 'customer.g.dart';

/// حالة رصيد العميل
enum CustomerBalanceStatus {
  debt, // العميل مدين (له رصيد)
  credit, // العميل دائن (عليه رصيد)
  balanced, // متوازن
}

extension CustomerBalanceStatusExt on CustomerBalanceStatus {
  String get displayName {
    switch (this) {
      case CustomerBalanceStatus.debt:
        return 'مدين';
      case CustomerBalanceStatus.credit:
        return 'دائن';
      case CustomerBalanceStatus.balanced:
        return 'متوازن';
    }
  }
}

/// نموذج العميل الكامل
@JsonSerializable()
class Customer {
  final String id;
  final String name;
  final String? phone;
  final String? email;
  final String? address; // تم تصحيح typo من addres إلى address
  final double balance;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final bool isSynced;

  const Customer({
    required this.id,
    required this.name,
    this.phone,
    this.email,
    this.address,
    this.balance = 0.0,
    this.notes,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  factory Customer.fromMap(Map<String, dynamic> map) => Customer(
        id: map[DatabaseConstants.columnCustomerId] as String,
        name: map[DatabaseConstants.columnCustomerName] as String,
        phone: map[DatabaseConstants.columnCustomerPhone] as String?,
        email: map[DatabaseConstants.columnCustomerEmail] as String?,
        address: map[DatabaseConstants.columnCustomerAddress] as String?,
        balance: (map[DatabaseConstants.columnCustomerBalance] as num?)
                ?.toDouble() ??
            0.0,
        notes: map[DatabaseConstants.columnCustomerNotes] as String?,
        createdAt: map[DatabaseConstants.columnCustomerCreatedAt] != null
            ? DateTime.parse(
                map[DatabaseConstants.columnCustomerCreatedAt] as String)
            : null,
        updatedAt: map[DatabaseConstants.columnCustomerUpdatedAt] != null
            ? DateTime.parse(
                map[DatabaseConstants.columnCustomerUpdatedAt] as String)
            : null,
        deletedAt: map[DatabaseConstants.columnCustomerDeletedAt] != null
            ? DateTime.parse(
                map[DatabaseConstants.columnCustomerDeletedAt] as String)
            : null,
        isSynced:
            (map[DatabaseConstants.columnCustomerIsSynced] as int? ?? 0) == 1,
      );

  Map<String, dynamic> toMap() => {
        DatabaseConstants.columnCustomerId: id,
        DatabaseConstants.columnCustomerName: name,
        DatabaseConstants.columnCustomerPhone: phone,
        DatabaseConstants.columnCustomerEmail: email,
        DatabaseConstants.columnCustomerAddress: address,
        DatabaseConstants.columnCustomerBalance: balance,
        DatabaseConstants.columnCustomerNotes: notes,
        DatabaseConstants.columnCustomerCreatedAt: createdAt?.toIso8601String(),
        DatabaseConstants.columnCustomerUpdatedAt: updatedAt?.toIso8601String(),
        DatabaseConstants.columnCustomerDeletedAt: deletedAt?.toIso8601String(),
        DatabaseConstants.columnCustomerIsSynced: isSynced ? 1 : 0,
      };

  factory Customer.fromJson(Map<String, dynamic> json) =>
      _$CustomerFromJson(json);
  Map<String, dynamic> toJson() => _$CustomerToJson(this);

  Customer copyWith({
    String? id,
    String? name,
    String? phone,
    String? email,
    String? address,
    double? balance,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) =>
      Customer(
        id: id ?? this.id,
        name: name ?? this.name,
        phone: phone ?? this.phone,
        email: email ?? this.email,
        address: address ?? this.address,
        balance: balance ?? this.balance,
        notes: notes ?? this.notes,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        isSynced: isSynced ?? this.isSynced,
      );

  // Helper methods
  bool get isDeleted => deletedAt != null;
  bool get isActive => !isDeleted;
  bool get hasPhone => phone != null && phone!.isNotEmpty;
  bool get hasEmail => email != null && email!.isNotEmpty;
  bool get hasAddress => address != null && address!.isNotEmpty;
  bool get hasNotes => notes != null && notes!.isNotEmpty;
  bool get hasDebt => balance > 0;
  bool get hasCredit => balance < 0;
  bool get isBalanced => balance == 0;
  CustomerBalanceStatus get balanceStatus {
    if (balance > 0) return CustomerBalanceStatus.debt;
    if (balance < 0) return CustomerBalanceStatus.credit;
    return CustomerBalanceStatus.balanced;
  }

  double get absoluteBalance => balance.abs();

  static Customer create({
    required String id,
    required String name,
    String? phone,
    String? email,
    String? address,
    double balance = 0.0,
    String? notes,
  }) {
    final now = DateTime.now();
    return Customer(
      id: id,
      name: name,
      phone: phone,
      email: email,
      address: address,
      balance: balance,
      notes: notes,
      createdAt: now,
      updatedAt: now,
      isSynced: false,
    );
  }

  Customer update({
    String? name,
    String? phone,
    String? email,
    String? address,
    double? balance,
    String? notes,
  }) =>
      copyWith(
        name: name ?? this.name,
        phone: phone ?? this.phone,
        email: email ?? this.email,
        address: address ?? this.address,
        balance: balance ?? this.balance,
        notes: notes ?? this.notes,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Customer addToBalance(double amount) => copyWith(
        balance: balance + amount,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Customer subtractFromBalance(double amount) => copyWith(
        balance: balance - amount,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Customer clearBalance() => copyWith(
        balance: 0.0,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Customer markAsDeleted() => copyWith(
        deletedAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Customer markAsSynced() => copyWith(isSynced: true);

  Customer removePhone() => copyWith(
        phone: null,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Customer removeEmail() => copyWith(
        email: null,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Customer removeAddress() => copyWith(
        address: null,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Customer removeNotes() => copyWith(
        notes: null,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is Customer && other.id == id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() =>
      'Customer(id: $id, name: $name, balance: $balance, isSynced: $isSynced)';
}
