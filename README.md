# تجاري تك - Tijari Tech

نظام إدارة شامل للأعمال التجارية مبني بتقنية Flutter

## 📱 نظرة عامة

تجاري تك هو نظام إدارة شامل للأعمال التجارية يوفر حلولاً متكاملة لإدارة المبيعات والمشتريات والمخزون والعملاء والموردين والتقارير المالية. النظام مصمم ليكون سهل الاستخدام ومتجاوب مع جميع أحجام الشاشات.

## ✨ الميزات الرئيسية

### 🏪 نقطة البيع (POS)
- واجهة سهلة وسريعة للبيع
- دعم الباركود والبحث السريع
- حساب الضرائب والخصومات تلقائياً
- طباعة الفواتير

### 📊 لوحة التحكم
- إحصائيات شاملة ومرئية
- رسوم بيانية تفاعلية
- مؤشرات الأداء الرئيسية
- إشعارات ذكية

### 📦 إدارة المخزون
- تتبع المخزون في الوقت الفعلي
- تنبيهات المخزون المنخفض
- إدارة الفئات والوحدات
- تقارير حركة المخزون

### 💰 إدارة المبيعات والمشتريات
- فواتير مبيعات ومشتريات
- إدارة المرتجعات
- تتبع المدفوعات
- تقارير مالية مفصلة

### 👥 إدارة العملاء والموردين
- قاعدة بيانات شاملة
- تتبع المعاملات
- كشوف الحسابات
- إدارة الديون

### 📈 التقارير والتحليلات
- تقارير مالية شاملة
- تحليل المبيعات
- تقارير المخزون
- تصدير البيانات

## 🛠️ التقنيات المستخدمة

- **Flutter** - إطار العمل الرئيسي
- **Dart** - لغة البرمجة
- **Riverpod** - إدارة الحالة
- **SQLite** - قاعدة البيانات المحلية
- **Go Router** - التنقل
- **FL Chart** - الرسوم البيانية
- **Screen Util** - التصميم المتجاوب

## 📁 هيكل المشروع

```
lib/
├── core/                    # الملفات الأساسية
│   ├── constants/          # الثوابت
│   ├── database/           # قاعدة البيانات
│   ├── services/           # الخدمات
│   ├── theme/              # التصميم والألوان
│   ├── utils/              # المساعدات
│   └── validation/         # التحقق من البيانات
├── data/                   # طبقة البيانات
│   ├── models/             # نماذج البيانات
│   └── repositories/       # مستودعات البيانات
├── features/               # الميزات الرئيسية
│   ├── dashboard/          # لوحة التحكم
│   ├── pos/                # نقطة البيع
│   ├── products/           # المنتجات
│   ├── inventory/          # المخزون
│   ├── sales/              # المبيعات
│   ├── purchases/          # المشتريات
│   └── reports/            # التقارير
├── providers/              # مزودي الحالة
├── router/                 # التوجيه
├── widgets/                # الويدجت المشتركة
└── main.dart              # نقطة البداية
```

## 🚀 البدء

### المتطلبات
- Flutter SDK (3.0.0 أو أحدث)
- Dart SDK (3.0.0 أو أحدث)
- Android Studio أو VS Code
- Git

### التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/tijari_tech.git
cd tijari_tech
```

2. **تثبيت التبعيات**
```bash
flutter pub get
```

3. **تشغيل التطبيق**
```bash
flutter run
```

### إعداد قاعدة البيانات

قاعدة البيانات ستُنشأ تلقائياً عند أول تشغيل للتطبيق. يمكنك إضافة بيانات تجريبية من خلال:

```bash
flutter run --dart-define=SEED_DATA=true
```

## 🧪 الاختبارات

### تشغيل جميع الاختبارات
```bash
flutter test
```

### تشغيل اختبارات محددة
```bash
flutter test test/core/utils/app_utils_test.dart
```

### تقرير التغطية
```bash
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

## 📱 الشاشات المدعومة

- **الهواتف الذكية** (320px - 767px)
- **الأجهزة اللوحية** (768px - 1023px)
- **أجهزة سطح المكتب** (1024px+)

## 🎨 التصميم

النظام يدعم:
- **الوضع الليلي والنهاري**
- **اللغة العربية** (RTL)
- **تصميم متجاوب**
- **ألوان قابلة للتخصيص**

## 🔧 التكوين

### إعدادات التطبيق

يمكن تخصيص التطبيق من خلال ملف `lib/core/constants/app_constants.dart`:

```dart
class AppConstants {
  static const String appName = 'تجاري تك';
  static const String appVersion = '1.0.0';
  static const double taxRate = 0.15; // 15% ضريبة القيمة المضافة
  static const String currency = 'ر.س';
}
```

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى قراءة دليل المساهمة قبل البدء.

### خطوات المساهمة

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## 📞 الدعم

- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: https://tijaritech.com

---

**تم تطوير هذا المشروع بـ ❤️ للمجتمع العربي**
