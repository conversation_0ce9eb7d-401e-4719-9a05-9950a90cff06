import 'package:tijari_tech/core/constants/database_constants.dart';
import 'package:tijari_tech/core/exceptions/app_exceptions.dart';
import 'package:tijari_tech/core/utils/app_utils.dart';
import 'package:tijari_tech/data/local/dao/unit_dao.dart';

import '../models/unit.dart';

class UnitRepository {
  final UnitDao _unitDao;

  UnitRepository({UnitDao? unitDao}) : _unitDao = unitDao ?? UnitDao();

  // ------------------------------------------------------------------
  // CRUD Operations
  // ------------------------------------------------------------------

  /// إنشاء وحدة قياس جديدة
  Future<String> createUnit(Unit unit) async {
    try {
      await _validateUnit(unit);
      return await _unitDao.insert(unit);
    } catch (e) {
      AppUtils.logError('Error creating unit', e);
      rethrow;
    }
  }

  /// استرجاع جميع وحدات القياس
  Future<List<Unit>> getAllUnits({
    String? orderBy,
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    try {
      return await _unitDao.findAll(
        orderBy: orderBy ?? DatabaseConstants.columnUnitName,
        ascending: ascending,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      AppUtils.logError('Error getting all units', e);
      throw RepositoryException('Failed to get units');
    }
  }

  /// استرجاع وحدة قياس حسب ID
  Future<Unit?> getUnitById(String id) async {
    try {
      return await _unitDao.findById(id);
    } catch (e) {
      AppUtils.logError('Error getting unit by ID', e);
      return null;
    }
  }

  /// تحديث وحدة قياس
  Future<bool> updateUnit(String id, Unit unit) async {
    try {
      await _validateUnit(unit);
      return await _unitDao.update(id, unit);
    } catch (e) {
      AppUtils.logError('Error updating unit', e);
      return false;
    }
  }

  /// حذف وحدة قياس (soft delete)
  Future<bool> deleteUnit(String id) async {
    try {
      // التحقق من عدم استخدام الوحدة في منتجات
      final isUsed = await _isUnitUsedInProducts(id);
      if (isUsed) {
        throw BusinessLogicException(
            'Cannot delete unit: it is used in products');
      }

      return await _unitDao.delete(id);
    } catch (e) {
      AppUtils.logError('Error deleting unit', e);
      return false;
    }
  }

  // ------------------------------------------------------------------
  // Enhanced Operations
  // ------------------------------------------------------------------

  /// استرجاع وحدة قياس بالتفاصيل الكاملة
  Future<Unit?> getUnitWithDetails(String id) async {
    try {
      final unit = await _unitDao.findById(id);
      if (unit == null) return null;

      // إضافة الوحدات المشتقة إذا كانت وحدة أساسية
      List<Unit>? derivedUnits;
      if (unit.isBaseUnitType) {
        derivedUnits = await _unitDao.findDerivedUnits(id);
      }

      // إضافة الوحدة الأساسية إذا كانت وحدة مشتقة
      Unit? baseUnit;
      if (unit.hasBaseUnit) {
        baseUnit = await _unitDao.findById(unit.baseUnitId!);
      }

      // إضافة عدد الاستخدامات
      final usageCount = await _getUsageCount(id);

      return unit.copyWith(
        derivedUnits: derivedUnits,
        baseUnit: baseUnit,
        usageCount: usageCount,
      );
    } catch (e) {
      AppUtils.logError('Error getting unit with details', e);
      throw RepositoryException('Failed to get unit with details');
    }
  }

  /// استرجاع شجرة الوحدات (الوحدات الأساسية مع مشتقاتها)
  Future<List<Unit>> getUnitTree() async {
    try {
      return await _unitDao.getUnitTree();
    } catch (e) {
      AppUtils.logError('Error getting unit tree', e);
      throw RepositoryException('Failed to get unit tree');
    }
  }

  /// استرجاع الوحدات حسب النوع مع شكل الشجرة
  Future<List<Unit>> getUnitsByType(String unitType) async {
    try {
      return await _unitDao.getUnitsByType(unitType);
    } catch (e) {
      AppUtils.logError('Error getting units by type', e);
      throw RepositoryException('Failed to get units by type');
    }
  }

  /// إنشاء وحدة مشتقة من وحدة أساسية
  Future<String> createDerivedUnit({
    required String name,
    String? symbol,
    String? abbreviation,
    required String baseUnitId,
    required double factor,
    String? description,
  }) async {
    try {
      final baseUnit = await _unitDao.findById(baseUnitId);
      if (baseUnit == null) {
        throw ValidationException('Base unit not found');
      }

      final derivedUnit = Unit.create(
        id: AppUtils.generateId(),
        name: name,
        symbol: symbol,
        abbreviation: abbreviation,
        factor: factor,
        baseUnitId: baseUnitId,
        unitType: baseUnit.unitType,
        isBaseUnit: false,
        description: description,
      );

      return await createUnit(derivedUnit);
    } catch (e) {
      AppUtils.logError('Error creating derived unit', e);
      rethrow;
    }
  }

  /// إنشاء وحدة أساسية مع وحدات مشتقة
  Future<String> createBaseUnitWithDerivedUnits({
    required String baseName,
    String? baseSymbol,
    String? baseAbbreviation,
    required String unitType,
    required List<Map<String, dynamic>> derivedUnitsData,
    String? description,
  }) async {
    try {
      // إنشاء الوحدة الأساسية
      final baseUnit = Unit.create(
        id: AppUtils.generateId(),
        name: baseName,
        symbol: baseSymbol,
        abbreviation: baseAbbreviation,
        factor: 1.0,
        unitType: unitType,
        isBaseUnit: true,
        description: description,
      );

      final baseUnitId = await createUnit(baseUnit);

      // إنشاء الوحدات المشتقة
      for (final derivedData in derivedUnitsData) {
        await createDerivedUnit(
          name: derivedData['name'],
          symbol: derivedData['symbol'],
          abbreviation: derivedData['abbreviation'],
          baseUnitId: baseUnitId,
          factor: derivedData['factor'],
          description: derivedData['description'],
        );
      }

      return baseUnitId;
    } catch (e) {
      AppUtils.logError('Error creating base unit with derived units', e);
      rethrow;
    }
  }

  /// تحديث شجرة الوحدات (تحديث الوحدة الأساسية والوحدات المشتقة)
  Future<bool> updateUnitTree({
    required String baseUnitId,
    String? baseName,
    String? baseSymbol,
    String? baseAbbreviation,
    String? description,
    List<Map<String, dynamic>>? derivedUnitsData,
  }) async {
    try {
      // تحديث الوحدة الأساسية
      final baseUnit = await getUnitWithDetails(baseUnitId);
      if (baseUnit == null) {
        throw ValidationException('Base unit not found');
      }

      final updatedBaseUnit = baseUnit.update(
        name: baseName,
        symbol: baseSymbol,
        abbreviation: baseAbbreviation,
        description: description,
      );

      await updateUnit(baseUnitId, updatedBaseUnit);

      // تحديث الوحدات المشتقة إذا تم توفير البيانات
      if (derivedUnitsData != null) {
        // الحصول على الوحدات المشتقة الحالية
        final currentDerivedUnits = baseUnit.derivedUnits ?? [];

        // تحديث الوحدات المشتقة الموجودة
        for (final derivedData in derivedUnitsData) {
          if (derivedData['id'] != null) {
            // تحديث وحدة مشتقة موجودة
            final derivedUnit = currentDerivedUnits.firstWhere(
              (u) => u.id == derivedData['id'],
              orElse: () => throw ValidationException('Derived unit not found'),
            );

            final updatedDerivedUnit = derivedUnit.update(
              name: derivedData['name'],
              symbol: derivedData['symbol'],
              abbreviation: derivedData['abbreviation'],
              factor: derivedData['factor'],
              description: derivedData['description'],
            );

            await updateUnit(derivedUnit.id, updatedDerivedUnit);
          } else {
            // إنشاء وحدة مشتقة جديدة
            await createDerivedUnit(
              name: derivedData['name'],
              symbol: derivedData['symbol'],
              abbreviation: derivedData['abbreviation'],
              baseUnitId: baseUnitId,
              factor: derivedData['factor'],
              description: derivedData['description'],
            );
          }
        }

        // حذف الوحدات المشتقة غير الموجودة في القائمة الجديدة
        final newDerivedIds = derivedUnitsData
            .where((d) => d['id'] != null)
            .map((d) => d['id'] as String)
            .toSet();

        for (final derivedUnit in currentDerivedUnits) {
          if (!newDerivedIds.contains(derivedUnit.id)) {
            await deleteUnit(derivedUnit.id);
          }
        }
      }

      return true;
    } catch (e) {
      AppUtils.logError('Error updating unit tree', e);
      rethrow;
    }
  }

  /// تحويل كمية من وحدة إلى أخرى
  Future<double?> convertQuantity({
    required double quantity,
    required String fromUnitId,
    required String toUnitId,
  }) async {
    try {
      final fromUnit = await _unitDao.findById(fromUnitId);
      final toUnit = await _unitDao.findById(toUnitId);

      if (fromUnit == null || toUnit == null) {
        throw ValidationException('Unit not found');
      }

      if (!fromUnit.canConvertTo(toUnit)) {
        throw ValidationException(
            'Cannot convert between different unit types');
      }

      return fromUnit.convertTo(quantity, toUnit);
    } catch (e) {
      AppUtils.logError('Error converting quantity', e);
      rethrow;
    }
  }

  /// استرجاع الوحدات الأساسية
  Future<List<Unit>> getBaseUnits() async {
    try {
      return await _unitDao.getBaseUnits();
    } catch (e) {
      AppUtils.logError('Error getting base units', e);
      throw RepositoryException('Failed to get base units');
    }
  }

  /// استرجاع أنواع الوحدات المتاحة
  Future<List<String>> getUnitTypes() async {
    try {
      return await _unitDao.getUnitTypes();
    } catch (e) {
      AppUtils.logError('Error getting unit types', e);
      throw RepositoryException('Failed to get unit types');
    }
  }

  /// استرجاع إحصائيات الوحدات
  Future<Map<String, dynamic>> getUnitsStatistics() async {
    try {
      return await _unitDao.getUnitsStatistics();
    } catch (e) {
      AppUtils.logError('Error getting units statistics', e);
      throw RepositoryException('Failed to get units statistics');
    }
  }

  // ------------------------------------------------------------------
  // Helper Methods
  // ------------------------------------------------------------------

  /// التحقق من استخدام الوحدة في منتجات
  Future<bool> _isUnitUsedInProducts(String unitId) async {
    try {
      final sql = '''
        SELECT COUNT(*) as count
        FROM ${DatabaseConstants.tableProducts}
        WHERE ${DatabaseConstants.columnProductBaseUnitId} = ? 
          AND ${DatabaseConstants.columnProductDeletedAt} IS NULL
      ''';

      final result = await _unitDao.rawQuery(sql, [unitId]);
      return result.isNotEmpty && (result.first['count'] as int) > 0;
    } catch (e) {
      AppUtils.logError('Error checking unit usage', e);
      return false;
    }
  }

  /// استرجاع عدد استخدامات الوحدة
  Future<int> _getUsageCount(String unitId) async {
    try {
      final sql = '''
        SELECT COUNT(*) as count
        FROM ${DatabaseConstants.tableProducts}
        WHERE ${DatabaseConstants.columnProductBaseUnitId} = ?
          AND ${DatabaseConstants.columnProductDeletedAt} IS NULL
      ''';

      final result = await _unitDao.rawQuery(sql, [unitId]);
      return result.isNotEmpty ? (result.first['count'] as int) : 0;
    } catch (e) {
      AppUtils.logError('Error getting usage count', e);
      return 0;
    }
  }

  // ------------------------------------------------------------------
  // Validation
  // ------------------------------------------------------------------

  /// التحقق من صحة بيانات وحدة القياس
  Future<void> _validateUnit(Unit unit) async {
    if (unit.name.trim().isEmpty) {
      throw ValidationException('Unit name is required');
    }

    // التحقق من صحة معامل التحويل
    if (unit.factor <= 0) {
      throw ValidationException('Unit factor must be greater than 0');
    }

    // التحقق من عدم تكرار الاسم
    final existingUnits = await _unitDao.findWhere(
      where: '''
        ${DatabaseConstants.columnUnitName} = ? AND
        ${DatabaseConstants.columnUnitId} != ? AND
        ${DatabaseConstants.columnUnitDeletedAt} IS NULL
      ''',
      whereArgs: [unit.name, unit.id],
    );

    if (existingUnits.isNotEmpty) {
      throw ValidationException('Unit name already exists');
    }
  }

// في UnitRepository، أضف هذه الطرق الجديدة

  /// استرجاع الوحدات مع معلومات الفروع والتصنيفات
  Future<List<Unit>> getUnitsWithBranchAndCategoryInfo() async {
    try {
      return await _unitDao.getUnitsWithBranchAndCategoryInfo();
    } catch (e) {
      AppUtils.logError('Error getting units with branch and category info', e);
      throw RepositoryException(
          'Failed to get units with branch and category info');
    }
  }

  /// استرجاع إحصائيات الوحدات مع الفروع والتصنيفات
  Future<Map<String, dynamic>> getEnhancedUnitsStatistics() async {
    try {
      return await _unitDao.getEnhancedUnitsStatistics();
    } catch (e) {
      AppUtils.logError('Error getting enhanced units statistics', e);
      throw RepositoryException('Failed to get enhanced units statistics');
    }
  }

  /// إنشاء شجرة وحدات كاملة مع الوحدات الأساسية والمشتقة
  Future<String> createUnitTree({
    required String baseName,
    String? baseNameEn,
    String? baseSymbol,
    String? baseAbbreviation,
    required String unitType,
    required List<Map<String, dynamic>> derivedUnitsData,
    String? description,
  }) async {
    try {
      // إنشاء الوحدة الأساسية
      final baseUnit = Unit.create(
        id: AppUtils.generateId(),
        name: baseName,
        symbol: baseSymbol,
        abbreviation: baseAbbreviation,
        factor: 1.0,
        unitType: unitType,
        isBaseUnit: true,
        description: description,
      );

      final baseUnitId = await createUnit(baseUnit);

      // إنشاء الوحدات المشتقة
      for (final derivedData in derivedUnitsData) {
        final derivedUnit = Unit.create(
          id: AppUtils.generateId(),
          name: derivedData['name'],
          symbol: derivedData['symbol'],
          abbreviation: derivedData['abbreviation'],
          factor: derivedData['factor'],
          baseUnitId: baseUnitId,
          unitType: unitType,
          isBaseUnit: false,
          description: derivedData['description'],
        );

        await createUnit(derivedUnit);
      }

      return baseUnitId;
    } catch (e) {
      AppUtils.logError('Error creating unit tree', e);
      rethrow;
    }
  }
}
