import 'package:json_annotation/json_annotation.dart';
import '../../core/constants/database_constants.dart';

part 'audit_log.g.dart';

/// نموذج سجل المراجعة - Audit Log Model
/// يمثل سجل العمليات والتغييرات في النظام
@JsonSerializable()
class AuditLog {
  final String id;
  final String tableName;      // اسم الجدول المتأثر
  final String recordId;       // معرف السجل المتأثر
  final String action;         // نوع العملية (create, update, delete, view)
  final String? oldValues;     // القيم القديمة (JSON)
  final String? newValues;     // القيم الجديدة (JSON)
  final String? changedFields; // الحقول المتغيرة (مفصولة بفاصلة)
  final String userId;         // معرف المستخدم الذي قام بالعملية
  final String? userAgent;     // معلومات المتصفح/التطبيق
  final String? ipAddress;     // عنوان IP
  final String? sessionId;     // معرف الجلسة
  final String? description;   // وصف العملية
  final String? category;      // فئة العملية (sales, inventory, accounting, etc.)
  final String severity;       // مستوى الأهمية (low, medium, high, critical)
  final bool isSuccessful;     // هل العملية نجحت
  final String? errorMessage;  // رسالة الخطأ (إن وجدت)
  final Map<String, dynamic>? metadata; // بيانات إضافية
  final DateTime timestamp;    // وقت العملية
  final String? branchId;      // معرف الفرع
  final bool isSynced;         // هل تم المزامنة

  const AuditLog({
    required this.id,
    required this.tableName,
    required this.recordId,
    required this.action,
    this.oldValues,
    this.newValues,
    this.changedFields,
    required this.userId,
    this.userAgent,
    this.ipAddress,
    this.sessionId,
    this.description,
    this.category,
    this.severity = 'medium',
    this.isSuccessful = true,
    this.errorMessage,
    this.metadata,
    required this.timestamp,
    this.branchId,
    this.isSynced = false,
  });

  /// إنشاء سجل مراجعة من JSON
  factory AuditLog.fromJson(Map<String, dynamic> json) =>
      _$AuditLogFromJson(json);

  /// تحويل سجل المراجعة إلى JSON
  Map<String, dynamic> toJson() => _$AuditLogToJson(this);

  /// إنشاء سجل مراجعة من Map قاعدة البيانات
  factory AuditLog.fromMap(Map<String, dynamic> map) {
    return AuditLog(
      id: map[DatabaseConstants.columnAuditLogId] as String,
      tableName: map[DatabaseConstants.columnAuditLogTableName] as String,
      recordId: map[DatabaseConstants.columnAuditLogRecordId] as String,
      action: map[DatabaseConstants.columnAuditLogAction] as String,
      oldValues: map[DatabaseConstants.columnAuditLogOldValues] as String?,
      newValues: map[DatabaseConstants.columnAuditLogNewValues] as String?,
      changedFields: map[DatabaseConstants.columnAuditLogChangedFields] as String?,
      userId: map[DatabaseConstants.columnAuditLogUserId] as String,
      userAgent: map[DatabaseConstants.columnAuditLogUserAgent] as String?,
      ipAddress: map[DatabaseConstants.columnAuditLogIpAddress] as String?,
      sessionId: map[DatabaseConstants.columnAuditLogSessionId] as String?,
      description: map[DatabaseConstants.columnAuditLogDescription] as String?,
      category: map[DatabaseConstants.columnAuditLogCategory] as String?,
      severity: map[DatabaseConstants.columnAuditLogSeverity] as String? ?? 'medium',
      isSuccessful: (map[DatabaseConstants.columnAuditLogIsSuccessful] as int?) == 1,
      errorMessage: map[DatabaseConstants.columnAuditLogErrorMessage] as String?,
      metadata: map[DatabaseConstants.columnAuditLogMetadata] != null
          ? Map<String, dynamic>.from(map[DatabaseConstants.columnAuditLogMetadata] as Map)
          : null,
      timestamp: DateTime.parse(map[DatabaseConstants.columnAuditLogTimestamp] as String),
      branchId: map[DatabaseConstants.columnAuditLogBranchId] as String?,
      isSynced: (map[DatabaseConstants.columnAuditLogIsSynced] as int) == 1,
    );
  }

  /// تحويل سجل المراجعة إلى Map لقاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnAuditLogId: id,
      DatabaseConstants.columnAuditLogTableName: tableName,
      DatabaseConstants.columnAuditLogRecordId: recordId,
      DatabaseConstants.columnAuditLogAction: action,
      DatabaseConstants.columnAuditLogOldValues: oldValues,
      DatabaseConstants.columnAuditLogNewValues: newValues,
      DatabaseConstants.columnAuditLogChangedFields: changedFields,
      DatabaseConstants.columnAuditLogUserId: userId,
      DatabaseConstants.columnAuditLogUserAgent: userAgent,
      DatabaseConstants.columnAuditLogIpAddress: ipAddress,
      DatabaseConstants.columnAuditLogSessionId: sessionId,
      DatabaseConstants.columnAuditLogDescription: description,
      DatabaseConstants.columnAuditLogCategory: category,
      DatabaseConstants.columnAuditLogSeverity: severity,
      DatabaseConstants.columnAuditLogIsSuccessful: isSuccessful ? 1 : 0,
      DatabaseConstants.columnAuditLogErrorMessage: errorMessage,
      DatabaseConstants.columnAuditLogMetadata: metadata,
      DatabaseConstants.columnAuditLogTimestamp: timestamp.toIso8601String(),
      DatabaseConstants.columnAuditLogBranchId: branchId,
      DatabaseConstants.columnAuditLogIsSynced: isSynced ? 1 : 0,
    };
  }

  /// نسخ سجل المراجعة مع تعديل بعض الخصائص
  AuditLog copyWith({
    String? id,
    String? tableName,
    String? recordId,
    String? action,
    String? oldValues,
    String? newValues,
    String? changedFields,
    String? userId,
    String? userAgent,
    String? ipAddress,
    String? sessionId,
    String? description,
    String? category,
    String? severity,
    bool? isSuccessful,
    String? errorMessage,
    Map<String, dynamic>? metadata,
    DateTime? timestamp,
    String? branchId,
    bool? isSynced,
  }) {
    return AuditLog(
      id: id ?? this.id,
      tableName: tableName ?? this.tableName,
      recordId: recordId ?? this.recordId,
      action: action ?? this.action,
      oldValues: oldValues ?? this.oldValues,
      newValues: newValues ?? this.newValues,
      changedFields: changedFields ?? this.changedFields,
      userId: userId ?? this.userId,
      userAgent: userAgent ?? this.userAgent,
      ipAddress: ipAddress ?? this.ipAddress,
      sessionId: sessionId ?? this.sessionId,
      description: description ?? this.description,
      category: category ?? this.category,
      severity: severity ?? this.severity,
      isSuccessful: isSuccessful ?? this.isSuccessful,
      errorMessage: errorMessage ?? this.errorMessage,
      metadata: metadata ?? this.metadata,
      timestamp: timestamp ?? this.timestamp,
      branchId: branchId ?? this.branchId,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  /// إنشاء سجل مراجعة جديد
  factory AuditLog.create({
    required String id,
    required String tableName,
    required String recordId,
    required String action,
    String? oldValues,
    String? newValues,
    String? changedFields,
    required String userId,
    String? userAgent,
    String? ipAddress,
    String? sessionId,
    String? description,
    String? category,
    String severity = 'medium',
    bool isSuccessful = true,
    String? errorMessage,
    Map<String, dynamic>? metadata,
    String? branchId,
  }) {
    return AuditLog(
      id: id,
      tableName: tableName,
      recordId: recordId,
      action: action,
      oldValues: oldValues,
      newValues: newValues,
      changedFields: changedFields,
      userId: userId,
      userAgent: userAgent,
      ipAddress: ipAddress,
      sessionId: sessionId,
      description: description,
      category: category,
      severity: severity,
      isSuccessful: isSuccessful,
      errorMessage: errorMessage,
      metadata: metadata,
      timestamp: DateTime.now(),
      branchId: branchId,
      isSynced: false,
    );
  }

  /// التحقق من صحة بيانات سجل المراجعة
  bool get isValid {
    return id.isNotEmpty &&
        tableName.isNotEmpty &&
        recordId.isNotEmpty &&
        action.isNotEmpty &&
        userId.isNotEmpty;
  }

  /// هل العملية فاشلة؟
  bool get isFailed => !isSuccessful;

  /// هل العملية حرجة؟
  bool get isCritical => severity == 'critical';

  /// هل العملية عالية الأهمية؟
  bool get isHighSeverity => severity == 'high' || severity == 'critical';

  /// الحصول على وصف مختصر للعملية
  String get actionDescription {
    switch (action.toLowerCase()) {
      case 'create':
        return 'إنشاء';
      case 'update':
        return 'تحديث';
      case 'delete':
        return 'حذف';
      case 'view':
        return 'عرض';
      case 'login':
        return 'تسجيل دخول';
      case 'logout':
        return 'تسجيل خروج';
      default:
        return action;
    }
  }

  /// الحصول على لون مستوى الأهمية
  String get severityColor {
    switch (severity.toLowerCase()) {
      case 'low':
        return '#4CAF50'; // أخضر
      case 'medium':
        return '#FF9800'; // برتقالي
      case 'high':
        return '#F44336'; // أحمر
      case 'critical':
        return '#9C27B0'; // بنفسجي
      default:
        return '#757575'; // رمادي
    }
  }

  /// تحويل البيانات الوصفية إلى نص
  String get metadataString {
    if (metadata == null || metadata!.isEmpty) return '';
    return metadata!.entries
        .map((e) => '${e.key}: ${e.value}')
        .join(', ');
  }

  @override
  String toString() {
    return 'AuditLog(id: $id, tableName: $tableName, action: $action, userId: $userId, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuditLog && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
