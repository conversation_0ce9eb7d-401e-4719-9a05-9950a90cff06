# سجل التغييرات - Changelog

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

التنسيق مبني على [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)،
وهذا المشروع يتبع [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [غير منشور] - Unreleased

### مضاف - Added
- ميزة المزامنة مع الخادم
- دعم طباعة الفواتير
- تصدير التقارير إلى PDF
- دعم عدة فروع
- نظام الصلاحيات والمستخدمين

### محسن - Changed
- تحسين أداء قاعدة البيانات
- تحديث واجهة المستخدم
- تحسين التصميم المتجاوب

### مصلح - Fixed
- إصلاح مشاكل التزامن في قاعدة البيانات
- إصلاح مشاكل التنقل
- إصلاح حسابات الضرائب

## [1.0.0] - 2024-01-15

### مضاف - Added

#### 🏗️ البنية الأساسية
- إعداد مشروع Flutter الأساسي
- تكوين Riverpod لإدارة الحالة
- إعداد قاعدة بيانات SQLite
- نظام التوجيه باستخدام Go Router
- دعم التصميم المتجاوب
- دعم اللغة العربية (RTL)

#### 🎨 التصميم والواجهة
- نظام الألوان والتصميم
- مكونات واجهة المستخدم المخصصة
- التخطيط الرئيسي للتطبيق
- شريط التنقل السفلي
- القائمة الجانبية
- نظام الإشعارات

#### 📊 لوحة التحكم
- إحصائيات المبيعات والمشتريات
- رسوم بيانية تفاعلية
- مؤشرات الأداء الرئيسية
- الإجراءات السريعة
- النشاطات الأخيرة
- القائمة السريعة للوظائف

#### 🏪 نقطة البيع (POS)
- واجهة نقطة البيع الأساسية
- عرض المنتجات في شبكة
- سلة التسوق
- حساب الإجماليات والضرائب
- دعم التصميم المتجاوب (موبايل/تابلت/ديسكتوب)

#### 📦 إدارة المنتجات
- قائمة المنتجات مع البحث والفلترة
- إضافة وتعديل المنتجات
- إدارة الفئات والوحدات
- تتبع المخزون
- دعم الباركود

#### 💾 قاعدة البيانات
- نماذج البيانات الأساسية:
  - الفروع (Branches)
  - الفئات (Categories)
  - الوحدات (Units)
  - المنتجات (Products)
  - العملاء (Customers)
  - الموردين (Suppliers)
  - المبيعات (Sales)
  - المشتريات (Purchases)
- مزودي البيانات (Data Providers)
- خدمات قاعدة البيانات

#### 🔧 الخدمات والمساعدات
- خدمة الإشعارات
- مساعدات التطبيق (App Utils)
- نظام التحقق من صحة البيانات
- مكونات النصوص المخصصة
- إدارة الحالة المتقدمة

#### 🧪 الاختبارات
- اختبارات الوحدة للمساعدات
- اختبارات نماذج البيانات
- اختبارات التحقق من صحة البيانات
- اختبارات مزودي الحالة

#### 📱 التصميم المتجاوب
- دعم الهواتف الذكية
- دعم الأجهزة اللوحية
- دعم أجهزة سطح المكتب
- تخطيطات مختلفة حسب حجم الشاشة

#### 🌐 الدعم متعدد اللغات
- دعم اللغة العربية
- تخطيط من اليمين إلى اليسار (RTL)
- خطوط عربية مناسبة
- تنسيق التواريخ والأرقام

### التفاصيل التقنية - Technical Details

#### المكتبات المستخدمة
- `flutter`: ^3.24.5
- `flutter_riverpod`: ^2.6.1
- `go_router`: ^14.6.2
- `sqflite`: ^2.4.1
- `flutter_screenutil`: ^5.9.3
- `fl_chart`: ^0.70.1
- `uuid`: ^4.5.1

#### هيكل المشروع
```
lib/
├── core/                 # الملفات الأساسية
├── data/                 # طبقة البيانات
├── features/             # الميزات الرئيسية
├── providers/            # مزودي الحالة
├── router/               # التوجيه
├── widgets/              # الويدجت المشتركة
└── main.dart            # نقطة البداية
```

#### قاعدة البيانات
- 8 جداول رئيسية
- دعم العلاقات بين الجداول
- فهرسة محسنة للأداء
- دعم المعاملات (Transactions)

#### الأداء
- تحميل البيانات بشكل تدريجي
- تخزين مؤقت ذكي
- تحسين استعلامات قاعدة البيانات
- إدارة ذاكرة محسنة

### المعروف - Known Issues
- بعض الميزات المتقدمة قيد التطوير
- التزامن مع الخادم غير مكتمل
- طباعة الفواتير تحتاج تحسين

### ملاحظات الترقية - Upgrade Notes
- هذا هو الإصدار الأول من التطبيق
- يُنصح بعمل نسخة احتياطية من البيانات قبل أي ترقية مستقبلية
- قاعدة البيانات ستُنشأ تلقائياً عند أول تشغيل

### الخطوات التالية - Next Steps
- إضافة المزيد من التقارير
- تحسين نقطة البيع
- إضافة ميزة المزامنة
- دعم عدة فروع
- تطبيق الويب

---

## تنسيق سجل التغييرات

### الأنواع
- **مضاف** - للميزات الجديدة
- **محسن** - للتغييرات في الميزات الموجودة
- **مهمل** - للميزات التي ستُحذف قريباً
- **محذوف** - للميزات المحذوفة
- **مصلح** - لإصلاح الأخطاء
- **أمان** - للتحديثات الأمنية

### الرموز
- 🏗️ البنية الأساسية
- 🎨 التصميم والواجهة
- 📊 لوحة التحكم
- 🏪 نقطة البيع
- 📦 إدارة المنتجات
- 💾 قاعدة البيانات
- 🔧 الخدمات والمساعدات
- 🧪 الاختبارات
- 📱 التصميم المتجاوب
- 🌐 الدعم متعدد اللغات
- 🐛 إصلاح الأخطاء
- ⚡ تحسين الأداء
- 🔒 الأمان
