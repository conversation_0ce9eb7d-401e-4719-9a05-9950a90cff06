import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/constants/app_constants.dart';

// Theme Mode State Notifier
class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  ThemeModeNotifier() : super(ThemeMode.system) {
    _loadThemeMode();
  }

  // Load theme mode from shared preferences
  Future<void> _loadThemeMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeModeString = prefs.getString(AppConstants.keyThemeMode);

      if (themeModeString != null) {
        switch (themeModeString) {
          case 'light':
            state = ThemeMode.light;
            break;
          case 'dark':
            state = ThemeMode.dark;
            break;
          case 'system':
            state = ThemeMode.system;
            break;
          default:
            state = ThemeMode.system;
            break;
        }
      }
    } catch (e) {
      // If there's an error loading, keep the default system theme
      state = ThemeMode.system;
    }
  }

  // Save theme mode to shared preferences
  Future<void> _saveThemeMode(ThemeMode themeMode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String themeModeString;

      switch (themeMode) {
        case ThemeMode.light:
          themeModeString = 'light';
          break;
        case ThemeMode.dark:
          themeModeString = 'dark';
          break;
        case ThemeMode.system:
          themeModeString = 'system';
          break;
      }

      await prefs.setString(AppConstants.keyThemeMode, themeModeString);
    } catch (e) {
      // Handle error silently
    }
  }

  // Set theme mode
  Future<void> setThemeMode(ThemeMode themeMode) async {
    state = themeMode;
    await _saveThemeMode(themeMode);
  }

  // Toggle between light and dark theme
  Future<void> toggleTheme() async {
    switch (state) {
      case ThemeMode.light:
        await setThemeMode(ThemeMode.dark);
        break;
      case ThemeMode.dark:
        await setThemeMode(ThemeMode.light);
        break;
      case ThemeMode.system:
        // If system theme, switch to light first
        await setThemeMode(ThemeMode.light);
        break;
    }
  }

  // Set light theme
  Future<void> setLightTheme() async {
    await setThemeMode(ThemeMode.light);
  }

  // Set dark theme
  Future<void> setDarkTheme() async {
    await setThemeMode(ThemeMode.dark);
  }

  // Set system theme
  Future<void> setSystemTheme() async {
    await setThemeMode(ThemeMode.system);
  }

  // Check if current theme is light
  bool get isLightTheme => state == ThemeMode.light;

  // Check if current theme is dark
  bool get isDarkTheme => state == ThemeMode.dark;

  // Check if current theme is system
  bool get isSystemTheme => state == ThemeMode.system;

  // Get theme mode as string
  String get themeModeString {
    switch (state) {
      case ThemeMode.light:
        return 'light';
      case ThemeMode.dark:
        return 'dark';
      case ThemeMode.system:
        return 'system';
    }
  }

  // Get localized theme mode name
  String getLocalizedThemeName() {
    switch (state) {
      case ThemeMode.light:
        return 'المظهر الفاتح';
      case ThemeMode.dark:
        return 'المظهر الداكن';
      case ThemeMode.system:
        return 'مظهر النظام';
    }
  }
}

// Theme Mode Provider
final themeModeProvider =
    StateNotifierProvider<ThemeModeNotifier, ThemeMode>((ref) {
  return ThemeModeNotifier();
});

// Helper provider to check if current theme is dark
final isDarkThemeProvider = Provider<bool>((ref) {
  final themeMode = ref.watch(themeModeProvider);

  switch (themeMode) {
    case ThemeMode.dark:
      return true;
    case ThemeMode.light:
      return false;
    case ThemeMode.system:
      // For system theme, we need to check the platform brightness
      // This will be handled by the MaterialApp's theme selection
      return false;
  }
});

// Helper provider to get theme mode as string
final themeModeStringProvider = Provider<String>((ref) {
  final themeModeNotifier = ref.read(themeModeProvider.notifier);
  return themeModeNotifier.themeModeString;
});

// Helper provider to get localized theme name
final localizedThemeNameProvider = Provider<String>((ref) {
  final themeModeNotifier = ref.read(themeModeProvider.notifier);
  return themeModeNotifier.getLocalizedThemeName();
});

// Theme actions provider for easy access to theme methods
final themeActionsProvider = Provider<ThemeActions>((ref) {
  final themeModeNotifier = ref.read(themeModeProvider.notifier);
  return ThemeActions(themeModeNotifier);
});

// Theme actions class to encapsulate theme-related actions
class ThemeActions {
  final ThemeModeNotifier _themeModeNotifier;

  ThemeActions(this._themeModeNotifier);

  Future<void> setLightTheme() => _themeModeNotifier.setLightTheme();
  Future<void> setDarkTheme() => _themeModeNotifier.setDarkTheme();
  Future<void> setSystemTheme() => _themeModeNotifier.setSystemTheme();
  Future<void> toggleTheme() => _themeModeNotifier.toggleTheme();
  Future<void> setThemeMode(ThemeMode themeMode) =>
      _themeModeNotifier.setThemeMode(themeMode);

  bool get isLightTheme => _themeModeNotifier.isLightTheme;
  bool get isDarkTheme => _themeModeNotifier.isDarkTheme;
  bool get isSystemTheme => _themeModeNotifier.isSystemTheme;
  String get themeModeString => _themeModeNotifier.themeModeString;
  String getLocalizedThemeName() => _themeModeNotifier.getLocalizedThemeName();
}
