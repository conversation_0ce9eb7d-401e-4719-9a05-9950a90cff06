import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/stock_alert.dart';
import '../services/stock_alert_service.dart';
import '../core/utils/app_utils.dart';
import '../core/utils/error_handler.dart';
import 'database_provider.dart';

// Stock alert service provider
final stockAlertServiceProvider = Provider<StockAlertService>((ref) {
  return StockAlertService();
});

// Stock alert management provider
final stockAlertManagementProvider = StateNotifierProvider<
    StockAlertManagementNotifier, StockAlertManagementState>((ref) {
  return StockAlertManagementNotifier(ref);
});

// Stock alert management state
class StockAlertManagementState {
  final bool isLoading;
  final String? error;
  final List<StockAlert> alerts;
  final List<StockAlert> filteredAlerts;
  final String searchQuery;
  final String? selectedWarehouse;
  final String? selectedProduct;
  final String? selectedAlertType;
  final String? selectedSeverity;
  final bool? showOnlyUnread;
  final bool? showOnlyActive;
  final DateTime? fromDate;
  final DateTime? toDate;
  final Map<String, dynamic> statistics;
  final String sortField;
  final bool sortAscending;

  const StockAlertManagementState({
    this.isLoading = false,
    this.error,
    this.alerts = const [],
    this.filteredAlerts = const [],
    this.searchQuery = '',
    this.selectedWarehouse,
    this.selectedProduct,
    this.selectedAlertType,
    this.selectedSeverity,
    this.showOnlyUnread,
    this.showOnlyActive,
    this.fromDate,
    this.toDate,
    this.statistics = const {},
    this.sortField = 'created_at',
    this.sortAscending = false,
  });

  StockAlertManagementState copyWith({
    bool? isLoading,
    String? error,
    List<StockAlert>? alerts,
    List<StockAlert>? filteredAlerts,
    String? searchQuery,
    String? selectedWarehouse,
    String? selectedProduct,
    String? selectedAlertType,
    String? selectedSeverity,
    bool? showOnlyUnread,
    bool? showOnlyActive,
    DateTime? fromDate,
    DateTime? toDate,
    Map<String, dynamic>? statistics,
    String? sortField,
    bool? sortAscending,
  }) {
    return StockAlertManagementState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      alerts: alerts ?? this.alerts,
      filteredAlerts: filteredAlerts ?? this.filteredAlerts,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedWarehouse: selectedWarehouse ?? this.selectedWarehouse,
      selectedProduct: selectedProduct ?? this.selectedProduct,
      selectedAlertType: selectedAlertType ?? this.selectedAlertType,
      selectedSeverity: selectedSeverity ?? this.selectedSeverity,
      showOnlyUnread: showOnlyUnread ?? this.showOnlyUnread,
      showOnlyActive: showOnlyActive ?? this.showOnlyActive,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      statistics: statistics ?? this.statistics,
      sortField: sortField ?? this.sortField,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }
}

// Stock alert management notifier
class StockAlertManagementNotifier
    extends StateNotifier<StockAlertManagementState> {
  final Ref _ref;

  StockAlertManagementNotifier(this._ref)
      : super(const StockAlertManagementState());

  StockAlertService get _service => _ref.read(stockAlertServiceProvider);
  DatabaseOperations get _dbOperations => _ref.read(databaseOperationsProvider);

  // Load alerts
  Future<void> loadAlerts() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final alerts = await _dbOperations.executeWithReadyCheck(() async {
        return await _service.getActiveAlerts(
          warehouseId: state.selectedWarehouse,
          productId: state.selectedProduct,
          alertType: state.selectedAlertType,
          severity: state.selectedSeverity,
          limit: 1000, // Limit for performance
        );
      });

      final statistics = await _dbOperations.executeWithReadyCheck(() async {
        return await _service.getAlertStatistics(
          warehouseId: state.selectedWarehouse,
          productId: state.selectedProduct,
        );
      });

      state = state.copyWith(
        isLoading: false,
        alerts: alerts,
        filteredAlerts: _filterAndSortAlerts(alerts),
        statistics: statistics,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error loading alerts', e);
    }
  }

  // Search alerts
  Future<void> search(String query) async {
    state = state.copyWith(searchQuery: query);

    if (query.isEmpty) {
      state = state.copyWith(
        filteredAlerts: _filterAndSortAlerts(state.alerts),
      );
      return;
    }

    try {
      state = state.copyWith(isLoading: true, error: null);

      final alerts = await _dbOperations.executeWithReadyCheck(() async {
        return await _service.getActiveAlerts(
          warehouseId: state.selectedWarehouse,
          alertType: state.selectedAlertType,
          severity: state.selectedSeverity,
          limit: 1000,
        );
      });

      state = state.copyWith(
        isLoading: false,
        filteredAlerts: _filterAndSortAlerts(alerts),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error searching alerts', e);
    }
  }

  // Filter by warehouse
  void filterByWarehouse(String? warehouseId) {
    state = state.copyWith(selectedWarehouse: warehouseId);
    loadAlerts();
  }

  // Filter by product
  void filterByProduct(String? productId) {
    state = state.copyWith(selectedProduct: productId);
    loadAlerts();
  }

  // Filter by alert type
  void filterByAlertType(String? alertType) {
    state = state.copyWith(selectedAlertType: alertType);
    loadAlerts();
  }

  // Filter by severity
  void filterBySeverity(String? severity) {
    state = state.copyWith(selectedSeverity: severity);
    loadAlerts();
  }

  // Filter by read status
  void filterByReadStatus(bool? showOnlyUnread) {
    state = state.copyWith(showOnlyUnread: showOnlyUnread);
    state = state.copyWith(
      filteredAlerts: _filterAndSortAlerts(state.alerts),
    );
  }

  // Filter by active status
  void filterByActiveStatus(bool? showOnlyActive) {
    state = state.copyWith(showOnlyActive: showOnlyActive);
    state = state.copyWith(
      filteredAlerts: _filterAndSortAlerts(state.alerts),
    );
  }

  // Filter by date range
  void filterByDateRange(DateTime? fromDate, DateTime? toDate) {
    state = state.copyWith(fromDate: fromDate, toDate: toDate);
    loadAlerts();
  }

  // Sort alerts
  void sortAlerts(String field, bool ascending) {
    state = state.copyWith(
      sortField: field,
      sortAscending: ascending,
      filteredAlerts: _filterAndSortAlerts(state.alerts),
    );
  }

  // Mark alert as read
  Future<bool> markAlertAsRead(String alertId) async {
    try {
      await _dbOperations.executeWithReadyCheck(() async {
        await _service.markAlertAsRead(alertId);
      });

      await loadAlerts();
      return true;
    } catch (e) {
      AppUtils.logError('Error marking alert as read', e);
      return false;
    }
  }

  // Resolve alert
  Future<bool> resolveAlert(String alertId, String resolvedBy) async {
    try {
      await _dbOperations.executeWithReadyCheck(() async {
        await _service.resolveAlert(alertId, resolvedBy);
      });

      await loadAlerts();
      return true;
    } catch (e) {
      AppUtils.logError('Error resolving alert', e);
      return false;
    }
  }

  // Mark all alerts as read
  Future<bool> markAllAsRead() async {
    try {
      await _dbOperations.executeWithReadyCheck(() async {
        await _service.markAllAlertsAsRead(
          warehouseId: state.selectedWarehouse,
          productId: state.selectedProduct,
        );
      });

      await loadAlerts();
      return true;
    } catch (e) {
      AppUtils.logError('Error marking all alerts as read', e);
      return false;
    }
  }

  // Resolve all alerts
  Future<bool> resolveAllAlerts(String resolvedBy) async {
    try {
      await _dbOperations.executeWithReadyCheck(() async {
        await _service.resolveAllAlerts(
          warehouseId: state.selectedWarehouse,
          productId: state.selectedProduct,
          alertType: state.selectedAlertType,
          resolvedBy: resolvedBy,
        );
      });

      await loadAlerts();
      return true;
    } catch (e) {
      AppUtils.logError('Error resolving all alerts', e);
      return false;
    }
  }

  // Generate alerts
  Future<bool> generateAlerts() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _dbOperations.executeWithReadyCheck(() async {
        await _service.checkAndGenerateAlerts();
      });

      await loadAlerts();

      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error generating alerts', e);
      return false;
    }
  }

  // Clear filters
  void clearFilters() {
    state = state.copyWith(
      searchQuery: '',
      selectedWarehouse: null,
      selectedProduct: null,
      selectedAlertType: null,
      selectedSeverity: null,
      showOnlyUnread: null,
      showOnlyActive: null,
      fromDate: null,
      toDate: null,
      filteredAlerts: _filterAndSortAlerts(state.alerts),
    );
  }

  // Private helper methods
  List<StockAlert> _filterAndSortAlerts(List<StockAlert> alerts) {
    var filtered = alerts.where((alert) {
      // Apply filters
      if (state.selectedWarehouse != null &&
          alert.warehouseId != state.selectedWarehouse) {
        return false;
      }

      if (state.selectedProduct != null &&
          alert.productId != state.selectedProduct) {
        return false;
      }

      if (state.selectedAlertType != null &&
          alert.alertType != state.selectedAlertType) {
        return false;
      }

      if (state.selectedSeverity != null &&
          alert.severity != state.selectedSeverity) {
        return false;
      }

      if (state.showOnlyUnread == true && alert.isRead) {
        return false;
      }

      if (state.showOnlyActive == true && alert.isResolved) {
        return false;
      }

      if (state.fromDate != null && alert.createdAt.isBefore(state.fromDate!)) {
        return false;
      }

      if (state.toDate != null && alert.createdAt.isAfter(state.toDate!)) {
        return false;
      }

      return true;
    }).toList();

    // Apply sorting
    filtered.sort((a, b) {
      int comparison = 0;

      switch (state.sortField) {
        case 'severity':
          final severityOrder = {
            'critical': 4,
            'high': 3,
            'medium': 2,
            'low': 1
          };
          comparison = (severityOrder[a.severity] ?? 0)
              .compareTo(severityOrder[b.severity] ?? 0);
          break;
        case 'alert_type':
          comparison = a.alertType.compareTo(b.alertType);
          break;
        case 'title':
          comparison = a.title.compareTo(b.title);
          break;
        case 'created_at':
        default:
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
      }

      return state.sortAscending ? comparison : -comparison;
    });

    return filtered;
  }
}
