import '../../../core/constants/database_constants.dart';
import '../../models/user.dart';
import 'base_dao.dart';

/// DAO للمستخدمين - User Data Access Object
/// يدير عمليات قاعدة البيانات للمستخدمين
class UserDao extends BaseDao<User> {
  @override
  String get tableName => DatabaseConstants.tableUsers;

  @override
  User fromMap(Map<String, dynamic> map) => User.fromMap(map);

  @override
  Map<String, dynamic> toMap(User entity) => entity.toMap();

  /// البحث عن مستخدم بالبريد الإلكتروني - Search for user by email
  Future<User?> findByEmail(String email) async {
    try {
      final users = await findWhere(
        where: '${DatabaseConstants.columnUserEmail} = ? AND deleted_at IS NULL',
        whereArgs: [email],
        limit: 1,
      );
      return users.isNotEmpty ? users.first : null;
    } catch (e) {
      print('خطأ في findByEmail: $e');
      return null;
    }
  }

  /// البحث عن مستخدم بالاسم - Search for user by name
  Future<User?> findByName(String name) async {
    try {
      final users = await findWhere(
        where: '${DatabaseConstants.columnUserName} = ? AND deleted_at IS NULL',
        whereArgs: [name],
        limit: 1,
      );
      return users.isNotEmpty ? users.first : null;
    } catch (e) {
      print('خطأ في findByName: $e');
      return null;
    }
  }

  /// التحقق من بيانات تسجيل الدخول - Validate login credentials
  Future<User?> validateLogin(String email, String passwordHash) async {
    try {
      final users = await findWhere(
        where: '''
          ${DatabaseConstants.columnUserEmail} = ? 
          AND ${DatabaseConstants.columnUserPasswordHash} = ? 
          AND ${DatabaseConstants.columnUserIsActive} = 1 
          AND deleted_at IS NULL
        ''',
        whereArgs: [email, passwordHash],
        limit: 1,
      );
      return users.isNotEmpty ? users.first : null;
    } catch (e) {
      print('خطأ في validateLogin: $e');
      return null;
    }
  }

  /// الحصول على المستخدمين النشطين - Get active users
  Future<List<User>> getActiveUsers() async {
    try {
      return await findWhere(
        where: '${DatabaseConstants.columnUserIsActive} = 1 AND deleted_at IS NULL',
        orderBy: '${DatabaseConstants.columnUserName} ASC',
      );
    } catch (e) {
      print('خطأ في getActiveUsers: $e');
      return [];
    }
  }

  /// الحصول على المستخدمين حسب الدور - Get users by role
  Future<List<User>> getUsersByRole(String role) async {
    try {
      return await findWhere(
        where: '${DatabaseConstants.columnUserRole} = ? AND deleted_at IS NULL',
        whereArgs: [role],
        orderBy: '${DatabaseConstants.columnUserName} ASC',
      );
    } catch (e) {
      print('خطأ في getUsersByRole: $e');
      return [];
    }
  }

  /// الحصول على المستخدمين حسب الفرع - Get users by branch
  Future<List<User>> getUsersByBranch(String branchId) async {
    try {
      return await findWhere(
        where: '${DatabaseConstants.columnUserBranchId} = ? AND deleted_at IS NULL',
        whereArgs: [branchId],
        orderBy: '${DatabaseConstants.columnUserName} ASC',
      );
    } catch (e) {
      print('خطأ في getUsersByBranch: $e');
      return [];
    }
  }

  /// تحديث آخر تسجيل دخول - Update last login
  Future<bool> updateLastLogin(String userId) async {
    try {
      final user = await findById(userId);
      if (user == null) return false;

      final updatedUser = user.copyWith(
        lastLoginAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isSynced: false,
      );

      return await update(userId, updatedUser);
    } catch (e) {
      print('خطأ في updateLastLogin: $e');
      return false;
    }
  }

  /// تفعيل/إلغاء تفعيل المستخدم - Activate/deactivate user
  Future<bool> setUserActive(String userId, bool isActive) async {
    try {
      final user = await findById(userId);
      if (user == null) return false;

      final updatedUser = user.copyWith(
        isActive: isActive,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

      return await update(userId, updatedUser);
    } catch (e) {
      print('خطأ في setUserActive: $e');
      return false;
    }
  }

  /// تحديث كلمة المرور - Update password
  Future<bool> updatePassword(String userId, String newPasswordHash) async {
    try {
      final user = await findById(userId);
      if (user == null) return false;

      final updatedUser = user.copyWith(
        passwordHash: newPasswordHash,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

      return await update(userId, updatedUser);
    } catch (e) {
      print('خطأ في updatePassword: $e');
      return false;
    }
  }

  /// البحث في المستخدمين - Search users
  Future<List<User>> searchUsers(String query) async {
    try {
      return await findWhere(
        where: '''
          (${DatabaseConstants.columnUserName} LIKE ? 
          OR ${DatabaseConstants.columnUserEmail} LIKE ? 
          OR ${DatabaseConstants.columnUserPhone} LIKE ?) 
          AND deleted_at IS NULL
        ''',
        whereArgs: ['%$query%', '%$query%', '%$query%'],
        orderBy: '${DatabaseConstants.columnUserName} ASC',
      );
    } catch (e) {
      print('خطأ في searchUsers: $e');
      return [];
    }
  }

  /// التحقق من وجود بريد إلكتروني - Check if email exists
  Future<bool> emailExists(String email, {String? excludeUserId}) async {
    try {
      String whereClause = '${DatabaseConstants.columnUserEmail} = ? AND deleted_at IS NULL';
      List<dynamic> whereArgs = [email];
      
      if (excludeUserId != null) {
        whereClause += ' AND ${DatabaseConstants.columnUserId} != ?';
        whereArgs.add(excludeUserId);
      }

      final users = await findWhere(
        where: whereClause,
        whereArgs: whereArgs,
        limit: 1,
      );
      
      return users.isNotEmpty;
    } catch (e) {
      print('خطأ في emailExists: $e');
      return false;
    }
  }

  /// التحقق من وجود رقم الهاتف - Check if phone exists
  Future<bool> phoneExists(String phone, {String? excludeUserId}) async {
    try {
      String whereClause = '${DatabaseConstants.columnUserPhone} = ? AND deleted_at IS NULL';
      List<dynamic> whereArgs = [phone];
      
      if (excludeUserId != null) {
        whereClause += ' AND ${DatabaseConstants.columnUserId} != ?';
        whereArgs.add(excludeUserId);
      }

      final users = await findWhere(
        where: whereClause,
        whereArgs: whereArgs,
        limit: 1,
      );
      
      return users.isNotEmpty;
    } catch (e) {
      print('خطأ في phoneExists: $e');
      return false;
    }
  }
}
