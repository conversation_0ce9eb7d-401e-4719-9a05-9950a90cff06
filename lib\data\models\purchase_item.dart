import 'package:json_annotation/json_annotation.dart';
import '../../core/constants/database_constants.dart';

part 'purchase_item.g.dart';

@JsonSerializable()
class PurchaseItem {
  final String id;
  final String purchaseId;
  final String productId;
  final String? unitId;
  final double qty;
  final double unitPrice;
  final double totalPrice;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final bool isSynced;

  const PurchaseItem({
    required this.id,
    required this.purchaseId,
    required this.productId,
    this.unitId,
    required this.qty,
    required this.unitPrice,
    required this.totalPrice,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  factory PurchaseItem.fromJson(Map<String, dynamic> json) =>
      _$PurchaseItemFromJson(json);
  Map<String, dynamic> toJson() => _$PurchaseItemToJson(this);

  factory PurchaseItem.fromMap(Map<String, dynamic> map) {
    return PurchaseItem(
      id: map[DatabaseConstants.columnPurchaseItemId] as String,
      purchaseId: map[DatabaseConstants.columnPurchaseItemPurchaseId] as String,
      productId: map[DatabaseConstants.columnPurchaseItemProductId] as String,
      unitId: map[DatabaseConstants.columnPurchaseItemUnitId] as String?,
      qty: (map[DatabaseConstants.columnPurchaseItemQty] as num).toDouble(),
      unitPrice: (map[DatabaseConstants.columnPurchaseItemUnitPrice] as num)
          .toDouble(),
      totalPrice: (map[DatabaseConstants.columnPurchaseItemTotalPrice] as num?)
              ?.toDouble() ??
          ((map[DatabaseConstants.columnPurchaseItemQty] as num).toDouble() *
              (map[DatabaseConstants.columnPurchaseItemUnitPrice] as num)
                  .toDouble()),
      createdAt: map[DatabaseConstants.columnPurchaseItemCreatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnPurchaseItemCreatedAt] as String)
          : null,
      updatedAt: map[DatabaseConstants.columnPurchaseItemUpdatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnPurchaseItemUpdatedAt] as String)
          : null,
      deletedAt: map[DatabaseConstants.columnPurchaseItemDeletedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnPurchaseItemDeletedAt] as String)
          : null,
      isSynced:
          (map[DatabaseConstants.columnPurchaseItemIsSynced] as int?) == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnPurchaseItemId: id,
      DatabaseConstants.columnPurchaseItemPurchaseId: purchaseId,
      DatabaseConstants.columnPurchaseItemProductId: productId,
      DatabaseConstants.columnPurchaseItemUnitId: unitId,
      DatabaseConstants.columnPurchaseItemQty: qty,
      DatabaseConstants.columnPurchaseItemUnitPrice: unitPrice,
      DatabaseConstants.columnPurchaseItemTotalPrice: totalPrice,
      DatabaseConstants.columnPurchaseItemCreatedAt:
          createdAt?.toIso8601String(),
      DatabaseConstants.columnPurchaseItemUpdatedAt:
          updatedAt?.toIso8601String(),
      DatabaseConstants.columnPurchaseItemDeletedAt:
          deletedAt?.toIso8601String(),
      DatabaseConstants.columnPurchaseItemIsSynced: isSynced ? 1 : 0,
    };
  }

  PurchaseItem copyWith({
    String? id,
    String? purchaseId,
    String? productId,
    String? unitId,
    double? qty,
    double? unitPrice,
    double? totalPrice,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return PurchaseItem(
      id: id ?? this.id,
      purchaseId: purchaseId ?? this.purchaseId,
      productId: productId ?? this.productId,
      unitId: unitId ?? this.unitId,
      qty: qty ?? this.qty,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PurchaseItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PurchaseItem(id: $id, productId: $productId, qty: $qty, unitPrice: $unitPrice, totalPrice: $totalPrice)';
  }

  // Helper methods
  double get calculatedTotal => qty * unitPrice;

  bool get isTotalCorrect => (totalPrice - calculatedTotal).abs() < 0.01;

  // Validation methods
  bool get isValid {
    return qty > 0 && unitPrice > 0 && totalPrice > 0 && isTotalCorrect;
  }

  List<String> get validationErrors {
    final errors = <String>[];

    if (qty <= 0) {
      errors.add('الكمية يجب أن تكون أكبر من صفر');
    }

    if (unitPrice <= 0) {
      errors.add('سعر الوحدة يجب أن يكون أكبر من صفر');
    }

    if (totalPrice <= 0) {
      errors.add('الإجمالي يجب أن يكون أكبر من صفر');
    }

    if (!isTotalCorrect) {
      errors.add('الإجمالي غير صحيح (يجب أن يساوي الكمية × سعر الوحدة)');
    }

    return errors;
  }

  // Factory constructors
  factory PurchaseItem.empty() {
    return PurchaseItem(
      id: '',
      purchaseId: '',
      productId: '',
      qty: 0,
      unitPrice: 0,
      totalPrice: 0,
    );
  }

  factory PurchaseItem.create({
    required String purchaseId,
    required String productId,
    String? unitId,
    required double qty,
    required double unitPrice,
  }) {
    final totalPrice = qty * unitPrice;

    return PurchaseItem(
      id: '', // Will be generated by DAO
      purchaseId: purchaseId,
      productId: productId,
      unitId: unitId,
      qty: qty,
      unitPrice: unitPrice,
      totalPrice: totalPrice,
      createdAt: DateTime.now(),
    );
  }

  // Update methods
  PurchaseItem updateQty(double newQty) {
    return copyWith(
      qty: newQty,
      totalPrice: newQty * unitPrice,
    );
  }

  PurchaseItem updateUnitPrice(double newUnitPrice) {
    return copyWith(
      unitPrice: newUnitPrice,
      totalPrice: qty * newUnitPrice,
    );
  }

  PurchaseItem updateBoth(double newQty, double newUnitPrice) {
    return copyWith(
      qty: newQty,
      unitPrice: newUnitPrice,
      totalPrice: newQty * newUnitPrice,
    );
  }
}
