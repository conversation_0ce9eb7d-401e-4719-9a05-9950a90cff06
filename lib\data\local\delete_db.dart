import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:tijari_tech/core/constants/app_constants.dart';
import 'package:tijari_tech/data/local/database.dart';

Future<void> deleteDatabaseFile() async {
  final dbPath = await getDatabasesPath();
  final path = join(dbPath, AppConstants.databaseName); // غيّر الاسم حسب ما تستخدم

  await deleteDatabase(path);
  print("📦 تم حذف قاعدة البيانات بنجاح");
}
/// تهيئة قاعدة البيانات
Future<void> initializeDatabase() async {
  try {
    print('🗄️ تهيئة قاعدة البيانات...');
    final databaseHelper = DatabaseHelper();
    await databaseHelper.database;
    print('✅ تم تهيئة قاعدة البيانات بنجاح');
  } catch (e) {
    print('❌ خطأ في تهيئة قاعدة البيانات: $e');
  }
}