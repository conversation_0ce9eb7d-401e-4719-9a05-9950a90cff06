import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/user.dart';
import '../data/repositories/user_repository.dart';

// User repository provider
final userRepositoryProvider = Provider<UserRepository>((ref) {
  return UserRepository();
});

// Current user provider
final currentUserProvider = StateNotifierProvider<CurrentUserNotifier, AsyncValue<User?>>((ref) {
  return CurrentUserNotifier(ref.read(userRepositoryProvider));
});

// All users provider
final usersProvider = StateNotifierProvider<UsersNotifier, AsyncValue<List<User>>>((ref) {
  return UsersNotifier(ref.read(userRepositoryProvider));
});

// Users search provider
final usersSearchProvider = StateNotifierProvider<UsersSearchNotifier, AsyncValue<List<User>>>((ref) {
  return UsersSearchNotifier(ref.read(userRepositoryProvider));
});

// User statistics provider
final userStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.read(userRepositoryProvider);
  return await repository.getUsersStatistics();
});

// Users by role provider
final usersByRoleProvider = FutureProvider.family<List<User>, String>((ref, role) async {
  final repository = ref.read(userRepositoryProvider);
  return await repository.getUsersByRole(role);
});

// Current user notifier
class CurrentUserNotifier extends StateNotifier<AsyncValue<User?>> {
  final UserRepository _repository;

  CurrentUserNotifier(this._repository) : super(const AsyncValue.loading()) {
    _loadCurrentUser();
  }

  Future<void> _loadCurrentUser() async {
    try {
      // In a real app, you'd get the current user ID from secure storage
      final user = await _repository.getUserById('admin-user');
      state = AsyncValue.data(user);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> login(String email, String password) async {
    state = const AsyncValue.loading();
    try {
      final user = await _repository.authenticateUser(email, password);
      if (user != null) {
        state = AsyncValue.data(user);
      } else {
        state = AsyncValue.error('Invalid credentials', StackTrace.current);
      }
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> logout() async {
    state = const AsyncValue.data(null);
  }

  Future<void> updateProfile(User updatedUser) async {
    try {
      await _repository.updateUser(updatedUser);
      state = AsyncValue.data(updatedUser);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> changePassword(String newPassword) async {
    final currentUser = state.value;
    if (currentUser != null) {
      try {
        await _repository.changePassword(currentUser.id, newPassword);
        // Password changed successfully
      } catch (error, stackTrace) {
        state = AsyncValue.error(error, stackTrace);
      }
    }
  }
}

// Users list notifier
class UsersNotifier extends StateNotifier<AsyncValue<List<User>>> {
  final UserRepository _repository;

  UsersNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadUsers();
  }

  Future<void> loadUsers() async {
    state = const AsyncValue.loading();
    try {
      final users = await _repository.getAllUsers();
      state = AsyncValue.data(users);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addUser(User user) async {
    try {
      await _repository.createUser(user);
      await loadUsers(); // Reload the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateUser(User user) async {
    try {
      await _repository.updateUser(user);
      await loadUsers(); // Reload the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteUser(String userId) async {
    try {
      await _repository.deleteUser(userId);
      await loadUsers(); // Reload the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> toggleUserStatus(String userId, bool isActive) async {
    try {
      await _repository.toggleUserStatus(userId, isActive);
      await loadUsers(); // Reload the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateUserPermissions(String userId, List<String> permissions) async {
    try {
      await _repository.updateUserPermissions(userId, permissions);
      await loadUsers(); // Reload the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> resetPassword(String userId, String newPassword) async {
    try {
      await _repository.changePassword(userId, newPassword);
      // Password reset successfully
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Users search notifier
class UsersSearchNotifier extends StateNotifier<AsyncValue<List<User>>> {
  final UserRepository _repository;

  UsersSearchNotifier(this._repository) : super(const AsyncValue.data([]));

  Future<void> searchUsers(String query) async {
    if (query.isEmpty) {
      state = const AsyncValue.data([]);
      return;
    }

    state = const AsyncValue.loading();
    try {
      final users = await _repository.searchUsers(query);
      state = AsyncValue.data(users);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void clearSearch() {
    state = const AsyncValue.data([]);
  }
}

// User form state provider
final userFormProvider = StateNotifierProvider<UserFormNotifier, UserFormState>((ref) {
  return UserFormNotifier(ref.read(userRepositoryProvider));
});

// User form state
class UserFormState {
  final User? user;
  final bool isLoading;
  final String? error;
  final bool isEditing;

  const UserFormState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isEditing = false,
  });

  UserFormState copyWith({
    User? user,
    bool? isLoading,
    String? error,
    bool? isEditing,
  }) {
    return UserFormState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isEditing: isEditing ?? this.isEditing,
    );
  }
}

// User form notifier
class UserFormNotifier extends StateNotifier<UserFormState> {
  final UserRepository _repository;

  UserFormNotifier(this._repository) : super(const UserFormState());

  void initializeForEdit(User user) {
    state = UserFormState(user: user, isEditing: true);
  }

  void initializeForCreate() {
    state = const UserFormState(isEditing: false);
  }

  Future<bool> saveUser(User user) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      if (state.isEditing) {
        await _repository.updateUser(user);
      } else {
        await _repository.createUser(user);
      }
      
      state = state.copyWith(isLoading: false);
      return true;
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        error: error.toString(),
      );
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  void reset() {
    state = const UserFormState();
  }
}

// User permissions provider
final userPermissionsProvider = Provider.family<List<String>, String>((ref, role) {
  return UserPermissions.getDefaultPermissions(role);
});

// User roles provider
final userRolesProvider = Provider<List<String>>((ref) {
  return UserRoles.allRoles;
});

// Active users count provider
final activeUsersCountProvider = FutureProvider<int>((ref) async {
  final repository = ref.read(userRepositoryProvider);
  final statistics = await repository.getUsersStatistics();
  return statistics['active'] as int;
});

// Recent users provider (users created in last 30 days)
final recentUsersProvider = FutureProvider<List<User>>((ref) async {
  final repository = ref.read(userRepositoryProvider);
  final allUsers = await repository.getAllUsers();
  final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
  
  return allUsers.where((user) => user.createdAt.isAfter(thirtyDaysAgo)).toList();
});

// User validation provider
final userValidationProvider = Provider<UserValidation>((ref) {
  return UserValidation();
});

// User validation class
class UserValidation {
  String? validateName(String? name) {
    if (name == null || name.trim().isEmpty) {
      return 'الاسم مطلوب';
    }
    if (name.trim().length < 2) {
      return 'الاسم يجب أن يكون أكثر من حرفين';
    }
    return null;
  }

  String? validateEmail(String? email) {
    if (email == null || email.trim().isEmpty) {
      return 'البريد الإلكتروني مطلوب';
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(email.trim())) {
      return 'البريد الإلكتروني غير صحيح';
    }
    return null;
  }

  String? validatePhone(String? phone) {
    if (phone != null && phone.trim().isNotEmpty) {
      final phoneRegex = RegExp(r'^05\d{8}$');
      if (!phoneRegex.hasMatch(phone.trim())) {
        return 'رقم الهاتف غير صحيح (يجب أن يبدأ بـ 05 ويحتوي على 10 أرقام)';
      }
    }
    return null;
  }

  String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'كلمة المرور مطلوبة';
    }
    if (password.length < 6) {
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    return null;
  }

  String? validateRole(String? role) {
    if (role == null || role.trim().isEmpty) {
      return 'الدور الوظيفي مطلوب';
    }
    if (!UserRoles.allRoles.contains(role)) {
      return 'الدور الوظيفي غير صحيح';
    }
    return null;
  }
}
