import '../data/local/dao/stock_dao.dart';
import '../data/local/dao/product_dao.dart';
import '../data/local/dao/warehouse_dao.dart';
import '../data/local/dao/stock_movement_dao.dart';
import '../data/models/inventory_report.dart';
import '../data/models/stock_movement.dart';
import '../core/utils/app_utils.dart';

class InventoryReportService {
  final StockDao _stockDao = StockDao();
  final ProductDao _productDao = ProductDao();
  final WarehouseDao _warehouseDao = WarehouseDao();
  final StockMovementDao _movementDao = StockMovementDao();

  // ------------------------------------------------------------------
  // Stock Summary Report
  // ------------------------------------------------------------------

  /// إنشاء تقرير ملخص المخزون
  Future<StockSummaryData> generateStockSummaryReport({
    String? warehouseId,
    String? categoryId,
    DateTime? asOfDate,
  }) async {
    try {
      AppUtils.logInfo('Generating stock summary report...');

      // Get basic statistics
      final totalProducts = await _getProductCount(categoryId: categoryId);
      final totalWarehouses = await _getWarehouseCount();
      final totalValue = await _getTotalStockValue(warehouseId: warehouseId);

      // Get stock status counts
      final lowStockItems =
          await _getLowStockItemsCount(warehouseId: warehouseId);
      final outOfStockItems =
          await _getOutOfStockItemsCount(warehouseId: warehouseId);
      final expiredItems = await _getExpiredItemsCount();
      final nearExpiryItems = await _getNearExpiryItemsCount();

      // Get detailed summaries
      final productSummaries = await _getProductStockSummaries(
        warehouseId: warehouseId,
        categoryId: categoryId,
      );

      final warehouseSummaries = await _getWarehouseStockSummaries();

      final summaryData = StockSummaryData(
        totalProducts: totalProducts,
        totalWarehouses: totalWarehouses,
        totalValue: totalValue,
        lowStockItems: lowStockItems,
        outOfStockItems: outOfStockItems,
        expiredItems: expiredItems,
        nearExpiryItems: nearExpiryItems,
        productSummaries: productSummaries,
        warehouseSummaries: warehouseSummaries,
      );

      AppUtils.logInfo('Stock summary report generated successfully');
      return summaryData;
    } catch (e) {
      AppUtils.logError('Error generating stock summary report', e);
      rethrow;
    }
  }

  // ------------------------------------------------------------------
  // Movement Analysis Report
  // ------------------------------------------------------------------

  /// إنشاء تقرير تحليل حركة المخزون
  Future<Map<String, dynamic>> generateMovementAnalysisReport({
    String? warehouseId,
    String? productId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      AppUtils.logInfo('Generating movement analysis report...');

      final movements = await _movementDao.getMovementsWithDetails(
        warehouseId: warehouseId,
        productId: productId,
        fromDate: fromDate,
        toDate: toDate,
        limit: 10000, // Large limit for comprehensive analysis
      );

      // Analyze movements
      final totalMovements = movements.length;
      final inMovements = movements.where((m) => m.movementType == 'in').length;
      final outMovements =
          movements.where((m) => m.movementType == 'out').length;
      final transferMovements =
          movements.where((m) => m.movementType == 'transfer').length;
      final adjustmentMovements =
          movements.where((m) => m.movementType == 'adjustment').length;

      final totalInQuantity = movements
          .where((m) => m.movementType == 'in')
          .fold(0.0, (sum, m) => sum + m.quantity);

      final totalOutQuantity = movements
          .where((m) => m.movementType == 'out')
          .fold(0.0, (sum, m) => sum + m.quantity);

      final totalInValue = movements
          .where((m) => m.movementType == 'in')
          .fold(0.0, (sum, m) => sum + (m.quantity * m.unitCost));

      final totalOutValue = movements
          .where((m) => m.movementType == 'out')
          .fold(0.0, (sum, m) => sum + (m.quantity * m.unitCost));

      // Group by product
      final productMovements = <String, List<dynamic>>{};
      for (final movement in movements) {
        productMovements
            .putIfAbsent(movement.productId, () => [])
            .add(movement);
      }

      // Group by warehouse
      final warehouseMovements = <String, List<dynamic>>{};
      for (final movement in movements) {
        warehouseMovements
            .putIfAbsent(movement.warehouseId, () => [])
            .add(movement);
      }

      // Group by date (daily)
      final dailyMovements = <String, List<dynamic>>{};
      for (final movement in movements) {
        final dateKey = movement.createdAt.toIso8601String().substring(0, 10);
        dailyMovements.putIfAbsent(dateKey, () => []).add(movement);
      }

      final analysisData = {
        'summary': {
          'total_movements': totalMovements,
          'in_movements': inMovements,
          'out_movements': outMovements,
          'transfer_movements': transferMovements,
          'adjustment_movements': adjustmentMovements,
          'total_in_quantity': totalInQuantity,
          'total_out_quantity': totalOutQuantity,
          'total_in_value': totalInValue,
          'total_out_value': totalOutValue,
          'net_quantity': totalInQuantity - totalOutQuantity,
          'net_value': totalInValue - totalOutValue,
        },
        'product_analysis':
            productMovements.map((productId, movements) => MapEntry(
                  productId,
                  {
                    'total_movements': movements.length,
                    'in_quantity': movements
                        .where((m) => m.movementType == 'in')
                        .fold(0.0, (sum, m) => sum + m.quantity),
                    'out_quantity': movements
                        .where((m) => m.movementType == 'out')
                        .fold(0.0, (sum, m) => sum + m.quantity),
                  },
                )),
        'warehouse_analysis':
            warehouseMovements.map((warehouseId, movements) => MapEntry(
                  warehouseId,
                  {
                    'total_movements': movements.length,
                    'in_quantity': movements
                        .where((m) => m.movementType == 'in')
                        .fold(0.0, (sum, m) => sum + m.quantity),
                    'out_quantity': movements
                        .where((m) => m.movementType == 'out')
                        .fold(0.0, (sum, m) => sum + m.quantity),
                  },
                )),
        'daily_analysis': dailyMovements.map((date, movements) => MapEntry(
              date,
              {
                'total_movements': movements.length,
                'in_quantity': movements
                    .where((m) => m.movementType == 'in')
                    .fold(0.0, (sum, m) => sum + m.quantity),
                'out_quantity': movements
                    .where((m) => m.movementType == 'out')
                    .fold(0.0, (sum, m) => sum + m.quantity),
              },
            )),
      };

      AppUtils.logInfo('Movement analysis report generated successfully');
      return analysisData;
    } catch (e) {
      AppUtils.logError('Error generating movement analysis report', e);
      rethrow;
    }
  }

  // ------------------------------------------------------------------
  // Low Stock Report
  // ------------------------------------------------------------------

  /// إنشاء تقرير المخزون المنخفض
  Future<Map<String, dynamic>> generateLowStockReport({
    String? warehouseId,
    String? categoryId,
    double? threshold,
  }) async {
    try {
      AppUtils.logInfo('Generating low stock report...');

      final lowStockItems = await _stockDao.getLowStockItems();

      final allStocks = await _stockDao.getStocksWithDetails(
        warehouseId: warehouseId,
      );
      final outOfStockItems =
          allStocks.where((stock) => stock.quantity <= 0).toList();

      // Group by category
      final categoryGroups = <String, List<dynamic>>{};
      for (final item in lowStockItems) {
        // Assuming item has category information
        final categoryName = 'Unknown'; // TODO: Get from product data
        categoryGroups.putIfAbsent(categoryName, () => []).add(item);
      }

      // Group by warehouse
      final warehouseGroups = <String, List<dynamic>>{};
      for (final item in lowStockItems) {
        warehouseGroups.putIfAbsent(item.warehouseId, () => []).add(item);
      }

      final reportData = {
        'summary': {
          'total_low_stock_items': lowStockItems.length,
          'total_out_of_stock_items': outOfStockItems.length,
          'threshold_used': threshold ?? 10.0,
        },
        'low_stock_items': lowStockItems
            .map((item) => {
                  'product_id': item.productId,
                  'warehouse_id': item.warehouseId,
                  'current_quantity': item.quantity,
                  'average_cost': item.averageCost,
                  'total_value': item.quantity * item.averageCost,
                })
            .toList(),
        'out_of_stock_items': outOfStockItems
            .map((item) => {
                  'product_id': item.productId,
                  'warehouse_id': item.warehouseId,
                  'last_updated': item.updatedAt?.toIso8601String() ??
                      DateTime.now().toIso8601String(),
                })
            .toList(),
        'category_breakdown': categoryGroups.map((category, items) => MapEntry(
              category,
              {
                'count': items.length,
                'total_value': items.fold(0.0,
                    (sum, item) => sum + (item.quantity * item.averageCost)),
              },
            )),
        'warehouse_breakdown':
            warehouseGroups.map((warehouseId, items) => MapEntry(
                  warehouseId,
                  {
                    'count': items.length,
                    'total_value': items.fold(
                        0.0,
                        (sum, item) =>
                            sum + (item.quantity * item.averageCost)),
                  },
                )),
      };

      AppUtils.logInfo('Low stock report generated successfully');
      return reportData;
    } catch (e) {
      AppUtils.logError('Error generating low stock report', e);
      rethrow;
    }
  }

  // ------------------------------------------------------------------
  // ABC Analysis Report
  // ------------------------------------------------------------------

  /// إنشاء تقرير تحليل ABC للمنتجات
  Future<Map<String, dynamic>> generateABCAnalysisReport({
    String? warehouseId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      AppUtils.logInfo('Generating ABC analysis report...');

      // Get movement data for analysis
      final movements = await _movementDao.getMovementsWithDetails(
        warehouseId: warehouseId,
        fromDate: fromDate,
        toDate: toDate,
        movementType: 'out', // Focus on outbound movements for ABC analysis
        limit: 10000,
      );

      // Calculate product values and frequencies
      final productAnalysis = <String, Map<String, dynamic>>{};

      for (final movement in movements) {
        final productId = movement.productId;
        final value = movement.quantity * movement.unitCost;

        if (productAnalysis.containsKey(productId)) {
          productAnalysis[productId]!['total_value'] += value;
          productAnalysis[productId]!['total_quantity'] += movement.quantity;
          productAnalysis[productId]!['frequency'] += 1;
        } else {
          productAnalysis[productId] = {
            'product_id': productId,
            'total_value': value,
            'total_quantity': movement.quantity,
            'frequency': 1,
          };
        }
      }

      // Sort by total value (descending)
      final sortedProducts = productAnalysis.values.toList()
        ..sort((a, b) =>
            (b['total_value'] as double).compareTo(a['total_value'] as double));

      // Calculate cumulative percentages
      final totalValue = sortedProducts.fold(
          0.0, (sum, product) => sum + (product['total_value'] as double));
      double cumulativeValue = 0.0;

      for (final product in sortedProducts) {
        cumulativeValue += product['total_value'] as double;
        product['cumulative_percentage'] = (cumulativeValue / totalValue) * 100;
      }

      // Classify products into A, B, C categories
      final aProducts = <Map<String, dynamic>>[];
      final bProducts = <Map<String, dynamic>>[];
      final cProducts = <Map<String, dynamic>>[];

      for (final product in sortedProducts) {
        final cumulativePercentage = product['cumulative_percentage'] as double;

        if (cumulativePercentage <= 80) {
          product['category'] = 'A';
          aProducts.add(product);
        } else if (cumulativePercentage <= 95) {
          product['category'] = 'B';
          bProducts.add(product);
        } else {
          product['category'] = 'C';
          cProducts.add(product);
        }
      }

      final reportData = {
        'summary': {
          'total_products': sortedProducts.length,
          'total_value': totalValue,
          'a_products_count': aProducts.length,
          'b_products_count': bProducts.length,
          'c_products_count': cProducts.length,
          'a_products_value': aProducts.fold(
              0.0, (sum, p) => sum + (p['total_value'] as double)),
          'b_products_value': bProducts.fold(
              0.0, (sum, p) => sum + (p['total_value'] as double)),
          'c_products_value': cProducts.fold(
              0.0, (sum, p) => sum + (p['total_value'] as double)),
        },
        'a_products': aProducts,
        'b_products': bProducts,
        'c_products': cProducts,
        'all_products': sortedProducts,
      };

      AppUtils.logInfo('ABC analysis report generated successfully');
      return reportData;
    } catch (e) {
      AppUtils.logError('Error generating ABC analysis report', e);
      rethrow;
    }
  }

  // ------------------------------------------------------------------
  // Helper Methods
  // ------------------------------------------------------------------

  Future<int> _getProductCount({String? categoryId}) async {
    // TODO: Implement with proper DAO method
    return 0;
  }

  Future<int> _getWarehouseCount() async {
    final warehouses = await _warehouseDao.findAll();
    return warehouses.length;
  }

  Future<double> _getTotalStockValue({String? warehouseId}) async {
    // TODO: Implement with proper DAO method
    return 0.0;
  }

  Future<int> _getLowStockItemsCount({String? warehouseId}) async {
    // TODO: Implement with proper DAO method
    return 0;
  }

  Future<int> _getOutOfStockItemsCount({String? warehouseId}) async {
    // TODO: Implement with proper DAO method
    return 0;
  }

  Future<int> _getExpiredItemsCount() async {
    // TODO: Implement with proper DAO method
    return 0;
  }

  Future<int> _getNearExpiryItemsCount() async {
    // TODO: Implement with proper DAO method
    return 0;
  }

  Future<List<ProductStockSummary>> _getProductStockSummaries({
    String? warehouseId,
    String? categoryId,
  }) async {
    // TODO: Implement with proper DAO method
    return [];
  }

  Future<List<WarehouseStockSummary>> _getWarehouseStockSummaries() async {
    // TODO: Implement with proper DAO method
    return [];
  }
}
