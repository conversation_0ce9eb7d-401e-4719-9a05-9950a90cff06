import '../data/repositories/product_repository.dart';
import '../data/repositories/sale_repository.dart';
import '../data/repositories/purchase_repository.dart';
import '../data/models/product.dart';
import '../data/models/category.dart';
import '../data/models/unit.dart';
import '../data/models/sale.dart';
import '../data/models/sale_item.dart';
import '../data/models/purchase.dart';
import '../data/models/purchase_item.dart';
import '../core/utils/app_utils.dart';
import '../core/exceptions/app_exceptions.dart';

/// خدمة العمليات التجارية الأساسية
/// تحتوي على أمثلة عملية لإضافة المنتجات وعمليات الشراء والبيع
class BusinessService {
  final ProductRepository _productRepository = ProductRepository();
  final SaleRepository _saleRepository = SaleRepository();
  final PurchaseRepository _purchaseRepository = PurchaseRepository();

  /// إضافة منتج جديد
  Future<String> addNewProduct({
    required String nameAr,
    String? nameEn,
    String? barcode,
    String? description,
    String? categoryId,
    String? unitId,
    required double costPrice,
    required double sellingPrice,
    double minStock = 10,
    double maxStock = 100,
    double reorderPoint = 0,
    String? imageUrl,
  }) async {
    try {
      AppUtils.logInfo('بدء إضافة منتج جديد: $nameAr');

      // التحقق من صحة البيانات
      if (nameAr.trim().isEmpty) {
        throw ValidationException('اسم المنتج مطلوب');
      }
      if (costPrice < 0) {
        throw ValidationException('سعر التكلفة لا يمكن أن يكون سالباً');
      }
      if (sellingPrice < 0) {
        throw ValidationException('سعر البيع لا يمكن أن يكون سالباً');
      }

      // إنشاء المنتج
      final product = Product.create(
        nameAr: nameAr,
        nameEn: nameEn,
        barcode: barcode,
        description: description,
        categoryId: categoryId,
        baseUnitId: unitId,
        costPrice: costPrice,
        sellingPrice: sellingPrice,
        minStock: minStock,
        maxStock: maxStock,
        reorderPoint: reorderPoint,
        imageUrl: imageUrl, id: '',
      );

      // حفظ المنتج في قاعدة البيانات
      final productId = await _productRepository.createProduct(product);

      AppUtils.logInfo('تم إضافة المنتج بنجاح - ID: $productId');
      return productId;
    } catch (e) {
      AppUtils.logError('خطأ في إضافة المنتج', e);
      rethrow;
    }
  }

  /// تعديل منتج موجود
  Future<bool> updateProduct({
    required String productId,
    String? nameAr,
    String? nameEn,
    String? barcode,
    String? description,
    String? categoryId,
    String? unitId,
    double? costPrice,
    double? sellingPrice,
    double? minStock,
    double? maxStock,
    double? reorderPoint,
    String? imageUrl,
    bool? isActive,
  }) async {
    try {
      AppUtils.logInfo('بدء تعديل المنتج: $productId');

      // الحصول على المنتج الحالي
      final existingProduct =
          await _productRepository.getProductById(productId);
      if (existingProduct == null) {
        throw RecordNotFoundException(
          recordType: 'Product',
          recordId: productId,
        );
      }

      // التحقق من صحة البيانات
      if (nameAr != null && nameAr.trim().isEmpty) {
        throw ValidationException('اسم المنتج مطلوب');
      }
      if (costPrice != null && costPrice < 0) {
        throw ValidationException('سعر التكلفة لا يمكن أن يكون سالباً');
      }
      if (sellingPrice != null && sellingPrice < 0) {
        throw ValidationException('سعر البيع لا يمكن أن يكون سالباً');
      }

      // إنشاء المنتج المحدث
      final updatedProduct = existingProduct.copyWith(
        nameAr: nameAr,
        nameEn: nameEn,
        barcode: barcode,
        description: description,
        categoryId: categoryId,
        baseUnitId: unitId,
        costPrice: costPrice,
        sellingPrice: sellingPrice,
        minStock: minStock,
        maxStock: maxStock,
        reorderPoint: reorderPoint,
        imageUrl: imageUrl,
        isActive: isActive,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

      // حفظ التحديثات
      final success =
          await _productRepository.updateProduct(productId, updatedProduct);

      if (success) {
        AppUtils.logInfo('تم تعديل المنتج بنجاح - ID: $productId');
      } else {
        AppUtils.logError('فشل في تعديل المنتج', null);
      }

      return success;
    } catch (e) {
      AppUtils.logError('خطأ في تعديل المنتج', e);
      rethrow;
    }
  }

  /// حذف منتج (حذف منطقي)
  Future<bool> deleteProduct(String productId) async {
    try {
      AppUtils.logInfo('بدء حذف المنتج: $productId');

      // التحقق من وجود المنتج
      final existingProduct =
          await _productRepository.getProductById(productId);
      if (existingProduct == null) {
        throw RecordNotFoundException(
          recordType: 'Product',
          recordId: productId,
        );
      }

      // التحقق من عدم وجود معاملات مرتبطة بالمنتج
      final hasTransactions = await _checkProductHasTransactions(productId);
      if (hasTransactions) {
        throw BusinessLogicException(
          'لا يمكن حذف المنتج لأنه مرتبط بمعاملات موجودة. يمكنك إلغاء تفعيله بدلاً من ذلك.',
        );
      }

      // حذف المنتج
      final success = await _productRepository.deleteProduct(productId);

      if (success) {
        AppUtils.logInfo('تم حذف المنتج بنجاح - ID: $productId');
      } else {
        AppUtils.logError('فشل في حذف المنتج', null);
      }

      return success;
    } catch (e) {
      AppUtils.logError('خطأ في حذف المنتج', e);
      rethrow;
    }
  }

  /// إلغاء تفعيل منتج
  Future<bool> deactivateProduct(String productId) async {
    try {
      AppUtils.logInfo('بدء إلغاء تفعيل المنتج: $productId');

      return await updateProduct(
        productId: productId,
        isActive: false,
      );
    } catch (e) {
      AppUtils.logError('خطأ في إلغاء تفعيل المنتج', e);
      rethrow;
    }
  }

  /// تفعيل منتج
  Future<bool> activateProduct(String productId) async {
    try {
      AppUtils.logInfo('بدء تفعيل المنتج: $productId');

      return await updateProduct(
        productId: productId,
        isActive: true,
      );
    } catch (e) {
      AppUtils.logError('خطأ في تفعيل المنتج', e);
      rethrow;
    }
  }

  /// التحقق من وجود معاملات مرتبطة بالمنتج
  Future<bool> _checkProductHasTransactions(String productId) async {
    try {
      // بساطة في التحقق - نفترض عدم وجود معاملات للآن
      // يمكن تطوير هذا لاحقاً عند إضافة الطرق المناسبة في المستودعات
      return false;
    } catch (e) {
      AppUtils.logError('خطأ في فحص معاملات المنتج', e);
      // في حالة الخطأ، نفترض وجود معاملات لتجنب الحذف الخاطئ
      return true;
    }
  }

  /// مثال شامل: عملية شراء كاملة
  Future<String> createPurchaseTransaction({
    required String supplierId,
    required List<Map<String, dynamic>>
        items, // [{productId, qty, unitPrice}, ...]
    String? invoiceNo,
    String? branchId,
    DateTime? purchaseDate,
    double paidAmount = 0,
    String? notes,
  }) async {
    try {
      AppUtils.logInfo('بدء عملية شراء جديدة');

      // التحقق من صحة البيانات
      if (items.isEmpty) {
        throw ValidationException('يجب إضافة عنصر واحد على الأقل للشراء');
      }

      // حساب الإجمالي وإنشاء عناصر الشراء
      double totalAmount = 0;
      final purchaseItems = <PurchaseItem>[];

      for (final itemData in items) {
        final productId = itemData['productId'] as String;
        final qty = (itemData['qty'] as num).toDouble();
        final unitPrice = (itemData['unitPrice'] as num).toDouble();
        final unitId = itemData['unitId'] as String?;

        // التحقق من وجود المنتج
        final product = await _productRepository.getProductById(productId);
        if (product == null) {
          throw RecordNotFoundException(
            recordType: 'Product',
            recordId: productId,
          );
        }

        final totalPrice = qty * unitPrice;
        totalAmount += totalPrice;

        final purchaseItem = PurchaseItem.create(
          purchaseId: '', // سيتم تعيينه لاحقاً
          productId: productId,
          unitId: unitId ?? product.baseUnitId,
          qty: qty,
          unitPrice: unitPrice,
        );

        purchaseItems.add(purchaseItem);
      }

      // إنشاء فاتورة الشراء
      final purchase = Purchase.create(
        supplierId: supplierId,
        invoiceNo:
            invoiceNo ?? await _purchaseRepository.generateInvoiceNumber(),
        branchId: branchId,
        purchaseDate: purchaseDate ?? DateTime.now(),
        totalAmount: totalAmount,
        paidAmount: paidAmount,
        notes: notes,
      );

      // حفظ عملية الشراء
      final purchaseId =
          await _purchaseRepository.createPurchase(purchase, purchaseItems);

      AppUtils.logInfo('تم إنشاء عملية الشراء بنجاح - ID: $purchaseId');
      AppUtils.logInfo(
          'إجمالي المبلغ: $totalAmount، المدفوع: $paidAmount، المستحق: ${totalAmount - paidAmount}');

      return purchaseId;
    } catch (e) {
      AppUtils.logError('خطأ في إنشاء عملية الشراء', e);
      rethrow;
    }
  }

  /// مثال شامل: عملية بيع كاملة
  Future<String> createSaleTransaction({
    String? customerId,
    required List<Map<String, dynamic>>
        items, // [{productId, qty, unitPrice}, ...]
    String? invoiceNo,
    String? branchId,
    DateTime? saleDate,
    double paidAmount = 0,
    String? notes,
  }) async {
    try {
      AppUtils.logInfo('بدء عملية بيع جديدة');

      // التحقق من صحة البيانات
      if (items.isEmpty) {
        throw ValidationException('يجب إضافة عنصر واحد على الأقل للبيع');
      }

      // حساب الإجمالي وإنشاء عناصر البيع
      double totalAmount = 0;
      final saleItems = <SaleItem>[];

      for (final itemData in items) {
        final productId = itemData['productId'] as String;
        final qty = (itemData['qty'] as num).toDouble();
        final unitPrice = (itemData['unitPrice'] as num).toDouble();
        final unitId = itemData['unitId'] as String?;

        // التحقق من وجود المنتج
        final product = await _productRepository.getProductById(productId);
        if (product == null) {
          throw RecordNotFoundException(
            recordType: 'Product',
            recordId: productId,
          );
        }

        // التحقق من توفر المخزون
        final currentStock = await _productRepository.getCurrentStock(
            productId, 'main-warehouse' // المخزن الافتراضي
            );

        if (product.trackStock && qty > currentStock) {
          throw InsufficientStockException(
            productId: productId,
            productName: product.nameAr,
            requestedQuantity: qty,
            availableQuantity: currentStock,
          );
        }

        final totalPrice = qty * unitPrice;
        totalAmount += totalPrice;

        final saleItem = SaleItem.create(
          id: AppUtils.generateId(),
          saleId: '', // سيتم تعيينه لاحقاً
          productId: productId,
          unitId: unitId ?? product.baseUnitId ?? '',
          qty: qty,
          unitPrice: unitPrice,
        );

        saleItems.add(saleItem);
      }

      // إنشاء فاتورة البيع
      final sale = Sale.create(
        id: AppUtils.generateId(),
        customerId: customerId,
        invoiceNo: invoiceNo ?? await _saleRepository.generateInvoiceNumber(),
        branchId: branchId,
        date: saleDate ?? DateTime.now(),
        total: totalAmount,
        paid: paidAmount,
        notes: notes,
      );

      // حفظ عملية البيع
      final saleId = await _saleRepository.createSale(sale, saleItems);

      AppUtils.logInfo('تم إنشاء عملية البيع بنجاح - ID: $saleId');
      AppUtils.logInfo(
          'إجمالي المبلغ: $totalAmount، المدفوع: $paidAmount، المستحق: ${totalAmount - paidAmount}');

      return saleId;
    } catch (e) {
      AppUtils.logError('خطأ في إنشاء عملية البيع', e);
      rethrow;
    }
  }

  /// مثال: إضافة دفعة من المنتجات
  Future<List<String>> addProductsBatch(
      List<Map<String, dynamic>> productsData) async {
    try {
      AppUtils.logInfo(
          'بدء إضافة دفعة من المنتجات: ${productsData.length} منتج');

      final productIds = <String>[];

      for (final productData in productsData) {
        final productId = await addNewProduct(
          nameAr: productData['nameAr'] as String,
          nameEn: productData['nameEn'] as String?,
          barcode: productData['barcode'] as String?,
          description: productData['description'] as String?,
          categoryId: productData['categoryId'] as String?,
          unitId: productData['unitId'] as String?,
          costPrice: (productData['costPrice'] as num).toDouble(),
          sellingPrice: (productData['sellingPrice'] as num).toDouble(),
          minStock: (productData['minStock'] as num?)?.toDouble() ?? 10,
          maxStock: (productData['maxStock'] as num?)?.toDouble() ?? 100,
          imageUrl: productData['imageUrl'] as String?,
        );

        productIds.add(productId);
      }

      AppUtils.logInfo('تم إضافة ${productIds.length} منتج بنجاح');
      return productIds;
    } catch (e) {
      AppUtils.logError('خطأ في إضافة دفعة المنتجات', e);
      rethrow;
    }
  }

  /// مثال: تحديث أسعار المنتجات بالجملة
  Future<bool> updateProductsPrices(
      Map<String, Map<String, double>> priceUpdates) async {
    try {
      AppUtils.logInfo('بدء تحديث أسعار المنتجات: ${priceUpdates.length} منتج');

      final success = await _productRepository.bulkUpdatePrices(priceUpdates);

      if (success) {
        AppUtils.logInfo('تم تحديث أسعار المنتجات بنجاح');
      } else {
        AppUtils.logError('فشل في تحديث أسعار المنتجات', null);
      }

      return success;
    } catch (e) {
      AppUtils.logError('خطأ في تحديث أسعار المنتجات', e);
      rethrow;
    }
  }

  /// مثال: الحصول على تقرير المبيعات اليومية
  Future<Map<String, dynamic>> getDailySalesReport({DateTime? date}) async {
    try {
      final targetDate = date ?? DateTime.now();
      final startOfDay =
          DateTime(targetDate.year, targetDate.month, targetDate.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final sales =
          await _saleRepository.getSalesByDateRange(startOfDay, endOfDay);

      double totalSales = 0;
      double totalPaid = 0;
      double totalDue = 0;
      int totalTransactions = sales.length;

      for (final sale in sales) {
        totalSales += (sale['total_amount'] as num).toDouble();
        totalPaid += (sale['paid_amount'] as num).toDouble();
        totalDue += (sale['due_amount'] as num).toDouble();
      }

      return {
        'date': targetDate.toIso8601String(),
        'total_transactions': totalTransactions,
        'total_sales': totalSales,
        'total_paid': totalPaid,
        'total_due': totalDue,
        'average_transaction':
            totalTransactions > 0 ? totalSales / totalTransactions : 0,
        'payment_rate': totalSales > 0 ? (totalPaid / totalSales) * 100 : 0,
        'sales': sales,
      };
    } catch (e) {
      AppUtils.logError('خطأ في الحصول على تقرير المبيعات اليومية', e);
      rethrow;
    }
  }

  /// مثال: الحصول على تقرير المشتريات اليومية
  Future<Map<String, dynamic>> getDailyPurchasesReport({DateTime? date}) async {
    try {
      final targetDate = date ?? DateTime.now();
      final startOfDay =
          DateTime(targetDate.year, targetDate.month, targetDate.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final purchases = await _purchaseRepository.getPurchasesByDateRange(
          startOfDay, endOfDay);

      double totalPurchases = 0;
      double totalPaid = 0;
      double totalDue = 0;
      int totalTransactions = purchases.length;

      for (final purchase in purchases) {
        totalPurchases += (purchase['total_amount'] as num).toDouble();
        totalPaid += (purchase['paid_amount'] as num).toDouble();
        totalDue += (purchase['due_amount'] as num).toDouble();
      }

      return {
        'date': targetDate.toIso8601String(),
        'total_transactions': totalTransactions,
        'total_purchases': totalPurchases,
        'total_paid': totalPaid,
        'total_due': totalDue,
        'average_transaction':
            totalTransactions > 0 ? totalPurchases / totalTransactions : 0,
        'payment_rate':
            totalPurchases > 0 ? (totalPaid / totalPurchases) * 100 : 0,
        'purchases': purchases,
      };
    } catch (e) {
      AppUtils.logError('خطأ في الحصول على تقرير المشتريات اليومية', e);
      rethrow;
    }
  }
}
