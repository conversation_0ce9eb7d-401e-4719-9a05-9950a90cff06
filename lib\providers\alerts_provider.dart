import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/stock.dart';
import '../data/models/product.dart';
import '../core/utils/app_utils.dart';
import 'stock_provider.dart';
import 'enhanced_product_provider.dart';

/// نموذج التنبيه
class StockAlert {
  final String id;
  final String productId;
  final String productName;
  final String warehouseId;
  final String warehouseName;
  final AlertType type;
  final AlertSeverity severity;
  final String message;
  final double currentStock;
  final double threshold;
  final DateTime createdAt;
  final bool isRead;
  final bool isActive;

  const StockAlert({
    required this.id,
    required this.productId,
    required this.productName,
    required this.warehouseId,
    required this.warehouseName,
    required this.type,
    required this.severity,
    required this.message,
    required this.currentStock,
    required this.threshold,
    required this.createdAt,
    this.isRead = false,
    this.isActive = true,
  });

  StockAlert copyWith({
    String? id,
    String? productId,
    String? productName,
    String? warehouseId,
    String? warehouseName,
    AlertType? type,
    AlertSeverity? severity,
    String? message,
    double? currentStock,
    double? threshold,
    DateTime? createdAt,
    bool? isRead,
    bool? isActive,
  }) {
    return StockAlert(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      warehouseId: warehouseId ?? this.warehouseId,
      warehouseName: warehouseName ?? this.warehouseName,
      type: type ?? this.type,
      severity: severity ?? this.severity,
      message: message ?? this.message,
      currentStock: currentStock ?? this.currentStock,
      threshold: threshold ?? this.threshold,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      isActive: isActive ?? this.isActive,
    );
  }
}

/// أنواع التنبيهات
enum AlertType {
  lowStock, // مخزون منخفض
  outOfStock, // نفد المخزون
  overStock, // مخزون زائد
  expiringSoon, // قريب الانتهاء
  expired, // منتهي الصلاحية
}

/// مستويات خطورة التنبيهات
enum AlertSeverity {
  low, // منخفض - أزرق
  medium, // متوسط - أصفر
  high, // عالي - برتقالي
  critical, // حرج - أحمر
}

/// حالة مزود التنبيهات
class AlertsState {
  final List<StockAlert> alerts;
  final bool isLoading;
  final String? error;
  final DateTime? lastUpdated;

  const AlertsState({
    this.alerts = const [],
    this.isLoading = false,
    this.error,
    this.lastUpdated,
  });

  AlertsState copyWith({
    List<StockAlert>? alerts,
    bool? isLoading,
    String? error,
    DateTime? lastUpdated,
  }) {
    return AlertsState(
      alerts: alerts ?? this.alerts,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  // الحصول على التنبيهات غير المقروءة
  List<StockAlert> get unreadAlerts =>
      alerts.where((a) => !a.isRead && a.isActive).toList();

  // الحصول على التنبيهات الحرجة
  List<StockAlert> get criticalAlerts => alerts
      .where((a) => a.severity == AlertSeverity.critical && a.isActive)
      .toList();

  // الحصول على عدد التنبيهات حسب النوع
  Map<AlertType, int> get alertCountsByType {
    final counts = <AlertType, int>{};
    for (final alert in alerts.where((a) => a.isActive)) {
      counts[alert.type] = (counts[alert.type] ?? 0) + 1;
    }
    return counts;
  }

  // الحصول على عدد التنبيهات حسب مستوى الخطورة
  Map<AlertSeverity, int> get alertCountsBySeverity {
    final counts = <AlertSeverity, int>{};
    for (final alert in alerts.where((a) => a.isActive)) {
      counts[alert.severity] = (counts[alert.severity] ?? 0) + 1;
    }
    return counts;
  }
}

/// مزود التنبيهات
class AlertsNotifier extends StateNotifier<AlertsState> {
  final Ref _ref;

  AlertsNotifier(this._ref) : super(const AlertsState());

  /// تحديث التنبيهات تلقائياً
  Future<void> updateAlerts() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final stockState = _ref.read(stockManagementProvider);
      final productState = _ref.read(enhancedProductManagementProvider);

      final alerts = <StockAlert>[];

      // فحص كل منتج في المخزون
      for (final stock in stockState.stocks) {
        Product? product;
        try {
          product = productState.products.firstWhere(
            (p) => p.id == stock.productId,
          );
        } catch (e) {
          product = null;
        }

        if (product == null || !product.trackStock) continue;

        // تحقق من المخزون المنخفض
        if (stock.quantity <= stock.reorderPoint && stock.quantity > 0) {
          alerts.add(StockAlert(
            id: AppUtils.generateId(),
            productId: stock.productId,
            productName: stock.productName ?? product.nameAr,
            warehouseId: stock.warehouseId,
            warehouseName: stock.warehouseName ?? 'مخزن غير محدد',
            type: AlertType.lowStock,
            severity: AlertSeverity.medium,
            message:
                'المخزون منخفض - الكمية الحالية: ${stock.quantity.toStringAsFixed(0)}، الحد الأدنى: ${stock.reorderPoint.toStringAsFixed(0)}',
            currentStock: stock.quantity,
            threshold: stock.reorderPoint,
            createdAt: DateTime.now(),
          ));
        }

        // تحقق من نفاد المخزون
        if (stock.quantity <= 0) {
          alerts.add(StockAlert(
            id: AppUtils.generateId(),
            productId: stock.productId,
            productName: stock.productName ?? product.nameAr,
            warehouseId: stock.warehouseId,
            warehouseName: stock.warehouseName ?? 'مخزن غير محدد',
            type: AlertType.outOfStock,
            severity: AlertSeverity.critical,
            message: 'نفد المخزون - يجب إعادة التزويد فوراً',
            currentStock: stock.quantity,
            threshold: 0,
            createdAt: DateTime.now(),
          ));
        }

        // تحقق من المخزون الزائد
        if (stock.maxStock > 0 && stock.quantity > stock.maxStock) {
          alerts.add(StockAlert(
            id: AppUtils.generateId(),
            productId: stock.productId,
            productName: stock.productName ?? product.nameAr,
            warehouseId: stock.warehouseId,
            warehouseName: stock.warehouseName ?? 'مخزن غير محدد',
            type: AlertType.overStock,
            severity: AlertSeverity.low,
            message:
                'مخزون زائد - الكمية الحالية: ${stock.quantity.toStringAsFixed(0)}، الحد الأقصى: ${stock.maxStock.toStringAsFixed(0)}',
            currentStock: stock.quantity,
            threshold: stock.maxStock,
            createdAt: DateTime.now(),
          ));
        }

        // تحقق من انتهاء الصلاحية (إذا كان المنتج له تاريخ انتهاء)
        if (stock.hasExpiryDate && stock.expiryDate != null) {
          final daysUntilExpiry =
              stock.expiryDate!.difference(DateTime.now()).inDays;

          if (daysUntilExpiry < 0) {
            // منتهي الصلاحية
            alerts.add(StockAlert(
              id: AppUtils.generateId(),
              productId: stock.productId,
              productName: stock.productName ?? product.nameAr,
              warehouseId: stock.warehouseId,
              warehouseName: stock.warehouseName ?? 'مخزن غير محدد',
              type: AlertType.expired,
              severity: AlertSeverity.critical,
              message: 'منتهي الصلاحية منذ ${(-daysUntilExpiry)} يوم',
              currentStock: stock.quantity,
              threshold: 0,
              createdAt: DateTime.now(),
            ));
          } else if (daysUntilExpiry <= 7) {
            // قريب الانتهاء (خلال 7 أيام)
            alerts.add(StockAlert(
              id: AppUtils.generateId(),
              productId: stock.productId,
              productName: stock.productName ?? product.nameAr,
              warehouseId: stock.warehouseId,
              warehouseName: stock.warehouseName ?? 'مخزن غير محدد',
              type: AlertType.expiringSoon,
              severity: daysUntilExpiry <= 3
                  ? AlertSeverity.high
                  : AlertSeverity.medium,
              message: 'قريب الانتهاء - باقي $daysUntilExpiry أيام',
              currentStock: stock.quantity,
              threshold: daysUntilExpiry.toDouble(),
              createdAt: DateTime.now(),
            ));
          }
        }
      }

      // ترتيب التنبيهات حسب الأولوية
      alerts.sort((a, b) {
        // ترتيب حسب مستوى الخطورة أولاً
        final severityOrder = {
          AlertSeverity.critical: 0,
          AlertSeverity.high: 1,
          AlertSeverity.medium: 2,
          AlertSeverity.low: 3,
        };

        final severityComparison =
            severityOrder[a.severity]!.compareTo(severityOrder[b.severity]!);
        if (severityComparison != 0) return severityComparison;

        // ثم حسب التاريخ (الأحدث أولاً)
        return b.createdAt.compareTo(a.createdAt);
      });

      state = state.copyWith(
        alerts: alerts,
        isLoading: false,
        lastUpdated: DateTime.now(),
      );

      AppUtils.logInfo('تم تحديث التنبيهات: ${alerts.length} تنبيه');
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      AppUtils.logError('خطأ في تحديث التنبيهات', e);
    }
  }

  /// تحديد تنبيه كمقروء
  void markAsRead(String alertId) {
    final updatedAlerts = state.alerts.map((alert) {
      if (alert.id == alertId) {
        return alert.copyWith(isRead: true);
      }
      return alert;
    }).toList();

    state = state.copyWith(alerts: updatedAlerts);
  }

  /// تحديد جميع التنبيهات كمقروءة
  void markAllAsRead() {
    final updatedAlerts = state.alerts.map((alert) {
      return alert.copyWith(isRead: true);
    }).toList();

    state = state.copyWith(alerts: updatedAlerts);
  }

  /// إلغاء تنبيه
  void dismissAlert(String alertId) {
    final updatedAlerts = state.alerts.map((alert) {
      if (alert.id == alertId) {
        return alert.copyWith(isActive: false);
      }
      return alert;
    }).toList();

    state = state.copyWith(alerts: updatedAlerts);
  }

  /// مسح التنبيهات القديمة (أكثر من 30 يوم)
  void clearOldAlerts() {
    final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
    final filteredAlerts = state.alerts.where((alert) {
      return alert.createdAt.isAfter(cutoffDate);
    }).toList();

    state = state.copyWith(alerts: filteredAlerts);
  }

  /// الحصول على التنبيهات حسب المنتج
  List<StockAlert> getAlertsForProduct(String productId) {
    return state.alerts
        .where((alert) => alert.productId == productId && alert.isActive)
        .toList();
  }

  /// الحصول على التنبيهات حسب المخزن
  List<StockAlert> getAlertsForWarehouse(String warehouseId) {
    return state.alerts
        .where((alert) => alert.warehouseId == warehouseId && alert.isActive)
        .toList();
  }

  /// الحصول على التنبيهات حسب النوع
  List<StockAlert> getAlertsByType(AlertType type) {
    return state.alerts
        .where((alert) => alert.type == type && alert.isActive)
        .toList();
  }

  /// الحصول على التنبيهات حسب مستوى الخطورة
  List<StockAlert> getAlertsBySeverity(AlertSeverity severity) {
    return state.alerts
        .where((alert) => alert.severity == severity && alert.isActive)
        .toList();
  }
}

/// مزود التنبيهات
final alertsProvider =
    StateNotifierProvider<AlertsNotifier, AlertsState>((ref) {
  return AlertsNotifier(ref);
});

/// مزود للتحديث التلقائي للتنبيهات
final autoUpdateAlertsProvider = Provider<void>((ref) {
  // تحديث التنبيهات كل 5 دقائق
  final timer = Stream.periodic(const Duration(minutes: 5));

  timer.listen((_) {
    ref.read(alertsProvider.notifier).updateAlerts();
  });

  // تحديث فوري عند تغيير المخزون
  ref.listen(stockManagementProvider, (previous, next) {
    if (previous?.stocks != next.stocks) {
      ref.read(alertsProvider.notifier).updateAlerts();
    }
  });

  return;
});

/// مزود عدد التنبيهات غير المقروءة
final unreadAlertsCountProvider = Provider<int>((ref) {
  final alertsState = ref.watch(alertsProvider);
  return alertsState.unreadAlerts.length;
});

/// مزود التنبيهات الحرجة
final criticalAlertsProvider = Provider<List<StockAlert>>((ref) {
  final alertsState = ref.watch(alertsProvider);
  return alertsState.criticalAlerts;
});
