import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../../shared/widgets/loading_widget.dart';
import '../../shared/widgets/error_display_widget.dart';
import '../../shared/widgets/empty_state_widget.dart';
import '../../shared/widgets/filter_chip_widget.dart';
import '../../shared/widgets/search_bar_widget.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../data/models/stock_movement.dart';
import '../../providers/stock_movement_provider.dart';
import 'widgets/stock_movement_card.dart';
import 'widgets/stock_movement_filters_dialog.dart';

class StockMovementsPage extends ConsumerStatefulWidget {
  const StockMovementsPage({super.key});

  @override
  ConsumerState<StockMovementsPage> createState() => _StockMovementsPageState();
}

class _StockMovementsPageState extends ConsumerState<StockMovementsPage> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(stockMovementManagementProvider.notifier).loadMovements();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(stockMovementManagementProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'حركة المخزون',
          style: AppTextStyles.titleLarge.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onPressed: () => _showFiltersDialog(),
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () =>
                ref.read(stockMovementManagementProvider.notifier).refresh(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filters section
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20.r),
                bottomRight: Radius.circular(20.r),
              ),
            ),
            child: Column(
              children: [
                // Search bar
                SearchBarWidget(
                  controller: _searchController,
                  hintText: 'البحث في حركة المخزون...',
                  onChanged: (value) {
                    ref
                        .read(stockMovementManagementProvider.notifier)
                        .searchMovements(value);
                  },
                ),
                SizedBox(height: 12.h),

                // Active filters
                _buildActiveFilters(),

                // Quick filter buttons
                SizedBox(height: 12.h),
                _buildQuickFilters(),
              ],
            ),
          ),

          // Statistics section
          _buildStatisticsSection(),

          // Movements list
          Expanded(
            child: _buildMovementsList(state),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveFilters() {
    final state = ref.watch(stockMovementManagementProvider);
    final filters = <Widget>[];

    if (state.selectedWarehouse != null) {
      filters.add(FilterChipWidget(
        label: 'مخزن محدد',
        onDeleted: () => ref
            .read(stockMovementManagementProvider.notifier)
            .filterByWarehouse(null),
      ));
    }

    if (state.selectedProduct != null) {
      filters.add(FilterChipWidget(
        label: 'منتج محدد',
        onDeleted: () => ref
            .read(stockMovementManagementProvider.notifier)
            .filterByProduct(null),
      ));
    }

    if (state.selectedMovementType != null) {
      filters.add(FilterChipWidget(
        label: _getMovementTypeText(state.selectedMovementType!),
        onDeleted: () => ref
            .read(stockMovementManagementProvider.notifier)
            .filterByMovementType(null),
      ));
    }

    if (state.fromDate != null || state.toDate != null) {
      filters.add(FilterChipWidget(
        label: 'فترة زمنية',
        onDeleted: () => ref
            .read(stockMovementManagementProvider.notifier)
            .filterByDateRange(null, null),
      ));
    }

    if (filters.isNotEmpty) {
      filters.add(
        TextButton.icon(
          onPressed: () =>
              ref.read(stockMovementManagementProvider.notifier).clearFilters(),
          icon: const Icon(Icons.clear_all, color: Colors.white70, size: 16),
          label: Text(
            'مسح الكل',
            style: AppTextStyles.bodySmall.copyWith(color: Colors.white70),
          ),
        ),
      );
    }

    return filters.isNotEmpty
        ? SizedBox(
            height: 40.h,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: filters.length,
              separatorBuilder: (context, index) => SizedBox(width: 8.w),
              itemBuilder: (context, index) => filters[index],
            ),
          )
        : const SizedBox.shrink();
  }

  Widget _buildQuickFilters() {
    return Row(
      children: [
        Expanded(
          child: _buildQuickFilterButton(
            'اليوم',
            Icons.today,
            () => ref
                .read(stockMovementManagementProvider.notifier)
                .loadTodayMovements(),
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: _buildQuickFilterButton(
            'الأسبوع',
            Icons.date_range,
            () => ref
                .read(stockMovementManagementProvider.notifier)
                .loadWeeklyMovements(),
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: _buildQuickFilterButton(
            'الشهر',
            Icons.calendar_month,
            () => ref
                .read(stockMovementManagementProvider.notifier)
                .loadMonthlyMovements(),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickFilterButton(
      String label, IconData icon, VoidCallback onPressed) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16.sp, color: Colors.white70),
      label: Text(
        label,
        style: AppTextStyles.bodySmall.copyWith(color: Colors.white70),
      ),
      style: OutlinedButton.styleFrom(
        side: const BorderSide(color: Colors.white30),
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      ),
    );
  }

  Widget _buildStatisticsSection() {
    return Consumer(
      builder: (context, ref, child) {
        final statisticsAsync = ref.watch(movementStatisticsProvider);

        return statisticsAsync.when(
          data: (statistics) => Container(
            margin: EdgeInsets.all(16.w),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي الحركات',
                    '${statistics['total_movements'] ?? 0}',
                    Icons.swap_horiz,
                    AppColors.primary,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildStatCard(
                    'حركات الإدخال',
                    '${statistics['inbound_movements'] ?? 0}',
                    Icons.arrow_downward,
                    Colors.green,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildStatCard(
                    'حركات الإخراج',
                    '${statistics['outbound_movements'] ?? 0}',
                    Icons.arrow_upward,
                    Colors.red,
                  ),
                ),
              ],
            ),
          ),
          loading: () => const SizedBox.shrink(),
          error: (error, stack) => const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.sp),
          SizedBox(height: 4.h),
          Text(
            value,
            style: AppTextStyles.titleMedium.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMovementsList(StockMovementManagementState state) {
    if (state.isLoading) {
      return const LoadingWidget();
    }

    if (state.error != null) {
      return ErrorDisplayWidget(
        error: state.error!,
        onRetry: () =>
            ref.read(stockMovementManagementProvider.notifier).refresh(),
      );
    }

    if (state.filteredMovements.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.swap_horiz,
        title: state.searchQuery.isNotEmpty ? 'لا توجد نتائج' : 'لا توجد حركات',
        subtitle: state.searchQuery.isNotEmpty
            ? 'جرب تغيير كلمات البحث أو المرشحات'
            : 'لا توجد حركات مخزون حالياً',
        actionText: 'تحديث',
        onActionPressed: () =>
            ref.read(stockMovementManagementProvider.notifier).refresh(),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: state.filteredMovements.length,
      itemBuilder: (context, index) {
        final movement = state.filteredMovements[index];
        return StockMovementCard(
          movement: movement,
          onTap: () => _showMovementDetails(movement),
        );
      },
    );
  }

  void _showFiltersDialog() {
    showDialog(
      context: context,
      builder: (context) => const StockMovementFiltersDialog(),
    );
  }

  void _showMovementDetails(StockMovement movement) {
    // Navigate to movement details page or show dialog
    // Implementation depends on your navigation structure
  }

  String _getMovementTypeText(String type) {
    switch (type) {
      case 'in':
        return 'إدخال';
      case 'out':
        return 'إخراج';
      case 'transfer':
        return 'نقل';
      case 'adjustment':
        return 'تسوية';
      default:
        return type;
    }
  }
}
