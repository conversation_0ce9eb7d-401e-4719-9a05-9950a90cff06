import 'package:flutter/material.dart';

class AddItemDialog extends StatelessWidget {
  final Map<String, dynamic>? initial;
  const AddItemDialog({super.key, this.initial});

  @override
  Widget build(BuildContext context) {
    final nameController = TextEditingController(text: initial?['name'] ?? '');
    final qtyController = TextEditingController(text: initial?['quantity']?.toString() ?? '');
    final priceController = TextEditingController(text: initial?['price']?.toString() ?? '');

    return AlertDialog(
      title: const Text('إضافة صنف'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(controller: nameController, decoration: const InputDecoration(labelText: 'الاسم')),
          TextField(controller: qtyController, keyboardType: TextInputType.number, decoration: const InputDecoration(labelText: 'الكمية')),
          TextField(controller: priceController, keyboardType: TextInputType.number, decoration: const InputDecoration(labelText: 'السعر')),
        ],
      ),
      actions: [
        TextButton(onPressed: () => Navigator.pop(context), child: const Text('إلغاء')),
        ElevatedButton(
          onPressed: () {
            final item = {
              'name': nameController.text,
              'quantity': int.tryParse(qtyController.text) ?? 0,
              'price': double.tryParse(priceController.text) ?? 0,
              'total': (int.tryParse(qtyController.text) ?? 0) * (double.tryParse(priceController.text) ?? 0),
            };
            Navigator.pop(context, item);
          },
          child: const Text('إضافة'),
        ),
      ],
    );
  }
}