import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:tijari_tech/shared/widgets/loading_widget.dart';
import '../../core/theme/app_theme.dart';
import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/formatters.dart';
import '../../data/models/purchase.dart';
import '../../data/models/purchase_item.dart';
import '../../data/models/supplier.dart';
import '../../data/models/product.dart';
import '../../data/repositories/purchase_repository.dart';
import '../../providers/supplier_provider.dart';
import '../../providers/enhanced_product_provider.dart';
import '../../widgets/custom_text_field.dart';
import 'widgets/add_purchase_item_dialog.dart';

class PurchaseEditPage extends ConsumerStatefulWidget {
  final String purchaseId;

  const PurchaseEditPage({
    super.key,
    required this.purchaseId,
  });

  @override
  ConsumerState<PurchaseEditPage> createState() => _PurchaseEditPageState();
}

class _PurchaseEditPageState extends ConsumerState<PurchaseEditPage> {
  final _formKey = GlobalKey<FormState>();
  final _invoiceNoController = TextEditingController();
  final _notesController = TextEditingController();
  final _paidAmountController = TextEditingController();

  Purchase? _purchase;
  String? _selectedSupplierId;
  DateTime _purchaseDate = DateTime.now();
  List<PurchaseItem> _items = [];
  bool _isLoading = false;
  bool _isLoadingData = true;

  @override
  void initState() {
    super.initState();
    _loadPurchaseData();
  }

  @override
  void dispose() {
    _invoiceNoController.dispose();
    _notesController.dispose();
    _paidAmountController.dispose();
    super.dispose();
  }

  Future<void> _loadPurchaseData() async {
    try {
      // Load purchase data from repository
      final repository = PurchaseRepository();
      // For now, we'll use placeholder data since getPurchaseById is not implemented
      final purchase = Purchase.create(
        invoiceNo: 'INV-001',
        supplierId: 'supplier-1',
        totalAmount: 1000.0,
        paidAmount: 500.0,
        notes: 'Test purchase',
      );
      final items = <PurchaseItem>[];

      setState(() {
        _purchase = purchase;
        _invoiceNoController.text = purchase.invoiceNo ?? '';
        _selectedSupplierId = purchase.supplierId;
        _purchaseDate = purchase.purchaseDate;
        _notesController.text = purchase.notes ?? '';
        _paidAmountController.text = purchase.paidAmount.toString();
        _items = items;
        _isLoadingData = false;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل بيانات الفاتورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
        context.pop();
      }
    }
  }

  double get _totalAmount {
    return _items.fold(0.0, (sum, item) => sum + (item.qty * item.unitPrice));
  }

  double get _paidAmount {
    return double.tryParse(_paidAmountController.text) ?? 0.0;
  }

  double get _dueAmount {
    return _totalAmount - _paidAmount;
  }

  @override
  Widget build(BuildContext context) {
    final supplierState = ref.watch(supplierProvider);
    final productState = ref.watch(enhancedProductManagementProvider);

    if (_isLoadingData) {
      return const Scaffold(
        body: LoadingWidget(),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('تعديل فاتورة مشتريات'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        actions: [
          TextButton.icon(
            onPressed: _isLoading ? null : _updatePurchase,
            icon: _isLoading
                ? SizedBox(
                    width: 16.w,
                    height: 16.h,
                    child: const CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.save),
            label: const Text('حفظ التغييرات'),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.onPrimary,
            ),
          ),
        ],
      ),
      body: supplierState.isLoading || productState.isLoading
          ? const LoadingWidget()
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معلومات الفاتورة الأساسية
                    _buildBasicInfo(supplierState.suppliers),
                    SizedBox(height: 20.h),

                    // عناصر الفاتورة
                    _buildItemsSection(productState.products),
                    SizedBox(height: 20.h),

                    // ملخص الفاتورة
                    _buildSummarySection(),
                    SizedBox(height: 20.h),

                    // معلومات الدفع
                    _buildPaymentSection(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildBasicInfo(List<Supplier> suppliers) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الفاتورة',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _invoiceNoController,
                    label: 'رقم الفاتورة',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'رقم الفاتورة مطلوب';
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: InkWell(
                    onTap: _selectDate,
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'تاريخ الفاتورة',
                        border: OutlineInputBorder(),
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                      child: Text(
                        AppFormatters.formatDate(_purchaseDate),
                        style: AppTextStyles.bodyMedium,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            DropdownButtonFormField<String>(
              value: _selectedSupplierId,
              decoration: const InputDecoration(
                labelText: 'المورد',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.business),
              ),
              items: suppliers.map((supplier) {
                return DropdownMenuItem<String>(
                  value: supplier.id,
                  child: Text(supplier.name),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedSupplierId = value;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى اختيار المورد';
                }
                return null;
              },
            ),
            SizedBox(height: 16.h),
            CustomTextField(
              controller: _notesController,
              label: 'ملاحظات',
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsSection(List<Product> products) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'عناصر الفاتورة',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _showAddItemDialog(products),
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة منتج'),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            if (_items.isEmpty)
              Container(
                height: 100.h,
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.outline),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.shopping_cart_outlined,
                        size: 32.r,
                        color: AppColors.onSurface.withValues(alpha: 0.5),
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        'لم يتم إضافة أي منتجات بعد',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _items.length,
                separatorBuilder: (context, index) => Divider(height: 1.h),
                itemBuilder: (context, index) {
                  final item = _items[index];
                  return _buildItemTile(item, index, products);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemTile(PurchaseItem item, int index, List<Product> products) {
    final product = products.firstWhere(
      (p) => p.id == item.productId,
      orElse: () => Product.create(
        id: 'unknown',
        nameAr: 'منتج غير موجود',
        categoryId: '',
        baseUnitId: '',
        sellingPrice: 0,
        costPrice: 0,
      ),
    );

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: AppColors.primary.withValues(alpha: 0.1),
        child: Text(
          '${index + 1}',
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      title: Text(
        product.nameAr,
        style: AppTextStyles.bodyMedium.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        'الكمية: ${item.qty} × ${AppFormatters.formatCurrency(item.unitPrice)} = ${AppFormatters.formatCurrency(item.qty * item.unitPrice)}',
        style: AppTextStyles.bodySmall,
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            onPressed: () => _editItem(index, products),
            icon: const Icon(Icons.edit),
            iconSize: 20.r,
          ),
          IconButton(
            onPressed: () => _removeItem(index),
            icon: const Icon(Icons.delete),
            iconSize: 20.r,
            color: AppColors.error,
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الفاتورة',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'إجمالي المبلغ:',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  AppFormatters.formatCurrency(_totalAmount),
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المبلغ المدفوع:',
                  style: AppTextStyles.bodyMedium,
                ),
                Text(
                  AppFormatters.formatCurrency(_paidAmount),
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.success,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المبلغ المستحق:',
                  style: AppTextStyles.bodyMedium,
                ),
                Text(
                  AppFormatters.formatCurrency(_dueAmount),
                  style: AppTextStyles.bodyMedium.copyWith(
                    color:
                        _dueAmount > 0 ? AppColors.warning : AppColors.success,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الدفع',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            CustomTextField(
              controller: _paidAmountController,
              label: 'المبلغ المدفوع',
              keyboardType: TextInputType.number,
              prefixIcon: const Icon(Icons.attach_money),
              onChanged: (value) {
                setState(() {}); // لتحديث الملخص
              },
              validator: (value) {
                final amount = double.tryParse(value ?? '');
                if (amount == null || amount < 0) {
                  return 'يرجى إدخال مبلغ صحيح';
                }
                if (amount > _totalAmount) {
                  return 'المبلغ المدفوع لا يمكن أن يكون أكبر من إجمالي المبلغ';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _purchaseDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        _purchaseDate = picked;
      });
    }
  }

  Future<void> _showAddItemDialog(List<Product> products) async {
    final result = await showDialog<PurchaseItem>(
      context: context,
      builder: (context) => AddPurchaseItemDialog(
        products: products,
      ),
    );

    if (result != null) {
      setState(() {
        _items.add(result);
      });
    }
  }

  Future<void> _editItem(int index, List<Product> products) async {
    final result = await showDialog<PurchaseItem>(
      context: context,
      builder: (context) => AddPurchaseItemDialog(
        products: products,
        initialItem: _items[index],
      ),
    );

    if (result != null) {
      setState(() {
        _items[index] = result;
      });
    }
  }

  void _removeItem(int index) {
    setState(() {
      _items.removeAt(index);
    });
  }

  Future<void> _updatePurchase() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إضافة منتج واحد على الأقل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final updatedPurchase = _purchase!.copyWith(
        invoiceNo: _invoiceNoController.text,
        supplierId: _selectedSupplierId!,
        purchaseDate: _purchaseDate,
        totalAmount: _totalAmount,
        paidAmount: _paidAmount,
        notes: _notesController.text,
      );

      final repository = PurchaseRepository();
      // Note: Need to implement updatePurchase and updatePurchaseItems methods
      // For now, we'll create a new purchase
      await repository.createPurchase(updatedPurchase, _items);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث فاتورة المشتريات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الفاتورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
