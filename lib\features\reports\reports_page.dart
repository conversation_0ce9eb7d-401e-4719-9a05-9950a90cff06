import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../router/routes.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/responsive_wrapper.dart';

class ReportsPage extends ConsumerWidget {
  const ReportsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MainLayout(
      title: 'التقارير',
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Quick Stats
            _buildQuickStats(context),
            SizedBox(height: 24.h),

            // Report Categories
            _buildReportCategories(context),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ملخص سريع',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 13.h),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 4,
          desktopColumns: 4,
          childAspectRatio: 1.0,
          children: [
            _buildStatCard(
              context,
              title: 'مبيعات اليوم',
              value: '12, ر.س',
              icon: Icons.trending_up_outlined,
              color: AppColors.success,
            ),
            _buildStatCard(
              context,
              title: 'مشتريات اليوم',
              value: '8,200 ر.س',
              icon: Icons.trending_down_outlined,
              color: AppColors.error,
            ),
            _buildStatCard(
              context,
              title: 'صافي الربح',
              value: '4,550 ر.س',
              icon: Icons.account_balance_outlined,
              color: AppColors.primary,
            ),
            _buildStatCard(
              context,
              title: 'عدد الفواتير',
              value: '47',
              icon: Icons.receipt_long_outlined,
              color: AppColors.info,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32.r,
              color: color,
            ),
            SizedBox(height: 8.h),
            Text(
              value,
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              title,
              style: AppTextStyles.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportCategories(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'أنواع التقارير',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 2,
          desktopColumns: 3,
          children: [
            _buildReportCard(
              context,
              title: 'لوحة التحكم المالية',
              subtitle: 'نظرة شاملة على الوضع المالي',
              icon: Icons.dashboard_outlined,
              color: AppColors.primary,
              reports: [
                'مؤشرات الأداء الرئيسية',
                'التدفق النقدي',
                'المعاملات الأخيرة',
                'تحليل الربحية',
              ],
              onTap: () => context.go('/reports/dashboard'),
            ),
            _buildReportCard(
              context,
              title: 'تقارير المبيعات والمشتريات',
              subtitle: 'تقارير مفصلة للمبيعات والمشتريات',
              icon: Icons.point_of_sale_outlined,
              color: AppColors.success,
              reports: [
                'تقرير المبيعات اليومي',
                'تقرير المشتريات الشهري',
                'مقارنة الفترات',
                'تحليل الاتجاهات',
              ],
              onTap: () => context.go('/reports/sales-purchases'),
            ),
            _buildReportCard(
              context,
              title: 'تقارير الأرباح والخسائر',
              subtitle: 'تحليل مالي مفصل للربحية',
              icon: Icons.analytics_outlined,
              color: AppColors.info,
              reports: [
                'بيان الأرباح والخسائر',
                'تحليل الهوامش',
                'مؤشرات الربحية',
                'اتجاهات الأداء',
              ],
              onTap: () => context.go('/reports/profit-loss'),
            ),
            _buildReportCard(
              context,
              title: 'تقارير المخزون',
              subtitle: 'تقارير المخزون والمنتجات',
              icon: Icons.inventory_outlined,
              color: AppColors.info,
              reports: [
                'تقرير المخزون الحالي',
                'تقرير حركة المخزون',
                'تقرير المنتجات الناقصة',
                'تقرير المنتجات المنتهية',
              ],
              onTap: () => context.go(AppRoutes.inventoryReport),
            ),
            _buildReportCard(
              context,
              title: 'تقارير العملاء والموردين',
              subtitle: 'تقارير حسابات العملاء والموردين',
              icon: Icons.people_alt_outlined,
              color: AppColors.warning,
              reports: [
                'حسابات العملاء',
                'حسابات الموردين',
                'المبالغ المستحقة',
                'تحليل الحسابات',
              ],
              onTap: () => context.go('/reports/accounts'),
            ),
            _buildReportCard(
              context,
              title: 'تقارير المخزون',
              subtitle: 'تقارير العملاء والموردين',
              icon: Icons.account_balance_wallet_outlined,
              color: AppColors.warning,
              reports: [
                'تقرير أرصدة العملاء',
                'تقرير أرصدة الموردين',
                'تقرير الديون المستحقة',
                'تقرير المدفوعات',
              ],
              onTap: () => context.go(AppRoutes.customerReport),
            ),
            _buildReportCard(
              context,
              title: 'تقارير الموظفين',
              subtitle: 'تقارير الرواتب والحضور',
              icon: Icons.people_outline,
              color: AppColors.accent,
              reports: [
                'تقرير الرواتب',
                'تقرير الحضور',
                'تقرير الإجازات',
                'تقرير الأداء',
              ],
              onTap: () {
                // TODO: Navigate to employee reports
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildReportCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required List<String> reports,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(20.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(12.r),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Icon(
                      icon,
                      size: 32.r,
                      color: color,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16.r,
                    color: Colors.grey[400],
                  ),
                ],
              ),
              SizedBox(height: 16.h),
              Text(
                title,
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                subtitle,
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 12.h),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: reports
                    .take(3)
                    .map((report) => Padding(
                          padding: EdgeInsets.only(bottom: 4.h),
                          child: Row(
                            children: [
                              Icon(
                                Icons.circle,
                                size: 6.r,
                                color: color,
                              ),
                              SizedBox(width: 8.w),
                              Expanded(
                                child: Text(
                                  report,
                                  style: AppTextStyles.bodySmall.copyWith(
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ))
                    .toList(),
              ),
              if (reports.length > 3) ...[
                SizedBox(height: 4.h),
                Text(
                  'و ${reports.length - 3} تقارير أخرى...',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
