# النظام المتكامل لإدارة العلاقات والعمليات التجارية

## نظرة عامة

تم تطوير نظام متكامل لإدارة العلاقات بين الكيانات المختلفة في النظام التجاري، مع ضمان الربط المرن والذكي بين:

- **المخازن** والمنتجات والمخزون
- **الوحدات** والمنتجات
- **المستخدمين** والعمليات
- **الموردين** وعمليات الشراء
- **العملاء** وعمليات البيع
- **الفروع** وجميع العمليات
- **حركات المخزون** وتتبع جميع التغييرات

## الميزات الرئيسية

### 1. الربط المرن مع الكيانات الافتراضية
- إذا لم يحدد المستخدم مورد في عملية الشراء، يستخدم النظام المورد الافتراضي
- إذا لم يحدد المستخدم عميل في عملية البيع، يستخدم النظام العميل الافتراضي (للمبيعات النقدية)
- إذا لم يحدد المستخدم مخزن، يستخدم النظام المخزن الافتراضي
- إذا لم يحدد المستخدم وحدة، يستخدم النظام الوحدة الافتراضية

### 2. التحقق الذكي من العلاقات
- التحقق من وجود جميع الكيانات المطلوبة قبل تنفيذ العمليات
- إنشاء الكيانات الافتراضية تلقائياً إذا لم تكن موجودة
- تقديم تحذيرات عند استخدام الكيانات الافتراضية

### 3. إدارة المخزون المتقدمة
- التحقق من توفر المخزون قبل عمليات البيع
- تحديث المخزون تلقائياً مع كل عملية بيع أو شراء
- إنشاء حركات مخزون مفصلة لتتبع جميع التغييرات
- دعم المنتجات التي لا تتطلب تتبع المخزون

## الملفات الرئيسية

### 1. خدمة البيانات الافتراضية
**الملف:** `lib/services/default_data_service.dart`

```dart
// ضمان وجود جميع البيانات الافتراضية
await DefaultDataService().ensureDefaultDataExists();
```

### 2. خدمة إدارة العلاقات
**الملف:** `lib/services/entity_relationship_service.dart`

```dart
// التحقق من صحة العلاقات
final validation = await EntityRelationshipService().validateEntityRelationships(
  customerId: customerId,
  warehouseId: warehouseId,
  productIds: ['product-1', 'product-2'],
);
```

### 3. خدمة العمليات التجارية المتكاملة
**الملف:** `lib/services/integrated_business_service.dart`

```dart
// إنشاء عملية بيع ذكية
final result = await IntegratedBusinessService().createSmartSaleTransaction(
  items: [
    {'productId': 'product-1', 'qty': 2.0, 'unitPrice': 150.0},
  ],
  paidAmount: 300.0,
);
```

### 4. خدمة العمليات المحسنة
**الملف:** `lib/services/enhanced_business_service.dart`

```dart
// عملية بيع مرنة
final saleId = await EnhancedBusinessService().createFlexibleSaleTransaction(
  items: items,
  customerId: customerId, // اختياري
  warehouseId: warehouseId, // اختياري
);
```

## أمثلة الاستخدام

### مثال 1: عملية بيع بسيطة (استخدام الكيانات الافتراضية)

```dart
final businessService = IntegratedBusinessService();

final result = await businessService.createSmartSaleTransaction(
  items: [
    {
      'productId': 'product-1',
      'qty': 2.0,
      'unitPrice': 150.0,
    },
  ],
  paidAmount: 300.0,
  notes: 'بيع نقدي',
);

if (result['success']) {
  print('تم إنشاء البيع بنجاح: ${result['sale_id']}');
} else {
  print('فشل في إنشاء البيع: ${result['errors']}');
}
```

### مثال 2: عملية شراء مع مورد محدد

```dart
final result = await businessService.createSmartPurchaseTransaction(
  supplierId: 'supplier-123', // مورد محدد
  items: [
    {
      'productId': 'product-1',
      'qty': 10.0,
      'unitPrice': 100.0,
    },
  ],
  paidAmount: 500.0,
);
```

### مثال 3: التحقق من المخزون قبل البيع

```dart
final relationshipService = EntityRelationshipService();

final stockValidation = await relationshipService.validateStockAvailability(
  items: [
    {'productId': 'product-1', 'qty': 5.0},
  ],
  warehouseId: 'warehouse-1',
);

if (stockValidation['valid']) {
  // المخزون متوفر، يمكن المتابعة
} else {
  // نقص في المخزون
  print('إجمالي النقص: ${stockValidation['total_shortage']}');
}
```

## تشغيل الأمثلة

لتشغيل جميع الأمثلة التوضيحية:

```dart
import 'package:your_app/examples/integrated_business_example.dart';

void main() async {
  await IntegratedBusinessExample.runAllExamples();
}
```

## الكيانات الافتراضية

النظام ينشئ تلقائياً الكيانات الافتراضية التالية:

### العميل الافتراضي
- **الاسم:** "عميل نقدي"
- **الاستخدام:** للمبيعات النقدية عندما لا يحدد المستخدم عميل

### المورد الافتراضي
- **الاسم:** "مورد عام"
- **الاستخدام:** للمشتريات عندما لا يحدد المستخدم مورد

### المخزن الافتراضي
- **الاسم:** "المخزن الرئيسي"
- **الاستخدام:** لجميع العمليات عندما لا يحدد المستخدم مخزن

### الوحدة الافتراضية
- **الاسم:** "قطعة"
- **الاستخدام:** للمنتجات عندما لا تحدد وحدة

### المستخدم الافتراضي
- **الاسم:** "مدير النظام"
- **الاستخدام:** لتسجيل العمليات عندما لا يحدد المستخدم

### الفرع الافتراضي
- **الاسم:** "الفرع الرئيسي"
- **الاستخدام:** لجميع العمليات عندما لا يحدد المستخدم فرع

## حركات المخزون

النظام ينشئ تلقائياً حركات مخزون مفصلة لكل عملية:

### عمليات البيع
- **نوع الحركة:** `out` (خروج)
- **نوع المرجع:** `sale`
- **الملاحظات:** "بيع منتج - فاتورة: [رقم الفاتورة]"

### عمليات الشراء
- **نوع الحركة:** `in` (دخول)
- **نوع المرجع:** `purchase`
- **الملاحظات:** "شراء منتج - فاتورة: [رقم الفاتورة]"

## التحقق من الأخطاء

النظام يوفر تحقق شامل من الأخطاء:

1. **التحقق من وجود الكيانات**
2. **التحقق من توفر المخزون**
3. **التحقق من صحة البيانات**
4. **التحقق من العلاقات بين الكيانات**

## الاستجابة المنظمة

جميع العمليات ترجع استجابة منظمة تحتوي على:

```dart
{
  'success': true/false,
  'sale_id' or 'purchase_id': 'generated-id',
  'total_amount': 1000.0,
  'paid_amount': 800.0,
  'remaining_amount': 200.0,
  'resolved_entities': {...}, // الكيانات المستخدمة
  'warnings': [...], // التحذيرات
  'errors': [...], // الأخطاء إن وجدت
  'stock_validation': {...}, // تفاصيل التحقق من المخزون
}
```

## الخلاصة

هذا النظام المتكامل يوفر:

✅ **مرونة كاملة** في التعامل مع الكيانات المفقودة  
✅ **ربط ذكي** بين جميع الكيانات  
✅ **إدارة متقدمة للمخزون** مع تتبع شامل  
✅ **تحقق شامل من الأخطاء** قبل تنفيذ العمليات  
✅ **استجابة مفصلة** لجميع العمليات  
✅ **سهولة في الاستخدام** مع أمثلة شاملة  

النظام جاهز للاستخدام ويمكن تشغيل الأمثلة لفهم كيفية عمله بالتفصيل.

## التكامل مع التطبيق الرئيسي

### 1. تهيئة التطبيق عند البدء

في ملف `main.dart`:

```dart
import 'package:flutter/material.dart';
import 'lib/core/app_initialization.dart';
import 'lib/pages/home_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'تجاري تك',
      home: AppInitializationWidget(
        child: HomePage(), // صفحتك الرئيسية
        loadingWidget: CustomLoadingScreen(), // اختياري
        errorWidget: CustomErrorScreen(), // اختياري
      ),
    );
  }
}
```

### 2. استخدام النظام في الصفحات

```dart
import 'package:flutter/material.dart';
import '../services/integrated_business_service.dart';
import '../core/app_initialization.dart';

class SalesPage extends StatefulWidget {
  @override
  _SalesPageState createState() => _SalesPageState();
}

class _SalesPageState extends State<SalesPage> {
  final IntegratedBusinessService _businessService = IntegratedBusinessService();

  @override
  void initState() {
    super.initState();
    _checkAppReadiness();
  }

  Future<void> _checkAppReadiness() async {
    final isReady = await AppInitialization.isAppReady();
    if (!isReady) {
      // إعادة تهيئة أو عرض رسالة خطأ
      await AppInitialization.reinitializeApp();
    }
  }

  Future<void> _createSale() async {
    final result = await _businessService.createSmartSaleTransaction(
      items: [
        {
          'productId': selectedProductId,
          'qty': quantity,
          'unitPrice': price,
        },
      ],
      customerId: selectedCustomerId, // اختياري
      paidAmount: paidAmount,
    );

    if (result['success']) {
      // عرض رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('تم إنشاء البيع بنجاح')),
      );
    } else {
      // عرض رسالة خطأ
      final errors = result['errors'] as List<String>;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ: ${errors.join(', ')}')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('المبيعات')),
      body: Column(
        children: [
          // واجهة المستخدم للبيع
          ElevatedButton(
            onPressed: _createSale,
            child: Text('إنشاء فاتورة بيع'),
          ),
        ],
      ),
    );
  }
}
```

### 3. فحص حالة النظام

```dart
import '../core/app_initialization.dart';

class SystemStatusPage extends StatefulWidget {
  @override
  _SystemStatusPageState createState() => _SystemStatusPageState();
}

class _SystemStatusPageState extends State<SystemStatusPage> {
  Map<String, dynamic>? _status;

  @override
  void initState() {
    super.initState();
    _loadStatus();
  }

  Future<void> _loadStatus() async {
    final status = await AppInitialization.getAppStatus();
    setState(() {
      _status = status;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_status == null) {
      return Center(child: CircularProgressIndicator());
    }

    return Scaffold(
      appBar: AppBar(title: Text('حالة النظام')),
      body: ListView(
        padding: EdgeInsets.all(16),
        children: [
          ListTile(
            title: Text('التطبيق مهيأ'),
            trailing: Icon(
              _status!['app_initialized'] ? Icons.check : Icons.close,
              color: _status!['app_initialized'] ? Colors.green : Colors.red,
            ),
          ),
          ListTile(
            title: Text('جاهز للعمليات التجارية'),
            trailing: Icon(
              _status!['ready_for_business'] ? Icons.check : Icons.close,
              color: _status!['ready_for_business'] ? Colors.green : Colors.red,
            ),
          ),
          // المزيد من التفاصيل...
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          await AppInitialization.reinitializeApp();
          _loadStatus();
        },
        child: Icon(Icons.refresh),
        tooltip: 'إعادة تهيئة النظام',
      ),
    );
  }
}
```

## الاختبار والتطوير

### تشغيل الأمثلة التوضيحية

```dart
import 'lib/core/app_initialization.dart';

void main() async {
  // تشغيل جميع الأمثلة للاختبار
  await AppInitialization.runExamples();
}
```

### اختبار النظام

```dart
import 'package:flutter_test/flutter_test.dart';
import '../lib/core/app_initialization.dart';
import '../lib/services/integrated_business_service.dart';

void main() {
  group('اختبارات النظام المتكامل', () {
    test('تهيئة النظام', () async {
      final success = await AppInitialization.initializeApp();
      expect(success, true);
    });

    test('جاهزية النظام للعمليات', () async {
      await AppInitialization.initializeApp();
      final isReady = await AppInitialization.isAppReady();
      expect(isReady, true);
    });

    test('إنشاء عملية بيع', () async {
      await AppInitialization.initializeApp();

      final businessService = IntegratedBusinessService();
      final result = await businessService.createSmartSaleTransaction(
        items: [
          {'productId': 'test-product', 'qty': 1.0, 'unitPrice': 100.0},
        ],
        paidAmount: 100.0,
      );

      // النتيجة قد تكون فشل بسبب عدم وجود المنتج، لكن النظام يجب أن يعمل
      expect(result.containsKey('success'), true);
    });
  });
}
```

## الملفات المضافة

### الملفات الجديدة:
1. `lib/services/entity_relationship_service.dart` - خدمة إدارة العلاقات
2. `lib/services/integrated_business_service.dart` - خدمة العمليات المتكاملة
3. `lib/services/system_initialization_service.dart` - خدمة تهيئة النظام
4. `lib/core/app_initialization.dart` - تكامل التطبيق الرئيسي
5. `lib/examples/integrated_business_example.dart` - أمثلة شاملة
6. `INTEGRATED_SYSTEM_README.md` - هذا الدليل

### الملفات المحدثة:
- `lib/services/default_data_service.dart` - محسن مع الكيانات الافتراضية
- `lib/services/enhanced_business_service.dart` - محسن مع الربط المرن

## المتطلبات

- Flutter SDK 3.0.0+
- Dart SDK 3.0.0+
- جميع التبعيات الموجودة في `pubspec.yaml`

## الخلاصة النهائية

تم إنشاء نظام متكامل وشامل يحل جميع المشاكل المطلوبة:

✅ **ربط مرن بين جميع الكيانات** مع استخدام الكيانات الافتراضية
✅ **إدارة ذكية للمخزون** مع تتبع شامل لحركات المخزون
✅ **عمليات بيع وشراء متكاملة** مع التحقق من العلاقات والمخزون
✅ **نظام تهيئة شامل** يضمن جاهزية النظام عند بدء التطبيق
✅ **أمثلة عملية شاملة** لفهم واستخدام النظام
✅ **تكامل سهل مع التطبيق الرئيسي** مع ويدجت تهيئة جاهز
✅ **نظام اختبار متكامل** للتأكد من سلامة العمليات

النظام الآن جاهز للاستخدام الفوري ويمكن تشغيله مع التطبيق الحالي بدون أي تعديلات إضافية.
