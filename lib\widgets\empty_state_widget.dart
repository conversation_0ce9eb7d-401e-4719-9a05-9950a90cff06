import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../core/theme/colors.dart';
import 'custom_button.dart';

class EmptyStateWidget extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final String? actionText;
  final VoidCallback? onAction;
  final Color? color;

  const EmptyStateWidget({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    this.actionText,
    this.onAction,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final emptyColor = color ?? Colors.grey[400]!;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 80.r,
              color: emptyColor,
            ),
            SizedBox(height: 24.h),
            Text(
              title,
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            if (actionText != null && onAction != null) ...[
              SizedBox(height: 32.h),
              ElevatedButton(
                onPressed: onAction,
                child: Text(actionText!),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// Specialized empty state widgets
class NoDataWidget extends StatelessWidget {
  final String? message;
  final VoidCallback? onRefresh;

  const NoDataWidget({
    super.key,
    this.message,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.inbox_outlined,
      title: 'لا توجد بيانات',
      subtitle: message ?? 'لا توجد بيانات لعرضها حالياً',
      actionText: onRefresh != null ? 'تحديث' : null,
      onAction: onRefresh,
    );
  }
}

class NoSearchResultsWidget extends StatelessWidget {
  final String searchQuery;
  final VoidCallback? onClearSearch;

  const NoSearchResultsWidget({
    super.key,
    required this.searchQuery,
    this.onClearSearch,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.search_off,
      title: 'لا توجد نتائج',
      subtitle: 'لم يتم العثور على نتائج للبحث عن "$searchQuery"',
      actionText: onClearSearch != null ? 'مسح البحث' : null,
      onAction: onClearSearch,
    );
  }
}

class NoItemsWidget extends StatelessWidget {
  final String itemType;
  final VoidCallback? onAdd;

  const NoItemsWidget({
    super.key,
    required this.itemType,
    this.onAdd,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.add_circle_outline,
      title: 'لا توجد $itemType',
      subtitle: 'لم يتم إضافة أي $itemType بعد',
      actionText: onAdd != null ? 'إضافة $itemType' : null,
      onAction: onAdd,
    );
  }
}
