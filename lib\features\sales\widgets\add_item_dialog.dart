import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/colors.dart';
import '../../../core/theme/text_styles.dart';
import '../../../core/utils/formatters.dart';
import '../../../data/models/product.dart';
import '../../../data/models/sale_item.dart';
import '../../../providers/enhanced_product_provider.dart';
import '../../../widgets/custom_text_field.dart';

class AddItemDialog extends ConsumerStatefulWidget {
  final SaleItem? existingItem;
  final int? editIndex;

  const AddItemDialog({
    super.key,
    this.existingItem,
    this.editIndex,
  });

  @override
  ConsumerState<AddItemDialog> createState() => _AddItemDialogState();
}

class _AddItemDialogState extends ConsumerState<AddItemDialog> {
  final _formKey = GlobalKey<FormState>();
  final _searchController = TextEditingController();
  final _qtyController = TextEditingController();
  final _priceController = TextEditingController();

  Product? _selectedProduct;
  List<Product> _filteredProducts = [];
  bool _showProductList = false;

  @override
  void initState() {
    super.initState();

    // إذا كان هناك عنصر موجود للتعديل
    if (widget.existingItem != null) {
      _qtyController.text = widget.existingItem!.qty.toString();
      _priceController.text = widget.existingItem!.unitPrice.toString();

      // البحث عن المنتج
      final productState = ref.read(enhancedProductManagementProvider);
      _selectedProduct = productState.products.firstWhere(
        (p) => p.id == widget.existingItem!.productId,
        orElse: () => Product.create(
          id: widget.existingItem!.productId,
          nameAr: 'منتج غير معروف',
          costPrice: 0,
          sellingPrice: widget.existingItem!.unitPrice,
        ),
      );
      _searchController.text = _selectedProduct!.nameAr;
    }

    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _qtyController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase();
    if (query.isEmpty) {
      setState(() {
        _filteredProducts = [];
        _showProductList = false;
      });
      return;
    }

    final productState = ref.read(enhancedProductManagementProvider);
    final filtered = productState.products
        .where((product) {
          return product.nameAr.toLowerCase().contains(query) ||
              (product.nameEn?.toLowerCase().contains(query) ?? false) ||
              (product.barcode?.toLowerCase().contains(query) ?? false);
        })
        .take(10)
        .toList();

    setState(() {
      _filteredProducts = filtered;
      _showProductList = filtered.isNotEmpty;
    });
  }

  void _selectProduct(Product product) {
    setState(() {
      _selectedProduct = product;
      _searchController.text = product.nameAr;
      _priceController.text = product.sellingPrice.toString();
      _showProductList = false;
      _filteredProducts = [];
    });
  }

  void _addItem() {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedProduct == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار منتج')),
      );
      return;
    }

    final qty = double.tryParse(_qtyController.text) ?? 0;
    final price = double.tryParse(_priceController.text) ?? 0;

    if (qty <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال كمية صحيحة')),
      );
      return;
    }

    if (price <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال سعر صحيح')),
      );
      return;
    }

    final item = SaleItem.create(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      saleId: '',
      productId: _selectedProduct!.id,
      unitId: _selectedProduct!.baseUnitId ?? '',
      qty: qty,
      unitPrice: price,
    );

    Navigator.of(context).pop({
      'item': item,
      'editIndex': widget.editIndex,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: 500.w,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Flexible(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(20.r),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildProductSearch(),
                      if (_showProductList) _buildProductList(),
                      if (_selectedProduct != null) _buildSelectedProduct(),
                      SizedBox(height: 16.h),
                      _buildQuantityField(),
                      SizedBox(height: 16.h),
                      _buildPriceField(),
                      if (_selectedProduct != null &&
                          _qtyController.text.isNotEmpty &&
                          _priceController.text.isNotEmpty)
                        _buildTotalSection(),
                    ],
                  ),
                ),
              ),
            ),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        color: AppColors.primary,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: Row(
        children: [
          Icon(
            widget.existingItem != null ? Icons.edit : Icons.add_shopping_cart,
            color: Colors.white,
            size: 24.r,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              widget.existingItem != null ? 'تعديل المنتج' : 'إضافة منتج',
              style: AppTextStyles.titleLarge.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildProductSearch() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'البحث عن المنتج',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        CustomTextField(
          controller: _searchController,
          label: 'اسم المنتج أو الباركود',
          prefixIcon: const Icon(Icons.search),
          validator: (value) {
            if (_selectedProduct == null) {
              return 'يرجى اختيار منتج';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildProductList() {
    return Container(
      margin: EdgeInsets.only(top: 8.h),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: _filteredProducts.length,
        itemBuilder: (context, index) {
          final product = _filteredProducts[index];
          return ListTile(
            title: Text(product.nameAr),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (product.barcode != null)
                  Text('الباركود: ${product.barcode}'),
                Text(
                    'السعر: ${AppFormatters.formatCurrency(product.sellingPrice)}'),
                if (product.currentStock != null)
                  Text('المخزون: ${product.currentStock}'),
              ],
            ),
            onTap: () => _selectProduct(product),
          );
        },
      ),
    );
  }

  Widget _buildSelectedProduct() {
    if (_selectedProduct == null) return const SizedBox.shrink();

    return Container(
      margin: EdgeInsets.only(top: 16.h),
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.check_circle, color: Colors.green[600], size: 20.r),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _selectedProduct!.nameAr,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (_selectedProduct!.currentStock != null)
                  Text(
                    'المخزون المتاح: ${_selectedProduct!.currentStock}',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuantityField() {
    return CustomTextField(
      controller: _qtyController,
      label: 'الكمية',
      prefixIcon: const Icon(Icons.inventory),
      keyboardType: TextInputType.number,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال الكمية';
        }
        final qty = double.tryParse(value);
        if (qty == null || qty <= 0) {
          return 'يرجى إدخال كمية صحيحة';
        }
        return null;
      },
      onChanged: (value) => setState(() {}),
    );
  }

  Widget _buildPriceField() {
    return CustomTextField(
      controller: _priceController,
      label: 'سعر الوحدة',
      prefixIcon: const Icon(Icons.attach_money),
      keyboardType: TextInputType.number,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال السعر';
        }
        final price = double.tryParse(value);
        if (price == null || price <= 0) {
          return 'يرجى إدخال سعر صحيح';
        }
        return null;
      },
      onChanged: (value) => setState(() {}),
    );
  }

  Widget _buildTotalSection() {
    final qty = double.tryParse(_qtyController.text) ?? 0;
    final price = double.tryParse(_priceController.text) ?? 0;
    final total = qty * price;

    return Container(
      margin: EdgeInsets.only(top: 16.h),
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.primary.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'الإجمالي:',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            AppFormatters.formatCurrency(total),
            style: AppTextStyles.titleLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions() {
    return Container(
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _addItem,
              child: Text(widget.existingItem != null ? 'تحديث' : 'إضافة'),
            ),
          ),
        ],
      ),
    );
  }
}
