import 'package:json_annotation/json_annotation.dart';
import 'package:tijari_tech/core/constants/database_constants.dart';

part 'stock.g.dart';

@JsonSerializable()
class Stock {
  final String id;
  final String productId;
  final String warehouseId;
  final double quantity;
  final double reservedQuantity; // الكمية المحجوزة
  final double availableQuantity; // الكمية المتاحة
  final double minStock;
  final double maxStock;
  final double reorderPoint;
  final double averageCost; // متوسط التكلفة
  final DateTime? lastMovementDate;
  final DateTime? lastCountDate; // آخر جرد
  final String? location; // موقع المنتج في المخزن
  final String? batchNumber; // رقم الدفعة
  final DateTime? expiryDate; // تاريخ الانتهاء
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool isSynced;

  // Related data (not stored in main table)
  final String? productName;
  final String? productBarcode;
  final String? warehouseName;
  final String? categoryName;
  final String? unitName;

  const Stock({
    required this.id,
    required this.productId,
    required this.warehouseId,
    required this.quantity,
    this.reservedQuantity = 0,
    this.availableQuantity = 0,
    this.minStock = 0,
    this.maxStock = 0,
    this.reorderPoint = 0,
    this.averageCost = 0,
    this.lastMovementDate,
    this.lastCountDate,
    this.location,
    this.batchNumber,
    this.expiryDate,
    this.createdAt,
    this.updatedAt,
    this.isSynced = false,
    this.productName,
    this.productBarcode,
    this.warehouseName,
    this.categoryName,
    this.unitName,
  });

  // JSON serialization
  factory Stock.fromJson(Map<String, dynamic> json) => _$StockFromJson(json);
  Map<String, dynamic> toJson() => _$StockToJson(this);

  // Database mapping
  factory Stock.fromMap(Map<String, dynamic> map) {
    return Stock(
      id: map[DatabaseConstants.columnStockId] as String,
      productId: map[DatabaseConstants.columnStockProductId] as String,
      warehouseId: map[DatabaseConstants.columnStockWarehouseId] as String,
      quantity: (map[DatabaseConstants.columnStockQuantity] as num).toDouble(),
      reservedQuantity: (map['reserved_quantity'] as num?)?.toDouble() ?? 0,
      availableQuantity: (map['available_quantity'] as num?)?.toDouble() ?? 0,
      minStock: (map['min_stock'] as num?)?.toDouble() ?? 0,
      maxStock: (map['max_stock'] as num?)?.toDouble() ?? 0,
      reorderPoint: (map['reorder_point'] as num?)?.toDouble() ?? 0,
      averageCost: (map['average_cost'] as num?)?.toDouble() ?? 0,
      lastMovementDate: map['last_movement_date'] != null
          ? DateTime.parse(map['last_movement_date'] as String)
          : null,
      lastCountDate: map['last_count_date'] != null
          ? DateTime.parse(map['last_count_date'] as String)
          : null,
      location: map['location'] as String?,
      batchNumber: map['batch_number'] as String?,
      expiryDate: map['expiry_date'] != null
          ? DateTime.parse(map['expiry_date'] as String)
          : null,
      createdAt: map[DatabaseConstants.columnStockCreatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnStockCreatedAt] as String)
          : null,
      updatedAt: map[DatabaseConstants.columnStockUpdatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnStockUpdatedAt] as String)
          : null,
      isSynced: (map[DatabaseConstants.columnStockIsSynced] as int? ?? 0) == 1,
      productName: map['product_name'] as String?,
      productBarcode: map['product_barcode'] as String?,
      warehouseName: map['warehouse_name'] as String?,
      categoryName: map['category_name'] as String?,
      unitName: map['unit_name'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnStockId: id,
      DatabaseConstants.columnStockProductId: productId,
      DatabaseConstants.columnStockWarehouseId: warehouseId,
      DatabaseConstants.columnStockQuantity: quantity,
      'reserved_quantity': reservedQuantity,
      'available_quantity': availableQuantity,
      'min_stock': minStock,
      'max_stock': maxStock,
      'reorder_point': reorderPoint,
      'average_cost': averageCost,
      'last_movement_date': lastMovementDate?.toIso8601String(),
      'last_count_date': lastCountDate?.toIso8601String(),
      'location': location,
      'batch_number': batchNumber,
      'expiry_date': expiryDate?.toIso8601String(),
      DatabaseConstants.columnStockCreatedAt: createdAt?.toIso8601String(),
      DatabaseConstants.columnStockUpdatedAt: updatedAt?.toIso8601String(),
      DatabaseConstants.columnStockIsSynced: isSynced ? 1 : 0,
    };
  }

  // Copy with method
  Stock copyWith({
    String? id,
    String? productId,
    String? warehouseId,
    double? quantity,
    double? reservedQuantity,
    double? availableQuantity,
    double? minStock,
    double? maxStock,
    double? reorderPoint,
    double? averageCost,
    DateTime? lastMovementDate,
    DateTime? lastCountDate,
    String? location,
    String? batchNumber,
    DateTime? expiryDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isSynced,
    String? productName,
    String? productBarcode,
    String? warehouseName,
    String? categoryName,
    String? unitName,
  }) {
    return Stock(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      warehouseId: warehouseId ?? this.warehouseId,
      quantity: quantity ?? this.quantity,
      reservedQuantity: reservedQuantity ?? this.reservedQuantity,
      availableQuantity: availableQuantity ?? this.availableQuantity,
      minStock: minStock ?? this.minStock,
      maxStock: maxStock ?? this.maxStock,
      reorderPoint: reorderPoint ?? this.reorderPoint,
      averageCost: averageCost ?? this.averageCost,
      lastMovementDate: lastMovementDate ?? this.lastMovementDate,
      lastCountDate: lastCountDate ?? this.lastCountDate,
      location: location ?? this.location,
      batchNumber: batchNumber ?? this.batchNumber,
      expiryDate: expiryDate ?? this.expiryDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSynced: isSynced ?? this.isSynced,
      productName: productName ?? this.productName,
      productBarcode: productBarcode ?? this.productBarcode,
      warehouseName: warehouseName ?? this.warehouseName,
      categoryName: categoryName ?? this.categoryName,
      unitName: unitName ?? this.unitName,
    );
  }

  // Helper methods
  bool get isLowStock => quantity <= reorderPoint;
  bool get isOutOfStock => quantity <= 0;
  bool get isOverStock => maxStock > 0 && quantity > maxStock;
  bool get hasReservedQuantity => reservedQuantity > 0;
  bool get hasLocation => location != null && location!.isNotEmpty;
  bool get hasBatch => batchNumber != null && batchNumber!.isNotEmpty;
  bool get hasExpiryDate => expiryDate != null;
  bool get isExpired => hasExpiryDate && expiryDate!.isBefore(DateTime.now());
  bool get isNearExpiry {
    if (!hasExpiryDate) return false;
    final daysUntilExpiry = expiryDate!.difference(DateTime.now()).inDays;
    return daysUntilExpiry <= 30; // خلال 30 يوم
  }

  // Get stock status
  StockStatus get stockStatus {
    if (isOutOfStock) return StockStatus.outOfStock;
    if (isLowStock) return StockStatus.lowStock;
    if (isOverStock) return StockStatus.overStock;
    return StockStatus.inStock;
  }

  // Get stock status text
  String get stockStatusText {
    switch (stockStatus) {
      case StockStatus.outOfStock:
        return 'نفد المخزون';
      case StockStatus.lowStock:
        return 'مخزون منخفض';
      case StockStatus.overStock:
        return 'مخزون زائد';
      case StockStatus.inStock:
        return 'متوفر';
    }
  }

  // Calculate stock value
  double get stockValue => quantity * averageCost;

  // Calculate available stock (quantity - reserved)
  double get actualAvailableQuantity => quantity - reservedQuantity;

  // Factory creators
  static Stock create({
    required String id,
    required String productId,
    required String warehouseId,
    double quantity = 0,
    // double qty = 0,
    double reservedQuantity = 0,
    double minStock = 0,
    double maxStock = 0,
    double reorderPoint = 0,
    double averageCost = 0,
    String? location,
    String? batchNumber,
    DateTime? expiryDate,
  }) {
    final now = DateTime.now();
    return Stock(
      id: id,
      productId: productId,
      warehouseId: warehouseId,
      quantity: quantity,
      // quantity: quantity,
      reservedQuantity: reservedQuantity,
      availableQuantity: quantity - reservedQuantity,
      minStock: minStock,
      maxStock: maxStock,
      reorderPoint: reorderPoint,
      averageCost: averageCost,
      location: location,
      batchNumber: batchNumber,
      expiryDate: expiryDate,
      createdAt: now,
      updatedAt: now,
      isSynced: false,
    );
  }

  // Update stock with new timestamp
  Stock updateQuantity({
    required double newQuantity,
    double? newReservedQuantity,
    double? newAverageCost,
  }) {
    final reserved = newReservedQuantity ?? reservedQuantity;
    return copyWith(
      quantity: newQuantity,
      reservedQuantity: reserved,
      availableQuantity: newQuantity - reserved,
      averageCost: newAverageCost ?? averageCost,
      lastMovementDate: DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  Stock markAsSynced() => copyWith(isSynced: true);

  // Equality and hashCode
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Stock && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // String representation
  @override
  String toString() {
    return 'Stock(id: $id, productId: $productId, warehouseId: $warehouseId, quantity: $quantity)';
  }
}

enum StockStatus {
  inStock,
  lowStock,
  outOfStock,
  overStock,
}
