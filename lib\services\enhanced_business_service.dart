// import 'package:tijari_tech/core/exceptions/app_exceptions.dart';
// import 'package:tijari_tech/core/utils/app_utils.dart';
// import 'package:tijari_tech/data/repositories/product_repository.dart';
// import 'package:tijari_tech/data/repositories/sale_repository.dart';
// import 'package:tijari_tech/data/repositories/purchase_repository.dart';
// import 'package:tijari_tech/data/repositories/stock_repository.dart';
// import 'package:tijari_tech/data/models/sale.dart';
// import 'package:tijari_tech/data/models/sale_item.dart';
// import 'package:tijari_tech/data/models/purchase.dart';
// import 'package:tijari_tech/data/models/purchase_item.dart';
// import 'package:tijari_tech/data/models/stock_movement.dart';
// import 'package:tijari_tech/services/default_data_service.dart';
// import 'package:tijari_tech/services/inventory_integration_service.dart';

// /// خدمة العمليات التجارية المحسنة مع الربط المرن
// class EnhancedBusinessService {
//   static final EnhancedBusinessService _instance =
//       EnhancedBusinessService._internal();
//   factory EnhancedBusinessService() => _instance;
//   EnhancedBusinessService._internal();

//   final ProductRepository _productRepository = ProductRepository();
//   final SaleRepository _saleRepository = SaleRepository();
//   final PurchaseRepository _purchaseRepository = PurchaseRepository();
//   final StockRepository _stockRepository = StockRepository();
//   final DefaultDataService _defaultDataService = DefaultDataService();
//   final InventoryIntegrationService _inventoryService =
//       InventoryIntegrationService();

//   /// إنشاء عملية بيع مع الربط المرن
//   Future<String> createFlexibleSaleTransaction({
//     String? customerId, // إذا لم يحدد، سيستخدم العميل الافتراضي
//     required List<Map<String, dynamic>> items,
//     String? warehouseId, // إذا لم يحدد، سيستخدم المخزن الافتراضي
//     String? userId, // إذا لم يحدد، سيستخدم المستخدم الافتراضي
//     String? branchId, // إذا لم يحدد، سيستخدم الفرع الافتراضي
//     String? invoiceNo,
//     DateTime? saleDate,
//     double paidAmount = 0,
//     String? notes,
//   }) async {
//     try {
//       AppUtils.logInfo('بدء عملية بيع مرنة جديدة');

//       // التأكد من وجود البيانات الافتراضية
//       await _defaultDataService.ensureDefaultDataExists();

//       // تحديد العميل (افتراضي إذا لم يحدد)
//       final finalCustomerId =
//           customerId ?? DefaultDataService.defaultCustomerId;

//       // تحديد المخزن (افتراضي إذا لم يحدد)
//       final finalWarehouseId =
//           warehouseId ?? DefaultDataService.defaultWarehouseId;

//       // تحديد المستخدم (افتراضي إذا لم يحدد)
//       final finalUserId = userId ?? DefaultDataService.defaultUserId;

//       // تحديد الفرع (افتراضي إذا لم يحدد)
//       final finalBranchId = branchId ?? DefaultDataService.defaultBranchId;

//       // التحقق من صحة البيانات
//       if (items.isEmpty) {
//         throw ValidationException('يجب إضافة عنصر واحد على الأقل للبيع');
//       }

//       // حساب الإجمالي وإنشاء عناصر البيع
//       double totalAmount = 0;
//       final saleItems = <SaleItem>[];

//       for (final item in items) {
//         final productId = item['productId'] as String;
//         final qty = (item['qty'] as num).toDouble();
//         final unitPrice = (item['unitPrice'] as num).toDouble();
//         final unitId =
//             item['unitId'] as String? ?? DefaultDataService.defaultUnitId;

//         // التحقق من وجود المنتج
//         final product = await _productRepository.getProductById(productId);
//         if (product == null) {
//           throw ValidationException('المنتج غير موجود: $productId');
//         }

//         // التحقق من المخزون إذا كان المنتج يتطلب تتبع المخزون
//         if (product.trackStock) {
//           final stockRecord =
//               await _stockRepository.getStockByProductAndWarehouse(
//             productId,
//             finalWarehouseId,
//           );

//           final currentStock = stockRecord?.actualAvailableQuantity ?? 0;

//           if (qty > currentStock && !product.allowNegativeStock) {
//             throw InsufficientStockException(
//               productId: productId,
//               productName: product.nameAr,
//               requestedQuantity: qty,
//               availableQuantity: currentStock,
//             );
//           }
//         }

//         final totalPrice = qty * unitPrice;
//         totalAmount += totalPrice;

//         final saleItem = SaleItem.create(
//           id: AppUtils.generateId(),
//           saleId: '', // سيتم تعيينه لاحقاً
//           productId: productId,
//           unitId: unitId,
//           qty: qty,
//           unitPrice: unitPrice,
//         );

//         saleItems.add(saleItem);
//       }

//       // إنشاء فاتورة البيع
//       final sale = Sale.create(
//         id: AppUtils.generateId(),
//         customerId: finalCustomerId,
//         invoiceNo: invoiceNo ?? await _saleRepository.generateInvoiceNumber(),
//         branchId: finalBranchId,
//         date: saleDate ?? DateTime.now(),
//         total: totalAmount,
//         paid: paidAmount,
//         notes: notes,
//       );

//       // حفظ عملية البيع
//       final saleId = await _saleRepository.createSale(sale, saleItems);

//       // تحديث المخزون
//       await _updateStockForSale(
//         saleItems: saleItems,
//         warehouseId: finalWarehouseId,
//         userId: finalUserId,
//         branchId: finalBranchId,
//         referenceId: saleId,
//       );

//       AppUtils.logInfo('تم إنشاء عملية البيع بنجاح - ID: $saleId');
//       AppUtils.logInfo('العميل: $finalCustomerId، المخزن: $finalWarehouseId');
//       AppUtils.logInfo('إجمالي المبلغ: $totalAmount، المدفوع: $paidAmount');

//       return saleId;
//     } catch (e) {
//       AppUtils.logError('خطأ في إنشاء عملية البيع المرنة', e);
//       rethrow;
//     }
//   }

//   /// إنشاء عملية شراء مع الربط المرن
//   Future<String> createFlexiblePurchaseTransaction({
//     String? supplierId, // إذا لم يحدد، سيستخدم المورد الافتراضي
//     required List<Map<String, dynamic>> items,
//     String? warehouseId, // إذا لم يحدد، سيستخدم المخزن الافتراضي
//     String? userId, // إذا لم يحدد، سيستخدم المستخدم الافتراضي
//     String? branchId, // إذا لم يحدد، سيستخدم الفرع الافتراضي
//     String? invoiceNo,
//     DateTime? purchaseDate,
//     double paidAmount = 0,
//     String? notes,
//   }) async {
//     try {
//       AppUtils.logInfo('بدء عملية شراء مرنة جديدة');

//       // التأكد من وجود البيانات الافتراضية
//       await _defaultDataService.ensureDefaultDataExists();

//       // تحديد المورد (افتراضي إذا لم يحدد)
//       final finalSupplierId =
//           supplierId ?? DefaultDataService.defaultSupplierId;

//       // تحديد المخزن (افتراضي إذا لم يحدد)
//       final finalWarehouseId =
//           warehouseId ?? DefaultDataService.defaultWarehouseId;

//       // تحديد المستخدم (افتراضي إذا لم يحدد)
//       final finalUserId = userId ?? DefaultDataService.defaultUserId;

//       // تحديد الفرع (افتراضي إذا لم يحدد)
//       final finalBranchId = branchId ?? DefaultDataService.defaultBranchId;

//       // التحقق من صحة البيانات
//       if (items.isEmpty) {
//         throw ValidationException('يجب إضافة عنصر واحد على الأقل للشراء');
//       }

//       // حساب الإجمالي وإنشاء عناصر الشراء
//       double totalAmount = 0;
//       final purchaseItems = <PurchaseItem>[];

//       for (final item in items) {
//         final productId = item['productId'] as String;
//         final qty = (item['qty'] as num).toDouble();
//         final unitPrice = (item['unitPrice'] as num).toDouble();
//         final unitId =
//             item['unitId'] as String? ?? DefaultDataService.defaultUnitId;

//         // التحقق من وجود المنتج
//         final product = await _productRepository.getProductById(productId);
//         if (product == null) {
//           throw ValidationException('المنتج غير موجود: $productId');
//         }

//         final totalPrice = qty * unitPrice;
//         totalAmount += totalPrice;

//         final purchaseItem = PurchaseItem.create(
//           purchaseId: '', // سيتم تعيينه لاحقاً
//           productId: productId,
//           unitId: unitId,
//           qty: qty,
//           unitPrice: unitPrice,
//         );

//         purchaseItems.add(purchaseItem);
//       }

//       // إنشاء فاتورة الشراء
//       final purchase = Purchase.create(
//         supplierId: finalSupplierId,
//         invoiceNo:
//             invoiceNo ?? await _purchaseRepository.generateInvoiceNumber(),
//         branchId: finalBranchId,
//         purchaseDate: purchaseDate ?? DateTime.now(),
//         totalAmount: totalAmount,
//         paidAmount: paidAmount,
//         notes: notes,
//       );

//       // حفظ عملية الشراء
//       final purchaseId =
//           await _purchaseRepository.createPurchase(purchase, purchaseItems);

//       // تحديث المخزون
//       await _updateStockForPurchase(
//         purchaseItems: purchaseItems,
//         warehouseId: finalWarehouseId,
//         userId: finalUserId,
//         branchId: finalBranchId,
//         referenceId: purchaseId,
//       );

//       AppUtils.logInfo('تم إنشاء عملية الشراء بنجاح - ID: $purchaseId');
//       AppUtils.logInfo('المورد: $finalSupplierId، المخزن: $finalWarehouseId');
//       AppUtils.logInfo('إجمالي المبلغ: $totalAmount، المدفوع: $paidAmount');

//       return purchaseId;
//     } catch (e) {
//       AppUtils.logError('خطأ في إنشاء عملية الشراء المرنة', e);
//       rethrow;
//     }
//   }

//   /// تحديث المخزون لعملية البيع
//   Future<void> _updateStockForSale({
//     required List<SaleItem> saleItems,
//     required String warehouseId,
//     required String userId,
//     required String branchId,
//     required String referenceId,
//   }) async {
//     for (final saleItem in saleItems) {
//       // إنشاء حركة مخزون للبيع (خروج)
//       final stockMovement = StockMovement.create(
//         id: AppUtils.generateId(),
//         productId: saleItem.productId,
//         warehouseId: warehouseId,
//         movementType: 'out',
//         quantity: saleItem.qty,
//         unitCost: saleItem.unitPrice,
//         unitId: saleItem.unitId.isNotEmpty
//             ? saleItem.unitId
//             : 'unit_piece', // Use item's unit or fallback
//         referenceType: 'sale',
//         referenceId: referenceId,
//         referenceNumber: referenceId,
//         notes: 'بيع منتج',
//         createdBy: userId,
//         branchId: branchId,
//       );

//       // تحديث المخزون
//       await _stockRepository.updateStockWithMovement(
//         productId: stockMovement.productId,
//         warehouseId: stockMovement.warehouseId,
//         movementType: stockMovement.movementType,
//         quantity: stockMovement.quantity,
//         unitCost: stockMovement.unitCost,
//         unitId: stockMovement.unitId,
//         referenceType: stockMovement.referenceType,
//         referenceId: stockMovement.referenceId,
//         referenceNumber: stockMovement.referenceNumber,
//         notes: stockMovement.notes,
//         createdBy: stockMovement.createdBy,
//         branchId: stockMovement.branchId,
//       );
//     }
//   }

//   /// تحديث المخزون لعملية الشراء
//   Future<void> _updateStockForPurchase({
//     required List<PurchaseItem> purchaseItems,
//     required String warehouseId,
//     required String userId,
//     required String branchId,
//     required String referenceId,
//   }) async {
//     for (final purchaseItem in purchaseItems) {
//       // تحديث المخزون مباشرة
//       await _stockRepository.updateStockWithMovement(
//         productId: purchaseItem.productId,
//         warehouseId: warehouseId,
//         movementType: 'in',
//         quantity: purchaseItem.qty,
//         unitCost: purchaseItem.unitPrice,
//         unitId: (purchaseItem.unitId?.isNotEmpty == true)
//             ? purchaseItem.unitId!
//             : 'unit_piece', // Use item's unit or fallback
//         referenceType: 'purchase',
//         referenceId: referenceId,
//         referenceNumber: referenceId,
//         notes: 'شراء منتج',
//         createdBy: userId,
//         branchId: branchId,
//       );
//     }
//   }
// }
