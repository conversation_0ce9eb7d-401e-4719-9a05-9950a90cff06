import 'package:sqflite/sqflite.dart' as sql;
import 'package:tijari_tech/core/constants/database_constants.dart';
import 'base_dao.dart';
import '../../models/product.dart';
import '../../../core/utils/app_utils.dart';

class ProductDao extends BaseDao<Product> {
  @override
  String get tableName => DatabaseConstants.tableProducts;

  @override
  Product fromMap(Map<String, dynamic> map) => Product.fromMap(map);

  @override
  Map<String, dynamic> toMap(Product entity) => entity.toMap();

  // ------------------------------------------------------------------
  // Product-specific queries
  // ------------------------------------------------------------------

  /// استرجاع المنتجات حسب الفئة
  Future<List<Product>> findByCategory(String categoryId) async {
    return await findWhere(
      where: '${DatabaseConstants.columnProductCategoryId} = ?',
      whereArgs: [categoryId],
      orderBy: DatabaseConstants.columnProductNameAr,
    );
  }

  /// استرجاع منتج حسب الباركود
  Future<Product?> findByBarcode(String barcode) async {
    final products = await findWhere(
      where: '${DatabaseConstants.columnProductBarcode} = ?',
      whereArgs: [barcode],
      limit: 1,
    );
    return products.isNotEmpty ? products.first : null;
  }

  /// البحث عن المنتجات حسب الاسم
  Future<List<Product>> searchByName(String searchTerm) async {
    return await findWhere(
      where:
          '${DatabaseConstants.columnProductNameAr} LIKE ? OR ${DatabaseConstants.columnProductNameEn} LIKE ?',
      whereArgs: ['%$searchTerm%', '%$searchTerm%'],
      orderBy: DatabaseConstants.columnProductNameAr,
    );
  }

  /// استرجاع المنتجات النشطة فقط
  Future<List<Product>> getActiveProducts() async {
    return await findWhere(
      where: '${DatabaseConstants.columnProductIsActive} = 1',
      orderBy: DatabaseConstants.columnProductNameAr,
    );
  }

  /// استرجاع المنتجات ذات المخزون المنخفض
  Future<List<Map<String, dynamic>>> getProductsWithLowStock() async {
    final sql = '''
      SELECT 
        p.*, 
        s.${DatabaseConstants.columnStockQuantity} as current_stock
      FROM ${DatabaseConstants.tableProducts} p
      LEFT JOIN ${DatabaseConstants.tableStocks} s 
        ON p.${DatabaseConstants.columnProductId} = s.${DatabaseConstants.columnStockProductId}
      WHERE p.${DatabaseConstants.columnProductDeletedAt} IS NULL 
        AND p.${DatabaseConstants.columnProductTrackStock} = 1
        AND (s.${DatabaseConstants.columnStockQuantity} <= p.${DatabaseConstants.columnProductMinStock} 
             OR s.${DatabaseConstants.columnStockQuantity} IS NULL)
      ORDER BY p.${DatabaseConstants.columnProductNameAr}
    ''';
    return await rawQuery(sql);
  }

  /// استرجاع المنتجات مع معلومات المخزون
  Future<List<Map<String, dynamic>>> getProductsWithStock(
      {String? warehouseId}) async {
    String sql = '''
      SELECT 
        p.*,
        s.${DatabaseConstants.columnStockQuantity} as current_stock,
        w.${DatabaseConstants.columnWarehouseName} as warehouse_name,
        c.${DatabaseConstants.columnCategoryNameAr} as category_name,
        u.${DatabaseConstants.columnUnitName} as unit_name
      FROM ${DatabaseConstants.tableProducts} p
      LEFT JOIN ${DatabaseConstants.tableStocks} s 
        ON p.${DatabaseConstants.columnProductId} = s.${DatabaseConstants.columnStockProductId}
      LEFT JOIN ${DatabaseConstants.tableWarehouses} w 
        ON s.${DatabaseConstants.columnStockWarehouseId} = w.${DatabaseConstants.columnWarehouseId}
      LEFT JOIN ${DatabaseConstants.tableCategories} c 
        ON p.${DatabaseConstants.columnProductCategoryId} = c.${DatabaseConstants.columnCategoryId}
      LEFT JOIN ${DatabaseConstants.tableUnits} u 
        ON p.${DatabaseConstants.columnProductBaseUnitId} = u.${DatabaseConstants.columnUnitId}
      WHERE p.${DatabaseConstants.columnProductDeletedAt} IS NULL
    ''';

    List<dynamic> args = [];
    if (warehouseId != null) {
      sql += ' AND s.${DatabaseConstants.columnStockWarehouseId} = ?';
      args.add(warehouseId);
    }

    sql += ' ORDER BY p.${DatabaseConstants.columnProductNameAr}';
    return await rawQuery(sql, args.isNotEmpty ? args : null);
  }

  /// استرجاع إحصاءات مبيعات المنتج
  Future<Map<String, dynamic>?> getProductSalesStats(String productId) async {
    final sql = '''
      SELECT 
        p.${DatabaseConstants.columnProductId},
        p.${DatabaseConstants.columnProductNameAr},
        COALESCE(SUM(si.${DatabaseConstants.columnSaleItemQty}), 0) as total_sold,
        COALESCE(SUM(si.${DatabaseConstants.columnSaleItemQty} * si.${DatabaseConstants.columnSaleItemUnitPrice}), 0) as total_revenue,
        COUNT(DISTINCT si.${DatabaseConstants.columnSaleItemSaleId}) as sales_count,
        AVG(si.${DatabaseConstants.columnSaleItemUnitPrice}) as avg_price
      FROM ${DatabaseConstants.tableProducts} p
      LEFT JOIN ${DatabaseConstants.tableSaleItems} si 
        ON p.${DatabaseConstants.columnProductId} = si.${DatabaseConstants.columnSaleItemProductId}
      LEFT JOIN ${DatabaseConstants.tableSales} s 
        ON si.${DatabaseConstants.columnSaleItemSaleId} = s.${DatabaseConstants.columnSaleId}
      WHERE p.${DatabaseConstants.columnProductId} = ? 
        AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
        AND s.${DatabaseConstants.columnSaleDeletedAt} IS NULL
      GROUP BY p.${DatabaseConstants.columnProductId}, p.${DatabaseConstants.columnProductNameAr}
    ''';

    final result = await rawQuery(sql, [productId]);
    return result.isNotEmpty ? result.first : null;
  }

  /// تحديث مخزون المنتج
  Future<bool> updateStock(
      String productId, String warehouseId, double quantity) async {
    try {
      final db = await database;

      final existingStock = await db.query(
        DatabaseConstants.tableStocks,
        where:
            '${DatabaseConstants.columnStockProductId} = ? AND ${DatabaseConstants.columnStockWarehouseId} = ?',
        whereArgs: [productId, warehouseId],
        limit: 1,
      );

      final now = DateTime.now().toIso8601String();

      if (existingStock.isNotEmpty) {
        await db.update(
          DatabaseConstants.tableStocks,
          {
            DatabaseConstants.columnStockQuantity: quantity,
            DatabaseConstants.columnStockUpdatedAt: now,
            DatabaseConstants.columnStockIsSynced: 0,
          },
          where:
              '${DatabaseConstants.columnStockProductId} = ? AND ${DatabaseConstants.columnStockWarehouseId} = ?',
          whereArgs: [productId, warehouseId],
        );
      } else {
        await db.insert(DatabaseConstants.tableStocks, {
          DatabaseConstants.columnStockId: AppUtils.generateId(),
          DatabaseConstants.columnStockProductId: productId,
          DatabaseConstants.columnStockWarehouseId: warehouseId,
          DatabaseConstants.columnStockQuantity: quantity,
          DatabaseConstants.columnStockCreatedAt: now,
          DatabaseConstants.columnStockUpdatedAt: now,
          DatabaseConstants.columnStockIsSynced: 0,
        });
      }
      return true;
    } catch (e) {
      AppUtils.logError('Error updating product stock', e);
      return false;
    }
  }

  /// استرجاع المخزون الحالي
  Future<double> getCurrentStock(String productId, String warehouseId) async {
    try {
      final db = await database;
      final result = await db.query(
        DatabaseConstants.tableStocks,
        columns: [DatabaseConstants.columnStockQuantity],
        where:
            '${DatabaseConstants.columnStockProductId} = ? AND ${DatabaseConstants.columnStockWarehouseId} = ?',
        whereArgs: [productId, warehouseId],
        limit: 1,
      );

      return result.isNotEmpty
          ? (result.first[DatabaseConstants.columnStockQuantity] as num)
              .toDouble()
          : 0.0;
    } catch (e) {
      AppUtils.logError('Error getting current stock', e);
      return 0.0;
    }
  }

  /// التحقق من تفرّد الباركود
  Future<bool> barcodeExists(String barcode, {String? excludeProductId}) async {
    try {
      String where =
          '${DatabaseConstants.columnProductBarcode} = ? AND ${DatabaseConstants.columnProductDeletedAt} IS NULL';
      List<dynamic> whereArgs = [barcode];

      if (excludeProductId != null) {
        where += ' AND ${DatabaseConstants.columnProductId} != ?';
        whereArgs.add(excludeProductId);
      }

      final count = await this.count(where: where, whereArgs: whereArgs);
      return count > 0;
    } catch (e) {
      AppUtils.logError('Error checking barcode existence', e);
      return false;
    }
  }

  /// استرجاع المنتجات حسب نطاق السعر
  Future<List<Product>> getProductsByPriceRange(
      double minPrice, double maxPrice) async {
    return await findWhere(
      where:
          '${DatabaseConstants.columnProductSellingPrice} >= ? AND ${DatabaseConstants.columnProductSellingPrice} <= ?',
      whereArgs: [minPrice, maxPrice],
      orderBy: DatabaseConstants.columnProductSellingPrice,
    );
  }

  /// استرجاع أكثر المنتجات مبيعاً
  Future<List<Map<String, dynamic>>> getTopSellingProducts(
      {int limit = 10}) async {
    final sql = '''
      SELECT 
        p.*,
        SUM(si.${DatabaseConstants.columnSaleItemQty}) as total_sold,
        SUM(si.${DatabaseConstants.columnSaleItemQty} * si.${DatabaseConstants.columnSaleItemUnitPrice}) as total_revenue
      FROM ${DatabaseConstants.tableProducts} p
      JOIN ${DatabaseConstants.tableSaleItems} si 
        ON p.${DatabaseConstants.columnProductId} = si.${DatabaseConstants.columnSaleItemProductId}
      JOIN ${DatabaseConstants.tableSales} s 
        ON si.${DatabaseConstants.columnSaleItemSaleId} = s.${DatabaseConstants.columnSaleId}
      WHERE p.${DatabaseConstants.columnProductDeletedAt} IS NULL 
        AND s.${DatabaseConstants.columnSaleDeletedAt} IS NULL
      GROUP BY p.${DatabaseConstants.columnProductId}
      ORDER BY total_sold DESC
      LIMIT ?
    ''';

    return await rawQuery(sql, [limit]);
  }

  /// استرجاع المنتجات الراكدة
  Future<List<Map<String, dynamic>>> getSlowMovingProducts(
      {int daysThreshold = 30}) async {
    final cutoffDate = DateTime.now().subtract(Duration(days: daysThreshold));

    final sql = '''
      SELECT 
        p.*,
        s.${DatabaseConstants.columnStockQuantity} as current_stock,
        COALESCE(SUM(si.${DatabaseConstants.columnSaleItemQty}), 0) as total_sold
      FROM ${DatabaseConstants.tableProducts} p
      LEFT JOIN ${DatabaseConstants.tableStocks} s 
        ON p.${DatabaseConstants.columnProductId} = s.${DatabaseConstants.columnStockProductId}
      LEFT JOIN ${DatabaseConstants.tableSaleItems} si 
        ON p.${DatabaseConstants.columnProductId} = si.${DatabaseConstants.columnSaleItemProductId}
      LEFT JOIN ${DatabaseConstants.tableSales} sal 
        ON si.${DatabaseConstants.columnSaleItemSaleId} = sal.${DatabaseConstants.columnSaleSaleDate}
      WHERE p.${DatabaseConstants.columnProductDeletedAt} IS NULL
      GROUP BY p.${DatabaseConstants.columnProductId}
      HAVING total_sold <= 5 OR total_sold IS NULL
      ORDER BY total_sold ASC
    ''';

    return await rawQuery(sql, [cutoffDate.toIso8601String()]);
  }

  /// تحديث الأسعار بالجملة
  Future<bool> bulkUpdatePrices(
      Map<String, Map<String, double>> priceUpdates) async {
    try {
      final db = await database;
      final batch = db.batch();
      final now = DateTime.now().toIso8601String();

      priceUpdates.forEach((productId, prices) {
        final updateData = <String, dynamic>{
          DatabaseConstants.columnProductUpdatedAt: now,
          DatabaseConstants.columnProductIsSynced: 0,
        };

        if (prices.containsKey(DatabaseConstants.columnProductCostPrice)) {
          updateData[DatabaseConstants.columnProductCostPrice] =
              prices[DatabaseConstants.columnProductCostPrice];
        }
        if (prices.containsKey(DatabaseConstants.columnProductSellingPrice)) {
          updateData[DatabaseConstants.columnProductSellingPrice] =
              prices[DatabaseConstants.columnProductSellingPrice];
        }

        batch.update(
          tableName,
          updateData,
          where:
              '${DatabaseConstants.columnProductId} = ? AND ${DatabaseConstants.columnProductDeletedAt} IS NULL',
          whereArgs: [productId],
        );
      });

      await batch.commit(noResult: true);
      AppUtils.logInfo(
          'Bulk updated prices for ${priceUpdates.length} products');
      return true;
    } catch (e) {
      AppUtils.logError('Error bulk updating prices', e);
      return false;
    }
  }

  // ------------------------------------------------------------------
  // Enhanced Product-specific queries (merged from EnhancedProductDao)
  // ------------------------------------------------------------------

  /// استرجاع المنتجات مع معلومات الفئة والوحدة والمخزون
  Future<List<Product>> getProductsWithFullDetails({
    String? orderBy,
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    final sqlQuery = '''
      SELECT
        p.*,
        c.${DatabaseConstants.columnCategoryNameAr} as category_name,
        u.${DatabaseConstants.columnUnitName} as unit_name,
        COALESCE(s.${DatabaseConstants.columnStockQuantity}, 0) as current_stock
      FROM ${DatabaseConstants.tableProducts} p
      LEFT JOIN ${DatabaseConstants.tableCategories} c
        ON p.${DatabaseConstants.columnProductCategoryId} = c.${DatabaseConstants.columnCategoryId}
      LEFT JOIN ${DatabaseConstants.tableUnits} u
        ON p.${DatabaseConstants.columnProductBaseUnitId} = u.${DatabaseConstants.columnUnitId}
      LEFT JOIN ${DatabaseConstants.tableStocks} s
        ON p.${DatabaseConstants.columnProductId} = s.${DatabaseConstants.columnStockProductId}
      WHERE p.${DatabaseConstants.columnProductDeletedAt} IS NULL
      ORDER BY ${orderBy ?? DatabaseConstants.columnProductNameAr} ${ascending ? 'ASC' : 'DESC'}
      ${limit != null ? 'LIMIT $limit' : ''}
      ${offset != null ? 'OFFSET $offset' : ''}
    ''';

    final result = await rawQuery(sqlQuery);
    return result.map((map) => Product.fromMap(map)).toList();
  }

  /// استرجاع منتج واحد مع التفاصيل الكاملة
  Future<Product?> getProductWithFullDetails(String productId) async {
    final sqlQuery = '''
      SELECT
        p.*,
        c.${DatabaseConstants.columnCategoryNameAr} as category_name,
        u.${DatabaseConstants.columnUnitName} as unit_name,
        COALESCE(s.${DatabaseConstants.columnStockQuantity}, 0) as current_stock
      FROM ${DatabaseConstants.tableProducts} p
      LEFT JOIN ${DatabaseConstants.tableCategories} c
        ON p.${DatabaseConstants.columnProductCategoryId} = c.${DatabaseConstants.columnCategoryId}
      LEFT JOIN ${DatabaseConstants.tableUnits} u
        ON p.${DatabaseConstants.columnProductBaseUnitId} = u.${DatabaseConstants.columnUnitId}
      LEFT JOIN ${DatabaseConstants.tableStocks} s
        ON p.${DatabaseConstants.columnProductId} = s.${DatabaseConstants.columnStockProductId}
      WHERE p.${DatabaseConstants.columnProductId} = ?
        AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
    ''';

    final result = await rawQuery(sqlQuery, [productId]);
    return result.isNotEmpty ? Product.fromMap(result.first) : null;
  }

  /// البحث المتقدم في المنتجات
  Future<List<Product>> advancedSearch({
    String? searchTerm,
    String? categoryId,
    String? unitId,
    double? minPrice,
    double? maxPrice,
    bool? isActive,
    bool? trackStock,
    String? orderBy,
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    final conditions = <String>[];
    final args = <dynamic>[];

    // Always exclude deleted products
    conditions.add('p.${DatabaseConstants.columnProductDeletedAt} IS NULL');

    if (searchTerm != null && searchTerm.isNotEmpty) {
      conditions.add('''
        (p.${DatabaseConstants.columnProductNameAr} LIKE ?
         OR p.${DatabaseConstants.columnProductNameEn} LIKE ?
         OR p.${DatabaseConstants.columnProductBarcode} LIKE ?
         OR p.sku LIKE ?)
      ''');
      final searchPattern = '%$searchTerm%';
      args.addAll([searchPattern, searchPattern, searchPattern, searchPattern]);
    }

    if (categoryId != null) {
      conditions.add('p.${DatabaseConstants.columnProductCategoryId} = ?');
      args.add(categoryId);
    }

    if (unitId != null) {
      conditions.add('p.${DatabaseConstants.columnProductBaseUnitId} = ?');
      args.add(unitId);
    }

    if (minPrice != null) {
      conditions.add('p.${DatabaseConstants.columnProductSellingPrice} >= ?');
      args.add(minPrice);
    }

    if (maxPrice != null) {
      conditions.add('p.${DatabaseConstants.columnProductSellingPrice} <= ?');
      args.add(maxPrice);
    }

    if (isActive != null) {
      conditions.add('p.${DatabaseConstants.columnProductIsActive} = ?');
      args.add(isActive ? 1 : 0);
    }

    if (trackStock != null) {
      conditions.add('p.${DatabaseConstants.columnProductTrackStock} = ?');
      args.add(trackStock ? 1 : 0);
    }

    final whereClause = conditions.join(' AND ');

    final sqlQuery = '''
      SELECT
        p.*,
        c.${DatabaseConstants.columnCategoryNameAr} as category_name,
        u.${DatabaseConstants.columnUnitName} as unit_name,
        COALESCE(s.${DatabaseConstants.columnStockQuantity}, 0) as current_stock
      FROM ${DatabaseConstants.tableProducts} p
      LEFT JOIN ${DatabaseConstants.tableCategories} c
        ON p.${DatabaseConstants.columnProductCategoryId} = c.${DatabaseConstants.columnCategoryId}
      LEFT JOIN ${DatabaseConstants.tableUnits} u
        ON p.${DatabaseConstants.columnProductBaseUnitId} = u.${DatabaseConstants.columnUnitId}
      LEFT JOIN ${DatabaseConstants.tableStocks} s
        ON p.${DatabaseConstants.columnProductId} = s.${DatabaseConstants.columnStockProductId}
      WHERE $whereClause
      ORDER BY ${orderBy ?? DatabaseConstants.columnProductNameAr} ${ascending ? 'ASC' : 'DESC'}
      ${limit != null ? 'LIMIT $limit' : ''}
      ${offset != null ? 'OFFSET $offset' : ''}
    ''';

    final result = await rawQuery(sqlQuery, args);
    return result.map((map) => Product.fromMap(map)).toList();
  }

  /// استرجاع المنتجات منخفضة المخزون
  Future<List<Product>> getLowStockProducts() async {
    final sqlQuery = '''
      SELECT
        p.*,
        c.${DatabaseConstants.columnCategoryNameAr} as category_name,
        u.${DatabaseConstants.columnUnitName} as unit_name,
        COALESCE(s.${DatabaseConstants.columnStockQuantity}, 0) as current_stock
      FROM ${DatabaseConstants.tableProducts} p
      LEFT JOIN ${DatabaseConstants.tableCategories} c
        ON p.${DatabaseConstants.columnProductCategoryId} = c.${DatabaseConstants.columnCategoryId}
      LEFT JOIN ${DatabaseConstants.tableUnits} u
        ON p.${DatabaseConstants.columnProductBaseUnitId} = u.${DatabaseConstants.columnUnitId}
      LEFT JOIN ${DatabaseConstants.tableStocks} s
        ON p.${DatabaseConstants.columnProductId} = s.${DatabaseConstants.columnStockProductId}
      WHERE p.${DatabaseConstants.columnProductDeletedAt} IS NULL
        AND p.${DatabaseConstants.columnProductTrackStock} = 1
        AND COALESCE(s.${DatabaseConstants.columnStockQuantity}, 0) <= p.${DatabaseConstants.columnProductReorderPoint}
      ORDER BY COALESCE(s.${DatabaseConstants.columnStockQuantity}, 0) ASC
    ''';

    final result = await rawQuery(sqlQuery);
    return result.map((map) => Product.fromMap(map)).toList();
  }

  /// استرجاع المنتجات نافدة المخزون
  Future<List<Product>> getOutOfStockProducts() async {
    final sqlQuery = '''
      SELECT
        p.*,
        c.${DatabaseConstants.columnCategoryNameAr} as category_name,
        u.${DatabaseConstants.columnUnitName} as unit_name,
        COALESCE(s.${DatabaseConstants.columnStockQuantity}, 0) as current_stock
      FROM ${DatabaseConstants.tableProducts} p
      LEFT JOIN ${DatabaseConstants.tableCategories} c
        ON p.${DatabaseConstants.columnProductCategoryId} = c.${DatabaseConstants.columnCategoryId}
      LEFT JOIN ${DatabaseConstants.tableUnits} u
        ON p.${DatabaseConstants.columnProductBaseUnitId} = u.${DatabaseConstants.columnUnitId}
      LEFT JOIN ${DatabaseConstants.tableStocks} s
        ON p.${DatabaseConstants.columnProductId} = s.${DatabaseConstants.columnStockProductId}
      WHERE p.${DatabaseConstants.columnProductDeletedAt} IS NULL
        AND p.${DatabaseConstants.columnProductTrackStock} = 1
        AND COALESCE(s.${DatabaseConstants.columnStockQuantity}, 0) <= 0
      ORDER BY p.${DatabaseConstants.columnProductNameAr}
    ''';

    final result = await rawQuery(sqlQuery);
    return result.map((map) => Product.fromMap(map)).toList();
  }

  /// إحصائيات المنتجات
  Future<Map<String, dynamic>> getProductsStatistics() async {
    final sqlQuery = '''
      SELECT
        COUNT(*) as total_products,
        COUNT(CASE WHEN p.${DatabaseConstants.columnProductIsActive} = 1 THEN 1 END) as active_products,
        COUNT(CASE WHEN p.${DatabaseConstants.columnProductTrackStock} = 1 THEN 1 END) as tracked_products,
        COUNT(CASE WHEN COALESCE(s.${DatabaseConstants.columnStockQuantity}, 0) <= 0 THEN 1 END) as out_of_stock,
        COUNT(CASE WHEN COALESCE(s.${DatabaseConstants.columnStockQuantity}, 0) <= p.${DatabaseConstants.columnProductReorderPoint} AND COALESCE(s.${DatabaseConstants.columnStockQuantity}, 0) > 0 THEN 1 END) as low_stock,
        AVG(p.${DatabaseConstants.columnProductCostPrice}) as avg_cost_price,
        AVG(p.${DatabaseConstants.columnProductSellingPrice}) as avg_selling_price,
        SUM(COALESCE(s.${DatabaseConstants.columnStockQuantity}, 0) * p.${DatabaseConstants.columnProductCostPrice}) as total_inventory_value
      FROM ${DatabaseConstants.tableProducts} p
      LEFT JOIN ${DatabaseConstants.tableStocks} s
        ON p.${DatabaseConstants.columnProductId} = s.${DatabaseConstants.columnStockProductId}
      WHERE p.${DatabaseConstants.columnProductDeletedAt} IS NULL
    ''';

    final result = await rawQuery(sqlQuery);
    return result.isNotEmpty ? result.first : {};
  }

  /// البحث بالباركود أو QR Code
  Future<Product?> findByBarcodeOrQr(String code) async {
    final sqlQuery = '''
      SELECT
        p.*,
        c.${DatabaseConstants.columnCategoryNameAr} as category_name,
        u.${DatabaseConstants.columnUnitName} as unit_name,
        COALESCE(s.${DatabaseConstants.columnStockQuantity}, 0) as current_stock
      FROM ${DatabaseConstants.tableProducts} p
      LEFT JOIN ${DatabaseConstants.tableCategories} c
        ON p.${DatabaseConstants.columnProductCategoryId} = c.${DatabaseConstants.columnCategoryId}
      LEFT JOIN ${DatabaseConstants.tableUnits} u
        ON p.${DatabaseConstants.columnProductBaseUnitId} = u.${DatabaseConstants.columnUnitId}
      LEFT JOIN ${DatabaseConstants.tableStocks} s
        ON p.${DatabaseConstants.columnProductId} = s.${DatabaseConstants.columnStockProductId}
      WHERE (p.${DatabaseConstants.columnProductBarcode} = ? OR p.qr_code = ?)
        AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
      LIMIT 1
    ''';

    final result = await rawQuery(sqlQuery, [code, code]);
    return result.isNotEmpty ? Product.fromMap(result.first) : null;
  }

  /// البحث بـ SKU
  Future<Product?> findBySku(String sku) async {
    final sqlQuery = '''
      SELECT
        p.*,
        c.${DatabaseConstants.columnCategoryNameAr} as category_name,
        u.${DatabaseConstants.columnUnitName} as unit_name,
        COALESCE(s.${DatabaseConstants.columnStockQuantity}, 0) as current_stock
      FROM ${DatabaseConstants.tableProducts} p
      LEFT JOIN ${DatabaseConstants.tableCategories} c
        ON p.${DatabaseConstants.columnProductCategoryId} = c.${DatabaseConstants.columnCategoryId}
      LEFT JOIN ${DatabaseConstants.tableUnits} u
        ON p.${DatabaseConstants.columnProductBaseUnitId} = u.${DatabaseConstants.columnUnitId}
      LEFT JOIN ${DatabaseConstants.tableStocks} s
        ON p.${DatabaseConstants.columnProductId} = s.${DatabaseConstants.columnStockProductId}
      WHERE p.sku = ? AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
      LIMIT 1
    ''';

    final result = await rawQuery(sqlQuery, [sku]);
    return result.isNotEmpty ? Product.fromMap(result.first) : null;
  }

  /// استرجاع المنتجات منتهية الصلاحية
  Future<List<Product>> getExpiredProducts() async {
    final now = DateTime.now().toIso8601String();
    return await findWhere(
      where:
          '${DatabaseConstants.columnProductExpiryDate} IS NOT NULL AND ${DatabaseConstants.columnProductExpiryDate} < ? AND ${DatabaseConstants.columnProductDeletedAt} IS NULL',
      whereArgs: [now],
      orderBy: '${DatabaseConstants.columnProductExpiryDate} ASC',
    );
  }

  /// استرجاع المنتجات قريبة انتهاء الصلاحية
  Future<List<Product>> getNearExpiryProducts(int daysAhead) async {
    final now = DateTime.now().toIso8601String();
    final futureDate =
        DateTime.now().add(Duration(days: daysAhead)).toIso8601String();
    return await findWhere(
      where:
          '${DatabaseConstants.columnProductExpiryDate} IS NOT NULL AND ${DatabaseConstants.columnProductExpiryDate} > ? AND ${DatabaseConstants.columnProductExpiryDate} <= ? AND ${DatabaseConstants.columnProductDeletedAt} IS NULL',
      whereArgs: [now, futureDate],
      orderBy: '${DatabaseConstants.columnProductExpiryDate} ASC',
    );
  }
}
