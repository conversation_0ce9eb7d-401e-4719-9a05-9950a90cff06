// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'salary.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Salary _$SalaryFromJson(Map<String, dynamic> json) => Salary(
      id: json['id'] as String,
      employeeId: json['employeeId'] as String,
      basicSalary: (json['basicSalary'] as num).toDouble(),
      allowances: (json['allowances'] as num?)?.toDouble() ?? 0.0,
      deductions: (json['deductions'] as num?)?.toDouble() ?? 0.0,
      overtime: (json['overtime'] as num?)?.toDouble() ?? 0.0,
      bonus: (json['bonus'] as num?)?.toDouble() ?? 0.0,
      netSalary: (json['netSalary'] as num).toDouble(),
      payrollDate: DateTime.parse(json['payrollDate'] as String),
      paidDate: json['paidDate'] == null
          ? null
          : DateTime.parse(json['paidDate'] as String),
      status: json['status'] as String? ?? 'pending',
      notes: json['notes'] as String?,
      paymentMethod: json['paymentMethod'] as String?,
      referenceNumber: json['referenceNumber'] as String?,
      createdBy: json['createdBy'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      isSynced: json['isSynced'] as bool? ?? false,
    );

Map<String, dynamic> _$SalaryToJson(Salary instance) => <String, dynamic>{
      'id': instance.id,
      'employeeId': instance.employeeId,
      'basicSalary': instance.basicSalary,
      'allowances': instance.allowances,
      'deductions': instance.deductions,
      'overtime': instance.overtime,
      'bonus': instance.bonus,
      'netSalary': instance.netSalary,
      'payrollDate': instance.payrollDate.toIso8601String(),
      'paidDate': instance.paidDate?.toIso8601String(),
      'status': instance.status,
      'notes': instance.notes,
      'paymentMethod': instance.paymentMethod,
      'referenceNumber': instance.referenceNumber,
      'createdBy': instance.createdBy,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'isSynced': instance.isSynced,
    };
