import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../router/routes.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/responsive_wrapper.dart';

class SettingsPage extends ConsumerWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MainLayout(
      title: 'الإعدادات',
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // System Info
            _buildSystemInfo(context),
            SizedBox(height: 24.h),

            // Settings Categories
            _buildSettingsCategories(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemInfo(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(20.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(12.r),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Icon(
                    Icons.business,
                    size: 32.r,
                    color: AppColors.primary,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'شركة تجاري تك المحدودة',
                        style: AppTextStyles.titleLarge.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        'نظام إدارة المحاسبة الشامل',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        'الإصدار 1.0.0',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsCategories(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إعدادات النظام',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 2,
          desktopColumns: 3,
          children: [
            _buildSettingCard(
              context,
              title: 'معلومات الشركة',
              subtitle: 'تحديث بيانات الشركة والفروع',
              icon: Icons.business_outlined,
              color: AppColors.primary,
              onTap: () => context.go(AppRoutes.companyInfo),
            ),
            _buildSettingCard(
              context,
              title: 'إدارة المستخدمين',
              subtitle: 'إضافة وإدارة مستخدمي النظام',
              icon: Icons.people_outline,
              color: AppColors.secondary,
              onTap: () => context.go(AppRoutes.userManagement),
            ),
            _buildSettingCard(
              context,
              title: 'إعدادات الطباعة',
              subtitle: 'تكوين الطابعات والفواتير',
              icon: Icons.print_outlined,
              color: AppColors.info,
              onTap: () => context.go(AppRoutes.printerSettings),
            ),
            _buildSettingCard(
              context,
              title: 'إعدادات الضريبة',
              subtitle: 'تكوين أنواع وقيم الضرائب',
              icon: Icons.receipt_outlined,
              color: AppColors.success,
              onTap: () => context.go(AppRoutes.taxSettings),
            ),
            _buildSettingCard(
              context,
              title: 'النسخ الاحتياطي',
              subtitle: 'نسخ احتياطي واستعادة البيانات',
              icon: Icons.backup_outlined,
              color: AppColors.warning,
              onTap: () => context.go(AppRoutes.backupRestore),
            ),
            _buildSettingCard(
              context,
              title: 'إدارة الفروع',
              subtitle: 'إضافة وإدارة فروع الشركة',
              icon: Icons.location_on_outlined,
              color: AppColors.accent,
              onTap: () => context.go(AppRoutes.branches),
            ),
            _buildSettingCard(
              context,
              title: 'إعدادات النظام',
              subtitle: 'إعدادات عامة للنظام',
              icon: Icons.settings_outlined,
              color: Colors.grey[600]!,
              onTap: () {
                // TODO: Navigate to system settings
              },
            ),
            _buildSettingCard(
              context,
              title: 'الأمان والخصوصية',
              subtitle: 'إعدادات الأمان وكلمات المرور',
              icon: Icons.security_outlined,
              color: Colors.red[600]!,
              onTap: () {
                // TODO: Navigate to security settings
              },
            ),
            _buildSettingCard(
              context,
              title: 'التحديثات',
              subtitle: 'فحص وتحديث النظام',
              icon: Icons.system_update_outlined,
              color: Colors.blue[600]!,
              onTap: () {
                _showUpdateDialog(context);
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSettingCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(20.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(12.r),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Icon(
                      icon,
                      size: 32.r,
                      color: color,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16.r,
                    color: Colors.grey[400],
                  ),
                ],
              ),
              SizedBox(height: 16.h),
              Text(
                title,
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                subtitle,
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showUpdateDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.system_update_outlined, color: AppColors.primary),
            SizedBox(width: 8.w),
            const Text('فحص التحديثات'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check_circle_outline,
              size: 64.r,
              color: AppColors.success,
            ),
            SizedBox(height: 16.h),
            Text(
              'النظام محدث',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'أنت تستخدم أحدث إصدار من النظام',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),
            Text(
              'الإصدار الحالي: 1.0.0',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
