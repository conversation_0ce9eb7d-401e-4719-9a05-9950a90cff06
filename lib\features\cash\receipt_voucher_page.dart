import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../widgets/main_layout.dart';

class ReceiptVoucherPage extends ConsumerStatefulWidget {
  const ReceiptVoucherPage({super.key});

  @override
  ConsumerState<ReceiptVoucherPage> createState() => _ReceiptVoucherPageState();
}

class _ReceiptVoucherPageState extends ConsumerState<ReceiptVoucherPage> {
  final _formKey = GlobalKey<FormState>();
  final _voucherNumberController = TextEditingController();
  final _amountController = TextEditingController();
  final _customerSearchController = TextEditingController();
  final _notesController = TextEditingController();
  
  DateTime _selectedDate = DateTime.now();
  String _selectedPaymentMethod = 'نقداً';
  String _selectedCashBox = 'الصندوق الرئيسي';
  String _selectedBank = '';
  String _receiptType = 'من عميل';
  
  Map<String, dynamic>? _selectedCustomer;
  final List<Map<String, dynamic>> _pendingInvoices = [];

  @override
  void dispose() {
    _voucherNumberController.dispose();
    _amountController.dispose();
    _customerSearchController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'سند قبض',
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Information
              _buildHeaderSection(),
              SizedBox(height: 16.h),
              
              // Receipt Type
              _buildReceiptTypeSection(),
              SizedBox(height: 16.h),
              
              // Customer Selection (if applicable)
              if (_receiptType == 'من عميل') ...[
                _buildCustomerSection(),
                SizedBox(height: 16.h),
              ],
              
              // Payment Information
              _buildPaymentSection(),
              SizedBox(height: 16.h),
              
              // Pending Invoices (if customer selected)
              if (_selectedCustomer != null) ...[
                _buildPendingInvoicesSection(),
                SizedBox(height: 16.h),
              ],
              
              // Notes
              _buildNotesSection(),
              SizedBox(height: 24.h),
              
              // Action Buttons
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات السند',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            
            Row(
              children: [
                // Voucher Number
                Expanded(
                  child: TextFormField(
                    controller: _voucherNumberController,
                    decoration: const InputDecoration(
                      labelText: 'رقم السند',
                      hintText: 'REC-001',
                      prefixIcon: Icon(Icons.numbers),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال رقم السند';
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(width: 16.w),
                
                // Date
                Expanded(
                  child: InkWell(
                    onTap: _selectDate,
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'التاريخ',
                        prefixIcon: Icon(Icons.calendar_today),
                      ),
                      child: Text(
                        '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReceiptTypeSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نوع السند',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            
            Row(
              children: [
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('من عميل'),
                    value: 'من عميل',
                    groupValue: _receiptType,
                    onChanged: (value) {
                      setState(() {
                        _receiptType = value!;
                        _selectedCustomer = null;
                        _pendingInvoices.clear();
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('إيراد عام'),
                    value: 'إيراد عام',
                    groupValue: _receiptType,
                    onChanged: (value) {
                      setState(() {
                        _receiptType = value!;
                        _selectedCustomer = null;
                        _pendingInvoices.clear();
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'العميل',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            
            // Customer Search
            TextFormField(
              controller: _customerSearchController,
              decoration: InputDecoration(
                labelText: 'البحث عن عميل',
                hintText: 'اسم العميل أو رقم الهاتف',
                prefixIcon: const Icon(Icons.person_search),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.search),
                  onPressed: _searchCustomer,
                ),
              ),
              onFieldSubmitted: (_) => _searchCustomer(),
            ),
            
            // Selected Customer Info
            if (_selectedCustomer != null) ...[
              SizedBox(height: 16.h),
              Container(
                padding: EdgeInsets.all(12.r),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: AppColors.success.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.person, color: AppColors.success),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _selectedCustomer!['name'],
                            style: AppTextStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            'الهاتف: ${_selectedCustomer!['phone']}',
                            style: AppTextStyles.bodySmall,
                          ),
                          Text(
                            'الرصيد: ${_selectedCustomer!['balance']} ر.س',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () {
                        setState(() {
                          _selectedCustomer = null;
                          _pendingInvoices.clear();
                        });
                      },
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الدفع',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            
            // Amount
            TextFormField(
              controller: _amountController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'المبلغ *',
                hintText: '0.00',
                prefixIcon: Icon(Icons.attach_money),
                suffixText: 'ر.س',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال المبلغ';
                }
                if (double.tryParse(value) == null) {
                  return 'يرجى إدخال مبلغ صحيح';
                }
                return null;
              },
            ),
            SizedBox(height: 16.h),
            
            // Payment Method
            DropdownButtonFormField<String>(
              value: _selectedPaymentMethod,
              decoration: const InputDecoration(
                labelText: 'طريقة الدفع',
                prefixIcon: Icon(Icons.payment),
              ),
              items: ['نقداً', 'تحويل بنكي', 'شيك', 'بطاقة ائتمان']
                  .map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedPaymentMethod = newValue!;
                });
              },
            ),
            SizedBox(height: 16.h),
            
            // Cash Box or Bank Selection
            if (_selectedPaymentMethod == 'نقداً')
              DropdownButtonFormField<String>(
                value: _selectedCashBox,
                decoration: const InputDecoration(
                  labelText: 'الصندوق',
                  prefixIcon: Icon(Icons.account_balance_wallet),
                ),
                items: ['الصندوق الرئيسي', 'صندوق الفرع', 'صندوق المبيعات']
                    .map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedCashBox = newValue!;
                  });
                },
              )
            else
              DropdownButtonFormField<String>(
                value: _selectedBank.isEmpty ? null : _selectedBank,
                decoration: const InputDecoration(
                  labelText: 'البنك',
                  prefixIcon: Icon(Icons.account_balance),
                ),
                items: ['البنك الأهلي', 'بنك الراجحي', 'بنك سامبا', 'البنك السعودي الفرنسي']
                    .map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedBank = newValue ?? '';
                  });
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPendingInvoicesSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الفواتير المعلقة',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            
            if (_pendingInvoices.isEmpty)
              Container(
                padding: EdgeInsets.all(16.r),
                child: Center(
                  child: Text(
                    'لا توجد فواتير معلقة لهذا العميل',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _pendingInvoices.length,
                itemBuilder: (context, index) {
                  final invoice = _pendingInvoices[index];
                  return ListTile(
                    leading: Icon(Icons.receipt_outlined, color: AppColors.warning),
                    title: Text('فاتورة #${invoice['number']}'),
                    subtitle: Text('التاريخ: ${invoice['date']}'),
                    trailing: Text(
                      '${invoice['amount']} ر.س',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.error,
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملاحظات',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            
            TextFormField(
              controller: _notesController,
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: 'ملاحظات إضافية',
                hintText: 'أدخل أي ملاحظات حول السند...',
                alignLabelWithHint: true,
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => context.pop(),
            child: const Text('إلغاء'),
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: ElevatedButton(
            onPressed: _saveReceipt,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              foregroundColor: Colors.white,
            ),
            child: const Text('حفظ السند'),
          ),
        ),
        SizedBox(width: 8.w),
        ElevatedButton(
          onPressed: _saveAndPrintReceipt,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
          child: const Icon(Icons.print),
        ),
      ],
    );
  }

  void _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _searchCustomer() {
    final searchTerm = _customerSearchController.text;
    if (searchTerm.isNotEmpty) {
      // Mock customer search
      final customers = [
        {'id': '1', 'name': 'أحمد محمد', 'phone': '0501234567', 'balance': '2,500'},
        {'id': '2', 'name': 'فاطمة أحمد', 'phone': '0507654321', 'balance': '1,200'},
      ];
      
      final customer = customers.firstWhere(
        (c) => c['name']!.contains(searchTerm) || c['phone']!.contains(searchTerm),
        orElse: () => {},
      );
      
      if (customer.isNotEmpty) {
        setState(() {
          _selectedCustomer = customer;
          _loadPendingInvoices(customer['id']!);
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('لم يتم العثور على العميل')),
        );
      }
    }
  }

  void _loadPendingInvoices(String customerId) {
    // Mock pending invoices
    setState(() {
      _pendingInvoices.clear();
      _pendingInvoices.addAll([
        {'number': 'INV-001', 'date': '2024/01/10', 'amount': '1,500'},
        {'number': 'INV-003', 'date': '2024/01/12', 'amount': '850'},
      ]);
    });
  }

  void _saveReceipt() {
    if (_formKey.currentState!.validate()) {
      // TODO: Save receipt to database
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم حفظ سند القبض بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
      context.pop();
    }
  }

  void _saveAndPrintReceipt() {
    if (_formKey.currentState!.validate()) {
      // TODO: Save and print receipt
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم حفظ وطباعة سند القبض بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
      context.pop();
    }
  }
}
