import 'package:json_annotation/json_annotation.dart';

part 'cash_box.g.dart';

/// نموذج الصندوق النقدي
@JsonSerializable()
class CashBox {
  final String id;
  final String name;
  final double balance;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final String? branchId;
  final bool isSynced;

  const CashBox({
    required this.id,
    required this.name,
    this.balance = 0.0,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.branchId,
    this.isSynced = false,
  });

  factory CashBox.fromJson(Map<String, dynamic> json) => _$CashBoxFromJson(json);
  Map<String, dynamic> toJson() => _$CashBoxToJson(this);

  factory CashBox.fromMap(Map<String, dynamic> map) {
    return CashBox(
      id: map['id'] as String,
      name: map['name'] as String,
      balance: (map['balance'] as num?)?.toDouble() ?? 0.0,
      isActive: (map['is_active'] as int?) == 1,
      createdAt: map['created_at'] != null 
          ? DateTime.parse(map['created_at'] as String) 
          : null,
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at'] as String) 
          : null,
      deletedAt: map['deleted_at'] != null 
          ? DateTime.parse(map['deleted_at'] as String) 
          : null,
      branchId: map['branch_id'] as String?,
      isSynced: (map['is_synced'] as int?) == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'balance': balance,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'deleted_at': deletedAt?.toIso8601String(),
      'branch_id': branchId,
      'is_synced': isSynced ? 1 : 0,
    };
  }

  CashBox copyWith({
    String? id,
    String? name,
    double? balance,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    String? branchId,
    bool? isSynced,
  }) {
    return CashBox(
      id: id ?? this.id,
      name: name ?? this.name,
      balance: balance ?? this.balance,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      branchId: branchId ?? this.branchId,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CashBox && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CashBox(id: $id, name: $name, balance: $balance, isActive: $isActive)';
  }

  // Helper methods
  bool get isDeleted => deletedAt != null;
  bool get isAvailable => isActive && !isDeleted;

  // Create a new cash box with current timestamp
  static CashBox create({
    required String id,
    required String name,
    double balance = 0.0,
    String? branchId,
  }) {
    final now = DateTime.now();
    return CashBox(
      id: id,
      name: name,
      balance: balance,
      isActive: true,
      createdAt: now,
      updatedAt: now,
      branchId: branchId,
      isSynced: false,
    );
  }

  // Update cash box with new timestamp
  CashBox update({
    String? name,
    double? balance,
    bool? isActive,
    String? branchId,
  }) {
    return copyWith(
      name: name ?? this.name,
      balance: balance ?? this.balance,
      isActive: isActive ?? this.isActive,
      branchId: branchId ?? this.branchId,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Soft delete
  CashBox delete() {
    return copyWith(
      deletedAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Update balance
  CashBox updateBalance(double newBalance) {
    return copyWith(
      balance: newBalance,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Add amount to balance
  CashBox addAmount(double amount) {
    return updateBalance(balance + amount);
  }

  // Subtract amount from balance
  CashBox subtractAmount(double amount) {
    return updateBalance(balance - amount);
  }

  // Check if cash box has sufficient balance
  bool hasSufficientBalance(double amount) {
    return balance >= amount;
  }

  // Get formatted balance
  String get formattedBalance {
    return '${balance.toStringAsFixed(2)} ر.س';
  }

  // Get status text
  String get statusText {
    if (isDeleted) return 'محذوف';
    if (!isActive) return 'غير نشط';
    return 'نشط';
  }

  // Get status color
  String get statusColor {
    if (isDeleted) return 'grey';
    if (!isActive) return 'orange';
    return 'green';
  }
}
