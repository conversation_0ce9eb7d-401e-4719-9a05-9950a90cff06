// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'category.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Category _$CategoryFromJson(Map<String, dynamic> json) => Category(
      id: json['id'] as String,
      nameAr: json['nameAr'] as String,
      nameEn: json['nameEn'] as String?,
      parentId: json['parentId'] as String?,
      description: json['description'] as String?,
      imageUrl: json['imageUrl'] as String?,
      iconName: json['iconName'] as String?,
      color: json['color'] as String?,
      sortOrder: (json['sortOrder'] as num?)?.toInt() ?? 0,
      isActive: json['isActive'] as bool? ?? true,
      taxRate: (json['taxRate'] as num?)?.toDouble(),
      discountRate: (json['discountRate'] as num?)?.toDouble(),
      tags: json['tags'] as String?,
      customFields: json['customFields'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      isSynced: json['isSynced'] as bool? ?? false,
      children: (json['children'] as List<dynamic>?)
          ?.map((e) => Category.fromJson(e as Map<String, dynamic>))
          .toList(),
      parent: json['parent'] == null
          ? null
          : Category.fromJson(json['parent'] as Map<String, dynamic>),
      productCount: (json['productCount'] as num?)?.toInt(),
      level: (json['level'] as num?)?.toInt(),
    );

Map<String, dynamic> _$CategoryToJson(Category instance) => <String, dynamic>{
      'id': instance.id,
      'nameAr': instance.nameAr,
      'nameEn': instance.nameEn,
      'parentId': instance.parentId,
      'description': instance.description,
      'imageUrl': instance.imageUrl,
      'iconName': instance.iconName,
      'color': instance.color,
      'sortOrder': instance.sortOrder,
      'isActive': instance.isActive,
      'taxRate': instance.taxRate,
      'discountRate': instance.discountRate,
      'tags': instance.tags,
      'customFields': instance.customFields,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'isSynced': instance.isSynced,
      'children': instance.children,
      'parent': instance.parent,
      'productCount': instance.productCount,
      'level': instance.level,
    };
