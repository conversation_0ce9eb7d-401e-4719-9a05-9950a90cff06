import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/sale.dart';
import '../data/models/sale_item.dart';
import '../data/models/product.dart';
import '../data/models/customer.dart';
import '../data/repositories/sale_repository.dart';
import '../core/utils/app_utils.dart';
import '../core/utils/error_handler.dart';
import '../core/constants/app_constants.dart';
import 'database_provider.dart';

// Sale management provider
final saleManagementProvider =
    StateNotifierProvider<SaleManagementNotifier, SaleManagementState>((ref) {
  return SaleManagementNotifier(ref);
});

// Sale management state
class SaleManagementState {
  final bool isLoading;
  final String? error;
  final List<Sale> sales;
  final List<Sale> filteredSales;
  final String searchQuery;
  final PaymentStatus? selectedPaymentStatus;
  final DateTime? startDate;
  final DateTime? endDate;
  final Sale? selectedSale;

  const SaleManagementState({
    this.isLoading = false,
    this.error,
    this.sales = const [],
    this.filteredSales = const [],
    this.searchQuery = '',
    this.selectedPaymentStatus,
    this.startDate,
    this.endDate,
    this.selectedSale,
  });

  SaleManagementState copyWith({
    bool? isLoading,
    String? error,
    List<Sale>? sales,
    List<Sale>? filteredSales,
    String? searchQuery,
    PaymentStatus? selectedPaymentStatus,
    DateTime? startDate,
    DateTime? endDate,
    Sale? selectedSale,
  }) {
    return SaleManagementState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      sales: sales ?? this.sales,
      filteredSales: filteredSales ?? this.filteredSales,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedPaymentStatus:
          selectedPaymentStatus ?? this.selectedPaymentStatus,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      selectedSale: selectedSale ?? this.selectedSale,
    );
  }
}

// Sale management notifier
class SaleManagementNotifier extends StateNotifier<SaleManagementState> {
  final Ref _ref;

  SaleManagementNotifier(this._ref) : super(const SaleManagementState()) {
    _loadSales();
  }

  final SaleRepository _saleRepository = SaleRepository();

  // Load all sales
  Future<void> _loadSales() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // استخدام SaleRepository لجلب المبيعات
      final salesData = await _saleRepository.getAllSales();
      final sales = salesData.map((data) => Sale.fromMap(data)).toList();

      state = state.copyWith(
        isLoading: false,
        sales: sales,
        filteredSales: _filterSales(sales),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error loading sales', e);
    }
  }

  // Filter sales based on criteria
  List<Sale> _filterSales(List<Sale> sales) {
    var filtered = sales;

    // Filter by search query (invoice number or customer)
    if (state.searchQuery.isNotEmpty) {
      filtered = filtered.where((sale) {
        return sale.invoiceNo
            .toLowerCase()
            .contains(state.searchQuery.toLowerCase());
      }).toList();
    }

    // Filter by payment status
    if (state.selectedPaymentStatus != null) {
      filtered = filtered.where((sale) {
        return sale.paymentStatus == state.selectedPaymentStatus;
      }).toList();
    }

    // Filter by date range
    if (state.startDate != null) {
      filtered = filtered.where((sale) {
        return sale.date.isAfter(state.startDate!) ||
            sale.date.isAtSameMomentAs(state.startDate!);
      }).toList();
    }

    if (state.endDate != null) {
      filtered = filtered.where((sale) {
        return sale.date.isBefore(state.endDate!.add(const Duration(days: 1)));
      }).toList();
    }

    return filtered;
  }

  // Search sales
  void searchSales(String query) {
    state = state.copyWith(
      searchQuery: query,
      filteredSales: _filterSales(state.sales),
    );
  }

  // Filter by payment status
  void filterByPaymentStatus(PaymentStatus? status) {
    state = state.copyWith(
      selectedPaymentStatus: status,
      filteredSales: _filterSales(state.sales),
    );
  }

  // Filter by date range
  void filterByDateRange(DateTime? startDate, DateTime? endDate) {
    state = state.copyWith(
      startDate: startDate,
      endDate: endDate,
      filteredSales: _filterSales(state.sales),
    );
  }

  // Clear filters
  void clearFilters() {
    state = state.copyWith(
      searchQuery: '',
      selectedPaymentStatus: null,
      startDate: null,
      endDate: null,
      filteredSales: state.sales,
    );
  }

  // Select sale
  void selectSale(Sale? sale) {
    state = state.copyWith(selectedSale: sale);
  }

  // Refresh sales
  Future<void> refresh() async {
    await _loadSales();
  }

  // Create new sale
  Future<String?> createSale({
    String? customerId,
    required List<SaleItem> items,
    double paid = 0.0,
    String? notes,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Calculate total
      final total = items.fold<double>(0.0, (sum, item) => sum + item.total);

      // Generate invoice number
      final invoiceNo = await _saleRepository.generateInvoiceNumber();

      final sale = Sale.create(
        id: AppUtils.generateId(),
        customerId: customerId,
        invoiceNo: invoiceNo,
        total: total,
        paid: paid,
        notes: notes,
      );

      // حفظ البيع في قاعدة البيانات
      final saleId = await _saleRepository.createSale(sale, items);

      await refresh();
      AppUtils.logInfo('Sale created successfully: $saleId');
      return saleId;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error creating sale', e);
      return null;
    }
  }

  // Add payment to sale
  Future<bool> addPayment(String saleId, double amount) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // In a real implementation, you would update in database
      // final sale = await saleDao.findById(saleId);
      // if (sale != null) {
      //   final updatedSale = sale.addPayment(amount);
      //   await saleDao.update(saleId, updatedSale);
      // }

      await refresh();
      AppUtils.logInfo('Payment added to sale: $saleId');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error adding payment to sale', e);
      return false;
    }
  }

  // Mark sale as paid
  Future<bool> markAsPaid(String saleId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // In a real implementation, you would update in database
      // final sale = await saleDao.findById(saleId);
      // if (sale != null) {
      //   final updatedSale = sale.markAsPaid();
      //   await saleDao.update(saleId, updatedSale);
      // }

      await refresh();
      AppUtils.logInfo('Sale marked as paid: $saleId');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error marking sale as paid', e);
      return false;
    }
  }

  // Delete sale
  Future<bool> deleteSale(String id) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // حذف البيع من قاعدة البيانات
      final success = await _saleRepository.deleteSale(id);

      if (success) {
        await refresh();
        AppUtils.logInfo('Sale deleted successfully: $id');
      }

      return success;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error deleting sale', e);
      return false;
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// POS (Point of Sale) provider
final posProvider = StateNotifierProvider<POSNotifier, POSState>((ref) {
  return POSNotifier(ref);
});

// POS state
class POSState {
  final List<SaleItem> items;
  final Customer? selectedCustomer;
  final double subtotal;
  final double taxAmount;
  final double discountAmount;
  final double total;
  final double paid;
  final double change;
  final String? notes;

  const POSState({
    this.items = const [],
    this.selectedCustomer,
    this.subtotal = 0.0,
    this.taxAmount = 0.0,
    this.discountAmount = 0.0,
    this.total = 0.0,
    this.paid = 0.0,
    this.change = 0.0,
    this.notes,
  });

  POSState copyWith({
    List<SaleItem>? items,
    Customer? selectedCustomer,
    double? subtotal,
    double? taxAmount,
    double? discountAmount,
    double? total,
    double? paid,
    double? change,
    String? notes,
  }) {
    return POSState(
      items: items ?? this.items,
      selectedCustomer: selectedCustomer ?? this.selectedCustomer,
      subtotal: subtotal ?? this.subtotal,
      taxAmount: taxAmount ?? this.taxAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      total: total ?? this.total,
      paid: paid ?? this.paid,
      change: change ?? this.change,
      notes: notes ?? this.notes,
    );
  }
}

// POS notifier
class POSNotifier extends StateNotifier<POSState> {
  final Ref _ref;

  POSNotifier(this._ref) : super(const POSState());

  // Add item to cart
  void addItem(
      Product product, String unitId, double quantity, double unitPrice) {
    final existingItemIndex = state.items.indexWhere(
      (item) => item.productId == product.id && item.unitId == unitId,
    );

    List<SaleItem> newItems;

    if (existingItemIndex >= 0) {
      // Update existing item
      final existingItem = state.items[existingItemIndex];
      final updatedItem =
          existingItem.updateQuantity(existingItem.qty + quantity);

      newItems = List.from(state.items);
      newItems[existingItemIndex] = updatedItem;
    } else {
      // Add new item
      final newItem = SaleItem.create(
        id: AppUtils.generateId(),
        saleId: '', // Will be set when sale is created
        productId: product.id,
        unitId: unitId,
        qty: quantity,
        unitPrice: unitPrice,
      );

      newItems = [...state.items, newItem];
    }

    _updateStateWithItems(newItems);
  }

  // Remove item from cart
  void removeItem(String itemId) {
    final newItems = state.items.where((item) => item.id != itemId).toList();
    _updateStateWithItems(newItems);
  }

  // Update item quantity
  void updateItemQuantity(String itemId, double quantity) {
    final newItems = state.items.map((item) {
      if (item.id == itemId) {
        return item.updateQuantity(quantity);
      }
      return item;
    }).toList();

    _updateStateWithItems(newItems);
  }

  // Update item price
  void updateItemPrice(String itemId, double unitPrice) {
    final newItems = state.items.map((item) {
      if (item.id == itemId) {
        return item.updateUnitPrice(unitPrice);
      }
      return item;
    }).toList();

    _updateStateWithItems(newItems);
  }

  // Set customer
  void setCustomer(Customer? customer) {
    state = state.copyWith(selectedCustomer: customer);
  }

  // Set paid amount
  void setPaidAmount(double paid) {
    final change = paid > state.total ? paid - state.total : 0.0;
    state = state.copyWith(paid: paid, change: change);
  }

  // Set notes
  void setNotes(String? notes) {
    state = state.copyWith(notes: notes);
  }

  // Clear cart
  void clearCart() {
    state = const POSState();
  }

  // Update state with new items and recalculate totals
  void _updateStateWithItems(List<SaleItem> items) {
    final subtotal = items.fold<double>(0.0, (sum, item) => sum + item.total);
    final taxAmount = subtotal * AppConstants.defaultTaxRate;
    final total = subtotal + taxAmount - state.discountAmount;
    final change = state.paid > total ? state.paid - total : 0.0;

    state = state.copyWith(
      items: items,
      subtotal: subtotal,
      taxAmount: taxAmount,
      total: total,
      change: change,
    );
  }

  // Complete sale
  Future<String?> completeSale() async {
    if (state.items.isEmpty) {
      throw Exception('لا توجد عناصر في السلة');
    }

    final saleManagement = _ref.read(saleManagementProvider.notifier);

    final saleId = await saleManagement.createSale(
      customerId: state.selectedCustomer?.id,
      items: state.items,
      paid: state.paid,
      notes: state.notes,
    );

    if (saleId != null) {
      clearCart();
    }

    return saleId;
  }
}

// Sale statistics provider
final saleStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  // In a real implementation, you would calculate from database
  return {
    'today_sales': 0.0,
    'today_count': 0,
    'month_sales': 0.0,
    'month_count': 0,
    'total_sales': 0.0,
    'total_count': 0,
    'pending_amount': 0.0,
    'pending_count': 0,
  };
});
