import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/colors.dart';
import '../../../core/theme/text_styles.dart';
import '../../../providers/stock_movement_provider.dart';
import '../../../providers/warehouse_provider.dart';
import '../../../providers/enhanced_product_provider.dart';

/// حوار تصفية حركة المخزون
///
/// يوفر واجهة متقدمة لتصفية حركات المخزون حسب:
/// - المخزن
/// - المنتج
/// - نوع الحركة (إدخال، إخراج، نقل، تسوية)
/// - نوع المرجع (مبيعة، مشتريات، تعديل يدوي)
/// - الفترة الزمنية
///
/// Ref: تم تطوير هذا الحوار لتوفير تحكم دقيق في عرض حركات المخزون

class StockMovementFiltersDialog extends ConsumerStatefulWidget {
  const StockMovementFiltersDialog({super.key});

  @override
  ConsumerState<StockMovementFiltersDialog> createState() =>
      _StockMovementFiltersDialogState();
}

class _StockMovementFiltersDialogState
    extends ConsumerState<StockMovementFiltersDialog> {
  String? selectedWarehouse;
  String? selectedProduct;
  String? selectedMovementType;
  String? selectedReferenceType;
  DateTime? fromDate;
  DateTime? toDate;

  @override
  void initState() {
    super.initState();
    final state = ref.read(stockMovementManagementProvider);
    selectedWarehouse = state.selectedWarehouse;
    selectedProduct = state.selectedProduct;
    selectedMovementType = state.selectedMovementType;
    selectedReferenceType = state.selectedReferenceType;
    fromDate = state.fromDate;
    toDate = state.toDate;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        padding: EdgeInsets.all(24.w),
        constraints: BoxConstraints(
          maxWidth: 400.w,
          maxHeight: 700.h,
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Icon(
                    Icons.filter_list,
                    color: AppColors.primary,
                    size: 24.sp,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'تصفية حركة المخزون',
                    style: AppTextStyles.titleLarge.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),

              SizedBox(height: 24.h),

              // Warehouse filter
              Text(
                'المخزن',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8.h),
              Consumer(
                builder: (context, ref, child) {
                  final warehousesAsync = ref.watch(activeWarehousesProvider);

                  return warehousesAsync.when(
                    data: (warehouses) => DropdownButtonFormField<String>(
                      value: selectedWarehouse,
                      decoration: InputDecoration(
                        hintText: 'اختر المخزن',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 12.w,
                          vertical: 8.h,
                        ),
                      ),
                      items: [
                        const DropdownMenuItem<String>(
                          value: null,
                          child: Text('جميع المخازن'),
                        ),
                        ...warehouses
                            .map((warehouse) => DropdownMenuItem<String>(
                                  value: warehouse.id,
                                  child: Text(warehouse.name),
                                )),
                      ],
                      onChanged: (value) {
                        setState(() {
                          selectedWarehouse = value;
                        });
                      },
                    ),
                    loading: () => const CircularProgressIndicator(),
                    error: (error, stack) => Text('خطأ في تحميل المخازن'),
                  );
                },
              ),

              SizedBox(height: 16.h),

              // Product filter
              Text(
                'المنتج',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8.h),
              Consumer(
                builder: (context, ref, child) {
                  final products = ref.watch(enhancedProductManagementProvider
                      .select((state) => state.products));

                  return DropdownButtonFormField<String>(
                    value: selectedProduct,
                    decoration: InputDecoration(
                      hintText: 'اختر المنتج',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 12.w,
                        vertical: 8.h,
                      ),
                    ),
                    items: [
                      const DropdownMenuItem<String>(
                        value: null,
                        child: Text('جميع المنتجات'),
                      ),
                      ...products.map((product) => DropdownMenuItem<String>(
                            value: product.id,
                            child: Text(product.nameAr),
                          )),
                    ],
                    onChanged: (value) {
                      setState(() {
                        selectedProduct = value;
                      });
                    },
                  );
                },
              ),

              SizedBox(height: 16.h),

              // Movement type filter
              Text(
                'نوع الحركة',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8.h),
              DropdownButtonFormField<String>(
                value: selectedMovementType,
                decoration: InputDecoration(
                  hintText: 'اختر نوع الحركة',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 8.h,
                  ),
                ),
                items: const [
                  DropdownMenuItem<String>(
                    value: null,
                    child: Text('جميع الأنواع'),
                  ),
                  DropdownMenuItem<String>(
                    value: 'in',
                    child: Text('إدخال'),
                  ),
                  DropdownMenuItem<String>(
                    value: 'out',
                    child: Text('إخراج'),
                  ),
                  DropdownMenuItem<String>(
                    value: 'transfer',
                    child: Text('نقل'),
                  ),
                  DropdownMenuItem<String>(
                    value: 'adjustment',
                    child: Text('تسوية'),
                  ),
                ],
                onChanged: (value) {
                  setState(() {
                    selectedMovementType = value;
                  });
                },
              ),

              SizedBox(height: 16.h),

              // Reference type filter
              Text(
                'نوع المرجع',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8.h),
              DropdownButtonFormField<String>(
                value: selectedReferenceType,
                decoration: InputDecoration(
                  hintText: 'اختر نوع المرجع',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 8.h,
                  ),
                ),
                items: const [
                  DropdownMenuItem<String>(
                    value: null,
                    child: Text('جميع المراجع'),
                  ),
                  DropdownMenuItem<String>(
                    value: 'sale',
                    child: Text('مبيعة'),
                  ),
                  DropdownMenuItem<String>(
                    value: 'purchase',
                    child: Text('مشتريات'),
                  ),
                  DropdownMenuItem<String>(
                    value: 'adjustment',
                    child: Text('تسوية'),
                  ),
                  DropdownMenuItem<String>(
                    value: 'transfer',
                    child: Text('نقل'),
                  ),
                  DropdownMenuItem<String>(
                    value: 'manual_adjustment',
                    child: Text('تعديل يدوي'),
                  ),
                ],
                onChanged: (value) {
                  setState(() {
                    selectedReferenceType = value;
                  });
                },
              ),

              SizedBox(height: 16.h),

              // Date range filter
              Text(
                'الفترة الزمنية',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8.h),
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () => _selectDate(context, true),
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 12.w,
                          vertical: 12.h,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.calendar_today, size: 16.sp),
                            SizedBox(width: 8.w),
                            Text(
                              fromDate != null
                                  ? '${fromDate!.day}/${fromDate!.month}/${fromDate!.year}'
                                  : 'من تاريخ',
                              style: AppTextStyles.bodyMedium,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: InkWell(
                      onTap: () => _selectDate(context, false),
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 12.w,
                          vertical: 12.h,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.calendar_today, size: 16.sp),
                            SizedBox(width: 8.w),
                            Text(
                              toDate != null
                                  ? '${toDate!.day}/${toDate!.month}/${toDate!.year}'
                                  : 'إلى تاريخ',
                              style: AppTextStyles.bodyMedium,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),

              SizedBox(height: 32.h),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        ref
                            .read(stockMovementManagementProvider.notifier)
                            .clearFilters();
                        Navigator.of(context).pop();
                      },
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        'مسح الكل',
                        style: AppTextStyles.bodyMedium,
                      ),
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        final notifier =
                            ref.read(stockMovementManagementProvider.notifier);
                        notifier
                          ..filterByWarehouse(selectedWarehouse)
                          ..filterByProduct(selectedProduct)
                          ..filterByMovementType(selectedMovementType)
                          ..filterByReferenceType(selectedReferenceType)
                          ..filterByDateRange(fromDate, toDate);
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        'تطبيق',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          isFromDate ? fromDate ?? DateTime.now() : toDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        if (isFromDate) {
          fromDate = picked;
        } else {
          toDate = picked;
        }
      });
    }
  }
}
