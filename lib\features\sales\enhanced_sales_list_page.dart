import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:tijari_tech/core/utils/formatters.dart';
import 'package:tijari_tech/shared/widgets/loading_widget.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../data/models/sale.dart';
import '../../data/repositories/sale_repository.dart';
import '../../data/repositories/customer_repository.dart';
import '../../providers/sale_provider.dart';
import '../../providers/customer_provider.dart';
import '../../router/routes.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/custom_card.dart';
import '../../widgets/error_widget.dart';
import '../../widgets/empty_state_widget.dart';

class EnhancedSalesListPage extends ConsumerStatefulWidget {
  const EnhancedSalesListPage({super.key});

  @override
  ConsumerState<EnhancedSalesListPage> createState() =>
      _EnhancedSalesListPageState();
}

class _EnhancedSalesListPageState extends ConsumerState<EnhancedSalesListPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedStatus = 'all';
  String _selectedCustomer = 'all';
  String _selectedPaymentMethod = 'all';
  DateTimeRange? _dateRange;
  bool _showOnlyDue = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // We'll load data manually for now
      _loadSalesData();
    });
  }

  Future<void> _loadSalesData() async {
    // TODO: Implement data loading
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final saleState = ref.watch(saleManagementProvider);
    final customerState = ref.watch(customerProvider);

    return MainLayout(
        title: 'إدارة المبيعات',
        floatingActionButton: FloatingActionButton.extended(
          onPressed: () => context.go(AppRoutes.saleAdd),
          icon: const Icon(Icons.add_shopping_cart),
          label: const Text('فاتورة جديدة'),
          backgroundColor: AppColors.primary,
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Enhanced Statistics Dashboard
              _buildEnhancedStatistics(saleState.sales),

              // Advanced Search and Filters
              _buildAdvancedFilters(customerState.customers),

              // Sales List with Real Data
              _buildEnhancedSalesList(saleState),
            ],
          ),
        ));
  }

  Widget _buildEnhancedStatistics(List<Sale> sales) {
    final today = DateTime.now();
    final todaySales = sales
        .where((s) =>
            s.createdAt != null &&
            s.createdAt!.year == today.year &&
            s.createdAt!.month == today.month &&
            s.createdAt!.day == today.day)
        .toList();

    final thisMonth = sales
        .where((s) =>
            s.createdAt != null &&
            s.createdAt!.year == today.year &&
            s.createdAt!.month == today.month)
        .toList();

    final totalSales = sales.length;
    final totalAmount = sales.fold<double>(0, (sum, sale) => sum + sale.total);
    final paidAmount = sales.fold<double>(0, (sum, sale) => sum + sale.paid);
    final dueAmount = totalAmount - paidAmount;
    final todayAmount =
        todaySales.fold<double>(0, (sum, sale) => sum + sale.total);
    final monthAmount =
        thisMonth.fold<double>(0, (sum, sale) => sum + sale.total);
    final completedSales = sales.where((s) => s.status == 'completed').length;

    return Container(
      padding: EdgeInsets.all(16.r),
      child: Column(
        children: [
          // Main Statistics Row
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  icon: Icons.receipt_long,
                  title: 'إجمالي الفواتير',
                  value: totalSales.toString(),
                  color: AppColors.primary,
                  subtitle: '$completedSales مكتملة',
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.attach_money,
                  title: 'إجمالي المبيعات',
                  value: AppFormatters.formatCurrency(totalAmount),
                  color: AppColors.success,
                  subtitle: 'جميع الفترات',
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.payment,
                  title: 'المحصل',
                  value: AppFormatters.formatCurrency(paidAmount),
                  color: AppColors.info,
                  subtitle:
                      '${((paidAmount / totalAmount) * 100).toStringAsFixed(1)}%',
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.pending_actions,
                  title: 'المستحق',
                  value: AppFormatters.formatCurrency(dueAmount),
                  color: AppColors.warning,
                  subtitle: '${sales.where((s) => s.due > 0).length} فاتورة',
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // Period Statistics Row
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  icon: Icons.today,
                  title: 'مبيعات اليوم',
                  value: AppFormatters.formatCurrency(todayAmount),
                  color: AppColors.secondary,
                  subtitle: '${todaySales.length} فاتورة',
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.calendar_month,
                  title: 'مبيعات الشهر',
                  value: AppFormatters.formatCurrency(monthAmount),
                  color: AppColors.accent,
                  subtitle: '${thisMonth.length} فاتورة',
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.trending_up,
                  title: 'متوسط الفاتورة',
                  value: AppFormatters.formatCurrency(
                      totalSales > 0 ? totalAmount / totalSales : 0),
                  color: AppColors.primary,
                  subtitle: 'لكل فاتورة',
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.speed,
                  title: 'معدل التحصيل',
                  value:
                      '${totalAmount > 0 ? ((paidAmount / totalAmount) * 100).toStringAsFixed(1) : 0}%',
                  color: paidAmount / totalAmount > 0.8
                      ? AppColors.success
                      : AppColors.warning,
                  subtitle: 'من الإجمالي',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
    required String subtitle,
  }) {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          children: [
            Icon(icon, size: 32.r, color: color),
            SizedBox(height: 8.h),
            Text(
              value,
              style: AppTextStyles.headlineSmall.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 4.h),
            Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 2.h),
            Text(
              subtitle,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.secondaryDark,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedFilters(List<dynamic> customers) {
    return Container(
      padding: EdgeInsets.all(16.r),
      child: Column(
        children: [
          // Search Bar with Enhanced Design
          Container(
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: AppColors.outline.withOpacity(0.3)),
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث برقم الفاتورة، العميل، أو الملاحظات...',
                prefixIcon: Icon(Icons.search, color: AppColors.primary),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() => _searchQuery = '');
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              ),
              onChanged: (value) => setState(() => _searchQuery = value),
            ),
          ),

          SizedBox(height: 16.h),

          // Advanced Filters Row
          Row(
            children: [
              // Status Filter
              Expanded(
                child: _buildFilterDropdown(
                  label: 'الحالة',
                  value: _selectedStatus,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                    DropdownMenuItem(value: 'pending', child: Text('معلقة')),
                    DropdownMenuItem(value: 'completed', child: Text('مكتملة')),
                    DropdownMenuItem(value: 'cancelled', child: Text('ملغية')),
                  ],
                  onChanged: (value) =>
                      setState(() => _selectedStatus = value!),
                ),
              ),

              SizedBox(width: 12.w),

              // Customer Filter
              Expanded(
                child: _buildFilterDropdown(
                  label: 'العميل',
                  value: _selectedCustomer,
                  items: [
                    const DropdownMenuItem(
                        value: 'all', child: Text('جميع العملاء')),
                    const DropdownMenuItem(value: 'cash', child: Text('نقدي')),
                    ...customers.map((customer) => DropdownMenuItem(
                          value: customer.id,
                          child: Text(customer.name),
                        )),
                  ],
                  onChanged: (value) =>
                      setState(() => _selectedCustomer = value!),
                ),
              ),

              SizedBox(width: 12.w),

              // Payment Method Filter
              Expanded(
                child: _buildFilterDropdown(
                  label: 'طريقة الدفع',
                  value: _selectedPaymentMethod,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الطرق')),
                    DropdownMenuItem(value: 'cash', child: Text('نقدي')),
                    DropdownMenuItem(value: 'credit', child: Text('آجل')),
                    DropdownMenuItem(value: 'card', child: Text('بطاقة')),
                    DropdownMenuItem(
                        value: 'bank_transfer', child: Text('تحويل بنكي')),
                  ],
                  onChanged: (value) =>
                      setState(() => _selectedPaymentMethod = value!),
                ),
              ),

              SizedBox(width: 12.w),

              // Date Range Filter
              OutlinedButton.icon(
                onPressed: _selectDateRange,
                icon: const Icon(Icons.date_range),
                label:
                    Text(_dateRange == null ? 'فترة زمنية' : 'مفلتر بالتاريخ'),
                style: OutlinedButton.styleFrom(
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                ),
              ),
            ],
          ),

          SizedBox(height: 12.h),

          // Quick Filter Chips
          Row(
            children: [
              FilterChip(
                label: const Text('المستحقات فقط'),
                selected: _showOnlyDue,
                onSelected: (selected) =>
                    setState(() => _showOnlyDue = selected),
                selectedColor: AppColors.warning.withOpacity(0.2),
              ),
              SizedBox(width: 8.w),
              if (_dateRange != null)
                ActionChip(
                  label: const Text('إلغاء فلتر التاريخ'),
                  onPressed: () => setState(() => _dateRange = null),
                  backgroundColor: AppColors.error.withOpacity(0.1),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown({
    required String label,
    required String value,
    required List<DropdownMenuItem<String>> items,
    required ValueChanged<String?> onChanged,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
        contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      ),
      items: items,
      onChanged: onChanged,
      isExpanded: true,
    );
  }

  Widget _buildEnhancedSalesList(dynamic saleState) {
    if (saleState.isLoading) {
      return const LoadingWidget();
    }

    if (saleState.error != null) {
      return CustomErrorWidget(
        message: saleState.error!,
        onRetry: () => ref.read(saleManagementProvider.notifier).refresh(),
      );
    }

    final filteredSales = _filterSales(saleState.sales);

    if (filteredSales.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.receipt_long_outlined,
        title: 'لا توجد فواتير مبيعات',
        subtitle: 'ابدأ بإضافة فاتورة مبيعات جديدة',
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      itemCount: filteredSales.length,
      itemBuilder: (context, index) {
        final sale = filteredSales[index];
        return _buildEnhancedSaleCard(sale);
      },
    );
  }

  Widget _buildEnhancedSaleCard(Sale sale) {
    final customerState = ref.watch(customerProvider);
    final customer = sale.customerId != null
        ? customerState.customers
            .where((c) => c.id == sale.customerId)
            .firstOrNull
        : null;

    return CustomCard(
      margin: EdgeInsets.only(bottom: 16.h),
      child: InkWell(
        onTap: () => context.go('${AppRoutes.sales}/${sale.id}'),
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(12.r),
                    decoration: BoxDecoration(
                      color: _getStatusColor(sale.paymentStatus.displayName)
                          .withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(
                      Icons.receipt_long,
                      color: _getStatusColor(sale.paymentStatus.displayName),
                      size: 24.r,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              sale.invoiceNo,
                              style: AppTextStyles.titleLarge.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Spacer(),
                            _buildStatusChip(sale.paymentStatus.displayName),
                          ],
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          customer?.name ?? 'عميل نقدي',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.onSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleMenuAction(value, sale),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                          value: 'view', child: Text('عرض التفاصيل')),
                      const PopupMenuItem(value: 'edit', child: Text('تعديل')),
                      const PopupMenuItem(value: 'print', child: Text('طباعة')),
                      const PopupMenuItem(value: 'delete', child: Text('حذف')),
                    ],
                  ),
                ],
              ),

              SizedBox(height: 16.h),

              // Details Grid
              Row(
                children: [
                  Expanded(
                    child: _buildDetailItem(
                      icon: Icons.calendar_today,
                      label: 'التاريخ',
                      value: AppFormatters.formatDate(
                          sale.createdAt ?? DateTime.now()),
                    ),
                  ),
                  Expanded(
                    child: _buildDetailItem(
                      icon: Icons.payment,
                      label: 'طريقة الدفع',
                      value:
                          _getPaymentMethodText(sale.paymentMethod ?? 'cash'),
                    ),
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // Financial Summary
              Container(
                padding: EdgeInsets.all(12.r),
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: _buildFinancialItem(
                        label: 'الإجمالي',
                        value: AppFormatters.formatCurrency(sale.total),
                        color: AppColors.primary,
                      ),
                    ),
                    Container(
                      width: 1,
                      height: 40.h,
                      color: AppColors.outline.withOpacity(0.3),
                    ),
                    Expanded(
                      child: _buildFinancialItem(
                        label: 'المدفوع',
                        value: AppFormatters.formatCurrency(sale.paid),
                        color: AppColors.success,
                      ),
                    ),
                    if (sale.due > 0) ...[
                      Container(
                        width: 1,
                        height: 40.h,
                        color: AppColors.outline.withOpacity(0.3),
                      ),
                      Expanded(
                        child: _buildFinancialItem(
                          label: 'المستحق',
                          value: AppFormatters.formatCurrency(sale.due),
                          color: AppColors.warning,
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              if (sale.notes != null && sale.notes!.isNotEmpty) ...[
                SizedBox(height: 12.h),
                Container(
                  padding: EdgeInsets.all(8.r),
                  decoration: BoxDecoration(
                    color: AppColors.info.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6.r),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.note, size: 16.r, color: AppColors.info),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Text(
                          sale.notes!,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.info,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16.r, color: AppColors.onSecondary),
        SizedBox(width: 8.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.onSecondary,
              ),
            ),
            Text(
              value,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFinancialItem({
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.onSecondary,
          ),
        ),
        SizedBox(height: 4.h),
        Text(
          value,
          style: AppTextStyles.titleMedium.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip(String status) {
    final color = _getStatusColor(status);
    final text = _getStatusText(status);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: AppTextStyles.bodySmall.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'completed':
        return AppColors.success;
      case 'pending':
        return AppColors.warning;
      case 'cancelled':
        return AppColors.error;
      default:
        return AppColors.onSecondary;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'completed':
        return 'مكتملة';
      case 'pending':
        return 'معلقة';
      case 'cancelled':
        return 'ملغية';
      default:
        return 'غير محدد';
    }
  }

  String _getPaymentMethodText(String method) {
    switch (method) {
      case 'cash':
        return 'نقدي';
      case 'credit':
        return 'آجل';
      case 'card':
        return 'بطاقة';
      case 'bank_transfer':
        return 'تحويل بنكي';
      default:
        return method;
    }
  }

  List<Sale> _filterSales(List<Sale> sales) {
    return sales.where((sale) {
      // Search filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!sale.invoiceNo.toLowerCase().contains(query) &&
            !(sale.notes?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // Status filter
      if (_selectedStatus != 'all' && sale.status != _selectedStatus) {
        return false;
      }

      // Customer filter
      if (_selectedCustomer != 'all') {
        if (_selectedCustomer == 'cash' && sale.customerId != null) {
          return false;
        } else if (_selectedCustomer != 'cash' &&
            sale.customerId != _selectedCustomer) {
          return false;
        }
      }

      // Payment method filter
      if (_selectedPaymentMethod != 'all' &&
          sale.paymentMethod != _selectedPaymentMethod) {
        return false;
      }

      // Due filter
      if (_showOnlyDue && sale.due <= 0) {
        return false;
      }

      // Date range filter
      if (_dateRange != null) {
        final saleDate = sale.createdAt;
        if (saleDate != null &&
            (saleDate.isBefore(_dateRange!.start) ||
                saleDate
                    .isAfter(_dateRange!.end.add(const Duration(days: 1))))) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _dateRange,
    );

    if (picked != null) {
      setState(() => _dateRange = picked);
    }
  }

  void _handleMenuAction(String action, Sale sale) {
    switch (action) {
      case 'view':
        context.go('${AppRoutes.sales}/${sale.id}');
        break;
      case 'edit':
        context.go('${AppRoutes.sales}/${sale.id}/edit');
        break;
      case 'print':
        _printInvoice(sale);
        break;
      case 'delete':
        _showDeleteDialog(sale);
        break;
    }
  }

  void _printInvoice(Sale sale) {
    // TODO: Implement print functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تنفيذ الطباعة قريباً')),
    );
  }

  void _showDeleteDialog(Sale sale) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف فاتورة المبيعات ${sale.invoiceNo}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(saleManagementProvider.notifier).deleteSale(sale.id);
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
