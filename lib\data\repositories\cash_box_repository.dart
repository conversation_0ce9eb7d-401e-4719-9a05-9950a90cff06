import 'package:tijari_tech/core/exceptions/app_exceptions.dart';

import '../models/cash_box.dart';
import '../local/dao/cash_box_dao.dart';
import '../../core/utils/app_utils.dart';


/// Repository للصناديق النقدية
class CashBoxRepository {
  final CashBoxDao _cashBoxDao;

  CashBoxRepository({CashBoxDao? cashBoxDao}) 
      : _cashBoxDao = cashBoxDao ?? CashBoxDao();

  // ------------------------------------------------------------------
  // العمليات الأساسية
  // ------------------------------------------------------------------

  /// إنشاء صندوق جديد
  Future<String> createCashBox(CashBox cashBox) async {
    try {
      AppUtils.logInfo('Creating cash box: ${cashBox.name}');
      
      // التحقق من عدم وجود صندوق بنفس الاسم
      final existingCashBox = await _cashBoxDao.findByName(cashBox.name);
      if (existingCashBox != null) {
        throw RepositoryException('يوجد صندوق بنفس الاسم مسبقاً');
      }

      final id = await _cashBoxDao.insert(cashBox);
      AppUtils.logInfo('Cash box created successfully with ID: $id');
      return id;
    } catch (e) {
      AppUtils.logError('Error creating cash box', e);
      if (e is RepositoryException) rethrow;
      throw RepositoryException('فشل في إنشاء الصندوق');
    }
  }

  /// تحديث صندوق موجود
  Future<void> updateCashBox(CashBox cashBox) async {
    try {
      AppUtils.logInfo('Updating cash box: ${cashBox.id}');
      
      // التحقق من وجود الصندوق
      final existingCashBox = await _cashBoxDao.findById(cashBox.id);
      if (existingCashBox == null) {
        throw RepositoryException('الصندوق غير موجود');
      }

      // التحقق من عدم وجود صندوق آخر بنفس الاسم
      final duplicateCashBox = await _cashBoxDao.findByName(cashBox.name);
      if (duplicateCashBox != null && duplicateCashBox.id != cashBox.id) {
        throw RepositoryException('يوجد صندوق آخر بنفس الاسم');
      }

      await _cashBoxDao.update(cashBox.id,cashBox);
      AppUtils.logInfo('Cash box updated successfully: ${cashBox.id}');
    } catch (e) {
      AppUtils.logError('Error updating cash box', e);
      if (e is RepositoryException) rethrow;
      throw RepositoryException('فشل في تحديث الصندوق');
    }
  }

  /// حذف صندوق (حذف ناعم)
  Future<void> deleteCashBox(String cashBoxId) async {
    try {
      AppUtils.logInfo('Deleting cash box: $cashBoxId');
      
      // التحقق من وجود الصندوق
      final cashBox = await _cashBoxDao.findById(cashBoxId);
      if (cashBox == null) {
        throw RepositoryException('الصندوق غير موجود');
      }

      // التحقق من أن الرصيد صفر قبل الحذف
      if (cashBox.balance != 0) {
        throw RepositoryException('لا يمكن حذف صندوق يحتوي على رصيد');
      }

      await _cashBoxDao.softDelete(cashBoxId);
      AppUtils.logInfo('Cash box deleted successfully: $cashBoxId');
    } catch (e) {
      AppUtils.logError('Error deleting cash box', e);
      if (e is RepositoryException) rethrow;
      throw RepositoryException('فشل في حذف الصندوق');
    }
  }

  /// استعادة صندوق محذوف
  Future<void> restoreCashBox(String cashBoxId) async {
    try {
      AppUtils.logInfo('Restoring cash box: $cashBoxId');
      await _cashBoxDao.restore(cashBoxId);
      AppUtils.logInfo('Cash box restored successfully: $cashBoxId');
    } catch (e) {
      AppUtils.logError('Error restoring cash box', e);
      throw RepositoryException('فشل في استعادة الصندوق');
    }
  }

  // ------------------------------------------------------------------
  // عمليات الاستعلام
  // ------------------------------------------------------------------

  /// الحصول على صندوق بالمعرف
  Future<CashBox?> getCashBoxById(String id) async {
    try {
      return await _cashBoxDao.findById(id);
    } catch (e) {
      AppUtils.logError('Error getting cash box by ID', e);
      throw RepositoryException('فشل في استرجاع الصندوق');
    }
  }

  /// الحصول على جميع الصناديق النشطة
  Future<List<CashBox>> getActiveCashBoxes() async {
    try {
      return await _cashBoxDao.getActiveCashBoxes();
    } catch (e) {
      AppUtils.logError('Error getting active cash boxes', e);
      throw RepositoryException('فشل في استرجاع الصناديق النشطة');
    }
  }

  /// الحصول على جميع الصناديق
  Future<List<CashBox>> getAllCashBoxes() async {
    try {
      return await _cashBoxDao.getAllCashBoxes();
    } catch (e) {
      AppUtils.logError('Error getting all cash boxes', e);
      throw RepositoryException('فشل في استرجاع جميع الصناديق');
    }
  }

  /// الحصول على الصندوق الافتراضي
  Future<CashBox?> getDefaultCashBox() async {
    try {
      return await _cashBoxDao.getDefaultCashBox();
    } catch (e) {
      AppUtils.logError('Error getting default cash box', e);
      throw RepositoryException('فشل في استرجاع الصندوق الافتراضي');
    }
  }

  /// الحصول على الصناديق حسب الفرع
  Future<List<CashBox>> getCashBoxesByBranch(String branchId) async {
    try {
      return await _cashBoxDao.getCashBoxesByBranch(branchId);
    } catch (e) {
      AppUtils.logError('Error getting cash boxes by branch', e);
      throw RepositoryException('فشل في استرجاع صناديق الفرع');
    }
  }

  /// البحث في الصناديق
  Future<List<CashBox>> searchCashBoxes(String query) async {
    try {
      if (query.trim().isEmpty) {
        return await getActiveCashBoxes();
      }
      return await _cashBoxDao.searchCashBoxes(query);
    } catch (e) {
      AppUtils.logError('Error searching cash boxes', e);
      throw RepositoryException('فشل في البحث عن الصناديق');
    }
  }

  // ------------------------------------------------------------------
  // عمليات الرصيد
  // ------------------------------------------------------------------

  /// تحديث رصيد الصندوق
  Future<void> updateBalance(String cashBoxId, double newBalance) async {
    try {
      AppUtils.logInfo('Updating cash box balance: $cashBoxId -> $newBalance');
      
      if (newBalance < 0) {
        throw RepositoryException('لا يمكن أن يكون الرصيد سالباً');
      }

      await _cashBoxDao.updateBalance(cashBoxId, newBalance);
      AppUtils.logInfo('Cash box balance updated successfully');
    } catch (e) {
      AppUtils.logError('Error updating cash box balance', e);
      if (e is RepositoryException) rethrow;
      throw RepositoryException('فشل في تحديث رصيد الصندوق');
    }
  }

  /// إضافة مبلغ إلى الصندوق
  Future<void> addToBalance(String cashBoxId, double amount) async {
    try {
      AppUtils.logInfo('Adding to cash box balance: $cashBoxId + $amount');
      
      if (amount <= 0) {
        throw RepositoryException('المبلغ يجب أن يكون أكبر من صفر');
      }

      await _cashBoxDao.addToBalance(cashBoxId, amount);
      AppUtils.logInfo('Amount added to cash box successfully');
    } catch (e) {
      AppUtils.logError('Error adding to cash box balance', e);
      if (e is RepositoryException) rethrow;
      throw RepositoryException('فشل في إضافة المبلغ للصندوق');
    }
  }

  /// طرح مبلغ من الصندوق
  Future<void> subtractFromBalance(String cashBoxId, double amount) async {
    try {
      AppUtils.logInfo('Subtracting from cash box balance: $cashBoxId - $amount');
      
      if (amount <= 0) {
        throw RepositoryException('المبلغ يجب أن يكون أكبر من صفر');
      }

      await _cashBoxDao.subtractFromBalance(cashBoxId, amount);
      AppUtils.logInfo('Amount subtracted from cash box successfully');
    } catch (e) {
      AppUtils.logError('Error subtracting from cash box balance', e);
      if (e is RepositoryException) rethrow;
      throw RepositoryException('فشل في طرح المبلغ من الصندوق');
    }
  }

  /// الحصول على إجمالي أرصدة الصناديق النشطة
  Future<double> getTotalActiveBalance() async {
    try {
      return await _cashBoxDao.getTotalActiveBalance();
    } catch (e) {
      AppUtils.logError('Error getting total active balance', e);
      throw RepositoryException('فشل في حساب إجمالي الأرصدة');
    }
  }

  // ------------------------------------------------------------------
  // عمليات إضافية
  // ------------------------------------------------------------------

  /// تفعيل/إلغاء تفعيل الصندوق
  Future<void> toggleActive(String cashBoxId, bool isActive) async {
    try {
      AppUtils.logInfo('Toggling cash box active status: $cashBoxId -> $isActive');
      await _cashBoxDao.toggleActive(cashBoxId, isActive);
      AppUtils.logInfo('Cash box active status updated successfully');
    } catch (e) {
      AppUtils.logError('Error toggling cash box active status', e);
      throw RepositoryException('فشل في تغيير حالة الصندوق');
    }
  }

  /// الحصول على إحصائيات الصناديق
  Future<Map<String, dynamic>> getCashBoxStatistics() async {
    try {
      return await _cashBoxDao.getCashBoxStatistics();
    } catch (e) {
      AppUtils.logError('Error getting cash box statistics', e);
      throw RepositoryException('فشل في استرجاع إحصائيات الصناديق');
    }
  }

  /// التحقق من صحة البيانات قبل الحفظ
  void _validateCashBox(CashBox cashBox) {
    if (cashBox.name.trim().isEmpty) {
      throw RepositoryException('اسم الصندوق مطلوب');
    }

    if (cashBox.name.trim().length < 2) {
      throw RepositoryException('اسم الصندوق يجب أن يكون أكثر من حرفين');
    }

    if (cashBox.balance < 0) {
      throw RepositoryException('رصيد الصندوق لا يمكن أن يكون سالباً');
    }
  }
}
