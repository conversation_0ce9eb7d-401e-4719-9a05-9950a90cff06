import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/ai_prediction.dart';
import '../data/repositories/ai_prediction_repository.dart';
import '../services/ai_analytics_service.dart';

// AI Prediction repository provider
final aiPredictionRepositoryProvider = Provider<AIPredictionRepository>((ref) {
  return AIPredictionRepository();
});

// AI Analytics service provider
final aiAnalyticsServiceProvider = Provider<AIAnalyticsService>((ref) {
  return AIAnalyticsService();
});

// All predictions provider
final aiPredictionsProvider = StateNotifierProvider<AIPredictionsNotifier, AsyncValue<List<AIPrediction>>>((ref) {
  return AIPredictionsNotifier(
    ref.read(aiPredictionRepositoryProvider),
    ref.read(aiAnalyticsServiceProvider),
  );
});

// Predictions by type provider
final predictionsByTypeProvider = StateNotifierProvider.family<PredictionsByTypeNotifier, AsyncValue<List<AIPrediction>>, String>((ref, type) {
  return PredictionsByTypeNotifier(
    ref.read(aiPredictionRepositoryProvider),
    type,
  );
});

// AI Insights provider
final aiInsightsProvider = StateNotifierProvider<AIInsightsNotifier, AsyncValue<List<AIInsight>>>((ref) {
  return AIInsightsNotifier(
    ref.read(aiPredictionRepositoryProvider),
    ref.read(aiAnalyticsServiceProvider),
  );
});

// Prediction statistics provider
final predictionStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.read(aiPredictionRepositoryProvider);
  return await repository.getPredictionStatistics();
});

// Insight statistics provider
final insightStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.read(aiPredictionRepositoryProvider);
  return await repository.getInsightStatistics();
});

// High confidence predictions provider
final highConfidencePredictionsProvider = FutureProvider<List<AIPrediction>>((ref) async {
  final repository = ref.read(aiPredictionRepositoryProvider);
  return await repository.getHighConfidencePredictions();
});

// Actionable insights provider
final actionableInsightsProvider = FutureProvider<List<AIInsight>>((ref) async {
  final repository = ref.read(aiPredictionRepositoryProvider);
  return await repository.getActionableInsights();
});

// AI Predictions notifier
class AIPredictionsNotifier extends StateNotifier<AsyncValue<List<AIPrediction>>> {
  final AIPredictionRepository _repository;
  final AIAnalyticsService _analyticsService;

  AIPredictionsNotifier(this._repository, this._analyticsService) 
      : super(const AsyncValue.loading()) {
    loadPredictions();
  }

  Future<void> loadPredictions() async {
    state = const AsyncValue.loading();
    try {
      final predictions = await _repository.getAllPredictions();
      state = AsyncValue.data(predictions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> generateSalesPrediction({
    required String period,
    required DateTime targetDate,
  }) async {
    try {
      final prediction = await _analyticsService.generateSalesPrediction(
        period: period,
        targetDate: targetDate,
      );
      
      await _repository.createPrediction(prediction);
      await loadPredictions(); // Reload the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> generateInventoryPrediction({
    required String period,
    required DateTime targetDate,
  }) async {
    try {
      final prediction = await _analyticsService.generateInventoryPrediction(
        period: period,
        targetDate: targetDate,
      );
      
      await _repository.createPrediction(prediction);
      await loadPredictions(); // Reload the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> generateCashFlowPrediction({
    required String period,
    required DateTime targetDate,
  }) async {
    try {
      final prediction = await _analyticsService.generateCashFlowPrediction(
        period: period,
        targetDate: targetDate,
      );
      
      await _repository.createPrediction(prediction);
      await loadPredictions(); // Reload the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deletePrediction(String predictionId) async {
    try {
      await _repository.deletePrediction(predictionId);
      await loadPredictions(); // Reload the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> generateAllPredictions() async {
    try {
      final DateTime targetDate = DateTime.now().add(const Duration(days: 30));
      
      // Generate sales prediction
      final salesPrediction = await _analyticsService.generateSalesPrediction(
        period: PredictionPeriod.monthly,
        targetDate: targetDate,
      );
      
      // Generate inventory prediction
      final inventoryPrediction = await _analyticsService.generateInventoryPrediction(
        period: PredictionPeriod.monthly,
        targetDate: targetDate,
      );
      
      // Generate cash flow prediction
      final cashFlowPrediction = await _analyticsService.generateCashFlowPrediction(
        period: PredictionPeriod.monthly,
        targetDate: targetDate,
      );
      
      // Save all predictions
      await _repository.createMultiplePredictions([
        salesPrediction,
        inventoryPrediction,
        cashFlowPrediction,
      ]);
      
      await loadPredictions(); // Reload the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Predictions by type notifier
class PredictionsByTypeNotifier extends StateNotifier<AsyncValue<List<AIPrediction>>> {
  final AIPredictionRepository _repository;
  final String _type;

  PredictionsByTypeNotifier(this._repository, this._type) 
      : super(const AsyncValue.loading()) {
    loadPredictionsByType();
  }

  Future<void> loadPredictionsByType() async {
    state = const AsyncValue.loading();
    try {
      final predictions = await _repository.getPredictionsByType(_type);
      state = AsyncValue.data(predictions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// AI Insights notifier
class AIInsightsNotifier extends StateNotifier<AsyncValue<List<AIInsight>>> {
  final AIPredictionRepository _repository;
  final AIAnalyticsService _analyticsService;

  AIInsightsNotifier(this._repository, this._analyticsService) 
      : super(const AsyncValue.loading()) {
    loadInsights();
  }

  Future<void> loadInsights() async {
    state = const AsyncValue.loading();
    try {
      final insights = await _repository.getAllInsights();
      state = AsyncValue.data(insights);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> generateInsights() async {
    try {
      final insights = await _analyticsService.generateInsights();
      await _repository.createMultipleInsights(insights);
      await loadInsights(); // Reload the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteInsight(String insightId) async {
    try {
      await _repository.deleteInsight(insightId);
      await loadInsights(); // Reload the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> markInsightAsActioned(String insightId) async {
    try {
      final insights = state.value ?? [];
      final insight = insights.firstWhere((i) => i.id == insightId);
      
      final updatedInsight = AIInsight(
        id: insight.id,
        category: insight.category,
        title: insight.title,
        description: insight.description,
        recommendation: insight.recommendation,
        impact: insight.impact,
        priority: insight.priority,
        data: insight.data,
        createdAt: insight.createdAt,
        actionDate: DateTime.now(),
        isActionable: false,
      );
      
      await _repository.updateInsight(updatedInsight);
      await loadInsights(); // Reload the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Latest prediction by type provider
final latestPredictionByTypeProvider = FutureProvider.family<AIPrediction?, String>((ref, type) async {
  final repository = ref.read(aiPredictionRepositoryProvider);
  return await repository.getLatestPredictionByType(type);
});

// Insights by category provider
final insightsByCategoryProvider = FutureProvider.family<List<AIInsight>, String>((ref, category) async {
  final repository = ref.read(aiPredictionRepositoryProvider);
  return await repository.getInsightsByCategory(category);
});

// AI Dashboard data provider
final aiDashboardDataProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.read(aiPredictionRepositoryProvider);
  
  // Get latest predictions
  final salesPrediction = await repository.getLatestPredictionByType(PredictionType.sales);
  final inventoryPrediction = await repository.getLatestPredictionByType(PredictionType.inventory);
  final cashFlowPrediction = await repository.getLatestPredictionByType(PredictionType.cashFlow);
  
  // Get high priority insights
  final insights = await repository.getAllInsights(limit: 5);
  final highPriorityInsights = insights.where((i) => i.priority == InsightPriority.high).toList();
  
  // Get statistics
  final predictionStats = await repository.getPredictionStatistics();
  final insightStats = await repository.getInsightStatistics();
  
  return {
    'latest_predictions': {
      'sales': salesPrediction,
      'inventory': inventoryPrediction,
      'cash_flow': cashFlowPrediction,
    },
    'high_priority_insights': highPriorityInsights,
    'prediction_statistics': predictionStats,
    'insight_statistics': insightStats,
  };
});

// AI Performance metrics provider
final aiPerformanceMetricsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.read(aiPredictionRepositoryProvider);
  
  // Get all predictions
  final predictions = await repository.getAllPredictions();
  
  // Calculate accuracy metrics (simplified)
  final Map<String, List<double>> confidenceByType = {};
  for (final prediction in predictions) {
    confidenceByType[prediction.type] ??= [];
    confidenceByType[prediction.type]!.add(prediction.confidence);
  }
  
  final Map<String, double> averageConfidence = {};
  for (final entry in confidenceByType.entries) {
    averageConfidence[entry.key] = entry.value.reduce((a, b) => a + b) / entry.value.length;
  }
  
  return {
    'total_predictions': predictions.length,
    'average_confidence_by_type': averageConfidence,
    'high_confidence_count': predictions.where((p) => p.isHighConfidence).length,
    'medium_confidence_count': predictions.where((p) => p.isMediumConfidence).length,
    'low_confidence_count': predictions.where((p) => p.isLowConfidence).length,
  };
});

// Prediction validation provider
final predictionValidationProvider = Provider<PredictionValidation>((ref) {
  return PredictionValidation();
});

// Prediction validation class
class PredictionValidation {
  String? validatePeriod(String? period) {
    if (period == null || period.trim().isEmpty) {
      return 'الفترة الزمنية مطلوبة';
    }
    
    if (!PredictionPeriod.allPeriods.contains(period)) {
      return 'الفترة الزمنية غير صحيحة';
    }
    
    return null;
  }

  String? validateTargetDate(DateTime? targetDate) {
    if (targetDate == null) {
      return 'التاريخ المستهدف مطلوب';
    }
    
    final now = DateTime.now();
    if (targetDate.isBefore(now)) {
      return 'التاريخ المستهدف يجب أن يكون في المستقبل';
    }
    
    final maxDate = now.add(const Duration(days: 365));
    if (targetDate.isAfter(maxDate)) {
      return 'التاريخ المستهدف لا يمكن أن يكون أكثر من سنة';
    }
    
    return null;
  }

  String? validatePredictionType(String? type) {
    if (type == null || type.trim().isEmpty) {
      return 'نوع التوقع مطلوب';
    }
    
    if (!PredictionType.allTypes.contains(type)) {
      return 'نوع التوقع غير صحيح';
    }
    
    return null;
  }
}
