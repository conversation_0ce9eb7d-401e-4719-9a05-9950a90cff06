import 'package:flutter/foundation.dart';
import 'development_config.dart';
import 'dev_auth_service.dart';
import '../utils/app_utils.dart';

/// Development environment initializer
/// Sets up the app for easy testing without production constraints
class DevInitializer {
  static bool _isInitialized = false;
  
  /// Initialize development environment
  static Future<void> initialize() async {
    if (_isInitialized || !kDebugMode) return;
    
    try {
      AppUtils.logInfo('🚀 Initializing development environment...');
      
      // Configure development settings
      DevelopmentConfig.configure(
        bypassAuth: true,
        bypassPermissions: true,
        bypassValidation: true,
        useMockData: true,
        skipEncryption: true,
        logAllOperations: true,
        usePredictableIds: false, // Keep UUIDs for realistic testing
      );
      
      // Set up mock user context
      DevUserContext.setMockContext({
        'userId': 'system',
        'userName': 'Development User',
        'userEmail': '<EMAIL>',
        'branchId': 'main-branch',
        'warehouseId': 'main-warehouse',
        'role': 'admin',
        'permissions': ['all'],
        'isActive': true,
        'canTransferStock': true,
        'canCreateSale': true,
        'canCreatePurchase': true,
        'canAdjustStock': true,
        'canViewReports': true,
        'canManageUsers': true,
      });
      
      // Initialize development auth
      await DevAuthService.init();
      await DevPermissionService.load(role: 'admin');
      
      _isInitialized = true;
      
      AppUtils.logInfo('✅ Development environment initialized successfully');
      _logDevStatus();
      
    } catch (e) {
      AppUtils.logError('❌ Failed to initialize development environment', e);
      rethrow;
    }
  }
  
  /// Configure for specific testing scenario
  static void configureForTesting({
    String? scenario,
    Map<String, dynamic>? customConfig,
  }) {
    if (!kDebugMode) return;
    
    AppUtils.logInfo('🔧 Configuring for testing scenario: ${scenario ?? 'custom'}');
    
    switch (scenario) {
      case 'stock_operations':
        _configureForStockOperations();
        break;
      case 'sales_testing':
        _configureForSalesTesting();
        break;
      case 'purchase_testing':
        _configureForPurchaseTesting();
        break;
      case 'full_bypass':
        _configureForFullBypass();
        break;
      case 'production_like':
        _configureForProductionLike();
        break;
      default:
        if (customConfig != null) {
          _applyCustomConfig(customConfig);
        }
    }
    
    _logDevStatus();
  }
  
  /// Configure for stock operations testing
  static void _configureForStockOperations() {
    DevelopmentConfig.configure(
      bypassAuth: true,
      bypassPermissions: true,
      bypassValidation: false, // Keep some validation for realistic testing
      useMockData: true,
      skipEncryption: true,
      logAllOperations: true,
    );
    
    DevUserContext.setMockContext({
      'userId': 'system',
      'userName': 'Stock Manager',
      'branchId': 'main-branch',
      'warehouseId': 'main-warehouse',
      'role': 'stock_manager',
      'canTransferStock': true,
      'canAdjustStock': true,
    });
    
    AppUtils.logInfo('📦 Configured for stock operations testing');
  }
  
  /// Configure for sales testing
  static void _configureForSalesTesting() {
    DevelopmentConfig.configure(
      bypassAuth: true,
      bypassPermissions: true,
      bypassValidation: false, // Keep validation for realistic sales flow
      useMockData: true,
      skipEncryption: true,
      logAllOperations: true,
    );
    
    DevUserContext.setMockContext({
      'userId': 'system',
      'userName': 'Sales Person',
      'branchId': 'main-branch',
      'warehouseId': 'main-warehouse',
      'role': 'salesperson',
      'canCreateSale': true,
      'canViewReports': true,
    });
    
    AppUtils.logInfo('💰 Configured for sales testing');
  }
  
  /// Configure for purchase testing
  static void _configureForPurchaseTesting() {
    DevelopmentConfig.configure(
      bypassAuth: true,
      bypassPermissions: true,
      bypassValidation: false,
      useMockData: true,
      skipEncryption: true,
      logAllOperations: true,
    );
    
    DevUserContext.setMockContext({
      'userId': 'system',
      'userName': 'Purchase Manager',
      'branchId': 'main-branch',
      'warehouseId': 'main-warehouse',
      'role': 'purchase_manager',
      'canCreatePurchase': true,
      'canViewReports': true,
    });
    
    AppUtils.logInfo('🛒 Configured for purchase testing');
  }
  
  /// Configure for full bypass (fastest testing)
  static void _configureForFullBypass() {
    DevelopmentConfig.configure(
      bypassAuth: true,
      bypassPermissions: true,
      bypassValidation: true,
      useMockData: true,
      skipEncryption: true,
      logAllOperations: true,
    );
    
    DevUserContext.setMockContext({
      'userId': 'system',
      'userName': 'Super Admin',
      'branchId': 'main-branch',
      'warehouseId': 'main-warehouse',
      'role': 'super_admin',
      'permissions': ['all'],
    });
    
    AppUtils.logInfo('🚀 Configured for full bypass testing');
  }
  
  /// Configure for production-like testing
  static void _configureForProductionLike() {
    DevelopmentConfig.configure(
      bypassAuth: false,
      bypassPermissions: false,
      bypassValidation: false,
      useMockData: false,
      skipEncryption: false,
      logAllOperations: true,
    );
    
    DevUserContext.clearMockContext();
    
    AppUtils.logInfo('🏭 Configured for production-like testing');
  }
  
  /// Apply custom configuration
  static void _applyCustomConfig(Map<String, dynamic> config) {
    DevelopmentConfig.configure(
      bypassAuth: config['bypassAuth'],
      bypassPermissions: config['bypassPermissions'],
      bypassValidation: config['bypassValidation'],
      useMockData: config['useMockData'],
      skipEncryption: config['skipEncryption'],
      logAllOperations: config['logAllOperations'],
      usePredictableIds: config['usePredictableIds'],
    );
    
    if (config['mockContext'] != null) {
      DevUserContext.setMockContext(config['mockContext']);
    }
    
    AppUtils.logInfo('⚙️ Applied custom configuration');
  }
  
  /// Log current development status
  static void _logDevStatus() {
    final status = DevelopmentConfig.getStatus();
    AppUtils.logInfo('📊 Development Status:');
    status.forEach((key, value) {
      AppUtils.logInfo('  $key: $value');
    });
  }
  
  /// Create test data for development
  static Future<void> createTestData() async {
    if (!DevelopmentConfig.isEnabled) return;
    
    AppUtils.logInfo('🧪 Creating test data...');
    
    try {
      // This would create sample products, customers, suppliers, etc.
      // for testing purposes
      
      final testData = {
        'products': await _createTestProducts(),
        'customers': await _createTestCustomers(),
        'suppliers': await _createTestSuppliers(),
        'warehouses': await _createTestWarehouses(),
      };
      
      AppUtils.logInfo('✅ Test data created successfully');
      AppUtils.logInfo('📈 Created: ${testData.values.map((v) => v.length).join(', ')} records');
      
    } catch (e) {
      AppUtils.logError('❌ Failed to create test data', e);
    }
  }
  
  /// Create test products
  static Future<List<Map<String, dynamic>>> _createTestProducts() async {
    return [
      {
        'id': 'test-product-1',
        'name': 'Test Product 1',
        'barcode': '1234567890',
        'cost_price': 10.0,
        'selling_price': 15.0,
        'current_stock': 100.0,
        'base_unit_id': 'unit_piece',
      },
      {
        'id': 'test-product-2',
        'name': 'Test Product 2',
        'barcode': '1234567891',
        'cost_price': 20.0,
        'selling_price': 30.0,
        'current_stock': 50.0,
        'base_unit_id': 'unit_piece',
      },
    ];
  }
  
  /// Create test customers
  static Future<List<Map<String, dynamic>>> _createTestCustomers() async {
    return [
      {
        'id': 'test-customer-1',
        'name': 'Test Customer 1',
        'phone': '1234567890',
        'email': '<EMAIL>',
      },
      {
        'id': 'test-customer-2',
        'name': 'Test Customer 2',
        'phone': '1234567891',
        'email': '<EMAIL>',
      },
    ];
  }
  
  /// Create test suppliers
  static Future<List<Map<String, dynamic>>> _createTestSuppliers() async {
    return [
      {
        'id': 'test-supplier-1',
        'name': 'Test Supplier 1',
        'phone': '1234567890',
        'email': '<EMAIL>',
      },
    ];
  }
  
  /// Create test warehouses
  static Future<List<Map<String, dynamic>>> _createTestWarehouses() async {
    return [
      {
        'id': 'test-warehouse-1',
        'name': 'Test Warehouse 1',
        'location': 'Test Location',
        'is_active': true,
      },
    ];
  }
  
  /// Reset development environment
  static void reset() {
    if (!kDebugMode) return;
    
    AppUtils.logInfo('🔄 Resetting development environment...');
    
    DevelopmentConfig.resetToDefaults();
    DevUserContext.clearMockContext();
    _isInitialized = false;
    
    AppUtils.logInfo('✅ Development environment reset');
  }
  
  /// Disable development mode
  static void disable() {
    if (!kDebugMode) return;
    
    AppUtils.logInfo('🛑 Disabling development mode...');
    
    DevelopmentConfig.disableAll();
    DevUserContext.clearMockContext();
    
    AppUtils.logInfo('✅ Development mode disabled');
  }
  
  /// Get development info for debugging
  static Map<String, dynamic> getDevInfo() {
    return {
      'isInitialized': _isInitialized,
      'isDebugMode': kDebugMode,
      'config': DevelopmentConfig.getStatus(),
      'mockContext': DevUserContext.getCurrentContext(),
    };
  }
}
