import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../widgets/main_layout.dart';
import '../../providers/inventory_report_provider.dart';

class InventoryReportsPage extends ConsumerStatefulWidget {
  const InventoryReportsPage({super.key});

  @override
  ConsumerState<InventoryReportsPage> createState() => _InventoryReportsPageState();
}

class _InventoryReportsPageState extends ConsumerState<InventoryReportsPage> {
  String _selectedReportType = 'stock_summary';
  final Map<String, dynamic> _reportParameters = {};

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(inventoryReportManagementProvider);

    return MainLayout(
      title: 'تقارير المخزون',
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _generateCurrentReport,
        ),
        IconButton(
          icon: const Icon(Icons.download),
          onPressed: state.currentReportData.isNotEmpty ? _exportReport : null,
        ),
      ],
      child: Row(
        children: [
          // Report types sidebar
          Container(
            width: 250.w,
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(
                right: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: _buildReportTypesList(),
          ),
          
          // Main content area
          Expanded(
            child: Column(
              children: [
                // Parameters section
                _buildParametersSection(),
                
                // Report content
                Expanded(
                  child: _buildReportContent(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportTypesList() {
    final reportTypes = [
      {
        'id': 'stock_summary',
        'title': 'ملخص المخزون',
        'description': 'نظرة عامة على حالة المخزون',
        'icon': Icons.inventory,
      },
      {
        'id': 'movement_analysis',
        'title': 'تحليل حركة المخزون',
        'description': 'تحليل حركات الدخول والخروج',
        'icon': Icons.trending_up,
      },
      {
        'id': 'low_stock',
        'title': 'تقرير المخزون المنخفض',
        'description': 'المنتجات ذات المخزون المنخفض',
        'icon': Icons.warning,
      },
      {
        'id': 'abc_analysis',
        'title': 'تحليل ABC',
        'description': 'تصنيف المنتجات حسب الأهمية',
        'icon': Icons.analytics,
      },
    ];

    return ListView.builder(
      padding: EdgeInsets.all(8.r),
      itemCount: reportTypes.length,
      itemBuilder: (context, index) {
        final reportType = reportTypes[index];
        final isSelected = _selectedReportType == reportType['id'];

        return Card(
          margin: EdgeInsets.only(bottom: 8.h),
          color: isSelected ? AppColors.primary.withValues(alpha: 0.1) : null,
          child: ListTile(
            leading: Icon(
              reportType['icon'] as IconData,
              color: isSelected ? AppColors.primary : Colors.grey[600],
            ),
            title: Text(
              reportType['title'] as String,
              style: AppTextStyles.titleSmall.copyWith(
                color: isSelected ? AppColors.primary : null,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            subtitle: Text(
              reportType['description'] as String,
              style: AppTextStyles.bodySmall,
            ),
            onTap: () {
              setState(() {
                _selectedReportType = reportType['id'] as String;
                _reportParameters.clear();
              });
              ref.read(inventoryReportManagementProvider.notifier).clearCurrentReport();
            },
          ),
        );
      },
    );
  }

  Widget _buildParametersSection() {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معايير التقرير',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 12.h),
          _buildParametersForm(),
          SizedBox(height: 12.h),
          Row(
            children: [
              ElevatedButton.icon(
                onPressed: _generateCurrentReport,
                icon: const Icon(Icons.play_arrow),
                label: const Text('إنشاء التقرير'),
              ),
              SizedBox(width: 12.w),
              OutlinedButton.icon(
                onPressed: _clearParameters,
                icon: const Icon(Icons.clear),
                label: const Text('مسح المعايير'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildParametersForm() {
    switch (_selectedReportType) {
      case 'stock_summary':
        return _buildStockSummaryParameters();
      case 'movement_analysis':
        return _buildMovementAnalysisParameters();
      case 'low_stock':
        return _buildLowStockParameters();
      case 'abc_analysis':
        return _buildABCAnalysisParameters();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildStockSummaryParameters() {
    return Row(
      children: [
        Expanded(
          child: DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: 'المخزن',
              hintText: 'جميع المخازن',
            ),
            value: _reportParameters['warehouse_id'] as String?,
            items: const [], // TODO: Load warehouses
            onChanged: (value) {
              setState(() {
                _reportParameters['warehouse_id'] = value;
              });
            },
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: 'الفئة',
              hintText: 'جميع الفئات',
            ),
            value: _reportParameters['category_id'] as String?,
            items: const [], // TODO: Load categories
            onChanged: (value) {
              setState(() {
                _reportParameters['category_id'] = value;
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMovementAnalysisParameters() {
    return Row(
      children: [
        Expanded(
          child: TextFormField(
            decoration: const InputDecoration(
              labelText: 'من تاريخ',
              hintText: 'اختر التاريخ',
              suffixIcon: Icon(Icons.calendar_today),
            ),
            readOnly: true,
            onTap: () async {
              final date = await showDatePicker(
                context: context,
                initialDate: DateTime.now().subtract(const Duration(days: 30)),
                firstDate: DateTime(2020),
                lastDate: DateTime.now(),
              );
              if (date != null) {
                setState(() {
                  _reportParameters['from_date'] = date;
                });
              }
            },
            controller: TextEditingController(
              text: _reportParameters['from_date'] != null
                  ? DateFormat('yyyy/MM/dd').format(_reportParameters['from_date'])
                  : '',
            ),
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: TextFormField(
            decoration: const InputDecoration(
              labelText: 'إلى تاريخ',
              hintText: 'اختر التاريخ',
              suffixIcon: Icon(Icons.calendar_today),
            ),
            readOnly: true,
            onTap: () async {
              final date = await showDatePicker(
                context: context,
                initialDate: DateTime.now(),
                firstDate: DateTime(2020),
                lastDate: DateTime.now(),
              );
              if (date != null) {
                setState(() {
                  _reportParameters['to_date'] = date;
                });
              }
            },
            controller: TextEditingController(
              text: _reportParameters['to_date'] != null
                  ? DateFormat('yyyy/MM/dd').format(_reportParameters['to_date'])
                  : '',
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLowStockParameters() {
    return Row(
      children: [
        Expanded(
          child: TextFormField(
            decoration: const InputDecoration(
              labelText: 'حد المخزون المنخفض',
              hintText: '10',
            ),
            keyboardType: TextInputType.number,
            onChanged: (value) {
              setState(() {
                _reportParameters['threshold'] = double.tryParse(value);
              });
            },
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: 'المخزن',
              hintText: 'جميع المخازن',
            ),
            value: _reportParameters['warehouse_id'] as String?,
            items: const [], // TODO: Load warehouses
            onChanged: (value) {
              setState(() {
                _reportParameters['warehouse_id'] = value;
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _buildABCAnalysisParameters() {
    return Row(
      children: [
        Expanded(
          child: TextFormField(
            decoration: const InputDecoration(
              labelText: 'من تاريخ',
              hintText: 'اختر التاريخ',
              suffixIcon: Icon(Icons.calendar_today),
            ),
            readOnly: true,
            onTap: () async {
              final date = await showDatePicker(
                context: context,
                initialDate: DateTime.now().subtract(const Duration(days: 90)),
                firstDate: DateTime(2020),
                lastDate: DateTime.now(),
              );
              if (date != null) {
                setState(() {
                  _reportParameters['from_date'] = date;
                });
              }
            },
            controller: TextEditingController(
              text: _reportParameters['from_date'] != null
                  ? DateFormat('yyyy/MM/dd').format(_reportParameters['from_date'])
                  : '',
            ),
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: TextFormField(
            decoration: const InputDecoration(
              labelText: 'إلى تاريخ',
              hintText: 'اختر التاريخ',
              suffixIcon: Icon(Icons.calendar_today),
            ),
            readOnly: true,
            onTap: () async {
              final date = await showDatePicker(
                context: context,
                initialDate: DateTime.now(),
                firstDate: DateTime(2020),
                lastDate: DateTime.now(),
              );
              if (date != null) {
                setState(() {
                  _reportParameters['to_date'] = date;
                });
              }
            },
            controller: TextEditingController(
              text: _reportParameters['to_date'] != null
                  ? DateFormat('yyyy/MM/dd').format(_reportParameters['to_date'])
                  : '',
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildReportContent() {
    final state = ref.watch(inventoryReportManagementProvider);

    if (state.isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري إنشاء التقرير...'),
          ],
        ),
      );
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64.r,
              color: AppColors.error,
            ),
            SizedBox(height: 16.h),
            Text(
              'حدث خطأ في إنشاء التقرير',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.error,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              state.error!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: _generateCurrentReport,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (state.currentReportData.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assessment,
              size: 64.r,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16.h),
            Text(
              'لم يتم إنشاء أي تقرير بعد',
              style: AppTextStyles.titleMedium.copyWith(
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'اختر نوع التقرير وحدد المعايير ثم اضغط على "إنشاء التقرير"',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Report header
          _buildReportHeader(),
          SizedBox(height: 24.h),
          
          // Report summary
          _buildReportSummary(),
          SizedBox(height: 24.h),
          
          // Report details
          _buildReportDetails(),
        ],
      ),
    );
  }

  Widget _buildReportHeader() {
    final reportTitles = {
      'stock_summary': 'تقرير ملخص المخزون',
      'movement_analysis': 'تقرير تحليل حركة المخزون',
      'low_stock': 'تقرير المخزون المنخفض',
      'abc_analysis': 'تقرير تحليل ABC',
    };

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              reportTitles[_selectedReportType] ?? 'تقرير المخزون',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'تم إنشاؤه في: ${DateFormat('yyyy/MM/dd HH:mm').format(DateTime.now())}',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportSummary() {
    final summary = ref.read(inventoryReportManagementProvider.notifier).getCurrentReportSummary();
    
    if (summary.isEmpty) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص التقرير',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            Wrap(
              spacing: 16.w,
              runSpacing: 12.h,
              children: summary.entries.map((entry) {
                return _buildSummaryItem(
                  _formatKey(entry.key),
                  entry.value.toString(),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value) {
    return Container(
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildReportDetails() {
    // TODO: Implement detailed report content based on report type
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل التقرير',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            Text(
              'سيتم إضافة تفاصيل التقرير هنا...',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatKey(String key) {
    final keyMap = {
      'total_products': 'إجمالي المنتجات',
      'total_warehouses': 'إجمالي المخازن',
      'total_value': 'إجمالي القيمة',
      'low_stock_items': 'مخزون منخفض',
      'out_of_stock_items': 'نفاد المخزون',
      'total_movements': 'إجمالي الحركات',
      'in_movements': 'حركات الدخول',
      'out_movements': 'حركات الخروج',
    };
    return keyMap[key] ?? key;
  }

  void _generateCurrentReport() async {
    final notifier = ref.read(inventoryReportManagementProvider.notifier);
    bool success = false;

    switch (_selectedReportType) {
      case 'stock_summary':
        success = await notifier.generateStockSummaryReport(
          warehouseId: _reportParameters['warehouse_id'] as String?,
          categoryId: _reportParameters['category_id'] as String?,
        );
        break;
      case 'movement_analysis':
        success = await notifier.generateMovementAnalysisReport(
          warehouseId: _reportParameters['warehouse_id'] as String?,
          fromDate: _reportParameters['from_date'] as DateTime?,
          toDate: _reportParameters['to_date'] as DateTime?,
        );
        break;
      case 'low_stock':
        success = await notifier.generateLowStockReport(
          warehouseId: _reportParameters['warehouse_id'] as String?,
          threshold: _reportParameters['threshold'] as double?,
        );
        break;
      case 'abc_analysis':
        success = await notifier.generateABCAnalysisReport(
          warehouseId: _reportParameters['warehouse_id'] as String?,
          fromDate: _reportParameters['from_date'] as DateTime?,
          toDate: _reportParameters['to_date'] as DateTime?,
        );
        break;
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'تم إنشاء التقرير بنجاح' : 'فشل في إنشاء التقرير'),
          backgroundColor: success ? AppColors.success : AppColors.error,
        ),
      );
    }
  }

  void _clearParameters() {
    setState(() {
      _reportParameters.clear();
    });
  }

  void _exportReport() {
    // TODO: Implement report export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تطوير وظيفة التصدير قريباً')),
    );
  }
}
