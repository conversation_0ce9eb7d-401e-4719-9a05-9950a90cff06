import 'package:json_annotation/json_annotation.dart';
import '../../core/constants/database_constants.dart';

part 'employee.g.dart';

/// نموذج الموظف - Employee Model
/// يمثل بيانات الموظفين في النظام
@JsonSerializable()
class Employee {
  final String id;
  final String name;           // اسم الموظف
  final String? email;         // البريد الإلكتروني
  final String? phone;         // رقم الهاتف
  final String? address;       // العنوان
  final String position;       // المنصب/الوظيفة
  final String department;     // القسم
  final double salary;         // الراتب الأساسي
  final DateTime hireDate;     // تاريخ التوظيف
  final DateTime? terminationDate; // تاريخ انتهاء الخدمة
  final bool isActive;         // هل الموظف نشط
  final String? notes;         // ملاحظات
  final String? branchId;      // معرف الفرع
  final String? managerId;     // معرف المدير المباشر
  final String createdBy;      // من أنشأ السجل
  final DateTime createdAt;    // تاريخ الإنشاء
  final DateTime? updatedAt;   // تاريخ التحديث
  final DateTime? deletedAt;   // تاريخ الحذف (soft delete)
  final bool isSynced;         // هل تم المزامنة

  const Employee({
    required this.id,
    required this.name,
    this.email,
    this.phone,
    this.address,
    required this.position,
    required this.department,
    required this.salary,
    required this.hireDate,
    this.terminationDate,
    this.isActive = true,
    this.notes,
    this.branchId,
    this.managerId,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  /// إنشاء موظف من JSON
  factory Employee.fromJson(Map<String, dynamic> json) =>
      _$EmployeeFromJson(json);

  /// تحويل الموظف إلى JSON
  Map<String, dynamic> toJson() => _$EmployeeToJson(this);

  /// إنشاء موظف من Map قاعدة البيانات
  factory Employee.fromMap(Map<String, dynamic> map) {
    return Employee(
      id: map[DatabaseConstants.columnEmployeeId] as String,
      name: map[DatabaseConstants.columnEmployeeName] as String,
      email: map[DatabaseConstants.columnEmployeeEmail] as String?,
      phone: map[DatabaseConstants.columnEmployeePhone] as String?,
      address: map[DatabaseConstants.columnEmployeeAddress] as String?,
      position: map[DatabaseConstants.columnEmployeePosition] as String,
      department: map[DatabaseConstants.columnEmployeeDepartment] as String,
      salary: (map[DatabaseConstants.columnEmployeeSalary] as num).toDouble(),
      hireDate: DateTime.parse(map[DatabaseConstants.columnEmployeeHireDate] as String),
      terminationDate: map[DatabaseConstants.columnEmployeeTerminationDate] != null
          ? DateTime.parse(map[DatabaseConstants.columnEmployeeTerminationDate] as String)
          : null,
      isActive: (map[DatabaseConstants.columnEmployeeIsActive] as int) == 1,
      notes: map[DatabaseConstants.columnEmployeeNotes] as String?,
      branchId: map[DatabaseConstants.columnEmployeeBranchId] as String?,
      managerId: map[DatabaseConstants.columnEmployeeManagerId] as String?,
      createdBy: map[DatabaseConstants.columnEmployeeCreatedBy] as String,
      createdAt: DateTime.parse(map[DatabaseConstants.columnEmployeeCreatedAt] as String),
      updatedAt: map[DatabaseConstants.columnEmployeeUpdatedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnEmployeeUpdatedAt] as String)
          : null,
      deletedAt: map[DatabaseConstants.columnEmployeeDeletedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnEmployeeDeletedAt] as String)
          : null,
      isSynced: (map[DatabaseConstants.columnEmployeeIsSynced] as int) == 1,
    );
  }

  /// تحويل الموظف إلى Map لقاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnEmployeeId: id,
      DatabaseConstants.columnEmployeeName: name,
      DatabaseConstants.columnEmployeeEmail: email,
      DatabaseConstants.columnEmployeePhone: phone,
      DatabaseConstants.columnEmployeeAddress: address,
      DatabaseConstants.columnEmployeePosition: position,
      DatabaseConstants.columnEmployeeDepartment: department,
      DatabaseConstants.columnEmployeeSalary: salary,
      DatabaseConstants.columnEmployeeHireDate: hireDate.toIso8601String(),
      DatabaseConstants.columnEmployeeTerminationDate: terminationDate?.toIso8601String(),
      DatabaseConstants.columnEmployeeIsActive: isActive ? 1 : 0,
      DatabaseConstants.columnEmployeeNotes: notes,
      DatabaseConstants.columnEmployeeBranchId: branchId,
      DatabaseConstants.columnEmployeeManagerId: managerId,
      DatabaseConstants.columnEmployeeCreatedBy: createdBy,
      DatabaseConstants.columnEmployeeCreatedAt: createdAt.toIso8601String(),
      DatabaseConstants.columnEmployeeUpdatedAt: updatedAt?.toIso8601String(),
      DatabaseConstants.columnEmployeeDeletedAt: deletedAt?.toIso8601String(),
      DatabaseConstants.columnEmployeeIsSynced: isSynced ? 1 : 0,
    };
  }

  /// نسخ الموظف مع تعديل بعض الخصائص
  Employee copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    String? position,
    String? department,
    double? salary,
    DateTime? hireDate,
    DateTime? terminationDate,
    bool? isActive,
    String? notes,
    String? branchId,
    String? managerId,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return Employee(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      position: position ?? this.position,
      department: department ?? this.department,
      salary: salary ?? this.salary,
      hireDate: hireDate ?? this.hireDate,
      terminationDate: terminationDate ?? this.terminationDate,
      isActive: isActive ?? this.isActive,
      notes: notes ?? this.notes,
      branchId: branchId ?? this.branchId,
      managerId: managerId ?? this.managerId,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  /// التحقق من صحة بيانات الموظف
  bool get isValid {
    return id.isNotEmpty &&
        name.isNotEmpty &&
        position.isNotEmpty &&
        department.isNotEmpty &&
        salary >= 0 &&
        createdBy.isNotEmpty;
  }

  /// هل الموظف محذوف؟
  bool get isDeleted => deletedAt != null;

  /// هل الموظف منتهي الخدمة؟
  bool get isTerminated => terminationDate != null;

  /// مدة الخدمة بالأيام
  int get serviceDays {
    final endDate = terminationDate ?? DateTime.now();
    return endDate.difference(hireDate).inDays;
  }

  /// مدة الخدمة بالسنوات
  double get serviceYears {
    return serviceDays / 365.25;
  }

  /// إنشاء موظف جديد
  factory Employee.create({
    required String id,
    required String name,
    String? email,
    String? phone,
    String? address,
    required String position,
    required String department,
    required double salary,
    DateTime? hireDate,
    String? notes,
    String? branchId,
    String? managerId,
    required String createdBy,
  }) {
    final now = DateTime.now();
    return Employee(
      id: id,
      name: name,
      email: email,
      phone: phone,
      address: address,
      position: position,
      department: department,
      salary: salary,
      hireDate: hireDate ?? now,
      notes: notes,
      branchId: branchId,
      managerId: managerId,
      createdBy: createdBy,
      createdAt: now,
      isSynced: false,
    );
  }

  /// إنهاء خدمة الموظف
  Employee terminate({DateTime? terminationDate, String? reason}) {
    return copyWith(
      terminationDate: terminationDate ?? DateTime.now(),
      isActive: false,
      notes: reason != null ? '$notes\nسبب إنهاء الخدمة: $reason' : notes,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  @override
  String toString() {
    return 'Employee(id: $id, name: $name, position: $position, department: $department, salary: $salary)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Employee && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
