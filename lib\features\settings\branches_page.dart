import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tijari_tech/widgets/custom_text_field.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/responsive_wrapper.dart';
import '../../providers/branch_provider.dart';
import '../../data/models/branch.dart';
import '../../core/extensions/context_extensions.dart';

class BranchesPage extends ConsumerStatefulWidget {
  const BranchesPage({super.key});

  @override
  ConsumerState<BranchesPage> createState() => _BranchesPageState();
}

class _BranchesPageState extends ConsumerState<BranchesPage> {
  final _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'إدارة الفروع',
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddBranchDialog(),
        child: const Icon(Icons.add),
      ),
      child: Column(
        children: [
          // Search Section
          _buildSearchSection(),

          // Branches List
          Expanded(
            child: _buildBranchesList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
        padding: EdgeInsets.all(16.r),
        color: Colors.grey[50],
        child: SearchTextField(
          controller: _searchController,
          hint: 'البحث عن فرع...',
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        )
       
        );
  }

  Widget _buildBranchesList() {
    return Consumer(
      builder: (context, ref, child) {
        final branchesAsync = ref.watch(branchesProvider);

        return branchesAsync.when(
          data: (branches) {
            if (branches.isEmpty) {
              return _buildEmptyState();
            }

            // Filter branches based on search query
            final filteredBranches = branches.where((branch) {
              return branch.name
                      .toLowerCase()
                      .contains(_searchQuery.toLowerCase()) ||
                  (branch.address
                          ?.toLowerCase()
                          .contains(_searchQuery.toLowerCase()) ??
                      false);
            }).toList();

            if (filteredBranches.isEmpty && _searchQuery.isNotEmpty) {
              return _buildNoResultsState();
            }

            return ListView.builder(
              padding: EdgeInsets.all(16.r),
              itemCount: filteredBranches.length,
              itemBuilder: (context, index) {
                final branch = filteredBranches[index];
                return _buildBranchCard(branch);
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorState(error),
        );
      },
    );
  }

  Widget _buildBranchCard(Branch branch) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Branch Icon
                Container(
                  padding: EdgeInsets.all(12.r),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Icon(
                    Icons.location_on,
                    size: 24.r,
                    color: AppColors.primary,
                  ),
                ),
                SizedBox(width: 16.w),

                // Branch Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        branch.name,
                        style: AppTextStyles.bodyLarge.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (branch.address != null) ...[
                        SizedBox(height: 4.h),
                        Text(
                          branch.address!,
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                      SizedBox(height: 8.h),
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 8.w, vertical: 2.h),
                            decoration: BoxDecoration(
                              color: branch.isActive
                                  ? AppColors.success.withValues(alpha: 0.1)
                                  : AppColors.error.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                            child: Text(
                              branch.isActive ? 'نشط' : 'غير نشط',
                              style: AppTextStyles.bodySmall.copyWith(
                                color: branch.isActive
                                    ? AppColors.success
                                    : AppColors.error,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          if (branch.phone != null) ...[
                            SizedBox(width: 8.w),
                            Icon(
                              Icons.phone,
                              size: 16.r,
                              color: Colors.grey[600],
                            ),
                            SizedBox(width: 4.w),
                            Text(
                              branch.phone!,
                              style: AppTextStyles.bodySmall.copyWith(
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),

                // Actions Menu
                PopupMenuButton<String>(
                  onSelected: (value) => _handleBranchAction(value, branch),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit_outlined),
                          SizedBox(width: 8),
                          Text('تعديل'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete_outlined, color: Colors.red),
                          SizedBox(width: 8),
                          Text('حذف', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off_outlined,
            size: 64.r,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16.h),
          Text(
            'لا يوجد فروع',
            style: AppTextStyles.titleMedium.copyWith(
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'اضغط على زر الإضافة لإنشاء فرع جديد',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64.r,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد نتائج',
            style: AppTextStyles.titleMedium.copyWith(
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'لم يتم العثور على فروع تطابق البحث',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.r,
            color: Colors.red[400],
          ),
          SizedBox(height: 16.h),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: AppTextStyles.titleMedium.copyWith(
              color: Colors.red[600],
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            error.toString(),
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: () => ref.refresh(branchesProvider),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  void _handleBranchAction(String action, Branch branch) {
    switch (action) {
      case 'edit':
        _showEditBranchDialog(branch);
        break;
      case 'delete':
        _showDeleteBranchDialog(branch);
        break;
    }
  }

  void _showAddBranchDialog() {
    _showBranchDialog();
  }

  void _showEditBranchDialog(Branch branch) {
    _showBranchDialog(branch: branch);
  }

  void _showBranchDialog({Branch? branch}) {
    final isEditing = branch != null;
    final nameController = TextEditingController(text: branch?.name ?? '');
    final addressController =
        TextEditingController(text: branch?.address ?? '');
    final phoneController = TextEditingController(text: branch?.phone ?? '');
    final emailController = TextEditingController(text: branch?.email ?? '');
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isEditing ? 'تعديل الفرع' : 'إضافة فرع جديد'),
        content: SingleChildScrollView(
          child: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم الفرع',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'اسم الفرع مطلوب';
                    }
                    return null;
                  },
                ),
                SizedBox(height: 16.h),
                TextFormField(
                  controller: addressController,
                  decoration: const InputDecoration(
                    labelText: 'العنوان',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                SizedBox(height: 16.h),
                TextFormField(
                  controller: phoneController,
                  decoration: const InputDecoration(
                    labelText: 'رقم الهاتف',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.phone,
                ),
                SizedBox(height: 16.h),
                TextFormField(
                  controller: emailController,
                  decoration: const InputDecoration(
                    labelText: 'البريد الإلكتروني',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.emailAddress,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          Consumer(
            builder: (context, ref, child) => ElevatedButton(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  final success = isEditing
                      ? await ref
                          .read(branchManagementProvider.notifier)
                          .updateBranch(
                            id: branch.id,
                            name: nameController.text.trim(),
                            address: addressController.text.trim().isEmpty
                                ? null
                                : addressController.text.trim(),
                          )
                      : await ref
                          .read(branchManagementProvider.notifier)
                          .createBranch(
                            name: nameController.text.trim(),
                            address: addressController.text.trim().isEmpty
                                ? null
                                : addressController.text.trim(),
                          );

                  if (success) {
                    Navigator.of(context).pop();
                    context.showSuccessSnackBar(isEditing
                        ? 'تم تحديث الفرع بنجاح'
                        : 'تم إضافة الفرع بنجاح');
                  } else {
                    context.showErrorSnackBar('حدث خطأ أثناء العملية');
                  }
                }
              },
              child: Text(isEditing ? 'تحديث' : 'إضافة'),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteBranchDialog(Branch branch) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الفرع'),
        content: Text('هل أنت متأكد من حذف الفرع "${branch.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          Consumer(
            builder: (context, ref, child) => ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
              ),
              onPressed: () async {
                final success = await ref
                    .read(branchManagementProvider.notifier)
                    .deleteBranch(branch.id);

                Navigator.of(context).pop();

                if (success) {
                  context.showSuccessSnackBar('تم حذف الفرع بنجاح');
                } else {
                  context.showErrorSnackBar('حدث خطأ أثناء حذف الفرع');
                }
              },
              child: const Text('حذف', style: TextStyle(color: Colors.white)),
            ),
          ),
        ],
      ),
    );
  }
}
