import 'package:json_annotation/json_annotation.dart';

part 'transaction.g.dart';

@JsonSerializable()
class Transactions {
  final String id;
  final String type; // receipt, payment, sale, purchase, adjustment
  final String subType; // cash, bank, customer, supplier, general
  final String referenceNumber;
  final double amount;
  final String? description;
  final String? notes;
  final DateTime transactionDate;
  final String? customerId;
  final String? supplierId;
  final String? saleId;
  final String? purchaseId;
  final String? cashBoxId;
  final String? bankAccountId;
  final String paymentMethod; // cash, bank_transfer, check, credit_card
  final String status; // pending, completed, cancelled
  final String createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? branchId;
  final Map<String, dynamic>? metadata;

  const Transactions({
    required this.id,
    required this.type,
    required this.subType,
    required this.referenceNumber,
    required this.amount,
    this.description,
    this.notes,
    required this.transactionDate,
    this.customerId,
    this.supplierId,
    this.saleId,
    this.purchaseId,
    this.cashBoxId,
    this.bankAccountId,
    required this.paymentMethod,
    this.status = 'completed',
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.branchId,
    this.metadata,
  });

  factory Transactions.fromJson(Map<String, dynamic> json) => _$TransactionsFromJson(json);
  Map<String, dynamic> toJson() => _$TransactionsToJson(this);

  factory Transactions.fromMap(Map<String, dynamic> map) {
    return Transactions(
      id: map['id'] as String,
      type: map['type'] as String,
      subType: map['sub_type'] as String,
      referenceNumber: map['reference_number'] as String,
      amount: (map['amount'] as num).toDouble(),
      description: map['description'] as String?,
      notes: map['notes'] as String?,
      transactionDate: DateTime.parse(map['transaction_date'] as String),
      customerId: map['customer_id'] as String?,
      supplierId: map['supplier_id'] as String?,
      saleId: map['sale_id'] as String?,
      purchaseId: map['purchase_id'] as String?,
      cashBoxId: map['cash_box_id'] as String?,
      bankAccountId: map['bank_account_id'] as String?,
      paymentMethod: map['payment_method'] as String,
      status: map['status'] as String? ?? 'completed',
      createdBy: map['created_by'] as String,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at'] as String) 
          : null,
      branchId: map['branch_id'] as String?,
      metadata: map['metadata'] != null 
          ? Map<String, dynamic>.from(map['metadata'] as Map)
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type,
      'sub_type': subType,
      'reference_number': referenceNumber,
      'amount': amount,
      'description': description,
      'notes': notes,
      'transaction_date': transactionDate.toIso8601String(),
      'customer_id': customerId,
      'supplier_id': supplierId,
      'sale_id': saleId,
      'purchase_id': purchaseId,
      'cash_box_id': cashBoxId,
      'bank_account_id': bankAccountId,
      'payment_method': paymentMethod,
      'status': status,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'branch_id': branchId,
      'metadata': metadata,
    };
  }

  Transactions copyWith({
    String? id,
    String? type,
    String? subType,
    String? referenceNumber,
    double? amount,
    String? description,
    String? notes,
    DateTime? transactionDate,
    String? customerId,
    String? supplierId,
    String? saleId,
    String? purchaseId,
    String? cashBoxId,
    String? bankAccountId,
    String? paymentMethod,
    String? status,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? branchId,
    Map<String, dynamic>? metadata,
  }) {
    return Transactions(
      id: id ?? this.id,
      type: type ?? this.type,
      subType: subType ?? this.subType,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      amount: amount ?? this.amount,
      description: description ?? this.description,
      notes: notes ?? this.notes,
      transactionDate: transactionDate ?? this.transactionDate,
      customerId: customerId ?? this.customerId,
      supplierId: supplierId ?? this.supplierId,
      saleId: saleId ?? this.saleId,
      purchaseId: purchaseId ?? this.purchaseId,
      cashBoxId: cashBoxId ?? this.cashBoxId,
      bankAccountId: bankAccountId ?? this.bankAccountId,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      status: status ?? this.status,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      branchId: branchId ?? this.branchId,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Transactions && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Transaction(id: $id, type: $type, amount: $amount, referenceNumber: $referenceNumber)';
  }

  // Helper methods
  bool get isIncome => type == TransactionType.receipt || type == TransactionType.sale;
  bool get isExpense => type == TransactionType.payment || type == TransactionType.purchase;
  bool get isPending => status == TransactionStatus.pending;
  bool get isCompleted => status == TransactionStatus.completed;
  bool get isCancelled => status == TransactionStatus.cancelled;

  String get displayType {
    switch (type) {
      case TransactionType.receipt:
        return 'إيصال قبض';
      case TransactionType.payment:
        return 'إيصال صرف';
      case TransactionType.sale:
        return 'فاتورة مبيعات';
      case TransactionType.purchase:
        return 'فاتورة مشتريات';
      case TransactionType.adjustment:
        return 'تسوية';
      case TransactionType.transfer:
        return 'تحويل';
      default:
        return type;
    }
  }

  String get displayPaymentMethod {
    switch (paymentMethod) {
      case PaymentMethod.cash:
        return 'نقداً';
      case PaymentMethod.bankTransfer:
        return 'تحويل بنكي';
      case PaymentMethod.check:
        return 'شيك';
      case PaymentMethod.creditCard:
        return 'بطاقة ائتمان';
      default:
        return paymentMethod;
    }
  }

  String get displayStatus {
    switch (status) {
      case TransactionStatus.pending:
        return 'معلق';
      case TransactionStatus.completed:
        return 'مكتمل';
      case TransactionStatus.cancelled:
        return 'ملغي';
      default:
        return status;
    }
  }
}

// Transaction types constants
class TransactionType {
  static const String receipt = 'receipt';
  static const String payment = 'payment';
  static const String sale = 'sale';
  static const String purchase = 'purchase';
  static const String adjustment = 'adjustment';
  static const String transfer = 'transfer';
  static const String opening = 'opening';
  static const String closing = 'closing';

  static List<String> get allTypes => [
    receipt, payment, sale, purchase, adjustment, transfer, opening, closing,
  ];
}

// Transaction sub-types constants
class TransactionSubType {
  static const String cash = 'cash';
  static const String bank = 'bank';
  static const String customer = 'customer';
  static const String supplier = 'supplier';
  static const String general = 'general';
  static const String internal = 'internal';

  static List<String> get allSubTypes => [
    cash, bank, customer, supplier, general, internal,
  ];
}

// Payment methods constants
class PaymentMethod {
  static const String cash = 'cash';
  static const String bankTransfer = 'bank_transfer';
  static const String check = 'check';
  static const String creditCard = 'credit_card';
  static const String debitCard = 'debit_card';
  static const String digitalWallet = 'digital_wallet';

  static List<String> get allMethods => [
    cash, bankTransfer, check, creditCard, debitCard, digitalWallet,
  ];

  static List<String> get displayNames => [
    'نقداً', 'تحويل بنكي', 'شيك', 'بطاقة ائتمان', 'بطاقة خصم', 'محفظة رقمية',
  ];
}

// Transaction status constants
class TransactionStatus {
  static const String pending = 'pending';
  static const String completed = 'completed';
  static const String cancelled = 'cancelled';
  static const String failed = 'failed';

  static List<String> get allStatuses => [
    pending, completed, cancelled, failed,
  ];
}
