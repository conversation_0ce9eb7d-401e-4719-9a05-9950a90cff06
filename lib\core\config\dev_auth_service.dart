import '../auth/auth_service.dart';
import '../permission/permission_service.dart';
import 'development_config.dart';
import '../utils/app_utils.dart';

/// Development-aware authentication service
/// Bypasses auth checks when in development mode
class DevAuthService {
  static final AuthService _authService = AuthService();
  
  /// Check if user is logged in (bypassed in dev mode)
  static bool isLoggedIn() {
    if (DevelopmentConfig.bypassAuth) {
      DevelopmentConfig.logDevOperation('Auth check bypassed', {'result': true});
      return true;
    }
    return AuthService.isLoggedIn();
  }
  
  /// Check if session is expired (bypassed in dev mode)
  static bool isSessionExpired() {
    if (DevelopmentConfig.bypassAuth) {
      DevelopmentConfig.logDevOperation('Session expiry check bypassed', {'result': false});
      return false;
    }
    return AuthService.isSessionExpired();
  }
  
  /// Get current user (mock in dev mode)
  static dynamic getCurrentUser() {
    if (DevelopmentConfig.useMockData) {
      final mockUser = DevelopmentConfig.getMockUserContext();
      DevelopmentConfig.logDevOperation('Mock user returned', mockUser);
      return mockUser;
    }
    return AuthService.currentUser;
  }
  
  /// Login (bypassed in dev mode)
  static Future<void> login({
    required String email,
    required String password,
  }) async {
    if (DevelopmentConfig.bypassAuth) {
      DevelopmentConfig.logDevOperation('Login bypassed', {'email': email});
      return;
    }
    return _authService.login(email: email, password: password);
  }
  
  /// Logout (bypassed in dev mode)
  static Future<void> logout() async {
    if (DevelopmentConfig.bypassAuth) {
      DevelopmentConfig.logDevOperation('Logout bypassed', null);
      return;
    }
    return AuthService.logout();
  }
  
  /// Refresh session (bypassed in dev mode)
  static Future<bool> refreshSession() async {
    if (DevelopmentConfig.bypassAuth) {
      DevelopmentConfig.logDevOperation('Session refresh bypassed', {'result': true});
      return true;
    }
    return AuthService.refreshSession();
  }
  
  /// Initialize auth service
  static Future<void> init() async {
    if (DevelopmentConfig.bypassAuth) {
      DevelopmentConfig.logDevOperation('Auth init bypassed', null);
      return;
    }
    return AuthService.init();
  }
}

/// Development-aware permission service
/// Bypasses permission checks when in development mode
class DevPermissionService {
  /// Load permissions (bypassed in dev mode)
  static Future<void> load({String? role}) async {
    if (DevelopmentConfig.bypassPermissions) {
      DevelopmentConfig.logDevOperation('Permission load bypassed', {'role': role});
      return;
    }
    return PermissionService.load(role: role);
  }
  
  /// Check any permission (bypassed in dev mode)
  static bool hasPermission(String permission) {
    if (DevelopmentConfig.bypassPermissions) {
      DevelopmentConfig.logDevOperation('Permission check bypassed', {
        'permission': permission,
        'result': true
      });
      return true;
    }
    
    // Map permission strings to actual permission checks
    switch (permission) {
      case 'inventory':
        return PermissionService.hasInventoryAccess();
      case 'inventoryEdit':
        return PermissionService.hasInventoryEditAccess();
      case 'inventoryDelete':
        return PermissionService.hasInventoryDeleteAccess();
      case 'sales':
        return PermissionService.hasSalesAccess();
      case 'salesEdit':
        return PermissionService.hasSalesEditAccess();
      case 'salesDelete':
        return PermissionService.hasSalesDeleteAccess();
      case 'purchase':
        return PermissionService.hasPurchaseAccess();
      case 'purchaseEdit':
        return PermissionService.hasPurchaseEditAccess();
      case 'purchaseDelete':
        return PermissionService.hasPurchaseDeleteAccess();
      case 'stockManagement':
        return PermissionService.hasStockManagementAccess();
      case 'stockAdjustment':
        return PermissionService.hasStockAdjustmentAccess();
      case 'stockTransfer':
        return PermissionService.hasStockTransferAccess();
      case 'warehouseManagement':
        return PermissionService.hasWarehouseManagementAccess();
      case 'pos':
        return PermissionService.hasPOSAccess();
      case 'reports':
        return PermissionService.hasReportsAccess();
      case 'admin':
        return PermissionService.hasAdminAccess();
      default:
        return false;
    }
  }
  
  // Convenience methods that bypass in dev mode
  static bool hasInventoryAccess() => hasPermission('inventory');
  static bool hasInventoryEditAccess() => hasPermission('inventoryEdit');
  static bool hasInventoryDeleteAccess() => hasPermission('inventoryDelete');
  static bool hasSalesAccess() => hasPermission('sales');
  static bool hasSalesEditAccess() => hasPermission('salesEdit');
  static bool hasSalesDeleteAccess() => hasPermission('salesDelete');
  static bool hasPurchaseAccess() => hasPermission('purchase');
  static bool hasPurchaseEditAccess() => hasPermission('purchaseEdit');
  static bool hasPurchaseDeleteAccess() => hasPermission('purchaseDelete');
  static bool hasStockManagementAccess() => hasPermission('stockManagement');
  static bool hasStockAdjustmentAccess() => hasPermission('stockAdjustment');
  static bool hasStockTransferAccess() => hasPermission('stockTransfer');
  static bool hasWarehouseManagementAccess() => hasPermission('warehouseManagement');
  static bool hasPOSAccess() => hasPermission('pos');
  static bool hasReportsAccess() => hasPermission('reports');
  static bool hasAdminAccess() => hasPermission('admin');
}

/// Development-aware route guard
/// Bypasses route protection when in development mode
class DevRouteGuard {
  /// Check access to route (bypassed in dev mode)
  static String? checkAccess(dynamic context, dynamic state) {
    if (DevelopmentConfig.bypassAuth) {
      DevelopmentConfig.logDevOperation('Route guard bypassed', {
        'route': state?.uri?.toString() ?? 'unknown'
      });
      return null; // Allow access
    }
    
    // Use original route guard logic
    // Note: You'll need to import and use the actual RouteGuard here
    return null; // For now, allow access
  }
  
  /// Check if user can edit module (bypassed in dev mode)
  static bool canEdit(String module) {
    if (DevelopmentConfig.bypassPermissions) {
      DevelopmentConfig.logDevOperation('Edit permission bypassed', {
        'module': module,
        'result': true
      });
      return true;
    }
    
    return DevPermissionService.hasPermission('${module}Edit');
  }
  
  /// Check if user can delete from module (bypassed in dev mode)
  static bool canDelete(String module) {
    if (DevelopmentConfig.bypassPermissions) {
      DevelopmentConfig.logDevOperation('Delete permission bypassed', {
        'module': module,
        'result': true
      });
      return true;
    }
    
    return DevPermissionService.hasPermission('${module}Delete');
  }
  
  /// Check if user can view reports (bypassed in dev mode)
  static bool canViewReports(String reportType) {
    if (DevelopmentConfig.bypassPermissions) {
      DevelopmentConfig.logDevOperation('Report permission bypassed', {
        'reportType': reportType,
        'result': true
      });
      return true;
    }
    
    return DevPermissionService.hasReportsAccess();
  }
  
  /// Check if user can access POS (bypassed in dev mode)
  static bool canAccessPOS() {
    if (DevelopmentConfig.bypassPermissions) {
      DevelopmentConfig.logDevOperation('POS permission bypassed', {'result': true});
      return true;
    }
    
    return DevPermissionService.hasPOSAccess();
  }
  
  /// Check if user can manage warehouses (bypassed in dev mode)
  static bool canManageWarehouses() {
    if (DevelopmentConfig.bypassPermissions) {
      DevelopmentConfig.logDevOperation('Warehouse management permission bypassed', {'result': true});
      return true;
    }
    
    return DevPermissionService.hasWarehouseManagementAccess();
  }
}

/// Development validation helper
/// Bypasses business rule validations when in development mode
class DevValidationHelper {
  /// Validate user context (bypassed in dev mode)
  static Map<String, dynamic> validateUserContext({
    String? userId,
    String? branchId,
    String? warehouseId,
  }) {
    if (DevelopmentConfig.bypassValidation) {
      final context = DevUserContext.getCurrentContext();
      final result = {
        'valid': true,
        'userId': userId ?? context['userId'],
        'branchId': branchId ?? context['branchId'],
        'warehouseId': warehouseId ?? context['warehouseId'],
      };
      DevelopmentConfig.logDevOperation('User context validation bypassed', result);
      return result;
    }
    
    // Perform actual validation
    return {
      'valid': userId != null && branchId != null,
      'userId': userId,
      'branchId': branchId,
      'warehouseId': warehouseId,
    };
  }
  
  /// Validate stock operation (bypassed in dev mode)
  static bool validateStockOperation({
    required String operation,
    required double quantity,
    double? currentStock,
  }) {
    if (DevelopmentConfig.bypassValidation) {
      DevelopmentConfig.logDevOperation('Stock operation validation bypassed', {
        'operation': operation,
        'quantity': quantity,
        'result': true
      });
      return true;
    }
    
    // Perform actual validation
    if (operation == 'subtract' && currentStock != null) {
      return currentStock >= quantity;
    }
    return quantity >= 0;
  }
  
  /// Validate business rules (bypassed in dev mode)
  static bool validateBusinessRules(String ruleType, Map<String, dynamic> data) {
    if (DevelopmentConfig.bypassValidation) {
      DevelopmentConfig.logDevOperation('Business rule validation bypassed', {
        'ruleType': ruleType,
        'result': true
      });
      return true;
    }
    
    // Implement actual business rule validation
    return true;
  }
}
