// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Product _$ProductFromJson(Map<String, dynamic> json) => Product(
      id: json['id'] as String,
      nameAr: json['nameAr'] as String,
      nameEn: json['nameEn'] as String?,
      barcode: json['barcode'] as String?,
      qrCode: json['qrCode'] as String?,
      description: json['description'] as String?,
      categoryId: json['categoryId'] as String?,
      baseUnitId: json['baseUnitId'] as String?,
      costPrice: (json['costPrice'] as num).toDouble(),
      sellingPrice: (json['sellingPrice'] as num).toDouble(),
      minStock: (json['minStock'] as num?)?.toDouble() ?? 0,
      maxStock: (json['maxStock'] as num?)?.toDouble() ?? 0,
      reorderPoint: (json['reorderPoint'] as num?)?.toDouble() ?? 0,
      trackStock: json['trackStock'] as bool? ?? true,
      isActive: json['isActive'] as bool? ?? true,
      allowNegativeStock: json['allowNegativeStock'] as bool? ?? false,
      hasExpiryDate: json['hasExpiryDate'] as bool? ?? false,
      expiryDate: json['expiryDate'] == null
          ? null
          : DateTime.parse(json['expiryDate'] as String),
      tags: json['tags'] as String?,
      notes: json['notes'] as String?,
      weight: (json['weight'] as num?)?.toDouble(),
      volume: (json['volume'] as num?)?.toDouble(),
      sku: json['sku'] as String?,
      manufacturerCode: json['manufacturerCode'] as String?,
      supplierCode: json['supplierCode'] as String?,
      imageUrl: json['imageUrl'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      isSynced: json['isSynced'] as bool? ?? false,
      images: (json['images'] as List<dynamic>?)
          ?.map((e) => ProductImage.fromJson(e as Map<String, dynamic>))
          .toList(),
      unitPrices: (json['unitPrices'] as List<dynamic>?)
          ?.map((e) => ProductUnitPrice.fromJson(e as Map<String, dynamic>))
          .toList(),
      categoryName: json['categoryName'] as String?,
      unitName: json['unitName'] as String?,
      currentStock: (json['currentStock'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$ProductToJson(Product instance) => <String, dynamic>{
      'id': instance.id,
      'nameAr': instance.nameAr,
      'nameEn': instance.nameEn,
      'barcode': instance.barcode,
      'qrCode': instance.qrCode,
      'description': instance.description,
      'categoryId': instance.categoryId,
      'baseUnitId': instance.baseUnitId,
      'costPrice': instance.costPrice,
      'sellingPrice': instance.sellingPrice,
      'minStock': instance.minStock,
      'maxStock': instance.maxStock,
      'reorderPoint': instance.reorderPoint,
      'trackStock': instance.trackStock,
      'isActive': instance.isActive,
      'allowNegativeStock': instance.allowNegativeStock,
      'hasExpiryDate': instance.hasExpiryDate,
      'expiryDate': instance.expiryDate?.toIso8601String(),
      'tags': instance.tags,
      'notes': instance.notes,
      'weight': instance.weight,
      'volume': instance.volume,
      'sku': instance.sku,
      'manufacturerCode': instance.manufacturerCode,
      'supplierCode': instance.supplierCode,
      'imageUrl': instance.imageUrl,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'isSynced': instance.isSynced,
      'images': instance.images,
      'unitPrices': instance.unitPrices,
      'categoryName': instance.categoryName,
      'unitName': instance.unitName,
      'currentStock': instance.currentStock,
    };
