// import '../data/repositories/product_repository.dart';
// import '../data/repositories/sale_repository.dart';
// import '../data/repositories/purchase_repository.dart';
// import '../data/repositories/stock_repository.dart';
// import '../data/models/sale.dart';
// import '../data/models/sale_item.dart';
// import '../data/models/purchase.dart';
// import '../data/models/purchase_item.dart';
// import '../core/utils/app_utils.dart';
// import '../core/exceptions/app_exceptions.dart';
// import 'default_data_service.dart';
// import 'entity_relationship_service.dart';

// /// خدمة العمليات التجارية المتكاملة مع الربط الذكي بين الكيانات
// class IntegratedBusinessService {
//   static final IntegratedBusinessService _instance =
//       IntegratedBusinessService._internal();
//   factory IntegratedBusinessService() => _instance;
//   IntegratedBusinessService._internal();

//   final ProductRepository _productRepository = ProductRepository();
//   final SaleRepository _saleRepository = SaleRepository();
//   final PurchaseRepository _purchaseRepository = PurchaseRepository();
//   final StockRepository _stockRepository = StockRepository();
//   final DefaultDataService _defaultDataService = DefaultDataService();
//   final EntityRelationshipService _relationshipService =
//       EntityRelationshipService();

//   /// إنشاء عملية بيع مع الربط الذكي والمرن
//   Future<Map<String, dynamic>> createSmartSaleTransaction({
//     String? customerId,
//     required List<Map<String, dynamic>>
//         items, // [{productId, qty, unitPrice, unitId?}]
//     String? warehouseId,
//     String? userId,
//     String? branchId,
//     String? invoiceNo,
//     DateTime? saleDate,
//     double paidAmount = 0,
//     String? notes,
//   }) async {
//     try {
//       AppUtils.logInfo('بدء عملية بيع ذكية مع الربط المرن');

//       // التحقق من صحة العلاقات وحل الكيانات الافتراضية
//       final relationshipValidation =
//           await _relationshipService.validateEntityRelationships(
//         customerId: customerId,
//         warehouseId: warehouseId,
//         userId: userId,
//         branchId: branchId,
//         productIds: items.map((item) => item['productId'] as String).toList(),
//       );

//       if (!relationshipValidation['valid']) {
//         return {
//           'success': false,
//           'errors': relationshipValidation['errors'],
//           'sale_id': null,
//         };
//       }

//       // استخراج الكيانات المحلولة
//       final resolvedEntities =
//           relationshipValidation['resolved_entities'] as Map<String, dynamic>;
//       final finalCustomerId =
//           customerId ?? DefaultDataService.defaultCustomerId;
//       final finalWarehouseId =
//           warehouseId ?? DefaultDataService.defaultWarehouseId;
//       final finalUserId = userId ?? DefaultDataService.defaultUserId;
//       final finalBranchId = branchId ?? DefaultDataService.defaultBranchId;

//       // التحقق من توفر المخزون
//       final stockValidation =
//           await _relationshipService.validateStockAvailability(
//         items: items,
//         warehouseId: finalWarehouseId,
//       );

//       if (!stockValidation['valid']) {
//         return {
//           'success': false,
//           'errors': ['نقص في المخزون'],
//           'stock_validation': stockValidation,
//           'sale_id': null,
//         };
//       }

//       // حساب الإجمالي وإنشاء عناصر البيع
//       double totalAmount = 0;
//       final saleItems = <SaleItem>[];

//       for (final item in items) {
//         final productId = item['productId'] as String;
//         final qty = (item['qty'] as num).toDouble();
//         final unitPrice = (item['unitPrice'] as num).toDouble();
//         final unitId =
//             item['unitId'] as String? ?? DefaultDataService.defaultUnitId;

//         final totalPrice = qty * unitPrice;
//         totalAmount += totalPrice;

//         final saleItem = SaleItem.create(
//           id: AppUtils.generateId(),
//           saleId: '', // سيتم تعيينه لاحقاً
//           productId: productId,
//           unitId: unitId,
//           qty: qty,
//           unitPrice: unitPrice,
//         );

//         saleItems.add(saleItem);
//       }

//       // إنشاء فاتورة البيع
//       final sale = Sale.create(
//         id: AppUtils.generateId(),
//         customerId: finalCustomerId,
//         invoiceNo: invoiceNo ?? await _saleRepository.generateInvoiceNumber(),
//         branchId: finalBranchId,
//         date: saleDate ?? DateTime.now(),
//         total: totalAmount,
//         paid: paidAmount,
//         notes: notes,
//       );

//       // حفظ عملية البيع
//       final saleId = await _saleRepository.createSale(sale, saleItems);

//       // تحديث المخزون
//       await _updateStockForSale(
//         saleItems: saleItems,
//         warehouseId: finalWarehouseId,
//         userId: finalUserId,
//         branchId: finalBranchId,
//         referenceId: saleId,
//       );

//       AppUtils.logInfo('تم إنشاء عملية البيع الذكية بنجاح - ID: $saleId');

//       return {
//         'success': true,
//         'sale_id': saleId,
//         'total_amount': totalAmount,
//         'paid_amount': paidAmount,
//         'remaining_amount': totalAmount - paidAmount,
//         'resolved_entities': resolvedEntities,
//         'warnings': relationshipValidation['warnings'],
//         'stock_validation': stockValidation,
//       };
//     } catch (e) {
//       AppUtils.logError('خطأ في إنشاء عملية البيع الذكية', e);
//       return {
//         'success': false,
//         'errors': ['خطأ في إنشاء عملية البيع: ${e.toString()}'],
//         'sale_id': null,
//       };
//     }
//   }

//   /// إنشاء عملية شراء مع الربط الذكي والمرن
//   Future<Map<String, dynamic>> createSmartPurchaseTransaction({
//     String? supplierId,
//     required List<Map<String, dynamic>>
//         items, // [{productId, qty, unitPrice, unitId?}]
//     String? warehouseId,
//     String? userId,
//     String? branchId,
//     String? invoiceNo,
//     DateTime? purchaseDate,
//     double paidAmount = 0,
//     String? notes,
//   }) async {
//     try {
//       AppUtils.logInfo('بدء عملية شراء ذكية مع الربط المرن');

//       // التحقق من صحة العلاقات وحل الكيانات الافتراضية
//       final relationshipValidation =
//           await _relationshipService.validateEntityRelationships(
//         supplierId: supplierId,
//         warehouseId: warehouseId,
//         userId: userId,
//         branchId: branchId,
//         productIds: items.map((item) => item['productId'] as String).toList(),
//       );

//       if (!relationshipValidation['valid']) {
//         return {
//           'success': false,
//           'errors': relationshipValidation['errors'],
//           'purchase_id': null,
//         };
//       }

//       // استخراج الكيانات المحلولة
//       final resolvedEntities =
//           relationshipValidation['resolved_entities'] as Map<String, dynamic>;
//       final finalSupplierId =
//           supplierId ?? DefaultDataService.defaultSupplierId;
//       final finalWarehouseId =
//           warehouseId ?? DefaultDataService.defaultWarehouseId;
//       final finalUserId = userId ?? DefaultDataService.defaultUserId;
//       final finalBranchId = branchId ?? DefaultDataService.defaultBranchId;

//       // حساب الإجمالي وإنشاء عناصر الشراء
//       double totalAmount = 0;
//       final purchaseItems = <PurchaseItem>[];

//       for (final item in items) {
//         final productId = item['productId'] as String;
//         final qty = (item['qty'] as num).toDouble();
//         final unitPrice = (item['unitPrice'] as num).toDouble();
//         final unitId =
//             item['unitId'] as String? ?? DefaultDataService.defaultUnitId;

//         final totalPrice = qty * unitPrice;
//         totalAmount += totalPrice;

//         final purchaseItem = PurchaseItem.create(
//           purchaseId: '', // سيتم تعيينه لاحقاً
//           productId: productId,
//           unitId: unitId,
//           qty: qty,
//           unitPrice: unitPrice,
//         );

//         purchaseItems.add(purchaseItem);
//       }

//       // إنشاء فاتورة الشراء
//       final purchase = Purchase.create(
//         supplierId: finalSupplierId,
//         invoiceNo:
//             invoiceNo ?? await _purchaseRepository.generateInvoiceNumber(),
//         branchId: finalBranchId,
//         purchaseDate: purchaseDate ?? DateTime.now(),
//         totalAmount: totalAmount,
//         paidAmount: paidAmount,
//         notes: notes,
//       );

//       // حفظ عملية الشراء
//       final purchaseId =
//           await _purchaseRepository.createPurchase(purchase, purchaseItems);

//       // تحديث المخزون
//       await _updateStockForPurchase(
//         purchaseItems: purchaseItems,
//         warehouseId: finalWarehouseId,
//         userId: finalUserId,
//         branchId: finalBranchId,
//         referenceId: purchaseId,
//       );

//       AppUtils.logInfo('تم إنشاء عملية الشراء الذكية بنجاح - ID: $purchaseId');

//       return {
//         'success': true,
//         'purchase_id': purchaseId,
//         'total_amount': totalAmount,
//         'paid_amount': paidAmount,
//         'remaining_amount': totalAmount - paidAmount,
//         'resolved_entities': resolvedEntities,
//         'warnings': relationshipValidation['warnings'],
//       };
//     } catch (e) {
//       AppUtils.logError('خطأ في إنشاء عملية الشراء الذكية', e);
//       return {
//         'success': false,
//         'errors': ['خطأ في إنشاء عملية الشراء: ${e.toString()}'],
//         'purchase_id': null,
//       };
//     }
//   }

//   /// تحديث المخزون لعملية البيع
//   Future<void> _updateStockForSale({
//     required List<SaleItem> saleItems,
//     required String warehouseId,
//     required String userId,
//     required String branchId,
//     required String referenceId,
//   }) async {
//     for (final saleItem in saleItems) {
//       await _stockRepository.updateStockWithMovement(
//         productId: saleItem.productId,
//         warehouseId: warehouseId,
//         movementType: 'out',
//         quantity: saleItem.qty,
//         unitCost: saleItem.unitPrice,
//         unitId: saleItem.unitId.isNotEmpty ? saleItem.unitId : 'unit_piece',
//         referenceType: 'sale',
//         referenceId: referenceId,
//         referenceNumber: referenceId,
//         notes: 'بيع منتج - فاتورة: $referenceId',
//         createdBy: userId,
//         branchId: branchId,
//       );
//     }
//   }

//   /// تحديث المخزون لعملية الشراء
//   Future<void> _updateStockForPurchase({
//     required List<PurchaseItem> purchaseItems,
//     required String warehouseId,
//     required String userId,
//     required String branchId,
//     required String referenceId,
//   }) async {
//     for (final purchaseItem in purchaseItems) {
//       await _stockRepository.updateStockWithMovement(
//         productId: purchaseItem.productId,
//         warehouseId: warehouseId,
//         movementType: 'in',
//         quantity: purchaseItem.qty,
//         unitCost: purchaseItem.unitPrice,
//         unitId: (purchaseItem.unitId?.isNotEmpty == true)
//             ? purchaseItem.unitId!
//             : 'unit_piece',
//         referenceType: 'purchase',
//         referenceId: referenceId,
//         referenceNumber: referenceId,
//         notes: 'شراء منتج - فاتورة: $referenceId',
//         createdBy: userId,
//         branchId: branchId,
//       );
//     }
//   }

//   /// الحصول على ملخص العلاقات للكيانات الافتراضية
//   Future<Map<String, dynamic>> getDefaultEntitiesSummary() async {
//     return await _relationshipService.getDefaultEntities();
//   }

//   /// التحقق من صحة بيانات العملية قبل التنفيذ
//   Future<Map<String, dynamic>> validateTransactionData({
//     String? customerId,
//     String? supplierId,
//     String? warehouseId,
//     String? userId,
//     String? branchId,
//     required List<Map<String, dynamic>> items,
//   }) async {
//     return await _relationshipService.validateEntityRelationships(
//       customerId: customerId,
//       supplierId: supplierId,
//       warehouseId: warehouseId,
//       userId: userId,
//       branchId: branchId,
//       productIds: items.map((item) => item['productId'] as String).toList(),
//     );
//   }
// }
