import 'package:tijari_tech/services/accounting_service.dart';
import 'package:tijari_tech/services/settings_service.dart';
import 'package:tijari_tech/services/user_service.dart';

import '../services/service_locator.dart';

/// نظام تهيئة التطبيق - Application Bootstrap System
/// يدير عملية تهيئة النظام عند بدء التشغيل
/// Manages system initialization during startup
class BootstrapSystem {
  static bool _isInitialized = false;

  /// تهيئة النظام الكامل - Initialize complete system
  /// يجب استدعاء هذه الدالة عند بدء تشغيل التطبيق
  static Future<bool> bootstrapSystem() async {
    try {
      print('🚀 بدء تهيئة النظام...');

      // 1. تهيئة الخدمات والتبعيات
      await _initializeServices();

      // 2. تهيئة قاعدة البيانات
      await _initializeDatabase();

      // 3. تحميل الإعدادات الافتراضية
      await _loadDefaultSettings();

      // 4. إنشاء دليل الحسابات الافتراضي
      await _createDefaultChartOfAccounts();

      // 5. إنشاء المستخدم الافتراضي
      await _createDefaultUser();

      // 6. مزامنة أرصدة الحسابات
      await _syncAccountBalances();

      // 7. تسجيل بدء تشغيل النظام
      await _logSystemStartup();

      _isInitialized = true;
      print('✅ تم تهيئة النظام بنجاح');

      return true;
    } catch (e) {
      print('❌ فشل في تهيئة النظام: $e');
      return false;
    }
  }

  /// تهيئة الخدمات والتبعيات - Initialize services and dependencies
  static Future<void> _initializeServices() async {
    print('📦 تهيئة الخدمات...');
    await ServiceLocator.init();
    print('✅ تم تهيئة الخدمات');
  }

  /// تهيئة قاعدة البيانات - Initialize database
  static Future<void> _initializeDatabase() async {
    print('🗄️ تهيئة قاعدة البيانات...');
    final dbHelper = databaseHelper;
    await dbHelper
        .database; // هذا سيقوم بإنشاء قاعدة البيانات إذا لم تكن موجودة
    print('✅ تم تهيئة قاعدة البيانات');
  }

  /// تحميل الإعدادات الافتراضية - Load default settings
  static Future<void> _loadDefaultSettings() async {
    print('⚙️ تحميل الإعدادات الافتراضية...');

    try {
      final success = await settingsService.loadDefaultSettings();
      if (success) {
        print('✅ تم تحميل الإعدادات الافتراضية');
      } else {
        print('⚠️ تحذير: فشل في تحميل بعض الإعدادات الافتراضية');
      }
    } catch (e) {
      print('⚠️ تحذير: خطأ في تحميل الإعدادات الافتراضية: $e');
    }
  }

  /// إنشاء دليل الحسابات الافتراضي - Create default chart of accounts
  static Future<void> _createDefaultChartOfAccounts() async {
    print('📊 إنشاء دليل الحسابات الافتراضي...');

    try {
      // التحقق من وجود حسابات مسبقاً
      final existingAccounts = await chartOfAccountsDao.getActiveAccounts();

      if (existingAccounts.isEmpty) {
        final success =
            await chartOfAccountsService.createDefaultChartOfAccounts(
          userId: 'system',
        );

        if (success) {
          print('✅ تم إنشاء دليل الحسابات الافتراضي');
        } else {
          print('⚠️ تحذير: فشل في إنشاء دليل الحسابات الافتراضي');
        }
      } else {
        print(
            'ℹ️ دليل الحسابات موجود مسبقاً (${existingAccounts.length} حساب)');
      }
    } catch (e) {
      print('⚠️ تحذير: خطأ في إنشاء دليل الحسابات: $e');
    }
  }

  /// إنشاء المستخدم الافتراضي - Create default user
  static Future<void> _createDefaultUser() async {
    print('👤 إنشاء المستخدم الافتراضي...');

    try {
      // التحقق من وجود مستخدمين مسبقاً
      final existingUsers = await userDao.getActiveUsers();

      if (existingUsers.isEmpty) {
        final userId = await userService.createUser(
          username: 'admin',
          email: '<EMAIL>',
          password: 'admin123', // كلمة مرور افتراضية للتطوير
          fullName: 'مدير النظام',
          role: 'مدير النظام',
          phoneNumber: '+************',
          isActive: true,
          createdByUserId: 'system',
          metadata: {
            'is_default_user': true,
            'created_by_bootstrap': true,
          },
        );

        if (userId != null) {
          print('✅ تم إنشاء المستخدم الافتراضي (admin/admin123)');
        } else {
          print('⚠️ تحذير: فشل في إنشاء المستخدم الافتراضي');
        }
      } else {
        print('ℹ️ يوجد مستخدمون مسبقاً (${existingUsers.length} مستخدم)');
      }
    } catch (e) {
      print('⚠️ تحذير: خطأ في إنشاء المستخدم الافتراضي: $e');
    }
  }

  /// مزامنة أرصدة الحسابات - Sync account balances
  static Future<void> _syncAccountBalances() async {
    print('💰 مزامنة أرصدة الحسابات...');

    try {
      final success = await accountingService.syncAllAccountBalances();
      if (success) {
        print('✅ تم مزامنة أرصدة الحسابات');
      } else {
        print('⚠️ تحذير: فشل في مزامنة بعض أرصدة الحسابات');
      }
    } catch (e) {
      print('⚠️ تحذير: خطأ في مزامنة أرصدة الحسابات: $e');
    }
  }

  /// تسجيل بدء تشغيل النظام - Log system startup
  static Future<void> _logSystemStartup() async {
    print('📝 تسجيل بدء تشغيل النظام...');

    try {
      await auditLogDao.logOperation(
        tableName: 'system',
        recordId: 'startup',
        action: 'system_startup',
        description: 'بدء تشغيل النظام',
        userId: 'system',
        category: 'system',
        severity: 'low',
        metadata: {
          'startup_time': DateTime.now().toIso8601String(),
          'version': '1.0.0',
        },
      );

      print('✅ تم تسجيل بدء تشغيل النظام');
    } catch (e) {
      print('⚠️ تحذير: خطأ في تسجيل بدء التشغيل: $e');
    }
  }

  /// التحقق من حالة التهيئة - Check initialization status
  static bool get isInitialized => _isInitialized;

  /// إعادة تهيئة النظام - Reinitialize system
  static Future<bool> reinitialize() async {
    print('🔄 إعادة تهيئة النظام...');

    try {
      // إعادة تعيين الخدمات
      await ServiceLocator.reset();
      _isInitialized = false;

      // إعادة التهيئة
      return await bootstrapSystem();
    } catch (e) {
      print('❌ فشل في إعادة تهيئة النظام: $e');
      return false;
    }
  }

  /// تنظيف النظام - Cleanup system
  static Future<void> cleanup() async {
    print('🧹 تنظيف النظام...');

    try {
      // تسجيل إغلاق النظام
      if (_isInitialized) {
        await auditLogDao.logOperation(
          tableName: 'system',
          recordId: 'shutdown',
          action: 'system_shutdown',
          description: 'إغلاق النظام',
          userId: 'system',
          category: 'system',
          severity: 'low',
          metadata: {
            'shutdown_time': DateTime.now().toIso8601String(),
          },
        );
      }

      // إغلاق قاعدة البيانات
      await databaseHelper.close();

      // إعادة تعيين الخدمات
      await ServiceLocator.reset();
      _isInitialized = false;

      print('✅ تم تنظيف النظام');
    } catch (e) {
      print('⚠️ تحذير: خطأ في تنظيف النظام: $e');
    }
  }

  /// الحصول على معلومات النظام - Get system information
  static Future<Map<String, dynamic>> getSystemInfo() async {
    try {
      final dbHelper = databaseHelper;
      final database = await dbHelper.database;

      // إحصائيات قاعدة البيانات
      final userCount =
          await userDao.getActiveUsers().then((users) => users.length);
      final accountCount = await chartOfAccountsDao
          .getActiveAccounts()
          .then((accounts) => accounts.length);
      final entryCount = await journalEntryDao
          .getJournalEntryStatistics()
          .then((stats) => stats['total_entries'] ?? 0);

      return {
        'is_initialized': _isInitialized,
        'database_version': await database.rawQuery('PRAGMA user_version').then(
            (result) =>
                result.isNotEmpty ? result.first['user_version'] as int : 0),
        'database_path': database.path,
        'user_count': userCount,
        'account_count': accountCount,
        'journal_entry_count': entryCount,
        'startup_time': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'is_initialized': _isInitialized,
        'error': e.toString(),
      };
    }
  }

  /// التحقق من صحة النظام - Validate system health
  static Future<Map<String, dynamic>> validateSystemHealth() async {
    Map<String, dynamic> healthCheck = {
      'overall_status': 'healthy',
      'checks': <String, dynamic>{},
      'warnings': <String>[],
      'errors': <String>[],
    };

    try {
      // فحص قاعدة البيانات
      try {
        await databaseHelper.database;
        healthCheck['checks']['database'] = 'healthy';
      } catch (e) {
        healthCheck['checks']['database'] = 'error';
        healthCheck['errors'].add('Database error: $e');
        healthCheck['overall_status'] = 'unhealthy';
      }

      // فحص الخدمات
      try {
        final servicesHealthy =
            ServiceLocator.isRegistered<AccountingService>() &&
                ServiceLocator.isRegistered<UserService>() &&
                ServiceLocator.isRegistered<SettingsService>();

        healthCheck['checks']['services'] =
            servicesHealthy ? 'healthy' : 'warning';
        if (!servicesHealthy) {
          healthCheck['warnings'].add('Some services are not registered');
        }
      } catch (e) {
        healthCheck['checks']['services'] = 'error';
        healthCheck['errors'].add('Services error: $e');
        healthCheck['overall_status'] = 'unhealthy';
      }

      // فحص الإعدادات
      try {
        final appName = await settingsService.getSetting(
            key: 'app_name', defaultValue: null);
        healthCheck['checks']['settings'] =
            appName != null ? 'healthy' : 'warning';
        if (appName == null) {
          healthCheck['warnings'].add('Default settings not loaded');
        }
      } catch (e) {
        healthCheck['checks']['settings'] = 'error';
        healthCheck['errors'].add('Settings error: $e');
      }

      // تحديد الحالة العامة
      if (healthCheck['errors'].isNotEmpty) {
        healthCheck['overall_status'] = 'unhealthy';
      } else if (healthCheck['warnings'].isNotEmpty) {
        healthCheck['overall_status'] = 'warning';
      }
    } catch (e) {
      healthCheck['overall_status'] = 'critical';
      healthCheck['errors'].add('System validation error: $e');
    }

    return healthCheck;
  }
}
