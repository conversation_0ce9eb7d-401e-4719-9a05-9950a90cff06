import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../widgets/main_layout.dart';
import '../../data/models/stock_movement.dart';
import '../../providers/stock_movement_provider.dart';
import 'widgets/stock_movement_card.dart';
import 'widgets/stock_movement_filters_dialog.dart';

class EnhancedStockMovementsPage extends ConsumerStatefulWidget {
  const EnhancedStockMovementsPage({super.key});

  @override
  ConsumerState<EnhancedStockMovementsPage> createState() =>
      _EnhancedStockMovementsPageState();
}

class _EnhancedStockMovementsPageState
    extends ConsumerState<EnhancedStockMovementsPage> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(stockMovementManagementProvider.notifier).loadMovements();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'حركة المخزون',
      actions: [
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: _showFiltersDialog,
        ),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: () => ref
              .read(stockMovementManagementProvider.notifier)
              .loadMovements(),
        ),
      ],
      child: Column(
        children: [
          // Search bar
          _buildSearchBar(),

          // Active filters
          _buildActiveFilters(),

          // Statistics
          _buildStatistics(),

          // Movements list
          Expanded(
            child: _buildMovementsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: EdgeInsets.all(16.r),
      child: TextFormField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث في حركة المخزون...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    ref
                        .read(stockMovementManagementProvider.notifier)
                        .searchMovements('');
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
        ),
        onChanged: (value) => ref
            .read(stockMovementManagementProvider.notifier)
            .searchMovements(value),
      ),
    );
  }

  Widget _buildActiveFilters() {
    final state = ref.watch(stockMovementManagementProvider);
    final filters = <Widget>[];

    if (state.selectedWarehouse != null) {
      filters.add(_buildFilterChip(
        'مخزن محدد',
        () => ref
            .read(stockMovementManagementProvider.notifier)
            .filterByWarehouse(null),
      ));
    }

    if (state.selectedProduct != null) {
      filters.add(_buildFilterChip(
        'منتج محدد',
        () => ref
            .read(stockMovementManagementProvider.notifier)
            .filterByProduct(null),
      ));
    }

    if (state.selectedMovementType != null) {
      filters.add(_buildFilterChip(
        'نوع الحركة: ${_getMovementTypeLabel(state.selectedMovementType!)}',
        () => ref
            .read(stockMovementManagementProvider.notifier)
            .filterByMovementType(null),
      ));
    }

    if (state.fromDate != null || state.toDate != null) {
      filters.add(_buildFilterChip(
        'فترة زمنية محددة',
        () => ref
            .read(stockMovementManagementProvider.notifier)
            .filterByDateRange(null, null),
      ));
    }

    if (filters.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الفلاتر النشطة:',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Wrap(
            spacing: 8.w,
            runSpacing: 4.h,
            children: filters,
          ),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, VoidCallback onDeleted) {
    return Chip(
      label: Text(label),
      deleteIcon: const Icon(Icons.close, size: 18),
      onDeleted: onDeleted,
      backgroundColor: AppColors.primary.withValues(alpha: 0.1),
      deleteIconColor: AppColors.primary,
    );
  }

  Widget _buildStatistics() {
    final state = ref.watch(stockMovementManagementProvider);
    final stats = state.statistics;

    if (stats.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.all(16.r),
      child: Card(
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'إحصائيات الحركة',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 12.h),
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'إجمالي الحركات',
                      '${stats['total_movements'] ?? 0}',
                      Icons.swap_horiz,
                      AppColors.primary,
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'حركات الدخول',
                      '${stats['in_movements'] ?? 0}',
                      Icons.arrow_downward,
                      AppColors.success,
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'حركات الخروج',
                      '${stats['out_movements'] ?? 0}',
                      Icons.arrow_upward,
                      AppColors.error,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(
      String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24.r),
        SizedBox(height: 4.h),
        Text(
          value,
          style: AppTextStyles.titleMedium.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMovementsList() {
    final state = ref.watch(stockMovementManagementProvider);

    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64.r,
              color: AppColors.error,
            ),
            SizedBox(height: 16.h),
            Text(
              'حدث خطأ في تحميل البيانات',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.error,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              state.error!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: () => ref
                  .read(stockMovementManagementProvider.notifier)
                  .loadMovements(),
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (state.filteredMovements.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 64.r,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16.h),
            Text(
              'لا توجد حركات مخزون',
              style: AppTextStyles.titleMedium.copyWith(
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'لم يتم العثور على أي حركات مخزون تطابق المعايير المحددة',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      itemCount: state.filteredMovements.length,
      itemBuilder: (context, index) {
        final movement = state.filteredMovements[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 8.h),
          child: StockMovementCard(
            movement: movement,
            onTap: () => _showMovementDetails(movement),
          ),
        );
      },
    );
  }

  void _showFiltersDialog() {
    showDialog(
      context: context,
      builder: (context) => const StockMovementFiltersDialog(),
    );
  }

  void _showMovementDetails(StockMovement movement) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل الحركة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow(
                'نوع الحركة', _getMovementTypeLabel(movement.movementType)),
            _buildDetailRow('الكمية', '${movement.quantity}'),
            _buildDetailRow('التكلفة الوحدة', '${movement.unitCost} ريال'),
            _buildDetailRow('التاريخ',
                DateFormat('yyyy/MM/dd HH:mm').format(movement.createdAt)),
            if (movement.notes != null && movement.notes!.isNotEmpty)
              _buildDetailRow('ملاحظات', movement.notes!),
            if (movement.referenceType != null)
              _buildDetailRow('نوع المرجع', movement.referenceType!),
            if (movement.referenceNumber != null)
              _buildDetailRow('رقم المرجع', movement.referenceNumber!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100.w,
            child: Text(
              '$label:',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  String _getMovementTypeLabel(String type) {
    switch (type) {
      case 'in':
        return 'دخول';
      case 'out':
        return 'خروج';
      case 'transfer':
        return 'نقل';
      case 'adjustment':
        return 'تسوية';
      default:
        return type;
    }
  }
}
