import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tijari_tech/core/auth/auth_service.dart';
import 'package:tijari_tech/core/permission/permission_service.dart';

import 'routes.dart';

/// حارس المسارات للتحكم في الوصول للصفحات
class RouteGuard {
  static final RouteGuard _instance = RouteGuard._internal();
  factory RouteGuard() => _instance;
  RouteGuard._internal();

  /// التحقق من صلاحية الوصول للمسار
  static String? checkAccess(BuildContext context, GoRouterState state) {
    final currentRoute = state.uri.toString();
    
    // التحقق من تسجيل الدخول
    if (!AuthService.isLoggedIn()) {
      // إذا لم يكن مسجل دخول، توجيه لصفحة تسجيل الدخول
      if (currentRoute != AppRoutes.login) {
        return AppRoutes.login;
      }
      return null;
    }

    // إذا كان في صفحة تسجيل الدخول وهو مسجل دخول، توجيه للرئيسية
    if (currentRoute == AppRoutes.login) {
      return AppRoutes.dashboard;
    }

    // التحقق من الصلاحيات للمسارات المحمية
    if (!_hasPermissionForRoute(currentRoute)) {
      // إذا لم يكن لديه صلاحية، توجيه لصفحة عدم الصلاحية
      // return AppRoutes.unauthorized;
    }

    return null;
  }

  /// التحقق من الصلاحية للمسار
  static bool _hasPermissionForRoute(String route) {
    // قائمة المسارات العامة التي لا تحتاج صلاحيات خاصة
    final publicRoutes = [
      AppRoutes.dashboard,
      AppRoutes.profile,
      AppRoutes.settings,
    ];

    if (publicRoutes.contains(route)) {
      return true;
    }

    // التحقق من صلاحيات المخزون
    if (AppRoutes.inventoryRoutes.any((r) => route.startsWith(r))) {
      return PermissionService.hasInventoryAccess();
    }

    // التحقق من صلاحيات المبيعات
    if (AppRoutes.salesRoutes.any((r) => route.startsWith(r))) {
      return PermissionService.hasSalesAccess();
    }

    // التحقق من صلاحيات المشتريات
    if (AppRoutes.purchaseRoutes.any((r) => route.startsWith(r))) {
      return PermissionService.hasPurchaseAccess();
    }

    // التحقق من صلاحيات الحسابات
    if (AppRoutes.accountRoutes.any((r) => route.startsWith(r))) {
      return PermissionService.hasAccountsAccess();
    }

    // التحقق من صلاحيات الخزينة
    if (AppRoutes.cashRoutes.any((r) => route.startsWith(r))) {
      return PermissionService.hasCashAccess();
    }

    // التحقق من صلاحيات الموظفين
    if (AppRoutes.employeeRoutes.any((r) => route.startsWith(r))) {
      return PermissionService.hasEmployeeAccess();
    }

    // التحقق من صلاحيات التقارير
    if (AppRoutes.reportRoutes.any((r) => route.startsWith(r))) {
      return PermissionService.hasReportsAccess();
    }

    // التحقق من صلاحيات الإعدادات المتقدمة
    // if (AppRoutes.settingsRoutes.any((r) => route.startsWith(r))) {
    //   return PermissionService.hasAdminAccess();
    // }

    // افتراضياً، السماح بالوصول
    return true;
  }

  /// التحقق من صلاحية تعديل البيانات
  static bool canEdit(String module) {
    switch (module) {
      case 'inventory':
        return PermissionService.hasInventoryEditAccess();
      case 'sales':
        return PermissionService.hasSalesEditAccess();
      case 'purchases':
        return PermissionService.hasPurchaseEditAccess();
      case 'accounts':
        return PermissionService.hasAccountsEditAccess();
      case 'cash':
        return PermissionService.hasCashEditAccess();
      case 'employees':
        return PermissionService.hasEmployeeEditAccess();
      case 'settings':
        return PermissionService.hasAdminAccess();
      default:
        return false;
    }
  }

  /// التحقق من صلاحية حذف البيانات
  static bool canDelete(String module) {
    switch (module) {
      case 'inventory':
        return PermissionService.hasInventoryDeleteAccess();
      case 'sales':
        return PermissionService.hasSalesDeleteAccess();
      case 'purchases':
        return PermissionService.hasPurchaseDeleteAccess();
      case 'accounts':
        return PermissionService.hasAccountsDeleteAccess();
      case 'cash':
        return PermissionService.hasCashDeleteAccess();
      case 'employees':
        return PermissionService.hasEmployeeDeleteAccess();
      case 'settings':
        return PermissionService.hasAdminAccess();
      default:
        return false;
    }
  }

  /// التحقق من صلاحية عرض التقارير
  static bool canViewReports(String reportType) {
    switch (reportType) {
      case 'sales':
        return PermissionService.hasSalesReportsAccess();
      case 'purchases':
        return PermissionService.hasPurchaseReportsAccess();
      case 'inventory':
        return PermissionService.hasInventoryReportsAccess();
      case 'financial':
        return PermissionService.hasFinancialReportsAccess();
      case 'all':
        return PermissionService.hasAllReportsAccess();
      default:
        return PermissionService.hasReportsAccess();
    }
  }

  /// التحقق من صلاحية الوصول لنقطة البيع
  static bool canAccessPOS() {
    return PermissionService.hasPOSAccess();
  }

  /// التحقق من صلاحية إدارة المخازن
  static bool canManageWarehouses() {
    return PermissionService.hasWarehouseManagementAccess();
  }

  /// التحقق من صلاحية إدارة المخزون
  static bool canManageStock() {
    return PermissionService.hasStockManagementAccess();
  }

  /// التحقق من صلاحية تسوية المخزون
  static bool canAdjustStock() {
    return PermissionService.hasStockAdjustmentAccess();
  }

  /// التحقق من صلاحية نقل المخزون
  static bool canTransferStock() {
    return PermissionService.hasStockTransferAccess();
  }

  /// التحقق من صلاحية إدارة الأسعار
  static bool canManagePrices() {
    return PermissionService.hasPriceManagementAccess();
  }

  /// التحقق من صلاحية إدارة الخصومات
  static bool canManageDiscounts() {
    return PermissionService.hasDiscountManagementAccess();
  }

  /// التحقق من صلاحية عرض التكاليف
  static bool canViewCosts() {
    return PermissionService.hasCostViewAccess();
  }

  /// التحقق من صلاحية إدارة النسخ الاحتياطي
  static bool canManageBackup() {
    return PermissionService.hasBackupAccess();
  }

  /// التحقق من صلاحية الوصول للذكاء الاصطناعي
  static bool canAccessAI() {
    return PermissionService.hasAIAccess();
  }

  /// الحصول على رسالة عدم الصلاحية
  static String getUnauthorizedMessage(String module) {
    return 'ليس لديك صلاحية للوصول إلى $module';
  }

  /// التحقق من انتهاء صلاحية الجلسة
  static bool isSessionExpired() {
    return AuthService.isSessionExpired();
  }

  /// تجديد الجلسة
  static Future<bool> refreshSession() {
    return AuthService.refreshSession();
  }

  /// تسجيل الخروج
  static Future<void> logout() {
    return AuthService.logout();
  }
}
