import 'dart:io';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import '../core/constants/app_constants.dart';
import '../data/local/database.dart';

/// أداة لحذف وإعادة إنشاء قاعدة البيانات
class DatabaseReset {
  /// حذف قاعدة البيانات الحالية
  static Future<void> deleteDatabase() async {
    try {
      final documentsDirectory = await getApplicationDocumentsDirectory();
      final path = join(documentsDirectory.path, AppConstants.databaseName);
      
      final file = File(path);
      if (await file.exists()) {
        await file.delete();
        print('✅ تم حذف قاعدة البيانات: $path');
      } else {
        print('ℹ️ قاعدة البيانات غير موجودة: $path');
      }
    } catch (e) {
      print('❌ خطأ في حذف قاعدة البيانات: $e');
    }
  }

  /// إعادة إنشاء قاعدة البيانات
  static Future<void> recreateDatabase() async {
    try {
      // حذف قاعدة البيانات الحالية
      await deleteDatabase();
      
      // إنشاء قاعدة بيانات جديدة
      final databaseHelper = DatabaseHelper();
      await databaseHelper.database;
      
      print('✅ تم إعادة إنشاء قاعدة البيانات بنجاح');
    } catch (e) {
      print('❌ خطأ في إعادة إنشاء قاعدة البيانات: $e');
    }
  }
}
