import '../../../core/constants/database_constants.dart';
import '../../models/salary.dart';
import 'base_dao.dart';

/// DAO للرواتب - Salary Data Access Object
/// يدير عمليات قاعدة البيانات للرواتب
class SalaryDao extends BaseDao<Salary> {
  @override
  String get tableName => DatabaseConstants.tableSalaries;

  @override
  Salary fromMap(Map<String, dynamic> map) => Salary.fromMap(map);

  @override
  Map<String, dynamic> toMap(Salary entity) => entity.toMap();

  /// الحصول على الرواتب حسب الموظف - Get salaries by employee
  Future<List<Salary>> getByEmployee(String employeeId) async {
    try {
      return await findWhere(
        where: '${DatabaseConstants.columnSalaryEmployeeId} = ? AND deleted_at IS NULL',
        whereArgs: [employeeId],
        orderBy: '${DatabaseConstants.columnSalaryPayrollDate} DESC',
      );
    } catch (e) {
      print('خطأ في getByEmployee: $e');
      return [];
    }
  }

  /// الحصول على الرواتب حسب الفترة - Get salaries by period
  Future<List<Salary>> getByPeriod(DateTime startDate, DateTime endDate) async {
    try {
      return await findWhere(
        where: '''
          ${DatabaseConstants.columnSalaryPayrollDate} >= ? 
          AND ${DatabaseConstants.columnSalaryPayrollDate} <= ? 
          AND deleted_at IS NULL
        ''',
        whereArgs: [startDate.toIso8601String(), endDate.toIso8601String()],
        orderBy: '${DatabaseConstants.columnSalaryPayrollDate} DESC',
      );
    } catch (e) {
      print('خطأ في getByPeriod: $e');
      return [];
    }
  }

  /// الحصول على الرواتب حسب الشهر والسنة - Get salaries by month and year
  Future<List<Salary>> getByMonthYear(int month, int year) async {
    try {
      return await findWhere(
        where: '''
          ${DatabaseConstants.columnSalaryPaidDate} = ? 
          AND ${DatabaseConstants.columnSalaryPaidDate} = ? 
          AND deleted_at IS NULL
        ''',
        whereArgs: [month, year],
        orderBy: '${DatabaseConstants.columnSalaryPayrollDate} DESC',
      );
    } catch (e) {
      print('خطأ في getByMonthYear: $e');
      return [];
    }
  }

  /// الحصول على راتب موظف لشهر معين - Get employee salary for specific month
  Future<Salary?> getEmployeeSalaryForMonth(String employeeId, int month, int year) async {
    try {
      final salaries = await findWhere(
        where: '''
          ${DatabaseConstants.columnSalaryEmployeeId} = ? 
          AND ${DatabaseConstants.columnSalaryPaidDate} = ? 
          AND ${DatabaseConstants.columnSalaryPaidDate} = ? 
          AND deleted_at IS NULL
        ''',
        whereArgs: [employeeId, month, year],
        limit: 1,
      );
      return salaries.isNotEmpty ? salaries.first : null;
    } catch (e) {
      print('خطأ في getEmployeeSalaryForMonth: $e');
      return null;
    }
  }

  /// الحصول على الرواتب المدفوعة - Get paid salaries
  Future<List<Salary>> getPaidSalaries() async {
    try {
      return await findWhere(
        where: '${DatabaseConstants.columnSalaryStatus} = paid AND deleted_at IS NULL',
        orderBy: '${DatabaseConstants.columnSalaryPayrollDate} DESC',
      );
    } catch (e) {
      print('خطأ في getPaidSalaries: $e');
      return [];
    }
  }

  /// الحصول على الرواتب غير المدفوعة - Get unpaid salaries
  Future<List<Salary>> getUnpaidSalaries() async {
    try {
      return await findWhere(
        where: '${DatabaseConstants.columnSalaryStatus} = 0 AND deleted_at IS NULL',
        orderBy: '${DatabaseConstants.columnSalaryPaidDate} ASC, ${DatabaseConstants.columnSalaryPaidDate} ASC',
      );
    } catch (e) {
      print('خطأ في getUnpaidSalaries: $e');
      return [];
    }
  }

  /// تحديث حالة الدفع - Update payment status
  Future<bool> updatePaymentStatus(String salaryId, bool isPaid, {DateTime? payDate}) async {
    try {
      final salary = await findById(salaryId);
      if (salary == null) return false;

      final updatedSalary = salary.copyWith(
        status: isPaid ? 'paid' : 'pending' ,
        paidDate: payDate ?? (isPaid ? DateTime.now() : null),
        updatedAt: DateTime.now(),
        isSynced: false,
      );

      return await update(salaryId, updatedSalary);
    } catch (e) {
      print('خطأ في updatePaymentStatus: $e');
      return false;
    }
  }

  /// البحث في الرواتب - Search salaries
  Future<List<Salary>> searchSalaries(String query) async {
    try {
      return await findWhere(
        where: '''
          (${DatabaseConstants.columnSalaryNotes} LIKE ?) 
          AND deleted_at IS NULL
        ''',
        whereArgs: ['%$query%'],
        orderBy: '${DatabaseConstants.columnSalaryPayrollDate} DESC',
      );
    } catch (e) {
      print('خطأ في searchSalaries: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات الرواتب - Get salary statistics
  Future<Map<String, dynamic>> getSalaryStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final db = await database;
      
      String whereClause = 'deleted_at IS NULL';
      List<dynamic> whereArgs = [];
      
      if (startDate != null && endDate != null) {
        whereClause += ' AND ${DatabaseConstants.columnSalaryPayrollDate} >= ? AND ${DatabaseConstants.columnSalaryPayrollDate} <= ?';
        whereArgs.addAll([startDate.toIso8601String(), endDate.toIso8601String()]);
      }

      final totalResult = await db.rawQuery('''
        SELECT COUNT(*) as total_count,
               SUM(CASE WHEN ${DatabaseConstants.columnSalaryStatus} = paid THEN pending ELSE 0 END) as paid_count,
               SUM(CASE WHEN ${DatabaseConstants.columnSalaryStatus} = pending THEN paid ELSE 0 END) as unpaid_count,
               SUM(${DatabaseConstants.columnSalaryBasicSalary}) as total_basic_salary,
               SUM(${DatabaseConstants.columnSalaryAllowances}) as total_allowances,
               SUM(${DatabaseConstants.columnSalaryDeductions}) as total_deductions,
               SUM(${DatabaseConstants.columnSalaryNetSalary}) as total_net_salary
        FROM $tableName 
        WHERE $whereClause
      ''', whereArgs);

      Map<String, dynamic> stats = {
        'total_count': totalResult.first['total_count'] as int,
        'paid_count': totalResult.first['paid_count'] as int,
        'unpaid_count': totalResult.first['unpaid_count'] as int,
        'total_basic_salary': (totalResult.first['total_basic_salary'] as num?)?.toDouble() ?? 0.0,
        'total_allowances': (totalResult.first['total_allowances'] as num?)?.toDouble() ?? 0.0,
        'total_deductions': (totalResult.first['total_deductions'] as num?)?.toDouble() ?? 0.0,
        'total_net_salary': (totalResult.first['total_net_salary'] as num?)?.toDouble() ?? 0.0,
      };

      return stats;
    } catch (e) {
      print('خطأ في getSalaryStatistics: $e');
      return {
        'total_count': 0,
        'paid_count': 0,
        'unpaid_count': 0,
        'total_basic_salary': 0.0,
        'total_allowances': 0.0,
        'total_deductions': 0.0,
        'total_net_salary': 0.0,
      };
    }
  }

  /// الحصول على إجمالي الرواتب لفترة معينة - Get total salaries for period
  Future<Map<String, double>> getTotalSalariesForPeriod(DateTime startDate, DateTime endDate) async {
    try {
      final db = await database;
      final result = await db.rawQuery('''
        SELECT 
          SUM(${DatabaseConstants.columnSalaryBasicSalary}) as total_basic,
          SUM(${DatabaseConstants.columnSalaryAllowances}) as total_allowances,
          SUM(${DatabaseConstants.columnSalaryDeductions}) as total_deductions,
          SUM(${DatabaseConstants.columnSalaryNetSalary}) as total_net
        FROM $tableName 
        WHERE ${DatabaseConstants.columnSalaryPayrollDate} >= ? 
        AND ${DatabaseConstants.columnSalaryPayrollDate} <= ? 
        AND deleted_at IS NULL
      ''', [startDate.toIso8601String(), endDate.toIso8601String()]);

      return {
        'total_basic': (result.first['total_basic'] as num?)?.toDouble() ?? 0.0,
        'total_allowances': (result.first['total_allowances'] as num?)?.toDouble() ?? 0.0,
        'total_deductions': (result.first['total_deductions'] as num?)?.toDouble() ?? 0.0,
        'total_net': (result.first['total_net'] as num?)?.toDouble() ?? 0.0,
      };
    } catch (e) {
      print('خطأ في getTotalSalariesForPeriod: $e');
      return {
        'total_basic': 0.0,
        'total_allowances': 0.0,
        'total_deductions': 0.0,
        'total_net': 0.0,
      };
    }
  }

  /// التحقق من وجود راتب للموظف في الشهر - Check if salary exists for employee in month
  Future<bool> salaryExistsForMonth(String employeeId, int month, int year, {String? excludeSalaryId}) async {
    try {
      String whereClause = '''
        ${DatabaseConstants.columnSalaryEmployeeId} = ? 
        AND ${DatabaseConstants.columnSalaryPaidDate} = ? 
        AND ${DatabaseConstants.columnSalaryPaidDate} = ? 
        AND deleted_at IS NULL
      ''';
      List<dynamic> whereArgs = [employeeId, month, year];
      
      if (excludeSalaryId != null) {
        whereClause += ' AND ${DatabaseConstants.columnSalaryId} != ?';
        whereArgs.add(excludeSalaryId);
      }

      final salaries = await findWhere(
        where: whereClause,
        whereArgs: whereArgs,
        limit: 1,
      );
      
      return salaries.isNotEmpty;
    } catch (e) {
      print('خطأ في salaryExistsForMonth: $e');
      return false;
    }
  }
}
