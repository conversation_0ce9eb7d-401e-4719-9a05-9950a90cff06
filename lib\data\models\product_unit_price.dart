class ProductUnitPrice {
  final String id;
  final String productId;
  final String unitId;
  final double costPrice;
  final double sellingPrice;
  final double? wholesalePrice;
  final double? retailPrice;
  final double conversionFactor; // معامل التحويل من الوحدة الأساسية
  final bool isDefault;
  final DateTime? validFrom;
  final DateTime? validTo;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final bool isSynced;

  const ProductUnitPrice({
    required this.id,
    required this.productId,
    required this.unitId,
    required this.costPrice,
    required this.sellingPrice,
    this.wholesalePrice,
    this.retailPrice,
    this.conversionFactor = 1.0,
    this.isDefault = false,
    this.validFrom,
    this.validTo,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  // Factory constructor for creating from map
  factory ProductUnitPrice.fromMap(Map<String, dynamic> map) {
    return ProductUnitPrice(
      id: map['id'] as String,
      productId: map['product_id'] as String,
      unitId: map['unit_id'] as String,
      costPrice: (map['cost_price'] as num).toDouble(),
      sellingPrice: (map['selling_price'] as num).toDouble(),
      wholesalePrice: map['wholesale_price'] != null
          ? (map['wholesale_price'] as num).toDouble()
          : null,
      retailPrice: map['retail_price'] != null
          ? (map['retail_price'] as num).toDouble()
          : null,
      conversionFactor: (map['conversion_factor'] as num?)?.toDouble() ?? 1.0,
      isDefault: (map['is_default'] as int? ?? 0) == 1,
      validFrom: map['valid_from'] != null
          ? DateTime.parse(map['valid_from'] as String)
          : null,
      validTo: map['valid_to'] != null
          ? DateTime.parse(map['valid_to'] as String)
          : null,
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'] as String)
          : null,
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
      deletedAt: map['deleted_at'] != null
          ? DateTime.parse(map['deleted_at'] as String)
          : null,
      isSynced: (map['is_synced'] as int? ?? 0) == 1,
    );
  }

  // Convert to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'product_id': productId,
      'unit_id': unitId,
      'cost_price': costPrice,
      'selling_price': sellingPrice,
      'wholesale_price': wholesalePrice,
      'retail_price': retailPrice,
      'conversion_factor': conversionFactor,
      'is_default': isDefault ? 1 : 0,
      'valid_from': validFrom?.toIso8601String(),
      'valid_to': validTo?.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'deleted_at': deletedAt?.toIso8601String(),
      'is_synced': isSynced ? 1 : 0,
    };
  }

  // Map serialization
  factory ProductUnitPrice.fromJson(Map<String, dynamic> json) =>
      ProductUnitPrice.fromMap(json);
  Map<String, dynamic> toJson() => toMap();

  // Copy with method
  ProductUnitPrice copyWith({
    String? id,
    String? productId,
    String? unitId,
    double? costPrice,
    double? sellingPrice,
    double? wholesalePrice,
    double? retailPrice,
    double? conversionFactor,
    bool? isDefault,
    DateTime? validFrom,
    DateTime? validTo,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return ProductUnitPrice(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      unitId: unitId ?? this.unitId,
      costPrice: costPrice ?? this.costPrice,
      sellingPrice: sellingPrice ?? this.sellingPrice,
      wholesalePrice: wholesalePrice ?? this.wholesalePrice,
      retailPrice: retailPrice ?? this.retailPrice,
      conversionFactor: conversionFactor ?? this.conversionFactor,
      isDefault: isDefault ?? this.isDefault,
      validFrom: validFrom ?? this.validFrom,
      validTo: validTo ?? this.validTo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  // Equality and hashCode
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductUnitPrice && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // String representation
  @override
  String toString() {
    return 'ProductUnitPrice(id: $id, productId: $productId, unitId: $unitId, sellingPrice: $sellingPrice)';
  }

  // Helper methods
  bool get isDeleted => deletedAt != null;
  bool get isActive => !isDeleted;
  bool get isCurrentlyValid {
    final now = DateTime.now();
    if (validFrom != null && now.isBefore(validFrom!)) return false;
    if (validTo != null && now.isAfter(validTo!)) return false;
    return true;
  }

  // Calculate profit margin
  double get profitMargin {
    if (costPrice == 0) return 0;
    return ((sellingPrice - costPrice) / costPrice) * 100;
  }

  // Calculate profit amount
  double get profitAmount => sellingPrice - costPrice;

  // Convert price to base unit
  double convertPriceToBaseUnit(double price) {
    return price / conversionFactor;
  }

  // Convert price from base unit
  double convertPriceFromBaseUnit(double basePrice) {
    return basePrice * conversionFactor;
  }

  // Create a new price with current timestamp
  static ProductUnitPrice create({
    required String id,
    required String productId,
    required String unitId,
    required double costPrice,
    required double sellingPrice,
    double? wholesalePrice,
    double? retailPrice,
    double conversionFactor = 1.0,
    bool isDefault = false,
    DateTime? validFrom,
    DateTime? validTo,
  }) {
    final now = DateTime.now();
    return ProductUnitPrice(
      id: id,
      productId: productId,
      unitId: unitId,
      costPrice: costPrice,
      sellingPrice: sellingPrice,
      wholesalePrice: wholesalePrice,
      retailPrice: retailPrice,
      conversionFactor: conversionFactor,
      isDefault: isDefault,
      validFrom: validFrom,
      validTo: validTo,
      createdAt: now,
      updatedAt: now,
      isSynced: false,
    );
  }

  // Update price with new timestamp
  ProductUnitPrice update({
    double? costPrice,
    double? sellingPrice,
    double? wholesalePrice,
    double? retailPrice,
    double? conversionFactor,
    bool? isDefault,
    DateTime? validFrom,
    DateTime? validTo,
  }) {
    return copyWith(
      costPrice: costPrice ?? this.costPrice,
      sellingPrice: sellingPrice ?? this.sellingPrice,
      wholesalePrice: wholesalePrice ?? this.wholesalePrice,
      retailPrice: retailPrice ?? this.retailPrice,
      conversionFactor: conversionFactor ?? this.conversionFactor,
      isDefault: isDefault ?? this.isDefault,
      validFrom: validFrom ?? this.validFrom,
      validTo: validTo ?? this.validTo,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as deleted
  ProductUnitPrice markAsDeleted() {
    return copyWith(
      deletedAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as synced
  ProductUnitPrice markAsSynced() {
    return copyWith(isSynced: true);
  }
}
