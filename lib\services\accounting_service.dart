import '../data/local/dao/journal_entry_dao.dart';
import '../data/local/dao/chart_of_accounts_dao.dart';
import '../data/local/dao/audit_log_dao.dart';
import '../data/models/journal_entry.dart';
import '../data/models/chart_of_accounts.dart';
import '../core/utils/app_utils.dart';

/// خدمة المحاسبة الأساسية - Accounting Service
/// تنفذ منطق القيد المزدوج وتحديث الأرصدة تلقائياً
/// Implements double-entry bookkeeping logic and automatic balance updates
class AccountingService {
  final JournalEntryDao _journalEntryDao;
  final ChartOfAccountsDao _chartOfAccountsDao;
  final AuditLogDao _auditLogDao;

  AccountingService({
    required JournalEntryDao journalEntryDao,
    required ChartOfAccountsDao chartOfAccountsDao,
    required AuditLogDao auditLogDao,
  })  : _journalEntryDao = journalEntryDao,
        _chartOfAccountsDao = chartOfAccountsDao,
        _auditLogDao = auditLogDao;

  /// إنشاء قيد محاسبي جديد - Create new journal entry
  /// يتحقق من صحة القيد ويطبق منطق القيد المزدوج
  Future<String?> createJournalEntry({
    required String debitAccountId,
    required String creditAccountId,
    required double amount,
    required String description,
    String? reference,
    DateTime? entryDate,
    String? userId,
    String? branchId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // التحقق من صحة المبلغ
      if (amount <= 0) {
        throw Exception('المبلغ يجب أن يكون أكبر من صفر');
      }

      // التحقق من وجود الحسابات
      final debitAccount = await _chartOfAccountsDao.getById(debitAccountId);
      final creditAccount = await _chartOfAccountsDao.getById(creditAccountId);

      if (debitAccount == null) {
        throw Exception('الحساب المدين غير موجود');
      }

      if (creditAccount == null) {
        throw Exception('الحساب الدائن غير موجود');
      }

      // التحقق من أن الحسابات نشطة
      if (!debitAccount.isActive) {
        throw Exception('الحساب المدين غير نشط: ${debitAccount.accountName}');
      }

      if (!creditAccount.isActive) {
        throw Exception('الحساب الدائن غير نشط: ${creditAccount.accountName}');
      }

      // إنشاء القيد اليومي
      final journalEntry = JournalEntry.create(
        id: AppUtils.generateId(),
        debitAccountId: debitAccountId,
        creditAccountId: creditAccountId,
        debitAmount: amount,
        creditAmount: amount,
        description: description,
        reference: reference,
        entryDate: entryDate ?? DateTime.now(),
        userId: userId,
        branchId: branchId,
        metadata: metadata,
      );

      // حفظ القيد في قاعدة البيانات
      final entryId = await _journalEntryDao.insert(journalEntry);
      if (entryId == null) {
        throw Exception('فشل في حفظ القيد اليومي');
      }

      // تحديث أرصدة الحسابات
      await _updateAccountBalances(debitAccount, creditAccount, amount);

      // تسجيل العملية في سجل المراجعة
      await _auditLogDao.logOperation(
        tableName: 'journal_entries',
        recordId: entryId,
        action: 'create',
        description: 'إنشاء قيد محاسبي جديد: $description',
        userId: userId ?? 'system',
        category: 'accounting',
        severity: 'medium',
        metadata: {
          'debit_account': debitAccount.accountName,
          'credit_account': creditAccount.accountName,
          'amount': amount,
        },
        branchId: branchId,
      );

      return entryId;
    } catch (e) {
      // تسجيل الخطأ في سجل المراجعة
      await _auditLogDao.logOperation(
        tableName: 'journal_entries',
        recordId: 'failed',
        action: 'create',
        description: 'فشل في إنشاء قيد محاسبي: $description',
        userId: userId ?? 'system',
        category: 'accounting',
        severity: 'high',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      rethrow;
    }
  }

  /// تحديث أرصدة الحسابات بعد القيد
  /// Update account balances after journal entry
  Future<void> _updateAccountBalances(
    ChartOfAccounts debitAccount,
    ChartOfAccounts creditAccount,
    double amount,
  ) async {
    // تحديث رصيد الحساب المدين
    // للأصول والمصروفات: الزيادة في الجانب المدين
    // للخصوم وحقوق الملكية والإيرادات: النقصان في الجانب المدين
    double newDebitBalance = debitAccount.balance;
    if (debitAccount.accountType == 'asset' || debitAccount.accountType == 'expense') {
      newDebitBalance += amount; // زيادة الأصول والمصروفات
    } else {
      newDebitBalance -= amount; // نقصان الخصوم وحقوق الملكية والإيرادات
    }

    // تحديث رصيد الحساب الدائن
    // للأصول والمصروفات: النقصان في الجانب الدائن
    // للخصوم وحقوق الملكية والإيرادات: الزيادة في الجانب الدائن
    double newCreditBalance = creditAccount.balance;
    if (creditAccount.accountType == 'asset' || creditAccount.accountType == 'expense') {
      newCreditBalance -= amount; // نقصان الأصول والمصروفات
    } else {
      newCreditBalance += amount; // زيادة الخصوم وحقوق الملكية والإيرادات
    }

    // حفظ الأرصدة الجديدة
    await _chartOfAccountsDao.updateBalance(debitAccount.id, newDebitBalance);
    await _chartOfAccountsDao.updateBalance(creditAccount.id, newCreditBalance);
  }

  /// عكس قيد محاسبي - Reverse journal entry
  /// ينشئ قيد عكسي لإلغاء تأثير القيد الأصلي
  Future<String?> reverseJournalEntry({
    required String originalEntryId,
    required String reason,
    String? userId,
    String? branchId,
  }) async {
    try {
      // الحصول على القيد الأصلي
      final originalEntry = await _journalEntryDao.getById(originalEntryId);
      if (originalEntry == null) {
        throw Exception('القيد الأصلي غير موجود');
      }

      // التحقق من أن القيد لم يتم عكسه مسبقاً
      if (originalEntry.isReversed) {
        throw Exception('تم عكس هذا القيد مسبقاً');
      }

      // إنشاء القيد العكسي (تبديل المدين والدائن)
      final reverseEntryId = await createJournalEntry(
        debitAccountId: originalEntry.creditAccountId,
        creditAccountId: originalEntry.debitAccountId,
        amount: originalEntry.debitAmount,
        description: 'عكس قيد: ${originalEntry.description} - السبب: $reason',
        reference: 'REV-${originalEntry.reference ?? originalEntry.id}',
        entryDate: DateTime.now(),
        userId: userId,
        branchId: branchId,
        metadata: {
          'original_entry_id': originalEntryId,
          'reversal_reason': reason,
          'is_reversal': true,
        },
      );

      if (reverseEntryId != null) {
        // تحديث القيد الأصلي لتمييزه كمعكوس
        await _journalEntryDao.markAsReversed(originalEntryId, reverseEntryId);

        // تسجيل العملية في سجل المراجعة
        await _auditLogDao.logOperation(
          tableName: 'journal_entries',
          recordId: originalEntryId,
          action: 'reverse',
          description: 'عكس قيد محاسبي: $reason',
          userId: userId ?? 'system',
          category: 'accounting',
          severity: 'medium',
          metadata: {
            'reverse_entry_id': reverseEntryId,
            'reason': reason,
          },
          branchId: branchId,
        );
      }

      return reverseEntryId;
    } catch (e) {
      // تسجيل الخطأ
      await _auditLogDao.logOperation(
        tableName: 'journal_entries',
        recordId: originalEntryId,
        action: 'reverse',
        description: 'فشل في عكس القيد المحاسبي',
        userId: userId ?? 'system',
        category: 'accounting',
        severity: 'high',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      rethrow;
    }
  }

  /// حساب رصيد حساب معين - Calculate account balance
  /// يحسب الرصيد من جميع القيود المؤثرة على الحساب
  Future<double> calculateAccountBalance(String accountId) async {
    try {
      return await _journalEntryDao.calculateAccountBalance(accountId);
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: accountId,
        action: 'calculate_balance',
        description: 'فشل في حساب رصيد الحساب',
        userId: 'system',
        category: 'accounting',
        severity: 'medium',
        isSuccessful: false,
        errorMessage: e.toString(),
      );

      return 0.0;
    }
  }

  /// مزامنة جميع أرصدة الحسابات - Sync all account balances
  /// يعيد حساب أرصدة جميع الحسابات من القيود
  Future<bool> syncAllAccountBalances() async {
    try {
      final accounts = await _chartOfAccountsDao.getActiveAccounts();
      bool allSuccess = true;

      for (var account in accounts) {
        final calculatedBalance = await calculateAccountBalance(account.id);
        final updateSuccess = await _chartOfAccountsDao.updateBalance(account.id, calculatedBalance);
        
        if (!updateSuccess) {
          allSuccess = false;
        }
      }

      // تسجيل العملية
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: 'all',
        action: 'sync_balances',
        description: 'مزامنة أرصدة جميع الحسابات',
        userId: 'system',
        category: 'accounting',
        severity: 'low',
        isSuccessful: allSuccess,
        metadata: {
          'accounts_count': accounts.length,
        },
      );

      return allSuccess;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: 'all',
        action: 'sync_balances',
        description: 'فشل في مزامنة أرصدة الحسابات',
        userId: 'system',
        category: 'accounting',
        severity: 'high',
        isSuccessful: false,
        errorMessage: e.toString(),
      );

      return false;
    }
  }

  /// التحقق من توازن الميزانية - Check balance sheet balance
  /// يتحقق من أن الأصول = الخصوم + حقوق الملكية
  Future<Map<String, dynamic>> checkBalanceSheetBalance() async {
    try {
      return await _chartOfAccountsDao.checkBalanceSheetBalance();
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'chart_of_accounts',
        recordId: 'balance_sheet',
        action: 'check_balance',
        description: 'فشل في التحقق من توازن الميزانية',
        userId: 'system',
        category: 'accounting',
        severity: 'medium',
        isSuccessful: false,
        errorMessage: e.toString(),
      );

      return {};
    }
  }

  /// إنشاء قيود الإقفال - Create closing entries
  /// ينشئ قيود إقفال الحسابات المؤقتة في نهاية الفترة المحاسبية
  Future<List<String>> createClosingEntries({
    required DateTime closingDate,
    required String retainedEarningsAccountId,
    String? userId,
    String? branchId,
  }) async {
    try {
      List<String> closingEntryIds = [];

      // إقفال حسابات الإيرادات
      final revenueAccounts = await _chartOfAccountsDao.getRevenueAccounts();
      for (var account in revenueAccounts) {
        if (account.balance > 0) {
          final entryId = await createJournalEntry(
            debitAccountId: account.id,
            creditAccountId: retainedEarningsAccountId,
            amount: account.balance,
            description: 'إقفال حساب الإيرادات: ${account.accountName}',
            reference: 'CLOSE-REV-${closingDate.year}',
            entryDate: closingDate,
            userId: userId,
            branchId: branchId,
            metadata: {'closing_entry': true, 'account_type': 'revenue'},
          );
          if (entryId != null) closingEntryIds.add(entryId);
        }
      }

      // إقفال حسابات المصروفات
      final expenseAccounts = await _chartOfAccountsDao.getExpenseAccounts();
      for (var account in expenseAccounts) {
        if (account.balance > 0) {
          final entryId = await createJournalEntry(
            debitAccountId: retainedEarningsAccountId,
            creditAccountId: account.id,
            amount: account.balance,
            description: 'إقفال حساب المصروفات: ${account.accountName}',
            reference: 'CLOSE-EXP-${closingDate.year}',
            entryDate: closingDate,
            userId: userId,
            branchId: branchId,
            metadata: {'closing_entry': true, 'account_type': 'expense'},
          );
          if (entryId != null) closingEntryIds.add(entryId);
        }
      }

      // تسجيل العملية
      await _auditLogDao.logOperation(
        tableName: 'journal_entries',
        recordId: 'closing_entries',
        action: 'create_closing',
        description: 'إنشاء قيود الإقفال للفترة المحاسبية',
        userId: userId ?? 'system',
        category: 'accounting',
        severity: 'high',
        metadata: {
          'closing_date': closingDate.toIso8601String(),
          'entries_count': closingEntryIds.length,
        },
        branchId: branchId,
      );

      return closingEntryIds;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'journal_entries',
        recordId: 'closing_entries',
        action: 'create_closing',
        description: 'فشل في إنشاء قيود الإقفال',
        userId: userId ?? 'system',
        category: 'accounting',
        severity: 'critical',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      rethrow;
    }
  }
}
