import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tijari_tech/data/local/dao/customer_dao.dart';
import 'package:tijari_tech/data/local/database.dart';
import 'package:tijari_tech/data/models/customer.dart';
import '../core/utils/app_utils.dart';
import '../core/utils/error_handler.dart';
import '../data/repositories/customer_repository.dart';

/// حالة إدارة العملاء
class CustomerState {
  final List<Customer> customers;
  final bool isLoading;
  final String? error;
  final String searchQuery;
  final CustomerBalanceStatus? filterStatus;
  final Map<String, dynamic>? statistics;

  const CustomerState({
    this.customers = const [],
    this.isLoading = false,
    this.error,
    this.searchQuery = '',
    this.filterStatus,
    this.statistics,
  });

  CustomerState copyWith({
    List<Customer>? customers,
    bool? isLoading,
    String? error,
    String? searchQuery,
    CustomerBalanceStatus? filterStatus,
    Map<String, dynamic>? statistics,
  }) {
    return CustomerState(
      customers: customers ?? this.customers,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      searchQuery: searchQuery ?? this.searchQuery,
      filterStatus: filterStatus ?? this.filterStatus,
      statistics: statistics ?? this.statistics,
    );
  }

  /// الحصول على العملاء المفلترة
  List<Customer> get filteredCustomers {
    var filtered = customers;

    if (searchQuery.isNotEmpty) {
      filtered = filtered
          .where((customer) =>
              customer.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
              (customer.phone
                      ?.toLowerCase()
                      .contains(searchQuery.toLowerCase()) ??
                  false) ||
              (customer.email
                      ?.toLowerCase()
                      .contains(searchQuery.toLowerCase()) ??
                  false))
          .toList();
    }

    if (filterStatus != null) {
      filtered = filtered
          .where((customer) => customer.balanceStatus == filterStatus)
          .toList();
    }

    return filtered;
  }

  int get activeCustomersCount => customers.where((c) => c.isActive).length;
  int get debtCustomersCount => customers.where((c) => c.hasDebt).length;
  int get creditCustomersCount => customers.where((c) => c.hasCredit).length;

  double get totalDebt => customers
      .where((c) => c.hasDebt)
      .fold(0.0, (sum, customer) => sum + customer.balance);

  double get totalCredit => customers
      .where((c) => c.hasCredit)
      .fold(0.0, (sum, customer) => sum + customer.absoluteBalance);
}

/// مزود إدارة العملاء
class CustomerNotifier extends StateNotifier<CustomerState> {
  final CustomerRepository _repository;

  CustomerNotifier(this._repository) : super(const CustomerState());

  void setSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
  }

  void setBalanceFilter(CustomerBalanceStatus? status) {
    state = state.copyWith(filterStatus: status);
  }

  void clearFilters() {
    state = state.copyWith(searchQuery: '', filterStatus: null);
  }

  Future<void> refresh() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final customers = await _repository.getAllCustomers();

      Map<String, dynamic>? statistics;
      try {
        statistics = await _repository.getCustomerStatistics();
      } catch (e) {
        AppUtils.logError('Failed to load customer statistics', e);
        statistics = {
          'total_count': customers.length,
          'active_count': customers.where((c) => c.isActive).length,
          'debt_count': customers.where((c) => c.hasDebt).length,
          'credit_count': customers.where((c) => c.hasCredit).length,
          'balanced_count': customers.where((c) => c.isBalanced).length,
          'total_debt': customers
              .where((c) => c.hasDebt)
              .fold(0.0, (sum, c) => sum + c.balance),
          'total_credit': customers
              .where((c) => c.hasCredit)
              .fold(0.0, (sum, c) => sum + c.absoluteBalance),
        };
      }

      state = state.copyWith(
        customers: customers,
        statistics: statistics,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error refreshing customers', e);
    }
  }

  Future<bool> createCustomer(Customer customer) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _repository.createCustomer(customer);
      await refresh();
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error creating customer', e);
      return false;
    }
  }

  Future<bool> updateCustomer(Customer customer) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _repository.updateCustomer(customer);
      await refresh();
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error updating customer', e);
      return false;
    }
  }

  Future<bool> deleteCustomer(String id) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _repository.deleteCustomer(id);
      await refresh();
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error deleting customer', e);
      return false;
    }
  }

  Future<bool> updateCustomerBalance(String id, double newBalance) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _repository.updateCustomerBalance(id, newBalance);
      await refresh();
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error updating customer balance', e);
      return false;
    }
  }

  Customer? getCustomerById(String id) {
    try {
      return state.customers.firstWhere((customer) => customer.id == id);
    } catch (e) {
      return null;
    }
  }

  bool isCustomerNameExists(String name, {String? excludeId}) {
    return state.customers.any((customer) =>
        customer.name.toLowerCase() == name.toLowerCase() &&
        customer.id != excludeId);
  }

  List<Customer> getTopCustomers({int limit = 10}) {
    final customers = state.customers.where((c) => c.isActive).toList();
    customers.sort((a, b) => b.absoluteBalance.compareTo(a.absoluteBalance));
    return customers.take(limit).toList();
  }
}

// في نهاية ملف customer_provider.dart
final databaseProvider = Provider<DatabaseHelper>((ref) => DatabaseHelper());

final customerDaoProvider = Provider<CustomerDao>((ref) {
  return CustomerDao(ref.watch(databaseProvider));
});

final customerRepositoryProvider = Provider<CustomerRepository>((ref) {
  return CustomerRepository(ref.watch(customerDaoProvider));
});

final customerProvider =
    StateNotifierProvider<CustomerNotifier, CustomerState>((ref) {
  return CustomerNotifier(ref.watch(customerRepositoryProvider));
});
