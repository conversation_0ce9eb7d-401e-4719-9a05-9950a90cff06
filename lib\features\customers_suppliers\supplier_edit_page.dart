import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:tijari_tech/widgets/custom_button.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../data/models/supplier.dart';
import '../../providers/supplier_provider.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/custom_text_field.dart';

/// صفحة تعديل المورد
/// Supplier Edit Page
class SupplierEditPage extends ConsumerStatefulWidget {
  final String supplierId;

  const SupplierEditPage({
    super.key,
    required this.supplierId,
  });

  @override
  ConsumerState<SupplierEditPage> createState() => _SupplierEditPageState();
}

class _SupplierEditPageState extends ConsumerState<SupplierEditPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _balanceController = TextEditingController();
  final _notesController = TextEditingController();

  bool _isLoading = false;
  Supplier? _supplier;

  @override
  void initState() {
    super.initState();
    _loadSupplier();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _balanceController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadSupplier() async {
    setState(() => _isLoading = true);

    try {
      final supplierState = ref.read(supplierProvider);
      _supplier = supplierState.suppliers.firstWhere(
        (s) => s.id == widget.supplierId,
        orElse: () => throw Exception('المورد غير موجود'),
      );

      // تعبئة الحقول بالبيانات الحالية
      _nameController.text = _supplier!.name;
      _phoneController.text = _supplier!.phone ?? '';
      _emailController.text = _supplier!.email ?? '';
      _addressController.text = _supplier!.address ?? '';
      _balanceController.text = _supplier!.balance.toString();
      _notesController.text = _supplier!.notes ?? '';
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل بيانات المورد: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
        context.pop();
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _updateSupplier() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final updatedSupplier = _supplier!.copyWith(
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim().isEmpty
            ? null
            : _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
        address: _addressController.text.trim().isEmpty
            ? null
            : _addressController.text.trim(),
        balance: double.tryParse(_balanceController.text.trim()) ?? 0.0,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        updatedAt: DateTime.now(),
      );

      await ref.read(supplierProvider.notifier).updateSupplier(updatedSupplier);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث المورد بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث المورد: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'تعديل المورد',
      child: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.r),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // معلومات أساسية
                    _buildSectionTitle('المعلومات الأساسية'),
                    SizedBox(height: 16.h),

                    CustomTextField(
                      controller: _nameController,
                      label: 'اسم المورد',
                      hint: 'أدخل اسم المورد',
                      prefixIcon: Icon(Icons.business),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'اسم المورد مطلوب';
                        }
                        if (value.trim().length < 2) {
                          return 'اسم المورد يجب أن يكون أكثر من حرفين';
                        }
                        return null;
                      },
                    ),

                    SizedBox(height: 16.h),

                    CustomTextField(
                      controller: _phoneController,
                      label: 'رقم الهاتف',
                      hint: 'أدخل رقم الهاتف (اختياري)',
                      prefixIcon: Icon(Icons.phone),
                      keyboardType: TextInputType.phone,
                    ),

                    SizedBox(height: 16.h),

                    CustomTextField(
                      controller: _emailController,
                      label: 'البريد الإلكتروني',
                      hint: 'أدخل البريد الإلكتروني (اختياري)',
                      prefixIcon: Icon(Icons.email),
                      keyboardType: TextInputType.emailAddress,
                      validator: (value) {
                        if (value != null && value.trim().isNotEmpty) {
                          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                              .hasMatch(value)) {
                            return 'البريد الإلكتروني غير صحيح';
                          }
                        }
                        return null;
                      },
                    ),

                    SizedBox(height: 16.h),

                    CustomTextField(
                      controller: _addressController,
                      label: 'العنوان',
                      hint: 'أدخل العنوان (اختياري)',
                      prefixIcon: Icon(Icons.location_on),
                      maxLines: 2,
                    ),

                    SizedBox(height: 24.h),

                    // معلومات مالية
                    _buildSectionTitle('المعلومات المالية'),
                    SizedBox(height: 16.h),

                    CustomTextField(
                      controller: _balanceController,
                      label: 'الرصيد الحالي',
                      hint: 'أدخل الرصيد الحالي',
                      prefixIcon: Icon(Icons.account_balance_wallet),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.trim().isNotEmpty) {
                          if (double.tryParse(value) == null) {
                            return 'الرصيد يجب أن يكون رقماً صحيحاً';
                          }
                        }
                        return null;
                      },
                    ),

                    SizedBox(height: 16.h),

                    CustomTextField(
                      controller: _notesController,
                      label: 'ملاحظات',
                      hint: 'أدخل ملاحظات إضافية (اختياري)',
                      prefixIcon: Icon(Icons.note),
                      maxLines: 3,
                    ),

                    SizedBox(height: 32.h),

                    // أزرار الحفظ والإلغاء
                    Row(
                      children: [
                        Expanded(
                          child: CustomButton(
                            text: 'حفظ التغييرات',
                            onPressed: _isLoading ? null : _updateSupplier,
                            isLoading: _isLoading,
                          ),
                        ),
                        SizedBox(width: 16.w),
                        Expanded(
                          child: CustomButton(
                            text: 'إلغاء',
                            onPressed: () => context.pop(),
                            // variant: ButtonVariant.outlined,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: AppTextStyles.headlineSmall.copyWith(
        color: AppColors.primary,
        fontWeight: FontWeight.bold,
      ),
    );
  }
}
