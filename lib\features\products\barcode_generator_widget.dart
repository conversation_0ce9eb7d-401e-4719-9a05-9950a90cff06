import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:barcode_widget/barcode_widget.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:tijari_tech/data/models/product.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/extensions.dart';

class BarcodeGeneratorWidget extends ConsumerStatefulWidget {
  final Product? product;
  final String? customBarcode;
  final Function(String)? onBarcodeGenerated;

  const BarcodeGeneratorWidget({
    super.key,
    this.product,
    this.customBarcode,
    this.onBarcodeGenerated,
  });

  @override
  ConsumerState<BarcodeGeneratorWidget> createState() => _BarcodeGeneratorWidgetState();
}

class _BarcodeGeneratorWidgetState extends ConsumerState<BarcodeGeneratorWidget> {
  final _barcodeController = TextEditingController();
  Barcode _selectedBarcodeType = Barcode.code128();
  bool _showProductInfo = true;
  bool _isGenerating = false;

  final List<BarcodeType> _barcodeTypes = [
    BarcodeType('Code 128', Barcode.code128()),
    BarcodeType('Code 39', Barcode.code39()),
    BarcodeType('EAN-13', Barcode.ean13()),
    BarcodeType('EAN-8', Barcode.ean8()),
    BarcodeType('UPC-A', Barcode.upcA()),
    BarcodeType('QR Code', Barcode.qrCode()),
  ];

  @override
  void initState() {
    super.initState();
    _initializeBarcode();
  }

  @override
  void dispose() {
    _barcodeController.dispose();
    super.dispose();
  }

  void _initializeBarcode() {
    if (widget.product?.barcode != null) {
      _barcodeController.text = widget.product!.barcode!;
    } else if (widget.customBarcode != null) {
      _barcodeController.text = widget.customBarcode!;
    } else {
      _generateRandomBarcode();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            _buildHeader(),
            SizedBox(height: 16.h),
            
            // Barcode input and type selection
            _buildBarcodeInput(),
            SizedBox(height: 16.h),
            
            // Barcode preview
            _buildBarcodePreview(),
            SizedBox(height: 16.h),
            
            // Options
            _buildOptions(),
            SizedBox(height: 16.h),
            
            // Action buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.qr_code_2,
          color: AppColors.primary,
          size: 24.r,
        ),
        SizedBox(width: 8.w),
        Text(
          'مولد الباركود',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        const Spacer(),
        if (widget.product != null)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Text(
              widget.product!.getDisplayName(),
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildBarcodeInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Barcode input
        Row(
          children: [
            Expanded(
              flex: 3,
              child: TextField(
                controller: _barcodeController,
                decoration: InputDecoration(
                  labelText: 'رقم الباركود',
                  hintText: 'أدخل رقم الباركود أو اتركه فارغاً للتوليد التلقائي',
                  prefixIcon: const Icon(Icons.qr_code),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                onChanged: (value) {
                  setState(() {});
                  widget.onBarcodeGenerated?.call(value);
                },
              ),
            ),
            SizedBox(width: 8.w),
            
            // Generate button
            ElevatedButton.icon(
              onPressed: _generateRandomBarcode,
              icon: const Icon(Icons.refresh),
              label: const Text('توليد'),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        
        // Barcode type selection
        DropdownButtonFormField<Barcode>(
          value: _selectedBarcodeType,
          decoration: InputDecoration(
            labelText: 'نوع الباركود',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
          ),
          items: _barcodeTypes.map((type) {
            return DropdownMenuItem(
              value: type.barcode,
              child: Text(type.name),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedBarcodeType = value;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildBarcodePreview() {
    if (_barcodeController.text.isEmpty) {
      return _buildEmptyPreview();
    }

    try {
      return Container(
        width: double.infinity,
        padding: EdgeInsets.all(16.r),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Column(
          children: [
            // Barcode
            Container(
              height: 100.h,
              child: BarcodeWidget(
                barcode: _selectedBarcodeType,
                data: _barcodeController.text,
                width: 200.w,
                height: 100.h,
                drawText: true,
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.bold,
                ),
                errorBuilder: (context, error) => _buildErrorPreview(error),
              ),
            ),
            
            // Product info if enabled
            if (_showProductInfo && widget.product != null) ...[
              SizedBox(height: 12.h),
              Divider(color: Colors.grey[300]),
              SizedBox(height: 8.h),
              _buildProductInfo(),
            ],
          ],
        ),
      );
    } catch (e) {
      return _buildErrorPreview(e.toString());
    }
  }

  Widget _buildEmptyPreview() {
    return Container(
      width: double.infinity,
      height: 150.h,
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border.all(
          color: Colors.grey[300]!,
          style: BorderStyle.solid,
        ),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.qr_code_2_outlined,
              size: 48.r,
              color: Colors.grey[400],
            ),
            SizedBox(height: 8.h),
            Text(
              'أدخل رقم الباركود لعرض المعاينة',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorPreview(String error) {
    return Container(
      width: double.infinity,
      height: 150.h,
      decoration: BoxDecoration(
        color: Colors.red[50],
        border: Border.all(color: Colors.red[300]!),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48.r,
              color: Colors.red[400],
            ),
            SizedBox(height: 8.h),
            Text(
              'خطأ في تكوين الباركود',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.red[700],
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              'تحقق من صحة البيانات ونوع الباركود',
              style: AppTextStyles.bodySmall.copyWith(
                color: Colors.red[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductInfo() {
    final product = widget.product!;
    
    return Column(
      children: [
        Text(
          product.getDisplayName(),
          style: AppTextStyles.titleSmall.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 4.h),
        
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            if (product.categoryName != null)
              Text(
                'الفئة: ${product.categoryName}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            Text(
              'السعر: ${product.sellingPrice.toStringAsFixed(2)} ر.س',
              style: AppTextStyles.bodySmall.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'خيارات العرض',
          style: AppTextStyles.titleSmall.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8.h),
        
        CheckboxListTile(
          title: const Text('عرض معلومات المنتج'),
          subtitle: const Text('إظهار اسم المنتج والسعر مع الباركود'),
          value: _showProductInfo,
          onChanged: widget.product != null ? (value) {
            setState(() {
              _showProductInfo = value ?? false;
            });
          } : null,
          controlAffinity: ListTileControlAffinity.leading,
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _barcodeController.text.isNotEmpty ? _copyBarcode : null,
            icon: const Icon(Icons.copy),
            label: const Text('نسخ'),
          ),
        ),
        SizedBox(width: 8.w),
        
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _barcodeController.text.isNotEmpty ? _shareBarcode : null,
            icon: const Icon(Icons.share),
            label: const Text('مشاركة'),
          ),
        ),
        SizedBox(width: 8.w),
        
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _barcodeController.text.isNotEmpty ? _printBarcode : null,
            icon: _isGenerating 
                ? SizedBox(
                    width: 16.w,
                    height: 16.h,
                    child: const CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.print),
            label: const Text('طباعة'),
          ),
        ),
      ],
    );
  }

  void _generateRandomBarcode() {
    // Generate a random 13-digit barcode (EAN-13 format)
    final random = DateTime.now().millisecondsSinceEpoch;
    final barcode = random.toString().padLeft(13, '0').substring(0, 13);
    
    setState(() {
      _barcodeController.text = barcode;
    });
    
    widget.onBarcodeGenerated?.call(barcode);
  }

  void _copyBarcode() {
    Clipboard.setData(ClipboardData(text: _barcodeController.text));
    context.showSuccessSnackBar('تم نسخ الباركود');
  }

  void _shareBarcode() {
    // TODO: Implement share functionality
    context.showInfoSnackBar('مشاركة الباركود قيد التطوير');
  }

  Future<void> _printBarcode() async {
    if (_barcodeController.text.isEmpty) return;
    
    setState(() {
      _isGenerating = true;
    });
    
    try {
      final pdf = await _generatePDF();
      
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: 'barcode_${_barcodeController.text}',
      );
    } catch (e) {
      context.showErrorSnackBar('فشل في إنشاء ملف الطباعة');
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  Future<pw.Document> _generatePDF() async {
    final pdf = pw.Document();
    
    // Create barcode widget for PDF
    final barcodeWidget = pw.BarcodeWidget(
      barcode: _selectedBarcodeType == Barcode.qrCode() 
          ? pw.Barcode.qrCode() 
          : pw.Barcode.code128(),
      data: _barcodeController.text,
      width: 200,
      height: 100,
      drawText: true,
    );
    
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Center(
            child: pw.Column(
              mainAxisAlignment: pw.MainAxisAlignment.center,
              children: [
                if (_showProductInfo && widget.product != null) ...[
                  pw.Text(
                    widget.product!.getDisplayName(),
                    style: pw.TextStyle(
                      fontSize: 18,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  
                  if (widget.product!.categoryName != null)
                    pw.Text(
                      'الفئة: ${widget.product!.categoryName}',
                      style: const pw.TextStyle(fontSize: 12),
                    ),
                  
                  pw.Text(
                    'السعر: ${widget.product!.sellingPrice.toStringAsFixed(2)} ر.س',
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                  
                  pw.SizedBox(height: 20),
                ],
                
                barcodeWidget,
                
                pw.SizedBox(height: 20),
                
                pw.Text(
                  'تاريخ الطباعة: ${DateTime.now().toString().split(' ')[0]}',
                  style: const pw.TextStyle(fontSize: 10),
                ),
              ],
            ),
          );
        },
      ),
    );
    
    return pdf;
  }
}

class BarcodeType {
  final String name;
  final Barcode barcode;
  
  BarcodeType(this.name, this.barcode);
}
