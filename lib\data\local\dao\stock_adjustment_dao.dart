import 'package:tijari_tech/core/constants/database_constants.dart';
import 'base_dao.dart';
import '../../models/stock_adjustment.dart';
import '../../../core/utils/app_utils.dart';

class StockAdjustmentDao extends BaseDao<StockAdjustment> {
  @override
  String get tableName => DatabaseConstants.tableStockAdjustments;

  @override
  StockAdjustment fromMap(Map<String, dynamic> map) => StockAdjustment.fromMap(map);

  @override
  Map<String, dynamic> toMap(StockAdjustment entity) => entity.toMap();

  // ------------------------------------------------------------------
  // StockAdjustment-specific queries
  // ------------------------------------------------------------------

  /// استرجاع تسويات المخزون مع التفاصيل
  Future<List<StockAdjustment>> getAdjustmentsWithDetails({
    String? warehouseId,
    String? status,
    DateTime? fromDate,
    DateTime? toDate,
    String? orderBy,
    bool ascending = false,
    int? limit,
    int? offset,
  }) async {
    final conditions = <String>[];
    final args = <dynamic>[];

    // Always exclude deleted adjustments
    conditions.add('sa.${DatabaseConstants.columnStockAdjustmentDeletedAt} IS NULL');

    if (warehouseId != null) {
      conditions.add('sa.${DatabaseConstants.columnStockAdjustmentWarehouseId} = ?');
      args.add(warehouseId);
    }

    if (status != null) {
      conditions.add('sa.${DatabaseConstants.columnStockAdjustmentStatus} = ?');
      args.add(status);
    }

    if (fromDate != null) {
      conditions.add('sa.${DatabaseConstants.columnStockAdjustmentDate} >= ?');
      args.add(fromDate.toIso8601String());
    }

    if (toDate != null) {
      conditions.add('sa.${DatabaseConstants.columnStockAdjustmentDate} <= ?');
      args.add(toDate.toIso8601String());
    }

    final whereClause = conditions.join(' AND ');
    final orderByClause = orderBy ?? 'sa.${DatabaseConstants.columnStockAdjustmentCreatedAt}';
    final direction = ascending ? 'ASC' : 'DESC';

    final sqlQuery = '''
      SELECT 
        sa.*,
        w.name as warehouse_name,
        u.name as created_by_name,
        au.name as approved_by_name,
        COUNT(sai.${DatabaseConstants.columnStockAdjustmentItemId}) as items_count
      FROM ${DatabaseConstants.tableStockAdjustments} sa
      LEFT JOIN ${DatabaseConstants.tableWarehouses} w 
        ON sa.${DatabaseConstants.columnStockAdjustmentWarehouseId} = w.${DatabaseConstants.columnWarehouseId}
      LEFT JOIN ${DatabaseConstants.tableUsers} u 
        ON sa.${DatabaseConstants.columnStockAdjustmentCreatedBy} = u.id
      LEFT JOIN ${DatabaseConstants.tableUsers} au 
        ON sa.${DatabaseConstants.columnStockAdjustmentApprovedBy} = au.id
      LEFT JOIN ${DatabaseConstants.tableStockAdjustmentItems} sai 
        ON sa.${DatabaseConstants.columnStockAdjustmentId} = sai.${DatabaseConstants.columnStockAdjustmentItemAdjustmentId}
        AND sai.${DatabaseConstants.columnStockAdjustmentItemDeletedAt} IS NULL
      WHERE $whereClause
      GROUP BY sa.${DatabaseConstants.columnStockAdjustmentId}
      ORDER BY $orderByClause $direction
      ${limit != null ? 'LIMIT $limit' : ''}
      ${offset != null ? 'OFFSET $offset' : ''}
    ''';

    final result = await rawQuery(sqlQuery, args);
    return result.map((row) => StockAdjustment.fromMap(row)).toList();
  }

  /// البحث في تسويات المخزون
  Future<List<StockAdjustment>> searchAdjustments({
    String? searchTerm,
    String? warehouseId,
    String? status,
    DateTime? fromDate,
    DateTime? toDate,
    String? orderBy,
    bool ascending = false,
    int? limit,
    int? offset,
  }) async {
    final conditions = <String>[];
    final args = <dynamic>[];

    // Always exclude deleted adjustments
    conditions.add('sa.${DatabaseConstants.columnStockAdjustmentDeletedAt} IS NULL');

    if (searchTerm != null && searchTerm.isNotEmpty) {
      conditions.add('''
        (sa.${DatabaseConstants.columnStockAdjustmentReferenceNumber} LIKE ?
         OR sa.${DatabaseConstants.columnStockAdjustmentReason} LIKE ?
         OR sa.${DatabaseConstants.columnStockAdjustmentNotes} LIKE ?)
      ''');
      final searchPattern = '%$searchTerm%';
      args.addAll([searchPattern, searchPattern, searchPattern]);
    }

    if (warehouseId != null) {
      conditions.add('sa.${DatabaseConstants.columnStockAdjustmentWarehouseId} = ?');
      args.add(warehouseId);
    }

    if (status != null) {
      conditions.add('sa.${DatabaseConstants.columnStockAdjustmentStatus} = ?');
      args.add(status);
    }

    if (fromDate != null) {
      conditions.add('sa.${DatabaseConstants.columnStockAdjustmentDate} >= ?');
      args.add(fromDate.toIso8601String());
    }

    if (toDate != null) {
      conditions.add('sa.${DatabaseConstants.columnStockAdjustmentDate} <= ?');
      args.add(toDate.toIso8601String());
    }

    final whereClause = conditions.join(' AND ');
    final orderByClause = orderBy ?? 'sa.${DatabaseConstants.columnStockAdjustmentCreatedAt}';
    final direction = ascending ? 'ASC' : 'DESC';

    final sqlQuery = '''
      SELECT 
        sa.*,
        w.name as warehouse_name,
        u.name as created_by_name,
        au.name as approved_by_name,
        COUNT(sai.${DatabaseConstants.columnStockAdjustmentItemId}) as items_count
      FROM ${DatabaseConstants.tableStockAdjustments} sa
      LEFT JOIN ${DatabaseConstants.tableWarehouses} w 
        ON sa.${DatabaseConstants.columnStockAdjustmentWarehouseId} = w.${DatabaseConstants.columnWarehouseId}
      LEFT JOIN ${DatabaseConstants.tableUsers} u 
        ON sa.${DatabaseConstants.columnStockAdjustmentCreatedBy} = u.id
      LEFT JOIN ${DatabaseConstants.tableUsers} au 
        ON sa.${DatabaseConstants.columnStockAdjustmentApprovedBy} = au.id
      LEFT JOIN ${DatabaseConstants.tableStockAdjustmentItems} sai 
        ON sa.${DatabaseConstants.columnStockAdjustmentId} = sai.${DatabaseConstants.columnStockAdjustmentItemAdjustmentId}
        AND sai.${DatabaseConstants.columnStockAdjustmentItemDeletedAt} IS NULL
      WHERE $whereClause
      GROUP BY sa.${DatabaseConstants.columnStockAdjustmentId}
      ORDER BY $orderByClause $direction
      ${limit != null ? 'LIMIT $limit' : ''}
      ${offset != null ? 'OFFSET $offset' : ''}
    ''';

    final result = await rawQuery(sqlQuery, args);
    return result.map((row) => StockAdjustment.fromMap(row)).toList();
  }

  /// استرجاع إحصائيات تسويات المخزون
  Future<Map<String, dynamic>> getAdjustmentStatistics({
    DateTime? fromDate,
    DateTime? toDate,
    String? warehouseId,
  }) async {
    final conditions = <String>[];
    final args = <dynamic>[];

    conditions.add('${DatabaseConstants.columnStockAdjustmentDeletedAt} IS NULL');

    if (fromDate != null) {
      conditions.add('${DatabaseConstants.columnStockAdjustmentDate} >= ?');
      args.add(fromDate.toIso8601String());
    }

    if (toDate != null) {
      conditions.add('${DatabaseConstants.columnStockAdjustmentDate} <= ?');
      args.add(toDate.toIso8601String());
    }

    if (warehouseId != null) {
      conditions.add('${DatabaseConstants.columnStockAdjustmentWarehouseId} = ?');
      args.add(warehouseId);
    }

    final whereClause = conditions.join(' AND ');

    final sqlQuery = '''
      SELECT 
        COUNT(*) as total_adjustments,
        COUNT(CASE WHEN ${DatabaseConstants.columnStockAdjustmentStatus} = 'draft' THEN 1 END) as draft_adjustments,
        COUNT(CASE WHEN ${DatabaseConstants.columnStockAdjustmentStatus} = 'approved' THEN 1 END) as approved_adjustments,
        COUNT(CASE WHEN ${DatabaseConstants.columnStockAdjustmentStatus} = 'cancelled' THEN 1 END) as cancelled_adjustments,
        COUNT(DISTINCT ${DatabaseConstants.columnStockAdjustmentWarehouseId}) as warehouses_count,
        COUNT(DISTINCT ${DatabaseConstants.columnStockAdjustmentCreatedBy}) as users_count
      FROM ${DatabaseConstants.tableStockAdjustments}
      WHERE $whereClause
    ''';

    final result = await rawQuery(sqlQuery, args);
    return result.isNotEmpty ? result.first : {};
  }

  /// التحقق من تفرد رقم المرجع
  Future<bool> isReferenceNumberUnique(String referenceNumber, {String? excludeId}) async {
    final conditions = ['${DatabaseConstants.columnStockAdjustmentReferenceNumber} = ?'];
    final args = [referenceNumber];

    if (excludeId != null) {
      conditions.add('${DatabaseConstants.columnStockAdjustmentId} != ?');
      args.add(excludeId);
    }

    conditions.add('${DatabaseConstants.columnStockAdjustmentDeletedAt} IS NULL');

    final result = await findWhere(
      where: conditions.join(' AND '),
      whereArgs: args,
    );

    return result.isEmpty;
  }

  /// إنشاء رقم مرجع تلقائي
  Future<String> generateReferenceNumber() async {
    final today = DateTime.now();
    final datePrefix = '${today.year}${today.month.toString().padLeft(2, '0')}${today.day.toString().padLeft(2, '0')}';
    
    final sqlQuery = '''
      SELECT COUNT(*) as count
      FROM ${DatabaseConstants.tableStockAdjustments}
      WHERE ${DatabaseConstants.columnStockAdjustmentReferenceNumber} LIKE ?
        AND ${DatabaseConstants.columnStockAdjustmentDeletedAt} IS NULL
    ''';

    final result = await rawQuery(sqlQuery, ['ADJ-$datePrefix%']);
    final count = result.isNotEmpty ? (result.first['count'] as int) : 0;
    
    return 'ADJ-$datePrefix-${(count + 1).toString().padLeft(3, '0')}';
  }

  /// الحصول على تسويات المخزون حسب المخزن
  Future<List<StockAdjustment>> getAdjustmentsByWarehouse(String warehouseId) async {
    return await findWhere(
      where: '${DatabaseConstants.columnStockAdjustmentWarehouseId} = ? AND ${DatabaseConstants.columnStockAdjustmentDeletedAt} IS NULL',
      whereArgs: [warehouseId],
      orderBy: '${DatabaseConstants.columnStockAdjustmentCreatedAt} DESC',
    );
  }

  /// الحصول على تسويات المخزون حسب الحالة
  Future<List<StockAdjustment>> getAdjustmentsByStatus(String status) async {
    return await findWhere(
      where: '${DatabaseConstants.columnStockAdjustmentStatus} = ? AND ${DatabaseConstants.columnStockAdjustmentDeletedAt} IS NULL',
      whereArgs: [status],
      orderBy: '${DatabaseConstants.columnStockAdjustmentCreatedAt} DESC',
    );
  }

  /// الحصول على تسويات المخزون المعلقة (draft)
  Future<List<StockAdjustment>> getPendingAdjustments() async {
    return await getAdjustmentsByStatus('draft');
  }

  /// الحصول على تسويات المخزون المعتمدة
  Future<List<StockAdjustment>> getApprovedAdjustments() async {
    return await getAdjustmentsByStatus('approved');
  }
}

class StockAdjustmentItemDao extends BaseDao<StockAdjustmentItem> {
  @override
  String get tableName => DatabaseConstants.tableStockAdjustmentItems;

  @override
  StockAdjustmentItem fromMap(Map<String, dynamic> map) => StockAdjustmentItem.fromMap(map);

  @override
  Map<String, dynamic> toMap(StockAdjustmentItem entity) => entity.toMap();

  // ------------------------------------------------------------------
  // StockAdjustmentItem-specific queries
  // ------------------------------------------------------------------

  /// استرجاع عناصر تسوية المخزون مع تفاصيل المنتج
  Future<List<StockAdjustmentItem>> getItemsWithProductDetails(String adjustmentId) async {
    final sqlQuery = '''
      SELECT 
        sai.*,
        p.name_ar as product_name,
        p.barcode as product_barcode,
        u.name as unit_name
      FROM ${DatabaseConstants.tableStockAdjustmentItems} sai
      LEFT JOIN ${DatabaseConstants.tableProducts} p 
        ON sai.${DatabaseConstants.columnStockAdjustmentItemProductId} = p.${DatabaseConstants.columnProductId}
      LEFT JOIN ${DatabaseConstants.tableUnits} u 
        ON p.${DatabaseConstants.columnProductBaseUnitId} = u.${DatabaseConstants.columnUnitId}
      WHERE sai.${DatabaseConstants.columnStockAdjustmentItemAdjustmentId} = ?
        AND sai.${DatabaseConstants.columnStockAdjustmentItemDeletedAt} IS NULL
      ORDER BY sai.${DatabaseConstants.columnStockAdjustmentItemCreatedAt}
    ''';

    final result = await rawQuery(sqlQuery, [adjustmentId]);
    return result.map((row) => StockAdjustmentItem.fromMap(row)).toList();
  }

  /// استرجاع عناصر تسوية المخزون حسب المنتج
  Future<List<StockAdjustmentItem>> getItemsByProduct(String productId) async {
    return await findWhere(
      where: '${DatabaseConstants.columnStockAdjustmentItemProductId} = ? AND ${DatabaseConstants.columnStockAdjustmentItemDeletedAt} IS NULL',
      whereArgs: [productId],
      orderBy: '${DatabaseConstants.columnStockAdjustmentItemCreatedAt} DESC',
    );
  }

  /// حذف جميع عناصر تسوية المخزون
  Future<void> deleteItemsByAdjustmentId(String adjustmentId) async {
    final items = await findWhere(
      where: '${DatabaseConstants.columnStockAdjustmentItemAdjustmentId} = ? AND ${DatabaseConstants.columnStockAdjustmentItemDeletedAt} IS NULL',
      whereArgs: [adjustmentId],
    );

    for (final item in items) {
      final deletedItem = item.copyWith(
        deletedAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isSynced: false,
      );
      await update(item.id, deletedItem);
    }
  }

  /// إدراج عدة عناصر
  Future<List<String>> insertItems(List<StockAdjustmentItem> items) async {
    final ids = <String>[];
    for (final item in items) {
      final id = await insert(item);
      ids.add(id);
    }
    return ids;
  }
}
