import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../core/services/notification_service.dart';

// Notification service provider
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService();
});

// Notifications provider
final notificationsProvider = StateNotifierProvider<NotificationNotifier, List<AppNotification>>((ref) {
  final notificationService = ref.read(notificationServiceProvider);
  return NotificationNotifier(notificationService);
});

// Unread notifications count provider
final unreadNotificationsCountProvider = Provider<int>((ref) {
  final notifications = ref.watch(notificationsProvider);
  return notifications.where((n) => !n.isRead).length;
});

// Unread notifications provider
final unreadNotificationsProvider = Provider<List<AppNotification>>((ref) {
  final notifications = ref.watch(notificationsProvider);
  return notifications.where((n) => !n.isRead).toList();
});

// Today's notifications provider
final todayNotificationsProvider = Provider<List<AppNotification>>((ref) {
  final notifications = ref.watch(notificationsProvider);
  final today = DateTime.now();
  final startOfDay = DateTime(today.year, today.month, today.day);
  final endOfDay = startOfDay.add(const Duration(days: 1));

  return notifications.where((n) => 
    n.timestamp.isAfter(startOfDay) && n.timestamp.isBefore(endOfDay)
  ).toList();
});

// Notifications by type provider
final notificationsByTypeProvider = Provider.family<List<AppNotification>, NotificationType>((ref, type) {
  final notifications = ref.watch(notificationsProvider);
  return notifications.where((n) => n.type == type).toList();
});

// Notification notifier
class NotificationNotifier extends StateNotifier<List<AppNotification>> {
  final NotificationService _notificationService;

  NotificationNotifier(this._notificationService) : super([]) {
    // Initialize with existing notifications
    state = _notificationService.notifications;
    
    // Listen to notification service changes
    _notificationService.addListener(_onNotificationsChanged);
  }

  @override
  void dispose() {
    _notificationService.removeListener(_onNotificationsChanged);
    super.dispose();
  }

  void _onNotificationsChanged() {
    state = _notificationService.notifications;
  }

  // Add notification
  void addNotification({
    required String title,
    required String message,
    required NotificationType type,
    Map<String, dynamic>? data,
  }) {
    _notificationService.addNotification(
      title: title,
      message: message,
      type: type,
      data: data,
    );
  }

  // Mark notification as read
  void markAsRead(String id) {
    _notificationService.markAsRead(id);
  }

  // Mark all notifications as read
  void markAllAsRead() {
    _notificationService.markAllAsRead();
  }

  // Remove notification
  void removeNotification(String id) {
    _notificationService.removeNotification(id);
  }

  // Clear all notifications
  void clearAll() {
    _notificationService.clearAll();
  }

  // Predefined notification methods
  void showSuccess(String message, {Map<String, dynamic>? data}) {
    _notificationService.showSuccess(message, data: data);
  }

  void showError(String message, {Map<String, dynamic>? data}) {
    _notificationService.showError(message, data: data);
  }

  void showWarning(String message, {Map<String, dynamic>? data}) {
    _notificationService.showWarning(message, data: data);
  }

  void showInfo(String message, {Map<String, dynamic>? data}) {
    _notificationService.showInfo(message, data: data);
  }

  void showLowStock(String productName, double currentStock, {Map<String, dynamic>? data}) {
    _notificationService.showLowStock(productName, currentStock, data: data);
  }

  void showNewSale(String invoiceNo, double amount, {Map<String, dynamic>? data}) {
    _notificationService.showNewSale(invoiceNo, amount, data: data);
  }

  void showNewPurchase(String invoiceNo, double amount, {Map<String, dynamic>? data}) {
    _notificationService.showNewPurchase(invoiceNo, amount, data: data);
  }

  void showPaymentDue(String customerName, double amount, {Map<String, dynamic>? data}) {
    _notificationService.showPaymentDue(customerName, amount, data: data);
  }
}

// Notification actions provider
final notificationActionsProvider = Provider<NotificationActions>((ref) {
  final notifier = ref.read(notificationsProvider.notifier);
  return NotificationActions(notifier);
});

// Notification actions class
class NotificationActions {
  final NotificationNotifier _notifier;

  NotificationActions(this._notifier);

  void addNotification({
    required String title,
    required String message,
    required NotificationType type,
    Map<String, dynamic>? data,
  }) {
    _notifier.addNotification(
      title: title,
      message: message,
      type: type,
      data: data,
    );
  }

  void markAsRead(String id) => _notifier.markAsRead(id);
  void markAllAsRead() => _notifier.markAllAsRead();
  void removeNotification(String id) => _notifier.removeNotification(id);
  void clearAll() => _notifier.clearAll();

  void showSuccess(String message, {Map<String, dynamic>? data}) =>
      _notifier.showSuccess(message, data: data);

  void showError(String message, {Map<String, dynamic>? data}) =>
      _notifier.showError(message, data: data);

  void showWarning(String message, {Map<String, dynamic>? data}) =>
      _notifier.showWarning(message, data: data);

  void showInfo(String message, {Map<String, dynamic>? data}) =>
      _notifier.showInfo(message, data: data);

  void showLowStock(String productName, double currentStock, {Map<String, dynamic>? data}) =>
      _notifier.showLowStock(productName, currentStock, data: data);

  void showNewSale(String invoiceNo, double amount, {Map<String, dynamic>? data}) =>
      _notifier.showNewSale(invoiceNo, amount, data: data);

  void showNewPurchase(String invoiceNo, double amount, {Map<String, dynamic>? data}) =>
      _notifier.showNewPurchase(invoiceNo, amount, data: data);

  void showPaymentDue(String customerName, double amount, {Map<String, dynamic>? data}) =>
      _notifier.showPaymentDue(customerName, amount, data: data);
}
