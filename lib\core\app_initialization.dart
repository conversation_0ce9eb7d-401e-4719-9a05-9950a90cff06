// import 'package:flutter/material.dart';
// import '../services/system_initialization_service.dart';
// import '../examples/integrated_business_example.dart';
// import '../core/utils/app_utils.dart';

// /// كلاس تهيئة التطبيق الرئيسي
// class AppInitialization {
//   static bool _isInitialized = false;
//   static final SystemInitializationService _initService = SystemInitializationService();

//   /// تهيئة التطبيق عند البدء
//   static Future<bool> initializeApp() async {
//     if (_isInitialized) {
//       AppUtils.logInfo('التطبيق مهيأ مسبقاً');
//       return true;
//     }

//     try {
//       AppUtils.logInfo('🚀 بدء تهيئة التطبيق...');

//       // تهيئة النظام المتكامل
//       final initResult = await _initService.initializeSystem();

//       if (initResult['success']) {
//         _isInitialized = true;
//         AppUtils.logInfo('✅ تم تهيئة التطبيق بنجاح');
        
//         // طباعة ملخص التهيئة
//         _printInitializationSummary(initResult);
        
//         return true;
//       } else {
//         AppUtils.logError('❌ فشل في تهيئة التطبيق: ${initResult['message']}', null);
//         return false;
//       }
//     } catch (e) {
//       AppUtils.logError('خطأ حرج في تهيئة التطبيق', e);
//       return false;
//     }
//   }

//   /// طباعة ملخص التهيئة
//   static void _printInitializationSummary(Map<String, dynamic> initResult) {
//     try {
//       AppUtils.logInfo('\n📊 ملخص تهيئة التطبيق:');
//       AppUtils.logInfo('⏱️ وقت التهيئة: ${initResult['initialization_time']}ms');

//       final details = initResult['details'] as Map<String, dynamic>?;
//       if (details != null) {
//         final steps = details['steps'] as List<dynamic>?;
//         if (steps != null) {
//           AppUtils.logInfo('📋 الخطوات المكتملة:');
//           for (final step in steps) {
//             final stepMap = step as Map<String, dynamic>;
//             final status = stepMap['status'] == 'completed' ? '✅' : 
//                           stepMap['status'] == 'warning' ? '⚠️' : '❌';
//             AppUtils.logInfo('  $status ${stepMap['name']}');
//           }
//         }

//         final warnings = details['warnings'] as List<String>?;
//         if (warnings != null && warnings.isNotEmpty) {
//           AppUtils.logInfo('⚠️ تحذيرات:');
//           for (final warning in warnings) {
//             AppUtils.logInfo('  • $warning');
//           }
//         }

//         final errors = details['errors'] as List<String>?;
//         if (errors != null && errors.isNotEmpty) {
//           AppUtils.logInfo('❌ أخطاء:');
//           for (final error in errors) {
//             AppUtils.logInfo('  • $error');
//           }
//         }
//       }

//       AppUtils.logInfo('🎯 التطبيق جاهز للاستخدام!\n');
//     } catch (e) {
//       AppUtils.logError('خطأ في طباعة ملخص التهيئة', e);
//     }
//   }

//   /// التحقق من جاهزية التطبيق
//   static Future<bool> isAppReady() async {
//     if (!_isInitialized) {
//       return false;
//     }

//     try {
//       return await _initService.isSystemReadyForBusiness();
//     } catch (e) {
//       AppUtils.logError('خطأ في التحقق من جاهزية التطبيق', e);
//       return false;
//     }
//   }

//   /// الحصول على حالة التطبيق
//   static Future<Map<String, dynamic>> getAppStatus() async {
//     try {
//       final systemStatus = await _initService.getSystemStatus();
//       return {
//         'app_initialized': _isInitialized,
//         'system_status': systemStatus,
//         'ready_for_business': await isAppReady(),
//         'timestamp': DateTime.now().toIso8601String(),
//       };
//     } catch (e) {
//       AppUtils.logError('خطأ في الحصول على حالة التطبيق', e);
//       return {
//         'app_initialized': _isInitialized,
//         'error': e.toString(),
//         'timestamp': DateTime.now().toIso8601String(),
//       };
//     }
//   }

//   /// إعادة تهيئة التطبيق
//   static Future<bool> reinitializeApp() async {
//     AppUtils.logInfo('🔄 إعادة تهيئة التطبيق...');
//     _isInitialized = false;
//     return await initializeApp();
//   }

//   /// تشغيل الأمثلة التوضيحية (للاختبار)
//   static Future<void> runExamples() async {
//     try {
//       if (!_isInitialized) {
//         AppUtils.logInfo('تهيئة التطبيق قبل تشغيل الأمثلة...');
//         final success = await initializeApp();
//         if (!success) {
//           AppUtils.logError('فشل في تهيئة التطبيق للأمثلة', null);
//           return;
//         }
//       }

//       AppUtils.logInfo('🧪 تشغيل الأمثلة التوضيحية...');
      
//       // تشغيل أمثلة النظام المتكامل
//       await IntegratedBusinessExample.runAllExamples();
      
//       // تشغيل مثال التكامل مع التطبيق الرئيسي
//       await MainAppIntegrationExample.initializeAppWithIntegratedSystem();
      
//       AppUtils.logInfo('✅ انتهاء تشغيل جميع الأمثلة');
//     } catch (e) {
//       AppUtils.logError('خطأ في تشغيل الأمثلة', e);
//     }
//   }
// }

// /// ويدجت تهيئة التطبيق مع شاشة تحميل
// class AppInitializationWidget extends StatefulWidget {
//   final Widget child;
//   final Widget? loadingWidget;
//   final Widget? errorWidget;

//   const AppInitializationWidget({
//     Key? key,
//     required this.child,
//     this.loadingWidget,
//     this.errorWidget,
//   }) : super(key: key);

//   @override
//   State<AppInitializationWidget> createState() => _AppInitializationWidgetState();
// }

// class _AppInitializationWidgetState extends State<AppInitializationWidget> {
//   bool _isLoading = true;
//   bool _hasError = false;
//   String _errorMessage = '';

//   @override
//   void initState() {
//     super.initState();
//     _initializeApp();
//   }

//   Future<void> _initializeApp() async {
//     try {
//       final success = await AppInitialization.initializeApp();
      
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//           _hasError = !success;
//           if (!success) {
//             _errorMessage = 'فشل في تهيئة التطبيق';
//           }
//         });
//       }
//     } catch (e) {
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//           _hasError = true;
//           _errorMessage = 'خطأ في تهيئة التطبيق: ${e.toString()}';
//         });
//       }
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     if (_isLoading) {
//       return widget.loadingWidget ?? _buildDefaultLoadingWidget();
//     }

//     if (_hasError) {
//       return widget.errorWidget ?? _buildDefaultErrorWidget();
//     }

//     return widget.child;
//   }

//   Widget _buildDefaultLoadingWidget() {
//     return Scaffold(
//       body: Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             const CircularProgressIndicator(),
//             const SizedBox(height: 16),
//             Text(
//               'جاري تهيئة التطبيق...',
//               style: Theme.of(context).textTheme.titleMedium,
//             ),
//             const SizedBox(height: 8),
//             Text(
//               'يرجى الانتظار',
//               style: Theme.of(context).textTheme.bodyMedium?.copyWith(
//                 color: Colors.grey[600],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildDefaultErrorWidget() {
//     return Scaffold(
//       body: Center(
//         child: Padding(
//           padding: const EdgeInsets.all(16.0),
//           child: Column(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               Icon(
//                 Icons.error_outline,
//                 size: 64,
//                 color: Colors.red[400],
//               ),
//               const SizedBox(height: 16),
//               Text(
//                 'خطأ في تهيئة التطبيق',
//                 style: Theme.of(context).textTheme.titleLarge?.copyWith(
//                   color: Colors.red[700],
//                 ),
//                 textAlign: TextAlign.center,
//               ),
//               const SizedBox(height: 8),
//               Text(
//                 _errorMessage,
//                 style: Theme.of(context).textTheme.bodyMedium,
//                 textAlign: TextAlign.center,
//               ),
//               const SizedBox(height: 24),
//               ElevatedButton.icon(
//                 onPressed: () {
//                   setState(() {
//                     _isLoading = true;
//                     _hasError = false;
//                     _errorMessage = '';
//                   });
//                   _initializeApp();
//                 },
//                 icon: const Icon(Icons.refresh),
//                 label: const Text('إعادة المحاولة'),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
