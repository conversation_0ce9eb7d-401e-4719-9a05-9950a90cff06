import 'package:sqflite/sqflite.dart';
import '../../../core/constants/database_constants.dart';
import '../../../core/utils/app_utils.dart';
import '../../../core/utils/error_handler.dart';
import '../database.dart';
import '../../models/customer.dart';

class CustomerDao {
  final DatabaseHelper _databaseHelper;

  CustomerDao(this._databaseHelper);

  Future<Database> get database async {
    try {
      final db = await _databaseHelper.database;
      AppUtils.logInfo('Database access successful in CustomerDao');
      return db;
    } catch (e) {
      AppUtils.logError('Database access failed in CustomerDao', e);
      
      if (e.toString().contains('read-only')) {
        AppUtils.logInfo('Attempting to reset database...');
        try {
          await _databaseHelper.resetDatabase();
          final db = await _databaseHelper.database;
          AppUtils.logInfo('Database reset successful');
          return db;
        } catch (resetError) {
          AppUtils.logError('Failed to reset database', resetError);
          rethrow;
        }
      }
      rethrow;
    }
  }

  Map<String, dynamic> _customerToMap(Customer customer) {
    return {
      DatabaseConstants.columnCustomerId: customer.id,
      DatabaseConstants.columnCustomerName: customer.name,
      DatabaseConstants.columnCustomerPhone: customer.phone,
      DatabaseConstants.columnCustomerEmail: customer.email,
      DatabaseConstants.columnCustomerAddress: customer.address,
      DatabaseConstants.columnCustomerBalance: customer.balance,
      DatabaseConstants.columnCustomerNotes: customer.notes,
      DatabaseConstants.columnCustomerCreatedAt: customer.createdAt?.toIso8601String(),
      DatabaseConstants.columnCustomerUpdatedAt: customer.updatedAt?.toIso8601String(),
      DatabaseConstants.columnCustomerDeletedAt: customer.deletedAt?.toIso8601String(),
      DatabaseConstants.columnCustomerIsSynced: customer.isSynced ? 1 : 0,
    };
  }

  Customer _mapToCustomer(Map<String, dynamic> map) {
    return Customer.fromMap(map);
  }

  Future<String> insert(Customer customer) async {
    try {
      AppUtils.logInfo('Inserting customer: ${customer.name}');
      final db = await database;
      final map = _customerToMap(customer);

      final now = DateTime.now().toIso8601String();
      map[DatabaseConstants.columnCustomerCreatedAt] = now;
      map[DatabaseConstants.columnCustomerUpdatedAt] = now;
      map[DatabaseConstants.columnCustomerIsSynced] = 0;

      if (map[DatabaseConstants.columnCustomerId] == null || 
          map[DatabaseConstants.columnCustomerId].toString().isEmpty) {
        map[DatabaseConstants.columnCustomerId] = AppUtils.generateId();
      }

      await db.insert(DatabaseConstants.tableCustomers, map);
      AppUtils.logInfo('Customer inserted successfully - ID: ${map[DatabaseConstants.columnCustomerId]}');
      return map[DatabaseConstants.columnCustomerId];
    } catch (e) {
      AppUtils.logError('Error inserting customer', e);
      rethrow;
    }
  }

  Future<void> update(Customer customer) async {
    try {
      final db = await database;
      final map = _customerToMap(customer);
      map[DatabaseConstants.columnCustomerUpdatedAt] = DateTime.now().toIso8601String();
      map[DatabaseConstants.columnCustomerIsSynced] = 0;

      final rowsAffected = await db.update(
        DatabaseConstants.tableCustomers,
        map,
        where: '${DatabaseConstants.columnCustomerId} = ?',
        whereArgs: [customer.id],
      );

      if (rowsAffected == 0) {
        throw Exception('Customer not found for update');
      }

      AppUtils.logInfo('Customer updated successfully - ID: ${customer.id}');
    } catch (e) {
      AppUtils.logError('Error updating customer', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  Future<void> delete(String id) async {
    try {
      final db = await database;
      final rowsAffected = await db.update(
        DatabaseConstants.tableCustomers,
        {
          DatabaseConstants.columnCustomerDeletedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnCustomerUpdatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnCustomerIsSynced: 0,
        },
        where: '${DatabaseConstants.columnCustomerId} = ?',
        whereArgs: [id],
      );

      if (rowsAffected == 0) {
        throw Exception('Customer not found for deletion');
      }

      AppUtils.logInfo('Customer deleted successfully - ID: $id');
    } catch (e) {
      AppUtils.logError('Error deleting customer', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  Future<Customer?> findById(String id) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.tableCustomers,
        where: '${DatabaseConstants.columnCustomerId} = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isEmpty) return null;
      return _mapToCustomer(maps.first);
    } catch (e) {
      AppUtils.logError('Error finding customer', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  Future<List<Customer>> findAll({bool includeDeleted = false}) async {
    try {
      final db = await database;
      String? where;
      if (!includeDeleted) {
        where = '${DatabaseConstants.columnCustomerDeletedAt} IS NULL';
      }

      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.tableCustomers,
        where: where,
        orderBy: DatabaseConstants.columnCustomerName,
      );

      return maps.map(_mapToCustomer).toList();
    } catch (e) {
      AppUtils.logError('Error retrieving customers', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  Future<List<Customer>> search(String query, {bool includeDeleted = false}) async {
    try {
      final db = await database;
      String where = '(${DatabaseConstants.columnCustomerName} LIKE ? OR '
          '${DatabaseConstants.columnCustomerPhone} LIKE ? OR '
          '${DatabaseConstants.columnCustomerEmail} LIKE ?)';
      List<dynamic> whereArgs = ['%$query%', '%$query%', '%$query%'];

      if (!includeDeleted) {
        where += ' AND ${DatabaseConstants.columnCustomerDeletedAt} IS NULL';
      }

      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.tableCustomers,
        where: where,
        whereArgs: whereArgs,
        orderBy: DatabaseConstants.columnCustomerName,
      );

      return maps.map(_mapToCustomer).toList();
    } catch (e) {
      AppUtils.logError('Error searching customers', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  Future<List<Customer>> findByBalanceStatus(
    CustomerBalanceStatus status, {
    bool includeDeleted = false,
  }) async {
    try {
      final db = await database;
      String balanceCondition;
      switch (status) {
        case CustomerBalanceStatus.debt:
          balanceCondition = '${DatabaseConstants.columnCustomerBalance} > 0';
          break;
        case CustomerBalanceStatus.credit:
          balanceCondition = '${DatabaseConstants.columnCustomerBalance} < 0';
          break;
        case CustomerBalanceStatus.balanced:
          balanceCondition = '${DatabaseConstants.columnCustomerBalance} = 0';
          break;
      }

      String where = balanceCondition;
      if (!includeDeleted) {
        where += ' AND ${DatabaseConstants.columnCustomerDeletedAt} IS NULL';
      }

      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.tableCustomers,
        where: where,
        orderBy: DatabaseConstants.columnCustomerName,
      );

      return maps.map(_mapToCustomer).toList();
    } catch (e) {
      AppUtils.logError('Error retrieving customers by balance status', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  Future<Map<String, dynamic>> getCustomerStats() async {
    try {
      final db = await database;
      final result = await db.rawQuery('''
        SELECT 
          COUNT(*) as total_count,
          COUNT(CASE WHEN ${DatabaseConstants.columnCustomerDeletedAt} IS NULL THEN 1 END) as active_count,
          COUNT(CASE WHEN ${DatabaseConstants.columnCustomerBalance} > 0 AND ${DatabaseConstants.columnCustomerDeletedAt} IS NULL THEN 1 END) as debt_count,
          COUNT(CASE WHEN ${DatabaseConstants.columnCustomerBalance} < 0 AND ${DatabaseConstants.columnCustomerDeletedAt} IS NULL THEN 1 END) as credit_count,
          COUNT(CASE WHEN ${DatabaseConstants.columnCustomerBalance} = 0 AND ${DatabaseConstants.columnCustomerDeletedAt} IS NULL THEN 1 END) as balanced_count,
          COALESCE(SUM(CASE WHEN ${DatabaseConstants.columnCustomerBalance} > 0 AND ${DatabaseConstants.columnCustomerDeletedAt} IS NULL THEN ${DatabaseConstants.columnCustomerBalance} END), 0) as total_debt,
          COALESCE(SUM(CASE WHEN ${DatabaseConstants.columnCustomerBalance} < 0 AND ${DatabaseConstants.columnCustomerDeletedAt} IS NULL THEN ABS(${DatabaseConstants.columnCustomerBalance}) END), 0) as total_credit
        FROM ${DatabaseConstants.tableCustomers}
      ''');

      return result.first;
    } catch (e) {
      AppUtils.logError('Error retrieving customer statistics', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  Future<List<Customer>> getUnsyncedCustomers() async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.tableCustomers,
        where: '${DatabaseConstants.columnCustomerIsSynced} = 0',
        orderBy: DatabaseConstants.columnCustomerUpdatedAt,
      );

      return maps.map(_mapToCustomer).toList();
    } catch (e) {
      AppUtils.logError('Error retrieving unsynced customers', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  Future<void> markAsSynced(String id) async {
    try {
      final db = await database;
      await db.update(
        DatabaseConstants.tableCustomers,
        {DatabaseConstants.columnCustomerIsSynced: 1},
        where: '${DatabaseConstants.columnCustomerId} = ?',
        whereArgs: [id],
      );

      AppUtils.logInfo('Customer marked as synced - ID: $id');
    } catch (e) {
      AppUtils.logError('Error marking customer as synced', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }
}