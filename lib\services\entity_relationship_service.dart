// import '../data/repositories/product_repository.dart';
// import '../data/repositories/warehouse_repository.dart';
// import '../data/repositories/customer_repository.dart';
// import '../data/repositories/supplier_repository.dart';
// import '../data/repositories/user_repository.dart';
// import '../data/repositories/branch_repository.dart';
// import '../data/repositories/unit_repository.dart';
// import '../data/repositories/stock_repository.dart';
// import '../data/models/product.dart';
// import '../data/models/warehouse.dart';
// import '../data/models/customer.dart';
// import '../data/models/supplier.dart';
// import '../data/models/user.dart';
// import '../data/models/branch.dart';
// import '../data/models/unit.dart';
// import '../core/utils/app_utils.dart';
// import '../core/exceptions/app_exceptions.dart';
// import 'default_data_service.dart';

// /// خدمة إدارة العلاقات بين الكيانات المختلفة
// /// تضمن الربط الصحيح بين المخازن والمنتجات والمخزون والوحدات والمستخدمين والموردين والعملاء
// class EntityRelationshipService {
//   static final EntityRelationshipService _instance = EntityRelationshipService._internal();
//   factory EntityRelationshipService() => _instance;
//   EntityRelationshipService._internal();

//   final ProductRepository _productRepository = ProductRepository();
//   final WarehouseRepository _warehouseRepository = WarehouseRepository();
//   final CustomerRepository _customerRepository = CustomerRepository();
//   final SupplierRepository _supplierRepository = SupplierRepository();
//   final UserRepository _userRepository = UserRepository();
//   final BranchRepository _branchRepository = BranchRepository();
//   final UnitRepository _unitRepository = UnitRepository();
//   final StockRepository _stockRepository = StockRepository();
//   final DefaultDataService _defaultDataService = DefaultDataService();

//   /// التحقق من صحة العلاقات قبل إجراء العمليات
//   Future<Map<String, dynamic>> validateEntityRelationships({
//     String? customerId,
//     String? supplierId,
//     String? warehouseId,
//     String? userId,
//     String? branchId,
//     List<String>? productIds,
//     List<String>? unitIds,
//   }) async {
//     try {
//       AppUtils.logInfo('بدء التحقق من صحة العلاقات بين الكيانات');

//       final validationResults = <String, dynamic>{
//         'valid': true,
//         'errors': <String>[],
//         'warnings': <String>[],
//         'resolved_entities': <String, dynamic>{},
//       };

//       // ضمان وجود البيانات الافتراضية
//       await _defaultDataService.ensureDefaultDataExists();

//       // التحقق من العميل
//       if (customerId != null) {
//         final customer = await _customerRepository.getCustomerById(customerId);
//         if (customer == null) {
//           validationResults['errors'].add('العميل غير موجود: $customerId');
//           validationResults['valid'] = false;
//         } else {
//           validationResults['resolved_entities']['customer'] = customer.toMap();
//         }
//       } else {
//         // استخدام العميل الافتراضي
//         final defaultCustomer = await _customerRepository.getCustomerById(
//           DefaultDataService.defaultCustomerId,
//         );
//         if (defaultCustomer != null) {
//           validationResults['resolved_entities']['customer'] = defaultCustomer.toMap();
//           validationResults['warnings'].add('تم استخدام العميل الافتراضي');
//         }
//       }

//       // التحقق من المورد
//       if (supplierId != null) {
//         final supplier = await _supplierRepository.getSupplierById(supplierId);
//         if (supplier == null) {
//           validationResults['errors'].add('المورد غير موجود: $supplierId');
//           validationResults['valid'] = false;
//         } else {
//           validationResults['resolved_entities']['supplier'] = supplier.toMap();
//         }
//       } else {
//         // استخدام المورد الافتراضي
//         final defaultSupplier = await _supplierRepository.getSupplierById(
//           DefaultDataService.defaultSupplierId,
//         );
//         if (defaultSupplier != null) {
//           validationResults['resolved_entities']['supplier'] = defaultSupplier.toMap();
//           validationResults['warnings'].add('تم استخدام المورد الافتراضي');
//         }
//       }

//       // التحقق من المخزن
//       if (warehouseId != null) {
//         final warehouse = await _warehouseRepository.getWarehouseById(warehouseId);
//         if (warehouse == null) {
//           validationResults['errors'].add('المخزن غير موجود: $warehouseId');
//           validationResults['valid'] = false;
//         } else {
//           validationResults['resolved_entities']['warehouse'] = warehouse.toMap();
//         }
//       } else {
//         // استخدام المخزن الافتراضي
//         final defaultWarehouse = await _warehouseRepository.getWarehouseById(
//           DefaultDataService.defaultWarehouseId,
//         );
//         if (defaultWarehouse != null) {
//           validationResults['resolved_entities']['warehouse'] = defaultWarehouse.toMap();
//           validationResults['warnings'].add('تم استخدام المخزن الافتراضي');
//         }
//       }

//       // التحقق من المستخدم
//       if (userId != null) {
//         final user = await _userRepository.getUserById(userId);
//         if (user == null) {
//           validationResults['errors'].add('المستخدم غير موجود: $userId');
//           validationResults['valid'] = false;
//         } else {
//           validationResults['resolved_entities']['user'] = user.toMap();
//         }
//       } else {
//         // استخدام المستخدم الافتراضي
//         final defaultUser = await _userRepository.getUserById(
//           DefaultDataService.defaultUserId,
//         );
//         if (defaultUser != null) {
//           validationResults['resolved_entities']['user'] = defaultUser.toMap();
//           validationResults['warnings'].add('تم استخدام المستخدم الافتراضي');
//         }
//       }

//       // التحقق من الفرع
//       if (branchId != null) {
//         final branch = await _branchRepository.getBranchById(branchId);
//         if (branch == null) {
//           validationResults['errors'].add('الفرع غير موجود: $branchId');
//           validationResults['valid'] = false;
//         } else {
//           validationResults['resolved_entities']['branch'] = branch.toMap();
//         }
//       } else {
//         // استخدام الفرع الافتراضي
//         final defaultBranch = await _branchRepository.getBranchById(
//           DefaultDataService.defaultBranchId,
//         );
//         if (defaultBranch != null) {
//           validationResults['resolved_entities']['branch'] = defaultBranch.toMap();
//           validationResults['warnings'].add('تم استخدام الفرع الافتراضي');
//         }
//       }

//       // التحقق من المنتجات
//       if (productIds != null && productIds.isNotEmpty) {
//         final products = <Map<String, dynamic>>[];
//         for (final productId in productIds) {
//           final product = await _productRepository.getProductById(productId);
//           if (product == null) {
//             validationResults['errors'].add('المنتج غير موجود: $productId');
//             validationResults['valid'] = false;
//           } else {
//             products.add(product.toMap());
//           }
//         }
//         validationResults['resolved_entities']['products'] = products;
//       }

//       // التحقق من الوحدات
//       if (unitIds != null && unitIds.isNotEmpty) {
//         final units = <Map<String, dynamic>>[];
//         for (final unitId in unitIds) {
//           final unit = await _unitRepository.getUnitById(unitId);
//           if (unit == null) {
//             validationResults['errors'].add('الوحدة غير موجودة: $unitId');
//             validationResults['valid'] = false;
//           } else {
//             units.add(unit.toMap());
//           }
//         }
//         validationResults['resolved_entities']['units'] = units;
//       }

//       AppUtils.logInfo('تم التحقق من العلاقات - صالح: ${validationResults['valid']}');
//       return validationResults;
//     } catch (e) {
//       AppUtils.logError('خطأ في التحقق من العلاقات', e);
//       return {
//         'valid': false,
//         'errors': ['خطأ في التحقق من العلاقات: ${e.toString()}'],
//         'warnings': <String>[],
//         'resolved_entities': <String, dynamic>{},
//       };
//     }
//   }

//   /// الحصول على الكيانات الافتراضية
//   Future<Map<String, dynamic>> getDefaultEntities() async {
//     try {
//       await _defaultDataService.ensureDefaultDataExists();

//       final defaultEntities = <String, dynamic>{};

//       // العميل الافتراضي
//       final defaultCustomer = await _customerRepository.getCustomerById(
//         DefaultDataService.defaultCustomerId,
//       );
//       if (defaultCustomer != null) {
//         defaultEntities['customer'] = defaultCustomer.toMap();
//       }

//       // المورد الافتراضي
//       final defaultSupplier = await _supplierRepository.getSupplierById(
//         DefaultDataService.defaultSupplierId,
//       );
//       if (defaultSupplier != null) {
//         defaultEntities['supplier'] = defaultSupplier.toMap();
//       }

//       // المخزن الافتراضي
//       final defaultWarehouse = await _warehouseRepository.getWarehouseById(
//         DefaultDataService.defaultWarehouseId,
//       );
//       if (defaultWarehouse != null) {
//         defaultEntities['warehouse'] = defaultWarehouse.toMap();
//       }

//       // المستخدم الافتراضي
//       final defaultUser = await _userRepository.getUserById(
//         DefaultDataService.defaultUserId,
//       );
//       if (defaultUser != null) {
//         defaultEntities['user'] = defaultUser.toMap();
//       }

//       // الفرع الافتراضي
//       final defaultBranch = await _branchRepository.getBranchById(
//         DefaultDataService.defaultBranchId,
//       );
//       if (defaultBranch != null) {
//         defaultEntities['branch'] = defaultBranch.toMap();
//       }

//       // الوحدة الافتراضية
//       final defaultUnit = await _unitRepository.getUnitById(
//         DefaultDataService.defaultUnitId,
//       );
//       if (defaultUnit != null) {
//         defaultEntities['unit'] = defaultUnit.toMap();
//       }

//       return defaultEntities;
//     } catch (e) {
//       AppUtils.logError('خطأ في الحصول على الكيانات الافتراضية', e);
//       return {};
//     }
//   }

//   /// التحقق من توفر المخزون للمنتجات في مخزن معين
//   Future<Map<String, dynamic>> validateStockAvailability({
//     required List<Map<String, dynamic>> items, // [{productId, qty, unitId?}]
//     String? warehouseId,
//   }) async {
//     try {
//       final finalWarehouseId = warehouseId ?? DefaultDataService.defaultWarehouseId;
      
//       final stockValidation = <String, dynamic>{
//         'valid': true,
//         'warehouse_id': finalWarehouseId,
//         'items': <Map<String, dynamic>>[],
//         'total_shortage': 0.0,
//       };

//       for (final item in items) {
//         final productId = item['productId'] as String;
//         final requestedQty = (item['qty'] as num).toDouble();

//         // الحصول على المنتج
//         final product = await _productRepository.getProductById(productId);
//         if (product == null) {
//           stockValidation['valid'] = false;
//           stockValidation['items'].add({
//             'product_id': productId,
//             'error': 'المنتج غير موجود',
//             'valid': false,
//           });
//           continue;
//         }

//         // التحقق من المخزون إذا كان المنتج يتطلب تتبع المخزون
//         if (product.trackStock) {
//           final stockRecord = await _stockRepository.getStockByProductAndWarehouse(
//             productId,
//             finalWarehouseId,
//           );

//           final availableQty = stockRecord?.actualAvailableQuantity ?? 0;
//           final isValid = availableQty >= requestedQty;
//           final shortage = isValid ? 0.0 : requestedQty - availableQty;

//           if (!isValid) {
//             stockValidation['valid'] = false;
//             stockValidation['total_shortage'] = 
//                 (stockValidation['total_shortage'] as double) + shortage;
//           }

//           stockValidation['items'].add({
//             'product_id': productId,
//             'product_name': product.nameAr,
//             'requested_qty': requestedQty,
//             'available_qty': availableQty,
//             'shortage': shortage,
//             'valid': isValid,
//             'track_stock': true,
//           });
//         } else {
//           // المنتج لا يتطلب تتبع المخزون
//           stockValidation['items'].add({
//             'product_id': productId,
//             'product_name': product.nameAr,
//             'requested_qty': requestedQty,
//             'available_qty': 'غير محدود',
//             'shortage': 0.0,
//             'valid': true,
//             'track_stock': false,
//           });
//         }
//       }

//       return stockValidation;
//     } catch (e) {
//       AppUtils.logError('خطأ في التحقق من توفر المخزون', e);
//       return {
//         'valid': false,
//         'error': 'خطأ في التحقق من المخزون: ${e.toString()}',
//       };
//     }
//   }
// }
