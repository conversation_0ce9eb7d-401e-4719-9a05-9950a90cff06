import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/colors.dart';
import '../../../core/theme/text_styles.dart';

import '../../../data/models/warehouse.dart';

class WarehouseCard extends StatelessWidget {
  final Warehouse warehouse;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onSetDefault;

  const WarehouseCard({
    super.key,
    required this.warehouse,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onSetDefault,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row
              Row(
                children: [
                  // Warehouse icon and name
                  Expanded(
                    child: Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(8.w),
                          decoration: BoxDecoration(
                            color: _getWarehouseTypeColor().withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Icon(
                            _getWarehouseTypeIcon(),
                            color: _getWarehouseTypeColor(),
                            size: 20.sp,
                          ),
                        ),
                        SizedBox(width: 12.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      warehouse.name,
                                      style: AppTextStyles.titleMedium.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  if (warehouse.isDefault)
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 8.w,
                                        vertical: 2.h,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.orange,
                                        borderRadius:
                                            BorderRadius.circular(12.r),
                                      ),
                                      child: Text(
                                        'افتراضي',
                                        style: AppTextStyles.bodySmall.copyWith(
                                          color: Colors.white,
                                          fontSize: 10.sp,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                              if (warehouse.location != null)
                                Text(
                                  warehouse.location!,
                                  style: AppTextStyles.bodySmall.copyWith(
                                    color: Colors.grey[600],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Status indicator
                  Container(
                    width: 8.w,
                    height: 8.w,
                    decoration: BoxDecoration(
                      color: warehouse.isActive ? Colors.green : Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),

                  // Actions menu
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          onEdit?.call();
                          break;
                        case 'delete':
                          onDelete?.call();
                          break;
                        case 'setDefault':
                          onSetDefault?.call();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 16),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                      if (onSetDefault != null)
                        const PopupMenuItem(
                          value: 'setDefault',
                          child: Row(
                            children: [
                              Icon(Icons.star, size: 16),
                              SizedBox(width: 8),
                              Text('تعيين كافتراضي'),
                            ],
                          ),
                        ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 16, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              SizedBox(height: 12.h),

              // Warehouse details
              Row(
                children: [
                  Expanded(
                    child: _buildDetailItem(
                      'النوع',
                      _getWarehouseTypeText(),
                      Icons.category,
                    ),
                  ),
                  if (warehouse.productCount != null)
                    Expanded(
                      child: _buildDetailItem(
                        'المنتجات',
                        '${warehouse.productCount}',
                        Icons.inventory,
                      ),
                    ),
                ],
              ),

              if (warehouse.capacity != null || warehouse.totalValue != null)
                SizedBox(height: 8.h),

              if (warehouse.capacity != null || warehouse.totalValue != null)
                Row(
                  children: [
                    if (warehouse.capacity != null)
                      Expanded(
                        child: _buildDetailItem(
                          'السعة',
                          '${warehouse.capacity!.toStringAsFixed(0)} وحدة',
                          Icons.storage,
                        ),
                      ),
                    if (warehouse.totalValue != null)
                      Expanded(
                        child: _buildDetailItem(
                          'القيمة الإجمالية',
                          '${warehouse.totalValue!.toStringAsFixed(2)} ر.س',
                          Icons.monetization_on,
                        ),
                      ),
                  ],
                ),

              // Capacity utilization bar (if capacity is available)
              if (warehouse.capacity != null && warehouse.usedCapacity != null)
                Column(
                  children: [
                    SizedBox(height: 12.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'استخدام السعة',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          '${warehouse.capacityUtilization.toStringAsFixed(1)}%',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: _getCapacityColor(),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    LinearProgressIndicator(
                      value: warehouse.capacityUtilization / 100,
                      backgroundColor: Colors.grey[300],
                      valueColor:
                          AlwaysStoppedAnimation<Color>(_getCapacityColor()),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.sp,
          color: Colors.grey[600],
        ),
        SizedBox(width: 4.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getWarehouseTypeColor() {
    switch (warehouse.warehouseType) {
      case 'main':
        return AppColors.primary;
      case 'branch':
        return Colors.blue;
      case 'temporary':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getWarehouseTypeIcon() {
    switch (warehouse.warehouseType) {
      case 'main':
        return Icons.business;
      case 'branch':
        return Icons.store;
      case 'temporary':
        return Icons.schedule;
      default:
        return Icons.warehouse;
    }
  }

  String _getWarehouseTypeText() {
    switch (warehouse.warehouseType) {
      case 'main':
        return 'رئيسي';
      case 'branch':
        return 'فرع';
      case 'temporary':
        return 'مؤقت';
      default:
        return warehouse.warehouseType ?? 'غير محدد';
    }
  }

  Color _getCapacityColor() {
    final utilization = warehouse.capacityUtilization;
    if (utilization >= 90) return Colors.red;
    if (utilization >= 80) return Colors.orange;
    if (utilization >= 60) return Colors.yellow[700]!;
    return Colors.green;
  }
}
