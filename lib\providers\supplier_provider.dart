import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tijari_tech/data/local/dao/supplier_dao.dart';
import 'package:tijari_tech/data/local/database.dart';
import 'package:tijari_tech/data/models/supplier.dart';
import '../core/utils/app_utils.dart';
import '../core/utils/error_handler.dart';
import '../data/repositories/supplier_repository.dart';


// Providers للموردين
final supplierDaoProvider = Provider<SupplierDao>((ref) {
  final databaseProvider = Provider<DatabaseHelper>((ref) => DatabaseHelper());
  return SupplierDao(ref.watch(databaseProvider));
});

final supplierRepositoryProvider = Provider<SupplierRepository>((ref) {
  return SupplierRepository(ref.watch(supplierDaoProvider));
});

final supplierProvider =
    StateNotifierProvider<SupplierNotifier, SupplierState>((ref) {
  return SupplierNotifier(ref.watch(supplierRepositoryProvider));
});
/// حالة إدارة الموردين
class SupplierState {
  final List<Supplier> suppliers;
  final bool isLoading;
  final String? error;
  final String searchQuery;
  final SupplierBalanceStatus? filterStatus;
  final Map<String, dynamic>? statistics;

  const SupplierState({
    this.suppliers = const [],
    this.isLoading = false,
    this.error,
    this.searchQuery = '',
    this.filterStatus,
    this.statistics,
  });

  SupplierState copyWith({
    List<Supplier>? suppliers,
    bool? isLoading,
    String? error,
    String? searchQuery,
    SupplierBalanceStatus? filterStatus,
    Map<String, dynamic>? statistics,
  }) {
    return SupplierState(
      suppliers: suppliers ?? this.suppliers,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      searchQuery: searchQuery ?? this.searchQuery,
      filterStatus: filterStatus ?? this.filterStatus,
      statistics: statistics ?? this.statistics,
    );
  }

  List<Supplier> get filteredSuppliers {
    var filtered = suppliers;

    if (searchQuery.isNotEmpty) {
      filtered = filtered
          .where((supplier) =>
              supplier.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
              (supplier.phone
                      ?.toLowerCase()
                      .contains(searchQuery.toLowerCase()) ??
                  false) ||
              (supplier.email
                      ?.toLowerCase()
                      .contains(searchQuery.toLowerCase()) ??
                  false))
          .toList();
    }

    if (filterStatus != null) {
      filtered = filtered
          .where((supplier) => supplier.balanceStatus == filterStatus)
          .toList();
    }

    return filtered;
  }

  int get activeSuppliersCount => suppliers.where((s) => s.isActive).length;
  int get weOweSuppliersCount => suppliers.where((s) => s.hasDebt).length;
  int get theyOweSuppliersCount => suppliers.where((s) => s.hasCredit).length;

  double get totalWeOwe => suppliers
      .where((s) => s.hasDebt)
      .fold(0.0, (sum, supplier) => sum + supplier.balance);

  double get totalTheyOwe => suppliers
      .where((s) => s.hasCredit)
      .fold(0.0, (sum, supplier) => sum + supplier.absoluteBalance);
}

/// مزود إدارة الموردين
class SupplierNotifier extends StateNotifier<SupplierState> {
  final SupplierRepository _repository;

  SupplierNotifier(this._repository) : super(const SupplierState());

  void setSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
  }

  void setBalanceFilter(SupplierBalanceStatus? status) {
    state = state.copyWith(filterStatus: status);
  }

  void clearFilters() {
    state = state.copyWith(searchQuery: '', filterStatus: null);
  }

  Future<void> refresh() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final suppliers = await _repository.getAllSuppliers();

      Map<String, dynamic>? statistics;
      try {
        statistics = await _repository.getSupplierStatistics();
      } catch (e) {
        AppUtils.logError('Failed to load supplier statistics', e);
        statistics = {
          'total_count': suppliers.length,
          'active_count': suppliers.where((s) => s.isActive).length,
          'we_owe_count': suppliers.where((s) => s.balance > 0).length,
          'they_owe_count': suppliers.where((s) => s.balance < 0).length,
          'balanced_count': suppliers.where((s) => s.balance == 0).length,
          'total_we_owe': suppliers
              .where((s) => s.balance > 0)
              .fold(0.0, (sum, s) => sum + s.balance),
          'total_they_owe': suppliers
              .where((s) => s.balance < 0)
              .fold(0.0, (sum, s) => sum + s.absoluteBalance),
        };
      }

      state = state.copyWith(
        suppliers: suppliers,
        statistics: statistics,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error refreshing suppliers', e);
    }
  }

  Future<bool> createSupplier(Supplier supplier) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _repository.createSupplier(supplier);
      await refresh();
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error creating supplier', e);
      return false;
    }
  }

  Future<bool> updateSupplier(Supplier supplier) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _repository.updateSupplier(supplier);
      await refresh();
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error updating supplier', e);
      return false;
    }
  }

  Future<bool> deleteSupplier(String id) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _repository.deleteSupplier(id);
      await refresh();
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error deleting supplier', e);
      return false;
    }
  }

  Future<bool> updateSupplierBalance(String id, double newBalance) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _repository.updateSupplierBalance(id, newBalance);
      await refresh();
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error updating supplier balance', e);
      return false;
    }
  }

  Supplier? getSupplierById(String id) {
    try {
      return state.suppliers.firstWhere((supplier) => supplier.id == id);
    } catch (e) {
      return null;
    }
  }

  bool isSupplierNameExists(String name, {String? excludeId}) {
    return state.suppliers.any((supplier) =>
        supplier.name.toLowerCase() == name.toLowerCase() &&
        supplier.id != excludeId);
  }

  List<Supplier> getTopSuppliers({int limit = 10}) {
    final suppliers = state.suppliers.where((s) => s.isActive).toList();
    suppliers.sort((a, b) => b.absoluteBalance.compareTo(a.absoluteBalance));
    return suppliers.take(limit).toList();
  }
}


