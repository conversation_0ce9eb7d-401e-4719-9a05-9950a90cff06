import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../shared/widgets/loading_widget.dart';
import '../../shared/widgets/error_display_widget.dart';
import '../../shared/widgets/empty_state_widget.dart';
import '../../data/models/warehouse.dart';
import '../../providers/warehouse_provider.dart';
import '../../providers/stock_provider.dart';

/// صفحة تقارير المخازن
/// 
/// توفر تقارير شاملة لجميع المخازن مع:
/// - إحصائيات كل مخزن (عدد المنتجات، القيمة الإجمالية، السعة المستخدمة)
/// - مقارنة بين المخازن
/// - رسوم بيانية للأداء
/// - تقارير المخزون منخفض المستوى
/// 
/// تم تطوير هذه الصفحة لتوفير رؤية شاملة لأداء المخازن

class WarehouseReportsPage extends ConsumerStatefulWidget {
  const WarehouseReportsPage({super.key});

  @override
  ConsumerState<WarehouseReportsPage> createState() =>
      _WarehouseReportsPageState();
}

class _WarehouseReportsPageState extends ConsumerState<WarehouseReportsPage>
    with SingleTickerProviderStateMixin {
  
  late TabController _tabController;
  String _selectedWarehouseId = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // تحميل البيانات عند فتح الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(warehouseManagementProvider.notifier).loadWarehouses();
      ref.read(stockManagementProvider.notifier).loadStocks();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'تقارير المخازن',
          style: AppTextStyles.titleLarge.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(
              icon: Icon(Icons.dashboard),
              text: 'نظرة عامة',
            ),
            Tab(
              icon: Icon(Icons.bar_chart),
              text: 'الإحصائيات',
            ),
            Tab(
              icon: Icon(Icons.warning),
              text: 'التنبيهات',
            ),
          ],
        ),
        actions: [
          IconButton(
            onPressed: _exportReport,
            icon: const Icon(Icons.download, color: Colors.white),
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildStatisticsTab(),
          _buildAlertsTab(),
        ],
      ),
    );
  }

  /// تبويب النظرة العامة
  Widget _buildOverviewTab() {
    final warehouseState = ref.watch(warehouseManagementProvider);
    final stockState = ref.watch(stockManagementProvider);

    if (warehouseState.isLoading || stockState.isLoading) {
      return const LoadingWidget();
    }

    if (warehouseState.error != null) {
      return ErrorDisplayWidget(
        error: warehouseState.error!,
        onRetry: () {
          ref.read(warehouseManagementProvider.notifier).loadWarehouses();
        },
      );
    }

    final warehouses = warehouseState.warehouses;
    if (warehouses.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.warehouse,
        title: 'لا توجد مخازن',
        subtitle: 'لم يتم إنشاء أي مخازن بعد',
      );
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إحصائيات عامة
          _buildGeneralStats(warehouses, stockState.stocks),
          
          SizedBox(height: 16.h),
          
          // قائمة المخازن مع التفاصيل
          Text(
            'تفاصيل المخازن',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 12.h),
          
          ...warehouses.map((warehouse) => _buildWarehouseCard(
            warehouse, 
            stockState.stocks.where((s) => s.warehouseId == warehouse.id).toList(),
          )),
        ],
      ),
    );
  }

  /// بناء الإحصائيات العامة
  Widget _buildGeneralStats(List<Warehouse> warehouses, List<dynamic> stocks) {
    final totalWarehouses = warehouses.length;
    final activeWarehouses = warehouses.where((w) => w.isActive).length;
    final totalProducts = stocks.length;
    final totalValue = stocks.fold<double>(0, (sum, stock) => sum + (stock.quantity * stock.averageCost));

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الإحصائيات العامة',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي المخازن',
                  totalWarehouses.toString(),
                  Icons.warehouse,
                  AppColors.primary,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildStatCard(
                  'المخازن النشطة',
                  activeWarehouses.toString(),
                  Icons.check_circle,
                  AppColors.success,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي المنتجات',
                  totalProducts.toString(),
                  Icons.inventory,
                  AppColors.info,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildStatCard(
                  'القيمة الإجمالية',
                  '${totalValue.toStringAsFixed(0)} ر.س',
                  Icons.attach_money,
                  AppColors.warning,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء كارت إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32.r),
          SizedBox(height: 8.h),
          Text(
            value,
            style: AppTextStyles.titleLarge.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء كارت مخزن
  Widget _buildWarehouseCard(Warehouse warehouse, List<dynamic> warehouseStocks) {
    final productCount = warehouseStocks.length;
    final totalValue = warehouseStocks.fold<double>(
      0, 
      (sum, stock) => sum + (stock.quantity * stock.averageCost),
    );
    final lowStockCount = warehouseStocks.where((s) => s.isLowStock).length;
    final outOfStockCount = warehouseStocks.where((s) => s.isOutOfStock).length;

    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: warehouse.isActive 
                      ? AppColors.success.withOpacity(0.1)
                      : Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    warehouse.isDefault ? Icons.star : Icons.warehouse,
                    color: warehouse.isActive ? AppColors.success : Colors.grey,
                    size: 24.r,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            warehouse.name,
                            style: AppTextStyles.bodyLarge.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (warehouse.isDefault) ...[
                            SizedBox(width: 8.w),
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 6.w,
                                vertical: 2.h,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.amber.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(4.r),
                              ),
                              child: Text(
                                'افتراضي',
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: Colors.amber[700],
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      if (warehouse.location != null)
                        Text(
                          warehouse.location!,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: warehouse.isActive 
                      ? AppColors.success.withOpacity(0.1)
                      : Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    warehouse.isActive ? 'نشط' : 'غير نشط',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: warehouse.isActive ? AppColors.success : Colors.grey,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            // إحصائيات المخزن
            Row(
              children: [
                Expanded(
                  child: _buildWarehouseStatItem(
                    'المنتجات',
                    productCount.toString(),
                    Icons.inventory,
                  ),
                ),
                Expanded(
                  child: _buildWarehouseStatItem(
                    'القيمة الإجمالية',
                    '${totalValue.toStringAsFixed(0)} ر.س',
                    Icons.attach_money,
                  ),
                ),
              ],
            ),
            
            if (lowStockCount > 0 || outOfStockCount > 0) ...[
              SizedBox(height: 12.h),
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  borderRadius: BorderRadius.circular(6.r),
                  border: Border.all(color: Colors.orange[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange[600], size: 16.r),
                    SizedBox(width: 6.w),
                    Expanded(
                      child: Text(
                        'مخزون منخفض: $lowStockCount، نفد المخزون: $outOfStockCount',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.orange[700],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء عنصر إحصائية المخزن
  Widget _buildWarehouseStatItem(String title, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16.r, color: Colors.grey[600]),
        SizedBox(width: 6.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: AppTextStyles.bodySmall.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// تبويب الإحصائيات
  Widget _buildStatisticsTab() {
    return const Center(
      child: Text(
        'سيتم إضافة الرسوم البيانية والإحصائيات المتقدمة قريباً',
        style: TextStyle(fontSize: 16),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// تبويب التنبيهات
  Widget _buildAlertsTab() {
    final stockState = ref.watch(stockManagementProvider);
    
    if (stockState.isLoading) {
      return const LoadingWidget();
    }
    
    final lowStockItems = stockState.stocks.where((s) => s.isLowStock).toList();
    final outOfStockItems = stockState.stocks.where((s) => s.isOutOfStock).toList();
    
    if (lowStockItems.isEmpty && outOfStockItems.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.check_circle,
        title: 'لا توجد تنبيهات',
        subtitle: 'جميع المنتجات في مستوى مخزون جيد',
      );
    }
    
    return ListView(
      padding: EdgeInsets.all(16.w),
      children: [
        if (outOfStockItems.isNotEmpty) ...[
          Text(
            'نفد المخزون (${outOfStockItems.length})',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.error,
            ),
          ),
          SizedBox(height: 8.h),
          ...outOfStockItems.map((stock) => _buildAlertCard(
            stock,
            'نفد المخزون',
            AppColors.error,
            Icons.error,
          )),
          SizedBox(height: 16.h),
        ],
        
        if (lowStockItems.isNotEmpty) ...[
          Text(
            'مخزون منخفض (${lowStockItems.length})',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.warning,
            ),
          ),
          SizedBox(height: 8.h),
          ...lowStockItems.map((stock) => _buildAlertCard(
            stock,
            'مخزون منخفض',
            AppColors.warning,
            Icons.warning,
          )),
        ],
      ],
    );
  }

  /// بناء كارت تنبيه
  Widget _buildAlertCard(dynamic stock, String alertType, Color color, IconData icon) {
    return Card(
      margin: EdgeInsets.only(bottom: 8.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(12.w),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(icon, color: color, size: 20.r),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    stock.productName ?? 'منتج غير محدد',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    '${stock.warehouseName ?? 'مخزن غير محدد'} - $alertType',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: color,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${stock.quantity.toStringAsFixed(0)}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  'الحد الأدنى: ${stock.reorderPoint.toStringAsFixed(0)}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// تصدير التقرير
  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة ميزة التصدير قريباً'),
        backgroundColor: AppColors.info,
      ),
    );
  }
}
