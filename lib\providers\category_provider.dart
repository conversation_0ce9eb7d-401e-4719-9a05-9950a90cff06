import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/category.dart';
import '../data/repositories/category_repository.dart';
import '../core/utils/app_utils.dart';
import '../core/utils/error_handler.dart';
import 'database_provider.dart';

// Enhanced Category repository provider
final enhancedCategoryRepositoryProvider = Provider<CategoryRepository>((ref) {
  return CategoryRepository();
});

// Enhanced Category management provider
final enhancedCategoryManagementProvider = StateNotifierProvider<
    EnhancedCategoryManagementNotifier, EnhancedCategoryManagementState>((ref) {
  return EnhancedCategoryManagementNotifier(ref);
});

// Enhanced Category management state
class EnhancedCategoryManagementState {
  final bool isLoading;
  final String? error;
  final List<Category> categories;
  final List<Category> categoriesTree;
  final List<Category> filteredCategories;
  final String searchQuery;
  final bool showInactive;
  final Category? selectedCategory;
  final Map<String, bool> expandedCategories;

  const EnhancedCategoryManagementState({
    this.isLoading = false,
    this.error,
    this.categories = const [],
    this.categoriesTree = const [],
    this.filteredCategories = const [],
    this.searchQuery = '',
    this.showInactive = false,
    this.selectedCategory,
    this.expandedCategories = const {},
  });

  EnhancedCategoryManagementState copyWith({
    bool? isLoading,
    String? error,
    List<Category>? categories,
    List<Category>? categoriesTree,
    List<Category>? filteredCategories,
    String? searchQuery,
    bool? showInactive,
    Category? selectedCategory,
    Map<String, bool>? expandedCategories,
  }) {
    return EnhancedCategoryManagementState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      categories: categories ?? this.categories,
      categoriesTree: categoriesTree ?? this.categoriesTree,
      filteredCategories: filteredCategories ?? this.filteredCategories,
      searchQuery: searchQuery ?? this.searchQuery,
      showInactive: showInactive ?? this.showInactive,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      expandedCategories: expandedCategories ?? this.expandedCategories,
    );
  }
}

// Enhanced Category management notifier
class EnhancedCategoryManagementNotifier
    extends StateNotifier<EnhancedCategoryManagementState> {
  final Ref _ref;

  EnhancedCategoryManagementNotifier(this._ref)
      : super(const EnhancedCategoryManagementState()) {
    _loadCategories();
  }

  CategoryRepository get _repository =>
      _ref.read(enhancedCategoryRepositoryProvider);
  DatabaseOperations get _dbOperations => _ref.read(databaseOperationsProvider);

  // Load all categories
  Future<void> _loadCategories() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final categoriesTree =
          await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getCategoriesTree();
      });

      final allCategories = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getActiveCategories();
      });

      state = state.copyWith(
        isLoading: false,
        categories: allCategories,
        categoriesTree: categoriesTree,
        filteredCategories: _filterCategories(categoriesTree),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error loading categories', e);
    }
  }

  // Filter categories based on search query and show inactive setting
  List<Category> _filterCategories(List<Category> categories) {
    var filtered = categories;

    // Filter by search query
    if (state.searchQuery.isNotEmpty) {
      filtered = _searchInCategories(filtered, state.searchQuery);
    }

    // Filter by active status
    if (!state.showInactive) {
      filtered = _filterActiveCategories(filtered);
    }

    return filtered;
  }

  // Recursive search in categories
  List<Category> _searchInCategories(List<Category> categories, String query) {
    final result = <Category>[];
    final lowerQuery = query.toLowerCase();

    for (final category in categories) {
      bool matches = category.nameAr.toLowerCase().contains(lowerQuery) ||
          (category.nameEn?.toLowerCase().contains(lowerQuery) ?? false) ||
          (category.description?.toLowerCase().contains(lowerQuery) ?? false);

      List<Category>? filteredChildren;
      if (category.children != null && category.children!.isNotEmpty) {
        filteredChildren = _searchInCategories(category.children!, query);
        if (filteredChildren.isNotEmpty) {
          matches = true; // Include parent if children match
        }
      }

      if (matches) {
        result.add(category.copyWith(children: filteredChildren));
      }
    }

    return result;
  }

  // Filter active categories recursively
  List<Category> _filterActiveCategories(List<Category> categories) {
    final result = <Category>[];

    for (final category in categories) {
      if (category.isActiveCategory) {
        List<Category>? filteredChildren;
        if (category.children != null && category.children!.isNotEmpty) {
          filteredChildren = _filterActiveCategories(category.children!);
        }
        result.add(category.copyWith(children: filteredChildren));
      }
    }

    return result;
  }

  // Search categories
  void searchCategories(String query) {
    state = state.copyWith(
      searchQuery: query,
      filteredCategories: _filterCategories(state.categoriesTree),
    );
  }

  // Toggle show inactive categories
  void toggleShowInactive(bool showInactive) {
    state = state.copyWith(
      showInactive: showInactive,
      filteredCategories: _filterCategories(state.categoriesTree),
    );
  }

  // Select category
  void selectCategory(Category? category) {
    state = state.copyWith(selectedCategory: category);
  }

  // Toggle category expansion
  void toggleCategoryExpansion(String categoryId) {
    final expanded = Map<String, bool>.from(state.expandedCategories);
    expanded[categoryId] = !(expanded[categoryId] ?? false);
    state = state.copyWith(expandedCategories: expanded);
  }

  // Expand all categories
  List<String> expandAll() {
    final expanded = <String, bool>{};
    _setAllExpanded(state.categoriesTree, expanded, true);
    state = state.copyWith(expandedCategories: expanded);
    return expanded.keys.toList(); // ترجع كل الـ IDs
  }

  void collapseAll() {
    final expanded = <String, bool>{};
    _setAllExpanded(state.categoriesTree, expanded, false);
    state = state.copyWith(expandedCategories: expanded);
  }

  // Helper method to set expansion state recursively
  // void _setAllExpanded(
  //     List<Category> categories, Map<String, bool> expanded, bool isExpanded) {
  //   for (final category in categories) {
  //     expanded[category.id] = isExpanded;
  //     if (category.children != null && category.children!.isNotEmpty) {
  //       _setAllExpanded(category.children!, expanded, isExpanded);
  //     }
  //   }
  // }
  void _setAllExpanded(
    List<Category> categories,
    Map<String, bool> expanded,
    bool isExpanded,
  ) {
    for (final category in categories) {
      expanded[category.id] = isExpanded;
      if (category.children != null && category.children!.isNotEmpty) {
        _setAllExpanded(category.children!, expanded, isExpanded);
      }
    }
  }

  // Refresh categories
  Future<void> refresh() async {
    await _loadCategories();
    _ref.invalidate(enhancedCategoryRepositoryProvider);
  }

  // Create new category
  Future<bool> createCategory({
    required String nameAr,
    String? nameEn,
    String? parentId,
    String? description,
    String? color,
    int sortOrder = 0,
    bool isActive = true,
    double? taxRate,
    double? discountRate,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final category = Category.create(
        id: AppUtils.generateId(),
        nameAr: nameAr,
        nameEn: nameEn,
        parentId: parentId,
        description: description,
        color: color,
        sortOrder: sortOrder,
        isActive: isActive,
        taxRate: taxRate,
        discountRate: discountRate,
      );

      await _repository.createCategory(category);
      await refresh();
      AppUtils.logInfo('Category created successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error creating category', e);
      return false;
    }
  }

  // Update category
  Future<bool> updateCategory({
    required String id,
    String? nameAr,
    String? nameEn,
    String? parentId,
    String? description,
    String? color,
    int? sortOrder,
    bool? isActive,
    double? taxRate,
    double? discountRate,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final existingCategory =
          await _repository.getEnhancedCategoryWithDetails(id);
      if (existingCategory == null) {
        throw Exception('Category not found');
      }

      final updatedCategory = existingCategory.update(
        nameAr: nameAr,
        nameEn: nameEn,
        parentId: parentId,
        description: description,
        color: color,
        sortOrder: sortOrder,
        isActive: isActive,
        taxRate: taxRate,
        discountRate: discountRate,
      );

      await _repository.updateCategory(id, updatedCategory);
      await refresh();
      AppUtils.logInfo('Category updated successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error updating category', e);
      return false;
    }
  }

  // Delete category
  Future<bool> deleteCategory(String id) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _repository.deleteCategory(id);
      await refresh();
      AppUtils.logInfo('Category deleted successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error deleting category', e);
      return false;
    }
  }

  // Move category
  Future<bool> moveCategory(String categoryId, String? newParentId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _repository.moveCategory(categoryId, newParentId);
      await refresh();
      AppUtils.logInfo('Category moved successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error moving category', e);
      return false;
    }
  }

  // Validate data integrity
  Future<List<Map<String, dynamic>>> validateDataIntegrity() async {
    try {
      // For now, return empty list as this functionality needs to be implemented
      return [];
    } catch (e) {
      AppUtils.logError('Error validating data integrity', e);
      return [];
    }
  }

  // Fix data integrity issues
  Future<bool> fixDataIntegrityIssues() async {
    try {
      // For now, return true as this functionality needs to be implemented
      return true;
    } catch (e) {
      AppUtils.logError('Error fixing data integrity issues', e);
      return false;
    }
  }

  // Get category by ID
  Future<Category?> getCategoryById(String id) async {
    try {
      return await _repository.getCategoryById(id);
    } catch (e) {
      AppUtils.logError('Error getting category by ID', e);
      return null;
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Category by ID provider
final enhancedCategoryByIdProvider =
    FutureProvider.family<Category?, String>((ref, id) async {
  final repository = ref.read(enhancedCategoryRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getEnhancedCategoryWithDetails(id);
  });
});

// Main categories provider
final mainCategoriesProvider = FutureProvider<List<Category>>((ref) async {
  final repository = ref.read(enhancedCategoryRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.searchCategories(parentId: null);
  });
});
final categoryByIdProvider =
    FutureProvider.family<Category?, String>((ref, id) async {
  final repository = ref.read(enhancedCategoryRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getCategoryById(id); // تأكد أن الميثود موجودة
  });
});
// Categories statistics provider
final categoriesStatsProvider =
    FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.read(enhancedCategoryRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getCategoriesStatistics();
  });
});

// Most used categories provider
final mostUsedCategoriesProvider = FutureProvider<List<Category>>((ref) async {
  final repository = ref.read(enhancedCategoryRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getMostUsedCategories();
  });
});
