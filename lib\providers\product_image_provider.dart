import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/product_image.dart';
import '../data/local/dao/product_image_dao.dart';
import '../core/utils/app_utils.dart';
import '../core/utils/error_handler.dart';
import 'database_provider.dart';

// Product Image DAO provider
final productImageDaoProvider = Provider<ProductImageDao>((ref) {
  return ProductImageDao();
});

// Product Image management provider
final productImageManagementProvider = StateNotifierProvider<ProductImageManagementNotifier, ProductImageManagementState>((ref) {
  return ProductImageManagementNotifier(ref);
});

// Product Image management state
class ProductImageManagementState {
  final bool isLoading;
  final String? error;
  final Map<String, List<ProductImage>> productImages; // productId -> images
  final Map<String, dynamic> statistics;

  const ProductImageManagementState({
    this.isLoading = false,
    this.error,
    this.productImages = const {},
    this.statistics = const {},
  });

  ProductImageManagementState copyWith({
    bool? isLoading,
    String? error,
    Map<String, List<ProductImage>>? productImages,
    Map<String, dynamic>? statistics,
  }) {
    return ProductImageManagementState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      productImages: productImages ?? this.productImages,
      statistics: statistics ?? this.statistics,
    );
  }
}

// Product Image management notifier
class ProductImageManagementNotifier extends StateNotifier<ProductImageManagementState> {
  final Ref _ref;

  ProductImageManagementNotifier(this._ref) : super(const ProductImageManagementState());

  ProductImageDao get _dao => _ref.read(productImageDaoProvider);
  DatabaseOperations get _dbOperations => _ref.read(databaseOperationsProvider);

  // Load images for a product
  Future<void> loadProductImages(String productId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final images = await _dbOperations.executeWithReadyCheck(() async {
        return await _dao.findByProductId(productId);
      });
      
      final updatedProductImages = Map<String, List<ProductImage>>.from(state.productImages);
      updatedProductImages[productId] = images;
      
      state = state.copyWith(
        isLoading: false,
        productImages: updatedProductImages,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error loading product images', e);
    }
  }

  // Add image to product
  Future<bool> addImage(ProductImage image) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      await _dbOperations.executeWithReadyCheck(() async {
        return await _dao.insert(image);
      });
      
      // Update local state
      final updatedProductImages = Map<String, List<ProductImage>>.from(state.productImages);
      final currentImages = updatedProductImages[image.productId] ?? [];
      updatedProductImages[image.productId] = [...currentImages, image];
      
      state = state.copyWith(
        isLoading: false,
        productImages: updatedProductImages,
      );
      
      AppUtils.logInfo('Image added successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error adding image', e);
      return false;
    }
  }

  // Update image
  Future<bool> updateImage(String imageId, ProductImage image) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      await _dbOperations.executeWithReadyCheck(() async {
        return await _dao.update(imageId, image);
      });
      
      // Update local state
      final updatedProductImages = Map<String, List<ProductImage>>.from(state.productImages);
      final currentImages = updatedProductImages[image.productId] ?? [];
      final imageIndex = currentImages.indexWhere((img) => img.id == imageId);
      
      if (imageIndex != -1) {
        currentImages[imageIndex] = image;
        updatedProductImages[image.productId] = currentImages;
      }
      
      state = state.copyWith(
        isLoading: false,
        productImages: updatedProductImages,
      );
      
      AppUtils.logInfo('Image updated successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error updating image', e);
      return false;
    }
  }

  // Delete image
  Future<bool> deleteImage(String imageId, String productId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      await _dbOperations.executeWithReadyCheck(() async {
        return await _dao.delete(imageId);
      });
      
      // Update local state
      final updatedProductImages = Map<String, List<ProductImage>>.from(state.productImages);
      final currentImages = updatedProductImages[productId] ?? [];
      updatedProductImages[productId] = currentImages.where((img) => img.id != imageId).toList();
      
      state = state.copyWith(
        isLoading: false,
        productImages: updatedProductImages,
      );
      
      AppUtils.logInfo('Image deleted successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error deleting image', e);
      return false;
    }
  }

  // Set primary image
  Future<bool> setPrimaryImage(String productId, String imageId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      await _dbOperations.executeWithReadyCheck(() async {
        return await _dao.setPrimaryImage(productId, imageId);
      });
      
      // Update local state
      final updatedProductImages = Map<String, List<ProductImage>>.from(state.productImages);
      final currentImages = updatedProductImages[productId] ?? [];
      
      for (int i = 0; i < currentImages.length; i++) {
        currentImages[i] = currentImages[i].copyWith(
          isPrimary: currentImages[i].id == imageId,
        );
      }
      
      updatedProductImages[productId] = currentImages;
      
      state = state.copyWith(
        isLoading: false,
        productImages: updatedProductImages,
      );
      
      AppUtils.logInfo('Primary image set successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error setting primary image', e);
      return false;
    }
  }

  // Update sort order
  Future<bool> updateSortOrder(Map<String, int> imageOrders) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      await _dbOperations.executeWithReadyCheck(() async {
        return await _dao.bulkUpdateSortOrder(imageOrders);
      });
      
      // Update local state
      final updatedProductImages = Map<String, List<ProductImage>>.from(state.productImages);
      
      for (final productId in updatedProductImages.keys) {
        final currentImages = updatedProductImages[productId] ?? [];
        
        for (int i = 0; i < currentImages.length; i++) {
          final imageId = currentImages[i].id;
          if (imageOrders.containsKey(imageId)) {
            currentImages[i] = currentImages[i].copyWith(
              sortOrder: imageOrders[imageId],
            );
          }
        }
        
        // Sort by sort order
        currentImages.sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
        updatedProductImages[productId] = currentImages;
      }
      
      state = state.copyWith(
        isLoading: false,
        productImages: updatedProductImages,
      );
      
      AppUtils.logInfo('Sort order updated successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error updating sort order', e);
      return false;
    }
  }

  // Delete all images for a product
  Future<bool> deleteAllProductImages(String productId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      await _dbOperations.executeWithReadyCheck(() async {
        return await _dao.deleteByProductId(productId);
      });
      
      // Update local state
      final updatedProductImages = Map<String, List<ProductImage>>.from(state.productImages);
      updatedProductImages[productId] = [];
      
      state = state.copyWith(
        isLoading: false,
        productImages: updatedProductImages,
      );
      
      AppUtils.logInfo('All product images deleted successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error deleting all product images', e);
      return false;
    }
  }

  // Load statistics
  Future<void> loadStatistics() async {
    try {
      final statistics = await _dbOperations.executeWithReadyCheck(() async {
        return await _dao.getImagesStatistics();
      });
      
      state = state.copyWith(statistics: statistics);
    } catch (e) {
      AppUtils.logError('Error loading image statistics', e);
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  // Get images for a product
  List<ProductImage> getProductImages(String productId) {
    return state.productImages[productId] ?? [];
  }

  // Get primary image for a product
  ProductImage? getPrimaryImage(String productId) {
    final images = getProductImages(productId);
    try {
      return images.firstWhere((img) => img.isPrimary);
    } catch (e) {
      return images.isNotEmpty ? images.first : null;
    }
  }
}

// Product images by product ID provider
final productImagesByIdProvider = FutureProvider.family<List<ProductImage>, String>((ref, productId) async {
  final dao = ref.read(productImageDaoProvider);
  final dbOperations = ref.read(databaseOperationsProvider);
  
  return await dbOperations.executeWithReadyCheck(() async {
    return await dao.findByProductId(productId);
  });
});

// Primary image by product ID provider
final primaryImageByProductIdProvider = FutureProvider.family<ProductImage?, String>((ref, productId) async {
  final dao = ref.read(productImageDaoProvider);
  final dbOperations = ref.read(databaseOperationsProvider);
  
  return await dbOperations.executeWithReadyCheck(() async {
    return await dao.findPrimaryImageByProductId(productId);
  });
});

// Images statistics provider
final imagesStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final dao = ref.read(productImageDaoProvider);
  final dbOperations = ref.read(databaseOperationsProvider);
  
  return await dbOperations.executeWithReadyCheck(() async {
    return await dao.getImagesStatistics();
  });
});
