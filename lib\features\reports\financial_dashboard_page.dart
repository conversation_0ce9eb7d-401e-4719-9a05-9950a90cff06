import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/formatters.dart';
import '../../widgets/custom_card.dart';
import '../../data/repositories/sale_repository.dart';
import '../../data/repositories/purchase_repository.dart';
import 'sales_purchases_reports_page.dart';
import 'profit_loss_reports_page.dart';
import 'customer_supplier_reports_page.dart';
import '../../services/financial_reports_export.dart';

/// لوحة التحكم المالية الشاملة
class FinancialDashboardPage extends ConsumerStatefulWidget {
  const FinancialDashboardPage({super.key});

  @override
  ConsumerState<FinancialDashboardPage> createState() =>
      _FinancialDashboardPageState();
}

class _FinancialDashboardPageState
    extends ConsumerState<FinancialDashboardPage> {
  bool _isLoading = false;

  // بيانات لوحة التحكم
  Map<String, dynamic> _dashboardData = {};
  List<Map<String, dynamic>> _recentTransactions = [];

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل بيانات المبيعات
      final saleRepository = SaleRepository();
      final allSales = await saleRepository.getAllSales();
      final salesStats = await saleRepository.getSalesStatistics();

      // تحميل بيانات المشتريات
      final purchaseRepository = PurchaseRepository();
      final allPurchases = await purchaseRepository.getAllPurchases();
      final purchasesStats = await purchaseRepository.getPurchasesStatistics();

      // حساب الإحصائيات
      final totalRevenue = salesStats['totalAmount'] ?? 0.0;
      final totalExpenses = purchasesStats['totalAmount'] ?? 0.0;
      final netProfit = totalRevenue - totalExpenses;
      final profitMargin =
          totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;

      // تجميع العملاء والموردين الفريدين
      final uniqueCustomers = <String>{};
      final uniqueSuppliers = <String>{};

      for (final sale in allSales) {
        final customerId = sale['customer_id'] as String?;
        if (customerId != null) uniqueCustomers.add(customerId);
      }

      for (final purchase in allPurchases) {
        final supplierId = purchase['supplier_id'] as String?;
        if (supplierId != null) uniqueSuppliers.add(supplierId);
      }

      _dashboardData = {
        'totalRevenue': totalRevenue,
        'totalExpenses': totalExpenses,
        'netProfit': netProfit,
        'profitMargin': profitMargin,
        'totalSales': allSales.length,
        'totalPurchases': allPurchases.length,
        'totalCustomers': uniqueCustomers.length,
        'totalSuppliers': uniqueSuppliers.length,
        'pendingPayments': salesStats['totalDue'] ?? 0.0,
        'pendingReceivables': purchasesStats['totalDue'] ?? 0.0,
        'cashFlow': (salesStats['totalPaid'] ?? 0.0) -
            (purchasesStats['totalPaid'] ?? 0.0),
        'inventoryValue': 85000.0, // قيمة تقديرية للمخزون
      };

      // تحميل المعاملات الأخيرة
      _loadRecentTransactions(allSales, allPurchases);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل البيانات: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _loadRecentTransactions(
      List<Map<String, dynamic>> sales, List<Map<String, dynamic>> purchases) {
    final List<Map<String, dynamic>> allTransactions = [];

    // إضافة المبيعات
    for (final sale in sales.take(10)) {
      allTransactions.add({
        'type': 'sale',
        'description': 'فاتورة مبيعات #${sale['invoice_no'] ?? 'غير محدد'}',
        'amount': (sale['total'] as num?)?.toDouble() ?? 0.0,
        'date': sale['sale_date'] ?? '',
        'customer': sale['customer_name'] ?? 'عميل نقدي',
      });
    }

    // إضافة المشتريات
    for (final purchase in purchases.take(10)) {
      allTransactions.add({
        'type': 'purchase',
        'description':
            'فاتورة مشتريات #${purchase['invoice_no'] ?? 'غير محدد'}',
        'amount': -((purchase['total_amount'] as num?)?.toDouble() ?? 0.0),
        'date': purchase['purchase_date'] ?? '',
        'supplier': purchase['supplier_name'] ?? 'مورد نقدي',
      });
    }

    // ترتيب حسب التاريخ
    allTransactions.sort((a, b) {
      final dateA = DateTime.tryParse(a['date'] ?? '') ?? DateTime.now();
      final dateB = DateTime.tryParse(b['date'] ?? '') ?? DateTime.now();
      return dateB.compareTo(dateA);
    });

    _recentTransactions = allTransactions.take(10).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة التحكم المالية'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportToPDF,
            tooltip: 'تصدير إلى PDF',
          ),
          IconButton(
            onPressed: _refreshData,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث البيانات',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.r),
              child: Column(
                children: [
                  _buildKPICards(),
                  SizedBox(height: 16.h),
                  _buildFinancialSummary(),
                  SizedBox(height: 16.h),
                  _buildQuickActions(),
                  SizedBox(height: 16.h),
                  _buildRecentTransactions(),
                  SizedBox(height: 16.h),
                  _buildPerformanceIndicators(),
                ],
              ),
            ),
    );
  }

  Widget _buildKPICards() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 12.w,
      mainAxisSpacing: 12.h,
      childAspectRatio: 1.0,
      children: [
        _buildKPICard(
          'إجمالي الإيرادات',
          AppFormatters.formatCurrency(_dashboardData['totalRevenue']),
          Icons.trending_up,
          AppColors.success,
          '+12%',
        ),
        _buildKPICard(
          'صافي الربح',
          AppFormatters.formatCurrency(_dashboardData['netProfit']),
          Icons.monetization_on,
          AppColors.primary,
          '+8%',
        ),
        _buildKPICard(
          'إجمالي المصروفات',
          AppFormatters.formatCurrency(_dashboardData['totalExpenses']),
          Icons.trending_down,
          AppColors.error,
          '+5%',
        ),
        _buildKPICard(
          'هامش الربح',
          '${_dashboardData['profitMargin']}%',
          Icons.percent,
          AppColors.info,
          '+2%',
        ),
      ],
    );
  }

  Widget _buildKPICard(
      String title, String value, IconData icon, Color color, String change) {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(icon, color: color, size: 24.r),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                  decoration: BoxDecoration(
                    color: AppColors.success.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    change,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.success,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Text(
              value,
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              title,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSummary() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الملخص المالي',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'المبيعات',
                    '${_dashboardData['totalSales']}',
                    Icons.receipt_long,
                    AppColors.primary,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'المشتريات',
                    '${_dashboardData['totalPurchases']}',
                    Icons.shopping_cart,
                    AppColors.error,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'العملاء',
                    '${_dashboardData['totalCustomers']}',
                    Icons.people,
                    AppColors.info,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'الموردين',
                    '${_dashboardData['totalSuppliers']}',
                    Icons.business,
                    AppColors.warning,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
      String title, String value, IconData icon, Color color) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20.r),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  title,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'التقارير المالية',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 3,
              crossAxisSpacing: 8.w,
              mainAxisSpacing: 8.h,
              childAspectRatio: 0.8,
              children: [
                _buildActionCard(
                  'تقارير المبيعات والمشتريات',
                  Icons.analytics,
                  AppColors.primary,
                  () => _navigateToSalesPurchasesReports(),
                ),
                _buildActionCard(
                  'تقارير الأرباح والخسائر',
                  Icons.account_balance_wallet,
                  AppColors.success,
                  () => _navigateToProfitLossReports(),
                ),
                _buildActionCard(
                  'تقارير العملاء والموردين',
                  Icons.people_alt,
                  AppColors.info,
                  () => _navigateToAccountsReports(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _refreshData() {
    _loadDashboardData();
  }

  /// تصدير لوحة التحكم إلى PDF
  Future<void> _exportToPDF() async {
    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // تصدير البيانات
      final filePath = await FinancialReportsExport.exportDashboardToPDF(
        dashboardData: _dashboardData,
        recentTransactions: _recentTransactions,
      );

      // التحقق من أن الويدجت ما زال موجوداً
      if (!mounted) return;

      // إخفاء مؤشر التحميل
      Navigator.of(context).pop();

      // إظهار رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تصدير لوحة التحكم بنجاح إلى: $filePath'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      // التحقق من أن الويدجت ما زال موجوداً
      if (!mounted) return;

      // إخفاء مؤشر التحميل
      Navigator.of(context).pop();

      // إظهار رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء التصدير: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  void _navigateToSalesPurchasesReports() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SalesPurchasesReportsPage(),
      ),
    );
  }

  void _navigateToProfitLossReports() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ProfitLossReportsPage(),
      ),
    );
  }

  void _navigateToAccountsReports() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CustomerSupplierReportsPage(),
      ),
    );
  }

  Widget _buildActionCard(
      String title, IconData icon, Color color, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        padding: EdgeInsets.all(12.r),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 24.r),
            SizedBox(height: 8.h),
            Text(
              title,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.onSurface,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentTransactions() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(16.r),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المعاملات الأخيرة',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () => _navigateToSalesPurchasesReports(),
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _recentTransactions.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final transaction = _recentTransactions[index];
              return _buildTransactionItem(transaction);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(Map<String, dynamic> transaction) {
    final type = transaction['type'] as String;
    final description = transaction['description'] as String;
    final amount = (transaction['amount'] as num).toDouble();
    final date = transaction['date'] as String;
    final isPositive = amount > 0;

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: isPositive
            ? AppColors.success.withValues(alpha: 0.1)
            : AppColors.error.withValues(alpha: 0.1),
        child: Icon(
          type == 'sale' ? Icons.trending_up : Icons.trending_down,
          color: isPositive ? AppColors.success : AppColors.error,
          size: 20.r,
        ),
      ),
      title: Text(
        description,
        style: AppTextStyles.titleSmall.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        AppFormatters.formatDate(DateTime.parse(date)),
        style: AppTextStyles.bodySmall.copyWith(
          color: AppColors.onSurface.withValues(alpha: 0.7),
        ),
      ),
      trailing: Text(
        AppFormatters.formatCurrency(amount.abs()),
        style: AppTextStyles.titleSmall.copyWith(
          fontWeight: FontWeight.bold,
          color: isPositive ? AppColors.success : AppColors.error,
        ),
      ),
    );
  }

  Widget _buildPerformanceIndicators() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'مؤشرات الأداء',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            _buildIndicatorRow(
              'التدفق النقدي',
              AppFormatters.formatCurrency(_dashboardData['cashFlow']),
              _dashboardData['cashFlow'] > 0
                  ? AppColors.success
                  : AppColors.error,
              _dashboardData['cashFlow'] > 0
                  ? Icons.arrow_upward
                  : Icons.arrow_downward,
            ),
            SizedBox(height: 12.h),
            _buildIndicatorRow(
              'قيمة المخزون',
              AppFormatters.formatCurrency(_dashboardData['inventoryValue']),
              AppColors.info,
              Icons.inventory,
            ),
            SizedBox(height: 12.h),
            _buildIndicatorRow(
              'المبالغ المستحقة للعملاء',
              AppFormatters.formatCurrency(
                  _dashboardData['pendingReceivables']),
              AppColors.warning,
              Icons.account_balance,
            ),
            SizedBox(height: 12.h),
            _buildIndicatorRow(
              'المبالغ المستحقة للموردين',
              AppFormatters.formatCurrency(_dashboardData['pendingPayments']),
              AppColors.error,
              Icons.payment,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIndicatorRow(
      String title, String value, Color color, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: color, size: 20.r),
        SizedBox(width: 12.w),
        Expanded(
          child: Text(
            title,
            style: AppTextStyles.bodyMedium,
          ),
        ),
        Text(
          value,
          style: AppTextStyles.titleSmall.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
