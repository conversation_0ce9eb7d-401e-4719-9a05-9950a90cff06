// import 'dart:io';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:tijari_tech/core/utils/app_utils.dart';
// import 'package:tijari_tech/data/local/database.dart';
// import 'package:tijari_tech/providers/stock_management_provider.dart';

// void main() async {
//   WidgetsFlutterBinding.ensureInitialized();
  
//   print('🧪 بدء اختبار إدارة المخزون...');
  
//   try {
//     // إنشاء قاعدة البيانات
//     final database = AppDatabase();
//     await database.database;
//     print('✅ تم إنشاء قاعدة البيانات بنجاح');
    
//     // إنشاء container للـ providers
//     final container = ProviderContainer();
    
//     // اختبار إضافة منتج للمخزون
//     await testStockAdjustment(container, 'add', 10.0);
    
//     // اختبار طرح من المخزون
//     await testStockAdjustment(container, 'subtract', 3.0);
    
//     // اختبار تعيين كمية مباشرة
//     await testStockAdjustment(container, 'set', 15.0);
    
//     print('🎉 تم إكمال جميع الاختبارات بنجاح!');
    
//   } catch (e, stackTrace) {
//     print('❌ خطأ في الاختبار: $e');
//     print('Stack trace: $stackTrace');
//   }
  
//   exit(0);
// }

// Future<void> testStockAdjustment(ProviderContainer container, String operationType, double quantity) async {
//   try {
//     print('\n🔄 اختبار عملية $operationType بكمية $quantity');
    
//     final stockProvider = container.read(stockManagementProvider.notifier);
    
//     // معرف منتج تجريبي
//     const productId = 'test_product_001';
//     const warehouseId = 'warehouse_main';
//     const userId = 'user_admin';
    
//     // تنفيذ العملية
//     await stockProvider.adjustStock(
//       productId: productId,
//       warehouseId: warehouseId,
//       operationType: operationType,
//       quantity: quantity,
//       reason: 'اختبار النظام',
//       notes: 'اختبار عملية $operationType',
//       userId: userId,
//     );
    
//     print('✅ تمت عملية $operationType بنجاح');
    
//   } catch (e) {
//     print('❌ خطأ في عملية $operationType: $e');
//     rethrow;
//   }
// }
