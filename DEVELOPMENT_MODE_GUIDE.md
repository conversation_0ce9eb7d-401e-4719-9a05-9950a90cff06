# Development Mode Configuration Guide

This guide explains how to use the development configuration system to bypass production constraints for easier testing.

## Quick Start

### 1. Initialize Development Environment

Add this to your `main.dart`:

```dart
import 'package:flutter/foundation.dart';
import 'lib/core/config/dev_initializer.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize development environment in debug mode
  if (kDebugMode) {
    await DevInitializer.initialize();
    
    // Configure for your testing needs
    DevInitializer.configureForTesting(scenario: 'full_bypass');
  }
  
  runApp(MyApp());
}
```

### 2. Replace Production Services

Replace your existing service calls with development-aware versions:

**Before:**
```dart
// Old way - strict production checks
if (!AuthService.isLoggedIn()) {
  return; // Blocks testing
}

if (!PermissionService.hasInventoryAccess()) {
  throw Exception('No permission'); // Blocks testing
}
```

**After:**
```dart
// New way - bypassed in development
if (!DevAuthService.isLoggedIn()) {
  return; // Always passes in dev mode
}

if (!DevPermissionService.hasInventoryAccess()) {
  throw Exception('No permission'); // Always passes in dev mode
}
```

## Configuration Options

### Available Scenarios

1. **`full_bypass`** - Bypasses everything (fastest testing)
2. **`stock_operations`** - Optimized for inventory testing
3. **`sales_testing`** - Optimized for sales flow testing
4. **`purchase_testing`** - Optimized for purchase flow testing
5. **`production_like`** - Keeps all validations (realistic testing)

### Custom Configuration

```dart
DevInitializer.configureForTesting(
  customConfig: {
    'bypassAuth': true,           // Skip login checks
    'bypassPermissions': true,    // Skip permission checks
    'bypassValidation': false,    // Keep business validations
    'useMockData': true,          // Use safe default values
    'skipEncryption': true,       // Skip encryption/decryption
    'logAllOperations': true,     // Log all operations for debugging
    'usePredictableIds': false,   // Use UUIDs (false) or predictable IDs (true)
  },
);
```

## Integration Examples

### 1. Modify Your DAOs

**Before:**
```dart
Future<String> insert(Map<String, dynamic> data) async {
  // Missing required fields cause foreign key errors
  await db.insert('stock_movements', data);
}
```

**After:**
```dart
Future<String> insert(Map<String, dynamic> data) async {
  // Use development wrapper to add safe defaults
  final safeData = DevDaoWrapper.prepareStockMovementData(data);
  await db.insert('stock_movements', safeData);
}
```

### 2. Modify Your Validation Methods

**Before:**
```dart
Future<void> validateStockOperation(String productId, double quantity) async {
  if (productId.isEmpty) {
    throw ValidationException('Product ID required');
  }
  // More validations...
}
```

**After:**
```dart
Future<void> validateStockOperation(String productId, double quantity) async {
  // Bypass validation in development mode
  if (DevelopmentConfig.bypassValidation) {
    DevelopmentConfig.logDevOperation('Validation bypassed', {
      'productId': productId,
      'quantity': quantity,
    });
    return;
  }
  
  // Original validation logic
  if (productId.isEmpty) {
    throw ValidationException('Product ID required');
  }
  // More validations...
}
```

### 3. Modify Your Route Guards

**Before:**
```dart
String? checkAccess(BuildContext context, GoRouterState state) {
  if (!AuthService.isLoggedIn()) {
    return '/login';
  }
  if (!PermissionService.hasInventoryAccess()) {
    return '/unauthorized';
  }
  return null;
}
```

**After:**
```dart
String? checkAccess(BuildContext context, GoRouterState state) {
  if (!DevAuthService.isLoggedIn()) {
    return '/login'; // Bypassed in dev mode
  }
  if (!DevPermissionService.hasInventoryAccess()) {
    return '/unauthorized'; // Bypassed in dev mode
  }
  return null;
}
```

## Common Use Cases

### Testing Stock Operations

```dart
// Configure for stock testing
DevInitializer.configureForTesting(scenario: 'stock_operations');

// Set custom user context
DevUserContext.setMockContext({
  'userId': 'stock-tester',
  'warehouseId': 'test-warehouse',
  'canTransferStock': true,
  'canAdjustStock': true,
});

// Now test your stock operations - they'll use safe defaults
```

### Testing Database Insertions

```dart
// Incomplete data that would normally fail
final incompleteData = {
  'product_id': 'test-product',
  'quantity': 10.0,
  // Missing: created_by, warehouse_id, unit_id, etc.
};

// Use wrapper to add safe defaults
final safeData = DevDaoWrapper.prepareStockMovementData(incompleteData);

// safeData now has all required fields with valid values
```

### Testing Without Authentication

```dart
// Configure to bypass all auth
DevelopmentConfig.configure(
  bypassAuth: true,
  bypassPermissions: true,
);

// All auth checks will now pass
assert(DevAuthService.isLoggedIn() == true);
assert(DevPermissionService.hasInventoryAccess() == true);
```

## Debugging and Logging

### Enable Detailed Logging

```dart
DevelopmentConfig.configure(logAllOperations: true);

// Now all operations will be logged:
// DEV: Auth check bypassed - {'result': true}
// DEV: Permission check bypassed - {'permission': 'inventory', 'result': true}
// DEV: Insert data prepared - {'table': 'stock_movements', 'id': 'generated-id'}
```

### Check Current Configuration

```dart
final status = DevelopmentConfig.getStatus();
print('Development Status: $status');

// Output:
// {
//   'isDevelopment': true,
//   'isEnabled': true,
//   'bypassAuth': true,
//   'bypassPermissions': true,
//   'bypassValidation': true,
//   'useMockData': true,
//   'skipEncryption': true,
//   'logAllOperations': true,
//   'usePredictableIds': false
// }
```

## Testing Scenarios

### Scenario 1: Quick Feature Testing

```dart
// For rapid prototyping and feature testing
DevInitializer.configureForTesting(scenario: 'full_bypass');

// Everything is bypassed - fastest testing
// Use when you just want to test UI/UX without backend constraints
```

### Scenario 2: Realistic Business Logic Testing

```dart
// Keep validations but bypass auth/permissions
DevelopmentConfig.configure(
  bypassAuth: true,
  bypassPermissions: true,
  bypassValidation: false, // Keep business rules
  useMockData: true,
);

// Use when you want to test business logic but skip user management
```

### Scenario 3: Production-like Testing

```dart
// Test with all constraints (like production)
DevInitializer.configureForTesting(scenario: 'production_like');

// Use when you want to test the complete flow including validations
```

## Best Practices

1. **Always check `kDebugMode`** before using development features
2. **Use specific scenarios** instead of full bypass when possible
3. **Log operations** to understand what's happening during tests
4. **Reset configuration** between different test suites
5. **Use mock contexts** to simulate different user roles
6. **Disable development mode** for production builds

## Troubleshooting

### Foreign Key Constraint Errors

**Problem:** Still getting foreign key errors even with development mode enabled.

**Solution:** Make sure you're using the development wrappers:

```dart
// Instead of direct insertion
final data = {'product_id': 'test'};
await dao.insert(data); // May fail

// Use development wrapper
final safeData = DevDaoWrapper.prepareStockMovementData(data);
await dao.insert(safeData); // Will have safe defaults
```

### Validation Still Blocking

**Problem:** Validations are still throwing exceptions.

**Solution:** Make sure you've modified your validation methods:

```dart
Future<void> myValidation() async {
  if (DevelopmentConfig.bypassValidation) {
    return; // Skip validation
  }
  // Original validation logic
}
```

### Authentication Still Required

**Problem:** Still being redirected to login screen.

**Solution:** Replace `AuthService` calls with `DevAuthService`:

```dart
// Change this
if (!AuthService.isLoggedIn()) { ... }

// To this
if (!DevAuthService.isLoggedIn()) { ... }
```

## Complete Example

See `lib/core/config/dev_usage_examples.dart` for comprehensive examples of all features.
