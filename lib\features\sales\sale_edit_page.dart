
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:tijari_tech/data/repositories/sale_repository.dart';
import 'package:tijari_tech/providers/enhanced_product_provider.dart';
import 'package:tijari_tech/shared/widgets/loading_widget.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/formatters.dart';
import '../../data/models/sale.dart';
import '../../data/models/sale_item.dart';
import '../../data/models/customer.dart';
import '../../providers/sale_provider.dart';
import '../../providers/customer_provider.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/error_widget.dart';
import '../../widgets/custom_card.dart';
import '../../widgets/custom_text_field.dart';
import 'widgets/add_item_dialog.dart';

class SaleEditPage extends ConsumerStatefulWidget {
  final String saleId;
  
  const SaleEditPage({super.key, required this.saleId});

  @override
  ConsumerState<SaleEditPage> createState() => _SaleEditPageState();
}

class _SaleEditPageState extends ConsumerState<SaleEditPage> {
  final _formKey = GlobalKey<FormState>();
  
  // Controllers
  final _discountController = TextEditingController();
  final _taxController = TextEditingController();
  final _paidAmountController = TextEditingController();
  final _notesController = TextEditingController();
  
  // State variables
  Sale? _originalSale;
  List<SaleItem> _items = [];
  Customer? _selectedCustomer;
  String _paymentMethod = 'cash';
  bool _isLoading = true;
  bool _isSaving = false;
  String? _error;

  Future<void> _loadSaleData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // تحميل بيانات الفاتورة
      final saleRepository = SaleRepository();
      final saleData = await saleRepository.getSaleById(widget.saleId);
      if (saleData == null) {
        throw Exception('لم يتم العثور على الفاتورة');
      }

      final sale = Sale.fromMap(saleData['sale']);
      final items = (saleData['items'] as List<Map<String, dynamic>>)
          .map((item) => SaleItem.fromMap(item))
          .toList();

      // تحميل بيانات العميل إذا كان موجود
      Customer? customer;
      if (sale.customerId != null) {
        final customerProvider = ref.read(customerDaoProvider);
        customer = await customerProvider.findById(sale.customerId!);
      }

      setState(() {
        _originalSale = sale;
        _items = List.from(items);
        _selectedCustomer = customer;
        _paymentMethod = sale.paymentMethod ?? 'cash';

        // تعبئة الحقول
        _notesController.text = sale.notes ?? '';
        _discountController.text = (sale.discount ?? 0.0).toString();
        _taxController.text = (sale.tax ?? 0.0).toString();
        _paidAmountController.text = sale.paid.toString();

        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'خطأ في تحميل بيانات الفاتورة: $e';
        _isLoading = false;
      });
    }
  }

  void _loadInitialData() {
    final customerNotifier = ref.read(customerProvider.notifier);
    final productNotifier =
        ref.read(enhancedProductManagementProvider.notifier);

    customerNotifier.refresh();
    productNotifier.refresh();
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'تعديل الفاتورة',
      actions: [
        if (!_isLoading && _originalSale != null)
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _isSaving ? null : _saveSale,
            tooltip: 'حفظ التعديلات',
          ),
      ],
      child: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: LoadingWidget());
    }

    if (_error != null) {
      return Center(
        child: CustomErrorWidget(
          message: _error!,
          onRetry: _loadSaleData,
        ),
      );
    }

    if (_originalSale == null) {
      return const Center(
        child: Text('لم يتم العثور على الفاتورة'),
      );
    }

    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSaleHeader(),
            SizedBox(height: 16.h),
            _buildCustomerSelection(),
            SizedBox(height: 16.h),
            _buildItemsSection(),
            SizedBox(height: 16.h),
            _buildCalculationsSection(),
            SizedBox(height: 16.h),
            _buildPaymentSection(),
            SizedBox(height: 16.h),
            _buildNotesSection(),
            SizedBox(height: 24.h),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildSaleHeader() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تعديل فاتورة مبيعات',
              style: AppTextStyles.headlineMedium.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8.h),
            Row(
              children: [
                Text(
                  'رقم الفاتورة: ${_originalSale!.invoiceNo}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const Spacer(),
                Text(
                  'التاريخ: ${AppFormatters.formatDate(_originalSale!.date)}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerSelection() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'العميل',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            Consumer(
              builder: (context, ref, child) {
                final customerState = ref.watch(customerProvider);

                return DropdownButtonFormField<Customer?>(
                  value: _selectedCustomer,
                  decoration: const InputDecoration(
                    labelText: 'اختر العميل',
                    hintText: 'عميل نقدي',
                  ),
                  items: [
                    const DropdownMenuItem<Customer?>(
                      value: null,
                      child: Text('عميل نقدي'),
                    ),
                    ...customerState.customers.map((customer) {
                      return DropdownMenuItem<Customer>(
                        value: customer,
                        child: Text(customer.name),
                      );
                    }),
                  ],
                  onChanged: (customer) {
                    setState(() {
                      _selectedCustomer = customer;
                    });
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsSection() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المنتجات',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _showAddProductDialog,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة منتج'),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            if (_items.isEmpty)
              Container(
                padding: EdgeInsets.all(32.r),
                child: const Center(
                  child: Text('لم يتم إضافة أي منتجات بعد'),
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _items.length,
                separatorBuilder: (context, index) => Divider(height: 16.h),
                itemBuilder: (context, index) {
                  final item = _items[index];
                  return _buildItemRow(item, index);
                },
              ),
            SizedBox(height: 16.h),
            Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'المجموع الفرعي:',
                    style: AppTextStyles.titleMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    AppFormatters.formatCurrency(_calculateSubtotal()),
                    style: AppTextStyles.titleMedium.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemRow(SaleItem item, int index) {
    return Container(
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.productId ?? 'منتج غير محدد',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'الكمية: ${item.qty} × ${AppFormatters.formatCurrency(item.unitPrice)}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            AppFormatters.formatCurrency(item.total),
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.success,
            ),
          ),
          SizedBox(width: 8.w),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'edit') {
                _editItem(index);
              } else if (value == 'delete') {
                _removeItem(index);
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit),
                    SizedBox(width: 8),
                    Text('تعديل'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCalculationsSection() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الحسابات',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _discountController,
                    label: 'الخصم',
                    keyboardType: TextInputType.number,
                    onChanged: (_) => setState(() {}),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: CustomTextField(
                    controller: _taxController,
                    label: 'الضريبة',
                    keyboardType: TextInputType.number,
                    onChanged: (_) => setState(() {}),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'الإجمالي النهائي:',
                    style: AppTextStyles.titleLarge.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    AppFormatters.formatCurrency(_calculateTotal()),
                    style: AppTextStyles.titleLarge.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.success,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentSection() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الدفع',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            DropdownButtonFormField<String>(
              value: _paymentMethod,
              decoration: const InputDecoration(
                labelText: 'طريقة الدفع',
              ),
              items: const [
                DropdownMenuItem(value: 'cash', child: Text('نقدي')),
                DropdownMenuItem(value: 'credit', child: Text('آجل')),
                DropdownMenuItem(value: 'card', child: Text('بطاقة')),
                DropdownMenuItem(
                    value: 'bank_transfer', child: Text('تحويل بنكي')),
              ],
              onChanged: (value) {
                setState(() {
                  _paymentMethod = value!;
                });
              },
            ),
            SizedBox(height: 16.h),
            CustomTextField(
              controller: _paidAmountController,
              label: 'المبلغ المدفوع',
              keyboardType: TextInputType.number,
              onChanged: (_) => setState(() {}),
            ),
            SizedBox(height: 12.h),
            Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: _calculateDue() > 0
                    ? AppColors.warning.withValues(alpha: 0.1)
                    : AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'المبلغ المستحق:',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    AppFormatters.formatCurrency(_calculateDue()),
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.bold,
                      color: _calculateDue() > 0
                          ? AppColors.warning
                          : AppColors.success,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملاحظات',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            CustomTextField(
              controller: _notesController,
              label: 'ملاحظات إضافية',
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: _isSaving ? null : _saveSale,
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 16.h),
            ),
            child: _isSaving
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('حفظ التعديلات'),
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: OutlinedButton(
            onPressed: _isSaving ? null : () => context.pop(),
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 16.h),
            ),
            child: const Text('إلغاء'),
          ),
        ),
      ],
    );
  }

  double _calculateSubtotal() {
    return _items.fold(0.0, (sum, item) => sum + item.total);
  }

  double _calculateDiscount() {
    return double.tryParse(_discountController.text) ?? 0.0;
  }

  double _calculateTax() {
    return double.tryParse(_taxController.text) ?? 0.0;
  }

  double _calculateTotal() {
    return _calculateSubtotal() - _calculateDiscount() + _calculateTax();
  }

  double _calculatePaidAmount() {
    return double.tryParse(_paidAmountController.text) ?? 0.0;
  }

  double _calculateDue() {
    return _calculateTotal() - _calculatePaidAmount();
  }

  void _showAddProductDialog() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => const AddItemDialog(),
    );

    if (result != null && result['item'] != null) {
      setState(() {
        _items.add(result['item'] as SaleItem);
      });
    }
  }

  void _editItem(int index) async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => AddItemDialog(
        editIndex: index,
        existingItem: _items[index],
      ),
    );

    if (result != null && result['item'] != null) {
      setState(() {
        _items[index] = result['item'] as SaleItem;
      });
    }
  }

  void _removeItem(int index) {
    setState(() {
      _items.removeAt(index);
    });
  }

  Future<void> _saveSale() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يجب إضافة منتج واحد على الأقل')),
      );
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final saleProvider = ref.read(saleManagementProvider.notifier);

      // إنشاء الفاتورة المحدثة
      final updatedSale = _originalSale!.copyWith(
        customerId: _selectedCustomer?.id,
        // customerName: _selectedCustomer?.name,
        // subtotal: _calculateSubtotal(),
        discount: _calculateDiscount(),
        tax: _calculateTax(),
        total: _calculateTotal(),
        paid: _calculatePaidAmount(),
        due: _calculateDue(),
        paymentMethod: _paymentMethod,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        updatedAt: DateTime.now(),
      );

      // حفظ التعديلات
      // await saleProvider.updateSale(updatedSale, _items);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ التعديلات بنجاح')),
        );
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في حفظ التعديلات: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}


