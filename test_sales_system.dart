// import 'dart:io';
// import 'package:sqflite_common_ffi/sqflite_ffi.dart';
// import 'package:tijari_tech/data/local/database.dart';
// import 'package:tijari_tech/data/local/dao/sale_dao.dart';
// import 'package:tijari_tech/data/models/sale.dart';
// import 'package:tijari_tech/data/models/sale_item.dart';
// import 'package:tijari_tech/core/utils/app_utils.dart';

// void main() async {
//   print('🚀 بدء اختبار نظام المبيعات...');
  
//   // تهيئة sqflite للعمل على سطح المكتب
//   if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
//     sqfliteFfiInit();
//     databaseFactory = databaseFactoryFfi;
//   }

//   try {
//     // تهيئة قاعدة البيانات
//     await initializeDatabase();
//     print('✅ تم تهيئة قاعدة البيانات بنجاح');

//     // إنشاء DAO للمبيعات
//     final saleDao = SaleDao();
    
//     // إنشاء فاتورة بيع تجريبية
//     final sale = Sale.create(
//       id: AppUtils.generateId(),
//       invoiceNo: AppUtils.generateInvoiceNumber('INV'),
//       customerId: null, // بيع نقدي
//       subtotal: 100.0,
//       tax: 15.0,
//       discount: 5.0,
//       total: 110.0,
//       paid: 110.0,
//       due: 0.0,
//       paymentMethod: 'cash',
//       status: 'completed',
//       notes: 'اختبار نظام المبيعات',
//     );

//     // إنشاء عناصر الفاتورة
//     final items = [
//       SaleItem.create(
//         id: AppUtils.generateId(),
//         saleId: sale.id,
//         productId: 'test-product-1',
//         unitId: 'test-unit-1',
//         qty: 2.0,
//         unitPrice: 50.0,
//         total: 100.0,
//       ),
//     ];

//     print('📝 إنشاء فاتورة بيع...');
    
//     // إنشاء الفاتورة مع العناصر
//     final saleId = await saleDao.createSaleWithItems(sale, items);
    
//     print('✅ تم إنشاء فاتورة البيع بنجاح - ID: $saleId');
//     print('📊 رقم الفاتورة: ${sale.invoiceNo}');
//     print('💰 إجمالي الفاتورة: ${sale.total}');
//     print('📦 عدد العناصر: ${items.length}');
    
//     // استرجاع الفاتورة للتأكد
//     final retrievedSale = await saleDao.getSaleWithItems(saleId);
//     if (retrievedSale != null) {
//       print('✅ تم استرجاع الفاتورة بنجاح');
//       print('📋 تفاصيل الفاتورة: ${retrievedSale['sale']}');
//       print('📦 عناصر الفاتورة: ${retrievedSale['items'].length} عنصر');
//     }

//     print('🎉 اكتمل اختبار نظام المبيعات بنجاح!');
    
//   } catch (e) {
//     print('❌ خطأ في اختبار النظام: $e');
//     print('📍 تفاصيل الخطأ: ${e.toString()}');
//   }
// }
