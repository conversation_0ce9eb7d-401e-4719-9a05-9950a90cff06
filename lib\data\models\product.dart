import 'package:json_annotation/json_annotation.dart';
import 'package:tijari_tech/core/constants/database_constants.dart';
import 'product_image.dart';
import 'product_unit_price.dart';

part 'product.g.dart';

@JsonSerializable()
class Product {
  final String id; // يُخزن هنا مُعرّف المنتج الفريد (المفتاح الأساسي).
  final String nameAr; // يُخزن هنا اسم المنتج باللغة العربية.
  final String? nameEn; // يُخزن هنا اسم المنتج باللغة الإنجليزية (اختياري).
  final String? barcode; // يُخزن هنا رمز الباركود الخاص بالمنتج (اختياري).
  final String? qrCode; // يُخزن هنا رمز QR الخاص بالمنتج (اختياري).
  final String? description; // يُخزن هنا وصف تفصيلي للمنتج (اختياري).
  final String? categoryId; // يُخزن هنا مُعرّف الفئة التي ينتمي إليها المنتج (مفتاح خارجي لجدول الفئات).
  final String? baseUnitId; // يُخزن هنا مُعرّف الوحدة الأساسية للمنتج (مثل: قطعة، كجم، لتر) (مفتاح خارجي لجدول الوحدات).
  final double costPrice; // يُخزن هنا سعر تكلفة شراء المنتج.
  final double sellingPrice; // يُخزن هنا سعر بيع المنتج للعميل.
  final double minStock; // يُخزن هنا الحد الأدنى لكمية المخزون الموصى بها لهذا المنتج.
  final double maxStock; // يُخزن هنا الحد الأقصى لكمية المخزون الموصى بها لهذا المنتج.
  final double reorderPoint; // يُخزن هنا نقطة إعادة الطلب، وهي الكمية التي عند الوصول إليها يجب إعادة طلب المنتج.
  final bool trackStock; // يُخزن هنا قيمة منطقية (صحيح/خطأ) لتحديد ما إذا كان يجب تتبع مخزون هذا المنتج أم لا.
  final bool isActive; // يُخزن هنا قيمة منطقية (صحيح/خطأ) لتحديد ما إذا كان المنتج نشطًا ومتاحًا للبيع.
  final bool allowNegativeStock; // يُخزن هنا قيمة منطقية (صحيح/خطأ) لتحديد ما إذا كان مسموحًا أن يصبح رصيد المخزون سالبًا لهذا المنتج.
  final bool hasExpiryDate; // يُخزن هنا قيمة منطقية (صحيح/خطأ) لتحديد ما إذا كان المنتج له تاريخ انتهاء صلاحية.
  final DateTime ?expiryDate; // يُخزن هنا تاريخ انتهاء صلاحية المنتج (اختياري، يظهر فقط إذا كان hasExpiryDate صحيحًا).
  final String? tags; // يُخزن هنا سلسلة نصية (JSON string) تحتوي على وسوم أو كلمات مفتاحية للمنتج (اختياري).
  final String? notes; // يُخزن هنا أي ملاحظات إضافية حول المنتج (اختياري).
  final double? weight; // يُخزن هنا وزن المنتج (اختياري).
  final double? volume; // يُخزن هنا حجم المنتج (اختياري).
  final String? sku; // يُخزن هنا وحدة حفظ المخزون (Stock Keeping Unit) للمنتج (اختياري).
  final String? manufacturerCode; // يُخزن هنا كود الشركة المصنعة للمنتج (اختياري).
  final String? supplierCode; // يُخزن هنا كود المورد للمنتج (اختياري).
  final String? imageUrl; // يُخزن هنا رابط صورة المنتج الرئيسية (اختياري).
  final DateTime? createdAt; // يُخزن هنا تاريخ ووقت إنشاء سجل المنتج.
  final DateTime? updatedAt; // يُخزن هنا تاريخ ووقت آخر تحديث لسجل المنتج.
  final DateTime? deletedAt; // يُخزن هنا تاريخ ووقت حذف المنتج (يستخدم للحذف المنطقي - Soft Delete).
  final bool isSynced; // يُخزن هنا قيمة منطقية (صحيح/خطأ) لتحديد ما إذا كان المنتج قد تمت مزامنته مع خادم خارجي.

  // Related data (not stored in main table) - بيانات مرتبطة (لا يتم تخزينها في الجدول الرئيسي للمنتجات)
  final List<ProductImage>? images; // لا يُخزن في جدول المنتج. تُستخدم هذه القائمة لتخزين كائنات صور المنتج المرتبطة به، وعادةً ما تُجلب من جدول منفصل للصور أو تُستخدم لعرض البيانات.
  final List<ProductUnitPrice>? unitPrices; // لا يُخزن في جدول المنتج. تُستخدم هذه القائمة لتخزين كائنات أسعار الوحدات البديلة للمنتج (مثل سعر البيع بالكرتونة)، وعادةً ما تُجلب من جدول منفصل لأسعار الوحدات.
  final String? categoryName; // لا يُخزن في جدول المنتج. يُستخدم لعرض اسم الفئة المرتبط بـ `categoryId`، ويُجلب عادةً عن طريق ربط (JOIN) مع جدول الفئات.
  final String? unitName; // لا يُخزن في جدول المنتج. يُستخدم لعرض اسم الوحدة الأساسية المرتبط بـ `baseUnitId`، ويُجلب عادةً عن طريق ربط (JOIN) مع جدول الوحدات.
  final double? currentStock; // لا يُخزن في جدول المنتج كحقل مباشر. يُستخدم لعرض الكمية الحالية للمخزون لهذا المنتج، وتُحسب عادةً بشكل ديناميكي من حركات المخزون (الداخل والخارج) أو تُجلب من جدول مخزون منفصل.

  const Product({
    required this.id,
    required this.nameAr,
    this.nameEn,
    this.barcode,
    this.qrCode,
    this.description,
    this.categoryId,
    this.baseUnitId,
    required this.costPrice,
    required this.sellingPrice,
    this.minStock = 0,
    this.maxStock = 0,
    this.reorderPoint = 0,
    this.trackStock = true,
    this.isActive = true,
    this.allowNegativeStock = false,
    this.hasExpiryDate = false,
    this.expiryDate ,
    this.tags,
    this.notes,
    this.weight,
    this.volume,
    this.sku,
    this.manufacturerCode,
    this.supplierCode,
    this.imageUrl,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
    this.images,
    this.unitPrices,
    this.categoryName,
    this.unitName,
    this.currentStock,
  });


  // ------------------------------------------------------------------
  // Map / JSON
  // ------------------------------------------------------------------
  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map[DatabaseConstants.columnProductId] as String,
      nameAr: map[DatabaseConstants.columnProductNameAr] as String,
      nameEn: map[DatabaseConstants.columnProductNameEn] as String?,
      barcode: map[DatabaseConstants.columnProductBarcode] as String?,
      qrCode: map[DatabaseConstants.columnProductQrCode] as String?,
      description: map[DatabaseConstants.columnProductDescription] as String?,
      categoryId: map[DatabaseConstants.columnProductCategoryId] as String?,
      baseUnitId: map[DatabaseConstants.columnProductBaseUnitId] as String?,
      costPrice:
          (map[DatabaseConstants.columnProductCostPrice] as num).toDouble(),
      sellingPrice:
          (map[DatabaseConstants.columnProductSellingPrice] as num).toDouble(),
      minStock:
          (map[DatabaseConstants.columnProductMinStock] as num?)?.toDouble() ??
              0,
      maxStock:
          (map[DatabaseConstants.columnProductMaxStock] as num?)?.toDouble() ??
              0,
      reorderPoint: (map[DatabaseConstants.columnProductReorderPoint] as num?)
              ?.toDouble() ??
          0,
      trackStock:
          (map[DatabaseConstants.columnProductTrackStock] as int? ?? 1) == 1,
      isActive:
          (map[DatabaseConstants.columnProductIsActive] as int? ?? 1) == 1,
      allowNegativeStock:
          (map[DatabaseConstants.columnProductAllowNegativeStock] as int? ??
                  0) ==
              1,
      hasExpiryDate:
          (map[DatabaseConstants.columnProductHasExpiryDate] as int? ?? 0) == 1,
            expiryDate: map[DatabaseConstants.columnProductExpiryDate] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnProductExpiryDate] as String)
          : null,
      tags: map[DatabaseConstants.columnProductTags] as String?,
      notes: map[DatabaseConstants.columnProductNotes] as String?,
      weight: (map[DatabaseConstants.columnProductWeight] as num?)?.toDouble(),
      volume: (map[DatabaseConstants.columnProductVolume] as num?)?.toDouble(),
      sku: map[DatabaseConstants.columnProductSku] as String?,
      manufacturerCode:
          map[DatabaseConstants.columnProductManufacturerCode] as String?,
      supplierCode: map[DatabaseConstants.columnProductSupplierCode] as String?,
      imageUrl: map[DatabaseConstants.columnProductImageUrl] as String?,
      createdAt: map[DatabaseConstants.columnProductCreatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnProductCreatedAt] as String)
          : null,
      updatedAt: map[DatabaseConstants.columnProductUpdatedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnProductUpdatedAt] as String)
          : null,
      deletedAt: map[DatabaseConstants.columnProductDeletedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnProductDeletedAt] as String)
          : null,
      isSynced:
          (map[DatabaseConstants.columnProductIsSynced] as int? ?? 0) == 1,
      categoryName: map['category_name'] as String?,
      unitName: map['unit_name'] as String?,
      currentStock: (map['current_stock'] as num?)?.toDouble(),
    );
  }
  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnProductId: id,
      DatabaseConstants.columnProductNameAr: nameAr,
      DatabaseConstants.columnProductNameEn: nameEn,
      DatabaseConstants.columnProductBarcode: barcode,
      DatabaseConstants.columnProductQrCode: qrCode,
      DatabaseConstants.columnProductDescription: description,
      DatabaseConstants.columnProductCategoryId: categoryId,
      DatabaseConstants.columnProductBaseUnitId: baseUnitId,
      DatabaseConstants.columnProductCostPrice: costPrice,
      DatabaseConstants.columnProductSellingPrice: sellingPrice,
      DatabaseConstants.columnProductMinStock: minStock,
      DatabaseConstants.columnProductMaxStock: maxStock,
      DatabaseConstants.columnProductReorderPoint: reorderPoint,
      DatabaseConstants.columnProductTrackStock: trackStock ? 1 : 0,
      DatabaseConstants.columnProductIsActive: isActive ? 1 : 0,
      DatabaseConstants.columnProductAllowNegativeStock:
          allowNegativeStock ? 1 : 0,
      DatabaseConstants.columnProductHasExpiryDate: hasExpiryDate ? 1 : 0,
      DatabaseConstants.columnProductExpiryDate: expiryDate?.toIso8601String(),
      DatabaseConstants.columnProductTags: tags,
      DatabaseConstants.columnProductNotes: notes,
      DatabaseConstants.columnProductWeight: weight,
      DatabaseConstants.columnProductVolume: volume,
      DatabaseConstants.columnProductSku: sku,
      DatabaseConstants.columnProductManufacturerCode: manufacturerCode,
      DatabaseConstants.columnProductSupplierCode: supplierCode,
      DatabaseConstants.columnProductImageUrl: imageUrl,
      DatabaseConstants.columnProductCreatedAt: createdAt?.toIso8601String(),
      DatabaseConstants.columnProductUpdatedAt: updatedAt?.toIso8601String(),
      DatabaseConstants.columnProductDeletedAt: deletedAt?.toIso8601String(),
      DatabaseConstants.columnProductIsSynced: isSynced ? 1 : 0,
    };
  }

  factory Product.fromJson(Map<String, dynamic> json) =>
      _$ProductFromJson(json);
  Map<String, dynamic> toJson() => _$ProductToJson(this);

  // ------------------------------------------------------------------
  // CopyWith
  // ------------------------------------------------------------------
  Product copyWith({
    String? id,
    String? nameAr,
    String? nameEn,
    String? barcode,
    String? qrCode,
    String? description,
    String? categoryId,
    String? baseUnitId,
    double? costPrice,
    double? sellingPrice,
    double? minStock,
    double? maxStock,
    double? reorderPoint,
    bool? trackStock,
    bool? isActive,
    bool? allowNegativeStock,
    bool? hasExpiryDate,
    DateTime? expiryDate,
    String? tags,
    String? notes,
    double? weight,
    double? volume,
    String? sku,
    String? manufacturerCode,
    String? supplierCode,
    String? imageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
    List<ProductImage>? images,
    List<ProductUnitPrice>? unitPrices,
    String? categoryName,
    String? unitName,
    double? currentStock,
  }) {
    return Product(
      id: id ?? this.id,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      barcode: barcode ?? this.barcode,
      qrCode: qrCode ?? this.qrCode,
      description: description ?? this.description,
      categoryId: categoryId ?? this.categoryId,
      baseUnitId: baseUnitId ?? this.baseUnitId,
      costPrice: costPrice ?? this.costPrice,
      sellingPrice: sellingPrice ?? this.sellingPrice,
      minStock: minStock ?? this.minStock,
      maxStock: maxStock ?? this.maxStock,
      reorderPoint: reorderPoint ?? this.reorderPoint,
      trackStock: trackStock ?? this.trackStock,
      isActive: isActive ?? this.isActive,
      allowNegativeStock: allowNegativeStock ?? this.allowNegativeStock,
      hasExpiryDate: hasExpiryDate ?? this.hasExpiryDate,
      expiryDate: expiryDate ?? this.expiryDate,

      tags: tags ?? this.tags,
      notes: notes ?? this.notes,
      weight: weight ?? this.weight,
      volume: volume ?? this.volume,
      sku: sku ?? this.sku,
      manufacturerCode: manufacturerCode ?? this.manufacturerCode,
      supplierCode: supplierCode ?? this.supplierCode,
      imageUrl: imageUrl ?? this.imageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
      images: images ?? this.images,
      unitPrices: unitPrices ?? this.unitPrices,
      categoryName: categoryName ?? this.categoryName,
      unitName: unitName ?? this.unitName,
      currentStock: currentStock ?? this.currentStock,
    );
  }

  // ------------------------------------------------------------------
  // Derived helpers
  // ------------------------------------------------------------------
  bool get isDeleted => deletedAt != null;
  bool get isActiveProduct => !isDeleted && isActive;
  bool get hasBarcode => barcode != null && barcode!.isNotEmpty;
  bool get hasQrCode => qrCode != null && qrCode!.isNotEmpty;
  bool get hasCategory => categoryId != null && categoryId!.isNotEmpty;
  bool get hasBaseUnit => baseUnitId != null && baseUnitId!.isNotEmpty;
  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;
  bool get hasImages => images != null && images!.isNotEmpty;
  bool get hasMultipleUnits => unitPrices != null && unitPrices!.length > 1;

  bool get needsReorder => minStock <= reorderPoint;
  double get profit => sellingPrice - costPrice;
  double get profitMargin => costPrice > 0 ? profit / costPrice : 0;

  // Calculate profit margin as percentage
  double get profitMarginPercentage {
    if (costPrice == 0) return 0;
    return ((sellingPrice - costPrice) / costPrice) * 100;
  }

  // Calculate profit amount
  double get profitAmount => sellingPrice - costPrice;

  // Check if stock is low
  bool get isLowStock {
    if (!trackStock || currentStock == null) return false;
    return currentStock! <= reorderPoint;
  }

  // Check if out of stock
  bool get isOutOfStock {
    if (!trackStock || currentStock == null) return false;
    return currentStock! <= 0;
  }

  // Get stock status
  String get stockStatus {
    if (!trackStock) return 'غير متتبع';
    if (currentStock == null) return 'غير محدد';
    if (isOutOfStock) return 'نفد المخزون';
    if (isLowStock) return 'مخزون منخفض';
    return 'متوفر';
  }

  StockStatus getStockStatus(double currentStock) {
    if (currentStock <= 0) return StockStatus.outOfStock;
    if (currentStock <= reorderPoint) return StockStatus.lowStock;
    return StockStatus.inStock;
  }

  // Get primary image
  ProductImage? get primaryImage {
    if (images == null || images!.isEmpty) return null;
    try {
      return images!.firstWhere((img) => img.isPrimary && img.isActive);
    } catch (e) {
      return images!.isNotEmpty ? images!.first : null;
    }
  }

  // Get default unit price
  ProductUnitPrice? get defaultUnitPrice {
    if (unitPrices == null || unitPrices!.isEmpty) return null;
    try {
      return unitPrices!
          .firstWhere((price) => price.isDefault && price.isActive);
    } catch (e) {
      return unitPrices!.isNotEmpty ? unitPrices!.first : null;
    }
  }

  // Parse tags
  List<String> get tagsList {
    if (tags == null || tags!.isEmpty) return [];
    try {
      // Assuming tags are stored as comma-separated values
      return tags!
          .split(',')
          .map((tag) => tag.trim())
          .where((tag) => tag.isNotEmpty)
          .toList();
    } catch (e) {
      return [];
    }
  }

  // Get display name based on locale
  String getDisplayName([String locale = 'ar']) {
    if (locale == 'en' && nameEn != null && nameEn!.isNotEmpty) {
      return nameEn!;
    }
    return nameAr;
  }

  // ------------------------------------------------------------------
  // Factory creators
  // ------------------------------------------------------------------
  static Product create({
    required String id,
    required String nameAr,
    String? nameEn,
    String? barcode,
    String? qrCode,
    String? description,
    String? categoryId,
    String? baseUnitId,
    double costPrice = 0,
    double sellingPrice = 0,
    double minStock = 0,
    double maxStock = 0,
    double reorderPoint = 0,
    bool trackStock = true,
    bool isActive = true,
    bool allowNegativeStock = false,
    bool hasExpiryDate = false,
    DateTime ?expiryDate ,
    String? tags,
    String? notes,
    double? weight,
    double? volume,
    String? sku,
    String? manufacturerCode,
    String? supplierCode,
    String? imageUrl,
  }) {
    final now = DateTime.now();
    return Product(
      id: id,
      nameAr: nameAr,
      nameEn: nameEn,
      barcode: barcode,
      qrCode: qrCode,
      description: description,
      categoryId: categoryId,
      baseUnitId: baseUnitId,
      costPrice: costPrice,
      sellingPrice: sellingPrice,
      minStock: minStock,
      maxStock: maxStock,
      reorderPoint: reorderPoint,
      trackStock: trackStock,
      isActive: isActive,
      allowNegativeStock: allowNegativeStock,
      hasExpiryDate: hasExpiryDate,
      expiryDate:expiryDate,
      tags: tags,
      notes: notes,
      weight: weight,
      volume: volume,
      sku: sku,
      manufacturerCode: manufacturerCode,
      supplierCode: supplierCode,
      imageUrl: imageUrl,
      createdAt: now,
      updatedAt: now,
      isSynced: false,
    );
  }

  // Update product with new timestamp
  Product update({
    String? nameAr,
    String? nameEn,
    String? barcode,
    String? qrCode,
    String? description,
    String? categoryId,
    String? baseUnitId,
    double? costPrice,
    double? sellingPrice,
    double? minStock,
    double? maxStock,
    double? reorderPoint,
    bool? trackStock,
    bool? isActive,
    bool? allowNegativeStock,
    bool? hasExpiryDate,
    DateTime? expiryDate,
    String? tags,
    String? notes,
    double? weight,
    double? volume,
    String? sku,
    String? manufacturerCode,
    String? supplierCode,
  }) {
    return copyWith(
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      barcode: barcode ?? this.barcode,
      qrCode: qrCode ?? this.qrCode,
      description: description ?? this.description,
      categoryId: categoryId ?? this.categoryId,
      baseUnitId: baseUnitId ?? this.baseUnitId,
      costPrice: costPrice ?? this.costPrice,
      sellingPrice: sellingPrice ?? this.sellingPrice,
      minStock: minStock ?? this.minStock,
      maxStock: maxStock ?? this.maxStock,
      reorderPoint: reorderPoint ?? this.reorderPoint,
      trackStock: trackStock ?? this.trackStock,
      isActive: isActive ?? this.isActive,
      allowNegativeStock: allowNegativeStock ?? this.allowNegativeStock,
      hasExpiryDate: hasExpiryDate ?? this.hasExpiryDate,
            expiryDate: expiryDate ?? this.expiryDate,

      tags: tags ?? this.tags,
      notes: notes ?? this.notes,
      weight: weight ?? this.weight,
      volume: volume ?? this.volume,
      sku: sku ?? this.sku,
      manufacturerCode: manufacturerCode ?? this.manufacturerCode,
      supplierCode: supplierCode ?? this.supplierCode,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  Product markAsDeleted() => copyWith(
        deletedAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isSynced: false,
      );

  Product markAsSynced() => copyWith(isSynced: true);

  // Equality and hashCode
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Product && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // String representation
  @override
  String toString() {
    return 'Product(id: $id, nameAr: $nameAr, barcode: $barcode, isActive: $isActive)';
  }
}

enum StockStatus {
  inStock,
  lowStock,
  outOfStock,
}

extension StockStatusExtension on StockStatus {
  String get displayName {
    switch (this) {
      case StockStatus.inStock:
        return 'متوفر';
      case StockStatus.lowStock:
        return 'منخفض';
      case StockStatus.outOfStock:
        return 'نفذ';
    }
  }
}
