import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/branch.dart';
import '../data/local/dao/branch_dao.dart';
import '../core/utils/app_utils.dart';
import '../core/utils/error_handler.dart';
import 'database_provider.dart';

// Branch DAO provider
final branchDaoProvider = Provider<BranchDao>((ref) {
  return BranchDao();
});

// All branches provider
final branchesProvider = FutureProvider<List<Branch>>((ref) async {
  final branchDao = ref.read(branchDaoProvider);
  final dbOperations = ref.read(databaseOperationsProvider);
  
  return await dbOperations.executeWithReadyCheck(() async {
    return await branchDao.getActiveBranches();
  });
});

// Branch by ID provider
final branchByIdProvider = FutureProvider.family<Branch?, String>((ref, id) async {
  final branchDao = ref.read(branchDaoProvider);
  final dbOperations = ref.read(databaseOperationsProvider);
  
  return await dbOperations.executeWithReadyCheck(() async {
    return await branchDao.findById(id);
  });
});

// Default branch provider
final defaultBranchProvider = FutureProvider<Branch?>((ref) async {
  final branchDao = ref.read(branchDaoProvider);
  final dbOperations = ref.read(databaseOperationsProvider);
  
  return await dbOperations.executeWithReadyCheck(() async {
    return await branchDao.getDefaultBranch();
  });
});

// Branch search provider
final branchSearchProvider = FutureProvider.family<List<Branch>, String>((ref, query) async {
  if (query.trim().isEmpty) {
    return ref.read(branchesProvider.future);
  }
  
  final branchDao = ref.read(branchDaoProvider);
  final dbOperations = ref.read(databaseOperationsProvider);
  
  return await dbOperations.executeWithReadyCheck(() async {
    return await branchDao.searchByName(query);
  });
});

// Branch statistics provider
final branchStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final branchDao = ref.read(branchDaoProvider);
  final dbOperations = ref.read(databaseOperationsProvider);
  
  return await dbOperations.executeWithReadyCheck(() async {
    return await branchDao.getBranchStats();
  });
});

// Branch management provider
final branchManagementProvider = StateNotifierProvider<BranchManagementNotifier, BranchManagementState>((ref) {
  return BranchManagementNotifier(ref);
});

// Branch management state
class BranchManagementState {
  final bool isLoading;
  final String? error;
  final Branch? selectedBranch;
  final List<Branch> branches;

  const BranchManagementState({
    this.isLoading = false,
    this.error,
    this.selectedBranch,
    this.branches = const [],
  });

  BranchManagementState copyWith({
    bool? isLoading,
    String? error,
    Branch? selectedBranch,
    List<Branch>? branches,
  }) {
    return BranchManagementState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      selectedBranch: selectedBranch ?? this.selectedBranch,
      branches: branches ?? this.branches,
    );
  }
}

// Branch management notifier
class BranchManagementNotifier extends StateNotifier<BranchManagementState> {
  final Ref _ref;

  BranchManagementNotifier(this._ref) : super(const BranchManagementState()) {
    _loadBranches();
  }

  BranchDao get _branchDao => _ref.read(branchDaoProvider);
  DatabaseOperations get _dbOperations => _ref.read(databaseOperationsProvider);

  // Load all branches
  Future<void> _loadBranches() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final branches = await _dbOperations.executeWithReadyCheck(() async {
        return await _branchDao.getActiveBranches();
      });
      
      state = state.copyWith(
        isLoading: false,
        branches: branches,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error loading branches', e);
    }
  }

  // Refresh branches
  Future<void> refresh() async {
    await _loadBranches();
    _ref.invalidate(branchesProvider);
  }

  // Create new branch
  Future<bool> createBranch({
    required String name,
    String? address,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final branchId = await _dbOperations.executeWithReadyCheck(() async {
        return await _branchDao.createBranch(
          name: name,
          address: address,
        );
      });
      
      if (branchId != null) {
        await refresh();
        AppUtils.logInfo('Branch created successfully: $branchId');
        return true;
      }
      
      return false;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error creating branch', e);
      return false;
    }
  }

  // Update branch
  Future<bool> updateBranch({
    required String id,
    String? name,
    String? address,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final success = await _dbOperations.executeWithReadyCheck(() async {
        return await _branchDao.updateBranch(
          id: id,
          name: name,
          address: address,
        );
      });
      
      if (success) {
        await refresh();
        AppUtils.logInfo('Branch updated successfully: $id');
        return true;
      }
      
      return false;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error updating branch', e);
      return false;
    }
  }

  // Delete branch
  Future<bool> deleteBranch(String id) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final success = await _dbOperations.executeWithReadyCheck(() async {
        return await _branchDao.delete(id);
      });
      
      if (success) {
        await refresh();
        AppUtils.logInfo('Branch deleted successfully: $id');
        return true;
      }
      
      return false;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error deleting branch', e);
      return false;
    }
  }

  // Select branch
  void selectBranch(Branch? branch) {
    state = state.copyWith(selectedBranch: branch);
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  // Check if branch name exists
  Future<bool> branchNameExists(String name, {String? excludeId}) async {
    try {
      return await _dbOperations.executeWithReadyCheck(() async {
        return await _branchDao.nameExists(name, excludeId: excludeId);
      });
    } catch (e) {
      AppUtils.logError('Error checking branch name existence', e);
      return false;
    }
  }

  // Get branches for selection (dropdown)
  Future<List<Map<String, dynamic>>> getBranchesForSelection() async {
    try {
      return await _dbOperations.executeWithReadyCheck(() async {
        return await _branchDao.getBranchesForSelection();
      });
    } catch (e) {
      AppUtils.logError('Error getting branches for selection', e);
      return [];
    }
  }
}

// Current branch provider (for multi-branch support)
final currentBranchProvider = StateNotifierProvider<CurrentBranchNotifier, Branch?>((ref) {
  return CurrentBranchNotifier(ref);
});

// Current branch notifier
class CurrentBranchNotifier extends StateNotifier<Branch?> {
  final Ref _ref;

  CurrentBranchNotifier(this._ref) : super(null) {
    _loadCurrentBranch();
  }

  // Load current branch from storage or use default
  Future<void> _loadCurrentBranch() async {
    try {
      // Try to get saved branch ID from storage
      // For now, use default branch
      final defaultBranch = await _ref.read(defaultBranchProvider.future);
      state = defaultBranch;
    } catch (e) {
      AppUtils.logError('Error loading current branch', e);
    }
  }

  // Set current branch
  Future<void> setBranch(Branch branch) async {
    try {
      state = branch;
      // Save to storage
      // await StorageHelper.setBranchId(branch.id);
      AppUtils.logInfo('Current branch set to: ${branch.name}');
    } catch (e) {
      AppUtils.logError('Error setting current branch', e);
    }
  }

  // Clear current branch
  void clearBranch() {
    state = null;
  }
}

// Branch validation provider
final branchValidationProvider = Provider<BranchValidation>((ref) {
  return BranchValidation(ref);
});

// Branch validation class
class BranchValidation {
  final Ref _ref;

  BranchValidation(this._ref);

  // Validate branch name
  Future<String?> validateName(String name, {String? excludeId}) async {
    if (name.trim().isEmpty) {
      return 'اسم الفرع مطلوب';
    }

    if (name.trim().length < 2) {
      return 'اسم الفرع يجب أن يكون حرفين على الأقل';
    }

    if (name.length > 100) {
      return 'اسم الفرع يجب أن يكون أقل من 100 حرف';
    }

    final branchManagement = _ref.read(branchManagementProvider.notifier);
    final exists = await branchManagement.branchNameExists(name, excludeId: excludeId);
    
    if (exists) {
      return 'اسم الفرع موجود مسبقاً';
    }

    return null;
  }

  // Validate branch address
  String? validateAddress(String? address) {
    if (address != null && address.length > 500) {
      return 'العنوان يجب أن يكون أقل من 500 حرف';
    }

    return null;
  }

  // Validate complete branch data
  Future<Map<String, String?>> validateBranch({
    required String name,
    String? address,
    String? excludeId,
  }) async {
    final errors = <String, String?>{};

    errors['name'] = await validateName(name, excludeId: excludeId);
    errors['address'] = validateAddress(address);

    // Remove null errors
    errors.removeWhere((key, value) => value == null);

    return errors;
  }
}
