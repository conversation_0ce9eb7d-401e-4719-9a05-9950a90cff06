import 'package:flutter/material.dart';
import 'package:tijari_tech/data/local/delete_db.dart';
import 'examples/business_operations_example.dart';
import 'data/local/database.dart';
import 'core/utils/app_utils.dart';
import 'core/constants/app_constants.dart';

/// التطبيق الرئيسي لعرض النظام التجاري
/// يحتوي على واجهة بسيطة لتشغيل الأمثلة العملية
void main() async {
  
  runApp(const BusinessDemoApp());
}



class BusinessDemoApp extends StatelessWidget {
  const BusinessDemoApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Cairo',
        textTheme: const TextTheme(
          bodyLarge: TextStyle(fontSize: 16),
          bodyMedium: TextStyle(fontSize: 14),
          titleLarge: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
      ),
      locale: const Locale('ar', 'SA'),
      home: const BusinessDemoHomePage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class BusinessDemoHomePage extends StatefulWidget {
  const BusinessDemoHomePage({Key? key}) : super(key: key);

  @override
  State<BusinessDemoHomePage> createState() => _BusinessDemoHomePageState();
}

class _BusinessDemoHomePageState extends State<BusinessDemoHomePage> {
  final BusinessOperationsExample _example = BusinessOperationsExample();
  bool _isLoading = false;
  String _output = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('نظام تجاري تك - العرض التوضيحي'),
        backgroundColor: Colors.blue[800],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // معلومات النظام
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'نظام تجاري تك المحاسبي المتكامل',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'نظام محاسبي شامل يوفر جميع الميزات المطلوبة لإدارة الأعمال التجارية',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'الإصدار: ${AppConstants.appVersion}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // أزرار العمليات
            Text(
              'العمليات المتاحة:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),

            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildActionButton(
                  'تشغيل المثال الشامل',
                  Icons.play_circle_filled,
                  Colors.green,
                  _runCompleteExample,
                ),
                _buildActionButton(
                  'إضافة منتجات',
                  Icons.add_box,
                  Colors.blue,
                  _addProducts,
                ),
                _buildActionButton(
                  'عملية شراء',
                  Icons.shopping_cart,
                  Colors.orange,
                  _createPurchase,
                ),
                _buildActionButton(
                  'عملية بيع',
                  Icons.point_of_sale,
                  Colors.purple,
                  _createSale,
                ),
                _buildActionButton(
                  'تحديث المخزون',
                  Icons.inventory,
                  Colors.teal,
                  _updateStock,
                ),
                _buildActionButton(
                  'البحث في المنتجات',
                  Icons.search,
                  Colors.indigo,
                  _searchProducts,
                ),
                _buildActionButton(
                  'مسح المخرجات',
                  Icons.clear,
                  Colors.red,
                  _clearOutput,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // منطقة المخرجات
            Expanded(
              child: Card(
                child: Container(
                  padding: const EdgeInsets.all(16.0),
                  width: double.infinity,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.terminal),
                          const SizedBox(width: 8),
                          Text(
                            'مخرجات النظام:',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const Spacer(),
                          if (_isLoading)
                            const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                      const Divider(),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _output.isEmpty ? 'لا توجد مخرجات بعد...' : _output,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: _isLoading ? null : onPressed,
      icon: Icon(icon),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  Future<void> _runOperation(
      String operationName, Future<void> Function() operation) async {
    setState(() {
      _isLoading = true;
      _output += '\n🚀 بدء تشغيل: $operationName\n';
      _output += '=' * 50 + '\n';
    });

    try {
      await operation();
      setState(() {
        _output += '\n✅ تم تشغيل $operationName بنجاح!\n';
        _output += '=' * 50 + '\n';
      });
    } catch (e) {
      setState(() {
        _output += '\n❌ خطأ في $operationName: $e\n';
        _output += '=' * 50 + '\n';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _runCompleteExample() async {
    await _runOperation('المثال الشامل', () async {
      await _example.runCompleteExample();
    });
  }

  Future<void> _addProducts() async {
    await _runOperation('إضافة المنتجات', () async {
      setState(() {
        _output += 'إضافة منتجات تجريبية...\n';
      });
      // هنا يمكن إضافة كود إضافة المنتجات
    });
  }

  Future<void> _createPurchase() async {
    await _runOperation('عملية الشراء', () async {
      setState(() {
        _output += 'إنشاء عملية شراء تجريبية...\n';
      });
      // هنا يمكن إضافة كود عملية الشراء
    });
  }

  Future<void> _createSale() async {
    await _runOperation('عملية البيع', () async {
      setState(() {
        _output += 'إنشاء عملية بيع تجريبية...\n';
      });
      // هنا يمكن إضافة كود عملية البيع
    });
  }

  Future<void> _updateStock() async {
    await _runOperation('تحديث المخزون', () async {
      await _example.updateStockExample();
    });
  }

  Future<void> _searchProducts() async {
    await _runOperation('البحث في المنتجات', () async {
      await _example.searchProductsExample();
    });
  }

  void _clearOutput() {
    setState(() {
      _output = '';
    });
  }
}
