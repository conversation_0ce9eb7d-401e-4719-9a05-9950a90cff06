import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../widgets/main_layout.dart';

class ProfitLossReportPage extends ConsumerStatefulWidget {
  const ProfitLossReportPage({super.key});

  @override
  ConsumerState<ProfitLossReportPage> createState() =>
      _ProfitLossReportPageState();
}

class _ProfitLossReportPageState extends ConsumerState<ProfitLossReportPage> {
  DateTime _fromDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _toDate = DateTime.now();
  String _selectedPeriod = 'شهري';
  String _selectedBranch = 'جميع الفروع';

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'تقرير الأرباح والخسائر',
      actions: [
        IconButton(
          icon: const Icon(Icons.print),
          onPressed: _printReport,
        ),
        IconButton(
          icon: const Icon(Icons.share),
          onPressed: _shareReport,
        ),
      ],
      child: Column(
        children: [
          // Filter Section
          _buildFilterSection(),

          // Report Content
          Expanded(
            child: _buildReportContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection() {
    return Container(
      padding: EdgeInsets.all(16.r),
      color: Colors.grey[50],
      child: Column(
        children: [
          Row(
            children: [
              // Period Selection
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedPeriod,
                  decoration: const InputDecoration(
                    labelText: 'الفترة',
                    prefixIcon: Icon(Icons.date_range),
                  ),
                  items: ['يومي', 'أسبوعي', 'شهري', 'ربع سنوي', 'سنوي', 'مخصص']
                      .map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    setState(() {
                      _selectedPeriod = newValue!;
                      _updateDateRange();
                    });
                  },
                ),
              ),
              SizedBox(width: 16.w),

              // Branch Selection
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedBranch,
                  decoration: const InputDecoration(
                    labelText: 'الفرع',
                    prefixIcon: Icon(Icons.location_on),
                  ),
                  items: [
                    'جميع الفروع',
                    'الفرع الرئيسي',
                    'الفرع الثاني',
                    'الفرع الثالث'
                  ].map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    setState(() {
                      _selectedBranch = newValue!;
                    });
                  },
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Date Range
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () => _selectDate(true),
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'من تاريخ',
                      prefixIcon: Icon(Icons.calendar_today),
                    ),
                    child: Text(
                      '${_fromDate.day}/${_fromDate.month}/${_fromDate.year}',
                    ),
                  ),
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: InkWell(
                  onTap: () => _selectDate(false),
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'إلى تاريخ',
                      prefixIcon: Icon(Icons.calendar_today),
                    ),
                    child: Text(
                      '${_toDate.day}/${_toDate.month}/${_toDate.year}',
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildReportContent() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Report Header
          _buildReportHeader(),
          SizedBox(height: 24.h),

          // Revenue Section
          _buildRevenueSection(),
          SizedBox(height: 16.h),

          // Cost of Goods Sold Section
          _buildCostOfGoodsSoldSection(),
          SizedBox(height: 16.h),

          // Gross Profit
          _buildGrossProfitSection(),
          SizedBox(height: 16.h),

          // Operating Expenses Section
          _buildOperatingExpensesSection(),
          SizedBox(height: 16.h),

          // Net Profit Section
          _buildNetProfitSection(),
          SizedBox(height: 24.h),

          // Summary Charts
          _buildSummaryCharts(),
        ],
      ),
    );
  }

  Widget _buildReportHeader() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          children: [
            Text(
              'تقرير الأرباح والخسائر',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'من ${_fromDate.day}/${_fromDate.month}/${_fromDate.year} إلى ${_toDate.day}/${_toDate.month}/${_toDate.year}',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[600],
              ),
            ),
            Text(
              _selectedBranch,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRevenueSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الإيرادات',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.success,
              ),
            ),
            SizedBox(height: 16.h),
            _buildReportRow('مبيعات نقدية', '125,750.00', false),
            _buildReportRow('مبيعات آجلة', '89,200.00', false),
            _buildReportRow('إيرادات أخرى', '5,500.00', false),
            const Divider(),
            _buildReportRow(
                'إجمالي الإيرادات', '220,450.00', true, AppColors.success),
          ],
        ),
      ),
    );
  }

  Widget _buildCostOfGoodsSoldSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تكلفة البضاعة المباعة',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.error,
              ),
            ),
            SizedBox(height: 16.h),
            _buildReportRow('مخزون أول المدة', '45,000.00', false),
            _buildReportRow('المشتريات', '95,200.00', false),
            _buildReportRow('مخزون آخر المدة', '(38,500.00)', false),
            const Divider(),
            _buildReportRow(
                'تكلفة البضاعة المباعة', '101,700.00', true, AppColors.error),
          ],
        ),
      ),
    );
  }

  Widget _buildGrossProfitSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildReportRow('إجمالي الإيرادات', '220,450.00', false),
            _buildReportRow('تكلفة البضاعة المباعة', '(101,700.00)', false),
            const Divider(thickness: 2),
            _buildReportRow(
                'إجمالي الربح', '118,750.00', true, AppColors.primary),
            SizedBox(height: 8.h),
            Text(
              'هامش الربح الإجمالي: 53.9%',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOperatingExpensesSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المصروفات التشغيلية',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.warning,
              ),
            ),
            SizedBox(height: 16.h),
            _buildReportRow('رواتب الموظفين', '35,000.00', false),
            _buildReportRow('إيجار المحل', '12,000.00', false),
            _buildReportRow('فواتير الكهرباء والماء', '2,500.00', false),
            _buildReportRow('مصروفات التسويق', '8,200.00', false),
            _buildReportRow('مصروفات إدارية', '4,800.00', false),
            _buildReportRow('مصروفات أخرى', '3,200.00', false),
            const Divider(),
            _buildReportRow('إجمالي المصروفات التشغيلية', '65,700.00', true,
                AppColors.warning),
          ],
        ),
      ),
    );
  }

  Widget _buildNetProfitSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'صافي الربح',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            _buildReportRow('إجمالي الربح', '118,750.00', false),
            _buildReportRow('المصروفات التشغيلية', '(65,700.00)', false),
            const Divider(thickness: 2),
            _buildReportRow('صافي الربح', '53,050.00', true, AppColors.success),
            SizedBox(height: 8.h),
            Text(
              'هامش الربح الصافي: 24.1%',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.success,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCharts() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص مرئي',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),

            // Revenue vs Expenses Chart Placeholder
            Container(
              height: 200.h,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.bar_chart,
                      size: 48.r,
                      color: Colors.grey[400],
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'مخطط الإيرادات مقابل المصروفات',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportRow(String label, String amount, bool isBold,
      [Color? color]) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              color: color,
            ),
          ),
          Text(
            '$amount ر.س',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  void _selectDate(bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isFromDate ? _fromDate : _toDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        if (isFromDate) {
          _fromDate = picked;
        } else {
          _toDate = picked;
        }
      });
    }
  }

  void _updateDateRange() {
    final now = DateTime.now();
    setState(() {
      switch (_selectedPeriod) {
        case 'يومي':
          _fromDate = now;
          _toDate = now;
          break;
        case 'أسبوعي':
          _fromDate = now.subtract(const Duration(days: 7));
          _toDate = now;
          break;
        case 'شهري':
          _fromDate = DateTime(now.year, now.month, 1);
          _toDate = now;
          break;
        case 'ربع سنوي':
          _fromDate = now.subtract(const Duration(days: 90));
          _toDate = now;
          break;
        case 'سنوي':
          _fromDate = DateTime(now.year, 1, 1);
          _toDate = now;
          break;
      }
    });
  }

  void _printReport() {
    // TODO: Implement print functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري طباعة التقرير...')),
    );
  }

  void _shareReport() {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري مشاركة التقرير...')),
    );
  }
}
