import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/stock_movement.dart';
import '../data/repositories/stock_movement_repository.dart';
import '../core/utils/app_utils.dart';
import '../core/utils/error_handler.dart';
import 'database_provider.dart';

// Stock movement repository provider
final stockMovementRepositoryProvider = Provider<StockMovementRepository>((ref) {
  return StockMovementRepository();
});

// Stock movement management provider
final stockMovementManagementProvider = StateNotifierProvider<
    StockMovementManagementNotifier, StockMovementManagementState>((ref) {
  return StockMovementManagementNotifier(ref);
});

// Stock movement management state
class StockMovementManagementState {
  final bool isLoading;
  final String? error;
  final List<StockMovement> movements;
  final List<StockMovement> filteredMovements;
  final String searchQuery;
  final String? selectedWarehouse;
  final String? selectedProduct;
  final String? selectedMovementType;
  final String? selectedReferenceType;
  final DateTime? fromDate;
  final DateTime? toDate;
  final StockMovement? selectedMovement;
  final Map<String, dynamic> statistics;
  final String sortField;
  final bool sortAscending;

  const StockMovementManagementState({
    this.isLoading = false,
    this.error,
    this.movements = const [],
    this.filteredMovements = const [],
    this.searchQuery = '',
    this.selectedWarehouse,
    this.selectedProduct,
    this.selectedMovementType,
    this.selectedReferenceType,
    this.fromDate,
    this.toDate,
    this.selectedMovement,
    this.statistics = const {},
    this.sortField = 'created_at',
    this.sortAscending = false,
  });

  StockMovementManagementState copyWith({
    bool? isLoading,
    String? error,
    List<StockMovement>? movements,
    List<StockMovement>? filteredMovements,
    String? searchQuery,
    String? selectedWarehouse,
    String? selectedProduct,
    String? selectedMovementType,
    String? selectedReferenceType,
    DateTime? fromDate,
    DateTime? toDate,
    StockMovement? selectedMovement,
    Map<String, dynamic>? statistics,
    String? sortField,
    bool? sortAscending,
  }) {
    return StockMovementManagementState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      movements: movements ?? this.movements,
      filteredMovements: filteredMovements ?? this.filteredMovements,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedWarehouse: selectedWarehouse ?? this.selectedWarehouse,
      selectedProduct: selectedProduct ?? this.selectedProduct,
      selectedMovementType: selectedMovementType ?? this.selectedMovementType,
      selectedReferenceType: selectedReferenceType ?? this.selectedReferenceType,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      selectedMovement: selectedMovement ?? this.selectedMovement,
      statistics: statistics ?? this.statistics,
      sortField: sortField ?? this.sortField,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }
}

// Stock movement management notifier
class StockMovementManagementNotifier extends StateNotifier<StockMovementManagementState> {
  final Ref _ref;

  StockMovementManagementNotifier(this._ref) : super(const StockMovementManagementState());

  StockMovementRepository get _repository => _ref.read(stockMovementRepositoryProvider);
  DatabaseOperations get _dbOperations => _ref.read(databaseOperationsProvider);

  // Load movements
  Future<void> loadMovements() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final movements = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getAllMovements(
          fromDate: state.fromDate,
          toDate: state.toDate,
          limit: 1000, // Limit for performance
        );
      });

      final statistics = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getMovementStatistics(
          fromDate: state.fromDate,
          toDate: state.toDate,
        );
      });

      state = state.copyWith(
        isLoading: false,
        movements: movements,
        filteredMovements: _filterAndSortMovements(movements),
        statistics: statistics,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error loading stock movements', e);
    }
  }

  // Filter and sort movements based on current state
  List<StockMovement> _filterAndSortMovements(List<StockMovement> movements) {
    var filtered = movements.where((movement) {
      // Search filter
      if (state.searchQuery.isNotEmpty) {
        final query = state.searchQuery.toLowerCase();
        if (!(movement.productName?.toLowerCase().contains(query) ?? false) &&
            !(movement.productBarcode?.toLowerCase().contains(query) ?? false) &&
            !(movement.warehouseName?.toLowerCase().contains(query) ?? false) &&
            !(movement.referenceNumber?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // Warehouse filter
      if (state.selectedWarehouse != null && 
          movement.warehouseId != state.selectedWarehouse) {
        return false;
      }

      // Product filter
      if (state.selectedProduct != null && 
          movement.productId != state.selectedProduct) {
        return false;
      }

      // Movement type filter
      if (state.selectedMovementType != null && 
          movement.movementType != state.selectedMovementType) {
        return false;
      }

      // Reference type filter
      if (state.selectedReferenceType != null && 
          movement.referenceType != state.selectedReferenceType) {
        return false;
      }

      return true;
    }).toList();

    // Sort
    filtered.sort((a, b) {
      int comparison = 0;
      switch (state.sortField) {
        case 'created_at':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case 'product_name':
          comparison = (a.productName ?? '').compareTo(b.productName ?? '');
          break;
        case 'warehouse_name':
          comparison = (a.warehouseName ?? '').compareTo(b.warehouseName ?? '');
          break;
        case 'movement_type':
          comparison = a.movementType.compareTo(b.movementType);
          break;
        case 'quantity':
          comparison = a.quantity.compareTo(b.quantity);
          break;
        case 'total_value':
          comparison = a.totalValue.compareTo(b.totalValue);
          break;
        default:
          comparison = a.createdAt.compareTo(b.createdAt);
      }
      return state.sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  // Refresh data
  Future<void> refresh() async {
    await loadMovements();
  }

  // Search movements
  void searchMovements(String query) {
    state = state.copyWith(
      searchQuery: query,
      filteredMovements: _filterAndSortMovements(state.movements),
    );
  }

  // Filter by warehouse
  void filterByWarehouse(String? warehouseId) {
    state = state.copyWith(
      selectedWarehouse: warehouseId,
      filteredMovements: _filterAndSortMovements(state.movements),
    );
  }

  // Filter by product
  void filterByProduct(String? productId) {
    state = state.copyWith(
      selectedProduct: productId,
      filteredMovements: _filterAndSortMovements(state.movements),
    );
  }

  // Filter by movement type
  void filterByMovementType(String? movementType) {
    state = state.copyWith(
      selectedMovementType: movementType,
      filteredMovements: _filterAndSortMovements(state.movements),
    );
  }

  // Filter by reference type
  void filterByReferenceType(String? referenceType) {
    state = state.copyWith(
      selectedReferenceType: referenceType,
      filteredMovements: _filterAndSortMovements(state.movements),
    );
  }

  // Filter by date range
  void filterByDateRange(DateTime? fromDate, DateTime? toDate) {
    state = state.copyWith(
      fromDate: fromDate,
      toDate: toDate,
    );
    loadMovements(); // Reload with new date range
  }

  // Sort movements
  void sortMovements(String field, bool ascending) {
    state = state.copyWith(
      sortField: field,
      sortAscending: ascending,
      filteredMovements: _filterAndSortMovements(state.movements),
    );
  }

  // Clear filters
  void clearFilters() {
    state = state.copyWith(
      searchQuery: '',
      selectedWarehouse: null,
      selectedProduct: null,
      selectedMovementType: null,
      selectedReferenceType: null,
      fromDate: null,
      toDate: null,
      filteredMovements: _filterAndSortMovements(state.movements),
    );
    loadMovements(); // Reload without date filters
  }

  // Select movement
  void selectMovement(StockMovement? movement) {
    state = state.copyWith(selectedMovement: movement);
  }

  // Load today's movements
  Future<void> loadTodayMovements() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final movements = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getTodayMovements();
      });

      state = state.copyWith(
        isLoading: false,
        movements: movements,
        filteredMovements: _filterAndSortMovements(movements),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error loading today movements', e);
    }
  }

  // Load weekly movements
  Future<void> loadWeeklyMovements() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final movements = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getWeeklyMovements();
      });

      state = state.copyWith(
        isLoading: false,
        movements: movements,
        filteredMovements: _filterAndSortMovements(movements),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error loading weekly movements', e);
    }
  }

  // Load monthly movements
  Future<void> loadMonthlyMovements() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final movements = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getMonthlyMovements();
      });

      state = state.copyWith(
        isLoading: false,
        movements: movements,
        filteredMovements: _filterAndSortMovements(movements),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error loading monthly movements', e);
    }
  }
}

// Additional providers
final stockMovementByIdProvider = FutureProvider.family<StockMovement?, String>((ref, id) async {
  final repository = ref.read(stockMovementRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getMovementById(id);
  });
});

final todayMovementsProvider = FutureProvider<List<StockMovement>>((ref) async {
  final repository = ref.read(stockMovementRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getTodayMovements();
  });
});

final weeklyMovementsProvider = FutureProvider<List<StockMovement>>((ref) async {
  final repository = ref.read(stockMovementRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getWeeklyMovements();
  });
});

final monthlyMovementsProvider = FutureProvider<List<StockMovement>>((ref) async {
  final repository = ref.read(stockMovementRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getMonthlyMovements();
  });
});

final movementStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.read(stockMovementRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getMovementStatistics();
  });
});

final mostActiveProductsProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  final repository = ref.read(stockMovementRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getMostActiveProducts();
  });
});
