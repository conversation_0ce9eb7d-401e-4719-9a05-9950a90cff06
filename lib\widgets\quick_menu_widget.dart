import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../core/theme/colors.dart';
import '../core/theme/text_styles.dart';
import '../router/routes.dart';

class QuickMenuWidget extends StatelessWidget {
  const QuickMenuWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final menuItems = [
      {
        'title': 'بيع وشراء العملات',
        'icon': Icons.currency_exchange,
        'color': const Color(0xFFFFB800),
        'route': AppRoutes.pos,
      },
      {
        'title': 'كشف حساب',
        'icon': Icons.description,
        'color': const Color(0xFF1976D2),
        'route': AppRoutes.reports,
      },
      {
        'title': 'كشف حساب',
        'icon': Icons.description,
        'color': const Color(0xFF1976D2),
        'route': AppRoutes.reports,
      },
      {
        'title': 'كشف حساب',
        'icon': Icons.description,
        'color': const Color(0xFF1976D2),
        'route': AppRoutes.reports,
      },
      {
        'title': 'كشف حساب',
        'icon': Icons.description,
        'color': const Color(0xFF1976D2),
        'route': AppRoutes.reports,
      },
      {
        'title': 'كشف حساب',
        'icon': Icons.description,
        'color': const Color(0xFF1976D2),
        'route': AppRoutes.reports,
      },
      {
        'title': 'تقرير حركة المخزون',
        'icon': Icons.inventory,
        'color': const Color(0xFFFFB800),
        'route': AppRoutes.inventory,
      },
      {
        'title': 'تقرير المشتريات',
        'icon': Icons.shopping_cart,
        'color': const Color(0xFF1976D2),
        'route': AppRoutes.purchases,
      },
      {
        'title': 'سند تحصيل/قبض',
        'icon': Icons.receipt,
        'color': const Color(0xFFFFB800),
        'route': AppRoutes.sales,
      },
      {
        'title': 'سند قيد جماعي',
        'icon': Icons.group,
        'color': const Color(0xFF1976D2),
        'route': AppRoutes.customers,
      },
      {
        'title': 'سند صرف',
        'icon': Icons.payment,
        'color': const Color(0xFFFFB800),
        'route': AppRoutes.suppliers,
      },
      {
        'title': 'قيد مركب/جماعي',
        'icon': Icons.add_circle,
        'color': const Color(0xFF1976D2),
        'route': AppRoutes.productAdd,
      },
      {
        'title': 'قيد يومي/بسيط',
        'icon': Icons.today,
        'color': const Color(0xFFFFB800),
        'route': AppRoutes.dashboard,
      },
      {
        'title': 'سند قيد سريع/متعدد',
        'icon': Icons.speed,
        'color': const Color(0xFF1976D2),
        'route': AppRoutes.settings,
      },
      {
        'title': 'ميزان المراجعة',
        'icon': Icons.balance,
        'color': const Color(0xFFFFB800),
        'route': AppRoutes.reports,
      },
      {
        'title': 'قوائم الدخل',
        'icon': Icons.trending_up,
        'color': const Color(0xFF1976D2),
        'route': AppRoutes.reports,
      },
      {
        'title': 'إضافة صنف',
        'icon': Icons.add_box,
        'color': const Color(0xFFFFB800),
        'route': AppRoutes.categoryAdd,
      },
      {
        'title': 'فاتورة مبيعات',
        'icon': Icons.receipt_long,
        'color': const Color(0xFF1976D2),
        'route': AppRoutes.saleAdd,
      },
      {
        'title': 'عرض بسعر',
        'icon': Icons.local_offer,
        'color': const Color(0xFFFFB800),
        'route': AppRoutes.products,
      },
      {
        'title': 'فاتورة مشتريات',
        'icon': Icons.shopping_bag,
        'color': const Color(0xFF1976D2),
        'route': AppRoutes.purchaseAdd,
      },
      {
        'title': 'تركيب الاصناف',
        'icon': Icons.build,
        'color': const Color(0xFF1976D2),
        'route': AppRoutes.products,
      },
      {
        'title': 'امر انتاج',
        'icon': Icons.precision_manufacturing,
        'color': const Color(0xFFFFB800),
        'route': AppRoutes.inventory,
      },
      {
        'title': 'مردود مبيعات',
        'icon': Icons.undo,
        'color': const Color(0xFF1976D2),
        'route': AppRoutes.sales,
      },
      {
        'title': 'مردود مشتريات',
        'icon': Icons.undo,
        'color': const Color(0xFFFFB800),
        'route': AppRoutes.purchases,
      },
      {
        'title': 'امر تحويل مخزني',
        'icon': Icons.transform,
        'color': const Color(0xFF1976D2),
        'route': AppRoutes.inventory,
      },
      {
        'title': 'استلام مخزني',
        'icon': Icons.inventory_2,
        'color': const Color(0xFFFFB800),
        'route': AppRoutes.inventory,
      },
    ];

    return SingleChildScrollView(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(10.r),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary,
                    AppColors.primary.withOpacity(0.8),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
              ),
              child: Row(
                children: [
                  // Icon(Icons.people_outline, color: Colors.white, size: 20.r),
                  // SizedBox(width: 10.w),
                  Expanded(
                    child: Text(
                      "القائمة السريعه",
                      style: AppTextStyles.titleMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 18.sp,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),

            // Grid Menu
            Padding(
              padding: EdgeInsets.all(16.r),
              child: GridView.count(
                crossAxisCount: 2,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisSpacing: 12.w,
                mainAxisSpacing: 12.h,
                childAspectRatio: 3.8,
                children: menuItems.map((item) {
                  return _buildMenuItem(
                    context,
                    title: item['title'] as String,
                    icon: item['icon'] as IconData,
                    color: item['color'] as Color,
                    onTap: () => context.go(item['route'] as String),
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 2.h),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Icon(icon, color: Colors.white, size: 20.r),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(
                title,
                softWrap: true,
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
                // maxLines: 1,
                overflow: TextOverflow.visible,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
