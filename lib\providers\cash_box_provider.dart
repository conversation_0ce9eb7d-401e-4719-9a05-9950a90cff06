import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/cash_box.dart';
import '../data/repositories/cash_box_repository.dart';
import '../core/utils/app_utils.dart';
import 'database_provider.dart';

// Cash Box Repository provider
final cashBoxRepositoryProvider = Provider<CashBoxRepository>((ref) {
  return CashBoxRepository();
});

// All cash boxes provider
final cashBoxesProvider = StateNotifierProvider<CashBoxesNotifier, AsyncValue<List<CashBox>>>((ref) {
  return CashBoxesNotifier(ref.read(cashBoxRepositoryProvider), ref);
});

// Active cash boxes provider
final activeCashBoxesProvider = FutureProvider<List<CashBox>>((ref) async {
  final repository = ref.read(cashBoxRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);
  
  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getActiveCashBoxes();
  });
});

// Default cash box provider
final defaultCashBoxProvider = FutureProvider<CashBox?>((ref) async {
  final repository = ref.read(cashBoxRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);
  
  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getDefaultCashBox();
  });
});

// Cash box by ID provider
final cashBoxByIdProvider = FutureProvider.family<CashBox?, String>((ref, id) async {
  final repository = ref.read(cashBoxRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);
  
  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getCashBoxById(id);
  });
});

// Cash box statistics provider
final cashBoxStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.read(cashBoxRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);
  
  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getCashBoxStatistics();
  });
});

// Total active balance provider
final totalActiveBalanceProvider = FutureProvider<double>((ref) async {
  final repository = ref.read(cashBoxRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);
  
  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getTotalActiveBalance();
  });
});

// Search cash boxes provider
final searchCashBoxesProvider = StateNotifierProvider<SearchCashBoxesNotifier, AsyncValue<List<CashBox>>>((ref) {
  return SearchCashBoxesNotifier(ref.read(cashBoxRepositoryProvider), ref);
});

// Cash boxes notifier
class CashBoxesNotifier extends StateNotifier<AsyncValue<List<CashBox>>> {
  final CashBoxRepository _repository;
  final Ref _ref;

  CashBoxesNotifier(this._repository, this._ref) : super(const AsyncValue.loading()) {
    loadCashBoxes();
  }

  // Load all cash boxes
  Future<void> loadCashBoxes() async {
    try {
      state = const AsyncValue.loading();
      final dbOperations = _ref.read(databaseOperationsProvider);
      
      final cashBoxes = await dbOperations.executeWithReadyCheck(() async {
        return await _repository.getAllCashBoxes();
      });
      
      state = AsyncValue.data(cashBoxes);
    } catch (e, stackTrace) {
      AppUtils.logError('Error loading cash boxes', e);
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Create new cash box
  Future<bool> createCashBox(CashBox cashBox) async {
    try {
      final dbOperations = _ref.read(databaseOperationsProvider);
      
      await dbOperations.executeWithReadyCheck(() async {
        await _repository.createCashBox(cashBox);
      });
      
      await loadCashBoxes(); // Refresh the list
      _ref.invalidate(activeCashBoxesProvider);
      _ref.invalidate(defaultCashBoxProvider);
      _ref.invalidate(cashBoxStatisticsProvider);
      _ref.invalidate(totalActiveBalanceProvider);
      
      return true;
    } catch (e) {
      AppUtils.logError('Error creating cash box', e);
      return false;
    }
  }

  // Update cash box
  Future<bool> updateCashBox(CashBox cashBox) async {
    try {
      final dbOperations = _ref.read(databaseOperationsProvider);
      
      await dbOperations.executeWithReadyCheck(() async {
        await _repository.updateCashBox(cashBox);
      });
      
      await loadCashBoxes(); // Refresh the list
      _ref.invalidate(activeCashBoxesProvider);
      _ref.invalidate(cashBoxByIdProvider(cashBox.id));
      _ref.invalidate(cashBoxStatisticsProvider);
      
      return true;
    } catch (e) {
      AppUtils.logError('Error updating cash box', e);
      return false;
    }
  }

  // Delete cash box
  Future<bool> deleteCashBox(String cashBoxId) async {
    try {
      final dbOperations = _ref.read(databaseOperationsProvider);
      
      await dbOperations.executeWithReadyCheck(() async {
        await _repository.deleteCashBox(cashBoxId);
      });
      
      await loadCashBoxes(); // Refresh the list
      _ref.invalidate(activeCashBoxesProvider);
      _ref.invalidate(cashBoxByIdProvider(cashBoxId));
      _ref.invalidate(cashBoxStatisticsProvider);
      _ref.invalidate(totalActiveBalanceProvider);
      
      return true;
    } catch (e) {
      AppUtils.logError('Error deleting cash box', e);
      return false;
    }
  }

  // Restore cash box
  Future<bool> restoreCashBox(String cashBoxId) async {
    try {
      final dbOperations = _ref.read(databaseOperationsProvider);
      
      await dbOperations.executeWithReadyCheck(() async {
        await _repository.restoreCashBox(cashBoxId);
      });
      
      await loadCashBoxes(); // Refresh the list
      _ref.invalidate(activeCashBoxesProvider);
      _ref.invalidate(cashBoxByIdProvider(cashBoxId));
      _ref.invalidate(cashBoxStatisticsProvider);
      _ref.invalidate(totalActiveBalanceProvider);
      
      return true;
    } catch (e) {
      AppUtils.logError('Error restoring cash box', e);
      return false;
    }
  }

  // Toggle active status
  Future<bool> toggleActive(String cashBoxId, bool isActive) async {
    try {
      final dbOperations = _ref.read(databaseOperationsProvider);
      
      await dbOperations.executeWithReadyCheck(() async {
        await _repository.toggleActive(cashBoxId, isActive);
      });
      
      await loadCashBoxes(); // Refresh the list
      _ref.invalidate(activeCashBoxesProvider);
      _ref.invalidate(cashBoxByIdProvider(cashBoxId));
      _ref.invalidate(cashBoxStatisticsProvider);
      _ref.invalidate(totalActiveBalanceProvider);
      
      return true;
    } catch (e) {
      AppUtils.logError('Error toggling cash box active status', e);
      return false;
    }
  }

  // Update balance
  Future<bool> updateBalance(String cashBoxId, double newBalance) async {
    try {
      final dbOperations = _ref.read(databaseOperationsProvider);
      
      await dbOperations.executeWithReadyCheck(() async {
        await _repository.updateBalance(cashBoxId, newBalance);
      });
      
      await loadCashBoxes(); // Refresh the list
      _ref.invalidate(activeCashBoxesProvider);
      _ref.invalidate(cashBoxByIdProvider(cashBoxId));
      _ref.invalidate(totalActiveBalanceProvider);
      
      return true;
    } catch (e) {
      AppUtils.logError('Error updating cash box balance', e);
      return false;
    }
  }

  // Add to balance
  Future<bool> addToBalance(String cashBoxId, double amount) async {
    try {
      final dbOperations = _ref.read(databaseOperationsProvider);
      
      await dbOperations.executeWithReadyCheck(() async {
        await _repository.addToBalance(cashBoxId, amount);
      });
      
      await loadCashBoxes(); // Refresh the list
      _ref.invalidate(activeCashBoxesProvider);
      _ref.invalidate(cashBoxByIdProvider(cashBoxId));
      _ref.invalidate(totalActiveBalanceProvider);
      
      return true;
    } catch (e) {
      AppUtils.logError('Error adding to cash box balance', e);
      return false;
    }
  }

  // Subtract from balance
  Future<bool> subtractFromBalance(String cashBoxId, double amount) async {
    try {
      final dbOperations = _ref.read(databaseOperationsProvider);
      
      await dbOperations.executeWithReadyCheck(() async {
        await _repository.subtractFromBalance(cashBoxId, amount);
      });
      
      await loadCashBoxes(); // Refresh the list
      _ref.invalidate(activeCashBoxesProvider);
      _ref.invalidate(cashBoxByIdProvider(cashBoxId));
      _ref.invalidate(totalActiveBalanceProvider);
      
      return true;
    } catch (e) {
      AppUtils.logError('Error subtracting from cash box balance', e);
      return false;
    }
  }
}

// Search cash boxes notifier
class SearchCashBoxesNotifier extends StateNotifier<AsyncValue<List<CashBox>>> {
  final CashBoxRepository _repository;
  final Ref _ref;

  SearchCashBoxesNotifier(this._repository, this._ref) : super(const AsyncValue.data([]));

  // Search cash boxes
  Future<void> searchCashBoxes(String query) async {
    try {
      state = const AsyncValue.loading();
      final dbOperations = _ref.read(databaseOperationsProvider);
      
      final cashBoxes = await dbOperations.executeWithReadyCheck(() async {
        return await _repository.searchCashBoxes(query);
      });
      
      state = AsyncValue.data(cashBoxes);
    } catch (e, stackTrace) {
      AppUtils.logError('Error searching cash boxes', e);
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Clear search
  void clearSearch() {
    state = const AsyncValue.data([]);
  }
}

// Selected cash box provider (for forms and operations)
final selectedCashBoxProvider = StateNotifierProvider<SelectedCashBoxNotifier, CashBox?>((ref) {
  return SelectedCashBoxNotifier(ref);
});

// Selected cash box notifier
class SelectedCashBoxNotifier extends StateNotifier<CashBox?> {
  final Ref _ref;

  SelectedCashBoxNotifier(this._ref) : super(null) {
    _loadDefaultCashBox();
  }

  // Load default cash box
  Future<void> _loadDefaultCashBox() async {
    try {
      final defaultCashBox = await _ref.read(defaultCashBoxProvider.future);
      state = defaultCashBox;
    } catch (e) {
      AppUtils.logError('Error loading default cash box', e);
    }
  }

  // Select cash box
  void selectCashBox(CashBox? cashBox) {
    state = cashBox;
  }

  // Clear selection
  void clearSelection() {
    state = null;
  }
}
