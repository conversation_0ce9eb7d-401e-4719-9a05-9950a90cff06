# دليل عمليات إدارة المنتجات والمبيعات

## نظرة عامة

تم إصلاح وتحسين الأخطاء في الملفات التالية:
- `SaleDao` - إدارة قاعدة بيانات المبيعات
- `SaleManagementState` - إدارة حالة المبيعات
- `BusinessService` - خدمات العمليات التجارية

## الملفات المصححة

### 1. BusinessService (`lib/services/business_service.dart`)

#### العمليات المتاحة:

**إضافة منتج جديد:**
```dart
final productId = await businessService.addNewProduct(
  nameAr: 'اسم المنتج',
  nameEn: 'Product Name',
  costPrice: 100.0,
  sellingPrice: 150.0,
  categoryId: 'category-id',
  unitId: 'unit-id',
  minStock: 10,
  maxStock: 100,
  reorderPoint: 20,
);
```

**تعديل منتج موجود:**
```dart
final success = await businessService.updateProduct(
  productId: 'product-id',
  nameAr: 'اسم جديد',
  sellingPrice: 200.0,
  minStock: 15,
);
```

**حذف منتج:**
```dart
final success = await businessService.deleteProduct('product-id');
```

**إلغاء/تفعيل منتج:**
```dart
await businessService.deactivateProduct('product-id');
await businessService.activateProduct('product-id');
```

**إنشاء عملية بيع:**
```dart
final saleId = await businessService.createSaleTransaction(
  customerId: 'customer-id',
  items: [
    {
      'productId': 'product-id',
      'qty': 2.0,
      'unitPrice': 150.0,
      'unitId': 'unit-id',
    }
  ],
  paidAmount: 300.0,
  notes: 'ملاحظات البيع',
);
```

### 2. SaleManagementState (`lib/providers/sale_provider.dart`)

#### الميزات المحسنة:

**تحميل المبيعات من قاعدة البيانات:**
```dart
// يتم تحميل المبيعات تلقائياً من SaleRepository
final saleProvider = ref.watch(saleManagementProvider);
final sales = saleProvider.sales;
```

**إنشاء بيع جديد:**
```dart
final saleNotifier = ref.read(saleManagementProvider.notifier);
final saleId = await saleNotifier.createSale(
  customerId: 'customer-id',
  items: saleItems,
  paid: 500.0,
  notes: 'ملاحظات',
);
```

**حذف بيع:**
```dart
final success = await saleNotifier.deleteSale('sale-id');
```

### 3. SaleRepository (`lib/data/repositories/sale_repository.dart`)

#### طرق جديدة مضافة:

**حذف بيع:**
```dart
final success = await saleRepository.deleteSale('sale-id');
```

## الأمثلة العملية

### 1. مثال عمليات المنتجات (`lib/examples/product_operations_example.dart`)

```dart
import 'package:tijari_tech/examples/product_operations_example.dart';

// تشغيل جميع أمثلة المنتجات
await runProductOperationsExamples();
```

**الأمثلة المتضمنة:**
- إضافة منتج جديد
- تعديل منتج موجود
- إلغاء تفعيل منتج
- حذف منتج
- إضافة دفعة من المنتجات

### 2. مثال عمليات المبيعات (`lib/examples/sale_operations_example.dart`)

```dart
import 'package:tijari_tech/examples/sale_operations_example.dart';

// تشغيل جميع أمثلة المبيعات
await runSaleOperationsExamples();
```

**الأمثلة المتضمنة:**
- إنشاء عملية بيع كاملة
- استخدام SaleManagementNotifier
- تحديث دفعة في البيع
- إلغاء بيع
- إحصاءات المبيعات

## الإصلاحات المطبقة

### 1. إصلاح BusinessService

- ✅ إصلاح توقيع `Product.create()`
- ✅ إصلاح توقيع `Sale.create()`
- ✅ إصلاح توقيع `SaleItem.create()`
- ✅ إصلاح استدعاء `ProductRepository.updateProduct()`
- ✅ إضافة التحقق من صحة البيانات
- ✅ إضافة عمليات تعديل وحذف المنتجات
- ✅ تحسين معالجة الأخطاء

### 2. إصلاح SaleManagementState

- ✅ ربط SaleRepository بـ Provider
- ✅ إصلاح تحميل المبيعات من قاعدة البيانات
- ✅ إصلاح إنشاء المبيعات
- ✅ إضافة عملية حذف المبيعات

### 3. إصلاح SaleDao

- ✅ لا توجد أخطاء مكتشفة
- ✅ يعمل بشكل صحيح

### 4. إصلاح Sale.create()

- ✅ إزالة المعاملات المكررة
- ✅ توحيد توقيع الطريقة

## كيفية الاستخدام

### 1. في التطبيق الأساسي

```dart
// إنشاء خدمة العمليات التجارية
final businessService = BusinessService();

// إضافة منتج
final productId = await businessService.addNewProduct(
  nameAr: 'لابتوب',
  costPrice: 800.0,
  sellingPrice: 1200.0,
);

// إنشاء بيع
final saleId = await businessService.createSaleTransaction(
  items: [
    {
      'productId': productId,
      'qty': 1.0,
      'unitPrice': 1200.0,
    }
  ],
  paidAmount: 1200.0,
);
```

### 2. مع Flutter Provider

```dart
class ProductManagementPage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final saleState = ref.watch(saleManagementProvider);
    
    return Scaffold(
      body: Column(
        children: [
          if (saleState.isLoading) CircularProgressIndicator(),
          if (saleState.error != null) Text('خطأ: ${saleState.error}'),
          Expanded(
            child: ListView.builder(
              itemCount: saleState.sales.length,
              itemBuilder: (context, index) {
                final sale = saleState.sales[index];
                return ListTile(
                  title: Text(sale.invoiceNo),
                  subtitle: Text('${sale.total} ريال'),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
```

## ملاحظات مهمة

1. **التحقق من البيانات**: جميع العمليات تتضمن التحقق من صحة البيانات
2. **معالجة الأخطاء**: تم تحسين معالجة الأخطاء مع رسائل واضحة
3. **الأمان**: التحقق من وجود المعاملات قبل الحذف
4. **الأداء**: استخدام المعاملات (Transactions) لضمان سلامة البيانات
5. **التوافق**: جميع الطرق متوافقة مع باقي النظام

## الاختبار

لاختبار العمليات المصححة:

```dart
// في main.dart أو ملف اختبار
void main() async {
  // اختبار عمليات المنتجات
  await runProductOperationsExamples();
  
  // اختبار عمليات المبيعات
  await runSaleOperationsExamples();
}
```

## الدعم والمساعدة

إذا واجهت أي مشاكل:
1. تحقق من وجود البيانات المطلوبة (فئات، وحدات، منتجات)
2. راجع رسائل الخطأ في السجلات
3. تأكد من صحة معرفات البيانات المرسلة
4. استخدم الأمثلة المرفقة كمرجع
