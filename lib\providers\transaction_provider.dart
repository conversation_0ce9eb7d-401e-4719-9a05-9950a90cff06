import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/transaction.dart';
import '../data/repositories/transaction_repository.dart';

// Transaction repository provider
final transactionRepositoryProvider = Provider<TransactionRepository>((ref) {
  return TransactionRepository();
});

// All transactions provider
final transactionsProvider = StateNotifierProvider<TransactionsNotifier, AsyncValue<List<Transactions>>>((ref) {
  return TransactionsNotifier(ref.read(transactionRepositoryProvider));
});

// Transactions by type provider
final transactionsByTypeProvider = StateNotifierProvider.family<TransactionsByTypeNotifier, AsyncValue<List<Transactions>>, String>((ref, type) {
  return TransactionsByTypeNotifier(ref.read(transactionRepositoryProvider), type);
});

// Daily transactions summary provider
final dailyTransactionsSummaryProvider = FutureProvider.family<Map<String, dynamic>, DateTime>((ref, date) async {
  final repository = ref.read(transactionRepositoryProvider);
  return await repository.getDailyTransactionsSummary(date);
});

// Monthly transactions summary provider
final monthlyTransactionsSummaryProvider = FutureProvider.family<Map<String, dynamic>, ({int year, int month})>((ref, params) async {
  final repository = ref.read(transactionRepositoryProvider);
  return await repository.getMonthlyTransactionsSummary(params.year, params.month);
});

// Cash flow report provider
final cashFlowReportProvider = FutureProvider.family<Map<String, dynamic>, ({DateTime startDate, DateTime endDate})>((ref, params) async {
  final repository = ref.read(transactionRepositoryProvider);
  return await repository.getCashFlowReport(params.startDate, params.endDate);
});

// Transaction form provider
final transactionFormProvider = StateNotifierProvider<TransactionFormNotifier, TransactionFormState>((ref) {
  return TransactionFormNotifier(ref.read(transactionRepositoryProvider));
});

// Transactions notifier
class TransactionsNotifier extends StateNotifier<AsyncValue<List<Transactions>>> {
  final TransactionRepository _repository;

  TransactionsNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadTransactions();
  }

  Future<void> loadTransactions({int? limit, int? offset}) async {
    state = const AsyncValue.loading();
    try {
      final transactions = await _repository.getAllTransactions(
        limit: limit,
        offset: offset,
      );
      state = AsyncValue.data(transactions!);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> addTransaction(Transactions transaction) async {
    try {
      await _repository.createTransaction(transaction);
      await loadTransactions(); // Reload the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateTransaction(Transactions transaction) async {
    try {
      await _repository.updateTransaction(transaction);
      await loadTransactions(); // Reload the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteTransaction(String transactionId) async {
    try {
      await _repository.deleteTransaction(transactionId);
      await loadTransactions(); // Reload the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> loadTransactionsByDateRange(
    DateTime startDate,
    DateTime endDate, {
    String? type,
    String? customerId,
    String? supplierId,
  }) async {
    state = const AsyncValue.loading();
    try {
      final transactions = await _repository.getTransactionsByDateRange(
        startDate,
        endDate,
        type: type,
        customerId: customerId,
        supplierId: supplierId,
      );
      state = AsyncValue.data(transactions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> searchTransactions(String query) async {
    if (query.isEmpty) {
      await loadTransactions();
      return;
    }

    state = const AsyncValue.loading();
    try {
      final transactions = await _repository.searchTransactions(query);
      state = AsyncValue.data(transactions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Transactions by type notifier
class TransactionsByTypeNotifier extends StateNotifier<AsyncValue<List<Transactions>>> {
  final TransactionRepository _repository;
  final String _type;

  TransactionsByTypeNotifier(this._repository, this._type) : super(const AsyncValue.loading()) {
    loadTransactionsByType();
  }

  Future<void> loadTransactionsByType() async {
    state = const AsyncValue.loading();
    try {
      final transactions = await _repository.getTransactionsByType(_type);
      state = AsyncValue.data(transactions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// Transaction form state
class TransactionFormState {
  final Transactions? transaction;
  final bool isLoading;
  final String? error;
  final bool isEditing;
  final String? generatedReferenceNumber;

  const TransactionFormState({
    this.transaction,
    this.isLoading = false,
    this.error,
    this.isEditing = false,
    this.generatedReferenceNumber,
  });

  TransactionFormState copyWith({
    Transactions? transaction,
    bool? isLoading,
    String? error,
    bool? isEditing,
    String? generatedReferenceNumber,
  }) {
    return TransactionFormState(
      transaction: transaction ?? this.transaction,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isEditing: isEditing ?? this.isEditing,
      generatedReferenceNumber: generatedReferenceNumber ?? this.generatedReferenceNumber,
    );
  }
}

// Transaction form notifier
class TransactionFormNotifier extends StateNotifier<TransactionFormState> {
  final TransactionRepository _repository;

  TransactionFormNotifier(this._repository) : super(const TransactionFormState());

  void initializeForEdit(Transactions transaction) {
    state = TransactionFormState(transaction: transaction, isEditing: true);
  }

  Future<void> initializeForCreate(String type) async {
    try {
      final referenceNumber = await _repository.generateNextReferenceNumber(type);
      state = TransactionFormState(
        isEditing: false,
        generatedReferenceNumber: referenceNumber,
      );
    } catch (error) {
      state = TransactionFormState(
        isEditing: false,
        error: error.toString(),
      );
    }
  }

  Future<bool> saveTransaction(Transactions transaction) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      if (state.isEditing) {
        await _repository.updateTransaction(transaction);
      } else {
        await _repository.createTransaction(transaction);
      }
      
      state = state.copyWith(isLoading: false);
      return true;
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        error: error.toString(),
      );
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  void reset() {
    state = const TransactionFormState();
  }
}

// Customer transactions provider
final customerTransactionsProvider = FutureProvider.family<List<Transactions>, String>((ref, customerId) async {
  final repository = ref.read(transactionRepositoryProvider);
  return await repository.getCustomerTransactions(customerId);
});

// Supplier transactions provider
final supplierTransactionsProvider = FutureProvider.family<List<Transactions>, String>((ref, supplierId) async {
  final repository = ref.read(transactionRepositoryProvider);
  return await repository.getSupplierTransactions(supplierId);
});

// Cash box transactions provider
final cashBoxTransactionsProvider = FutureProvider.family<List<Transactions>, String>((ref, cashBoxId) async {
  final repository = ref.read(transactionRepositoryProvider);
  return await repository.getCashBoxTransactions(cashBoxId);
});

// Bank account transactions provider
final bankAccountTransactionsProvider = FutureProvider.family<List<Transactions>, String>((ref, bankAccountId) async {
  final repository = ref.read(transactionRepositoryProvider);
  return await repository.getBankAccountTransactions(bankAccountId);
});

// Transaction statistics provider
final transactionStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.read(transactionRepositoryProvider);
  final today = DateTime.now();
  
  // Get today's summary
  final todaySummary = await repository.getDailyTransactionsSummary(today);
  
  // Get this month's summary
  final thisMonthSummary = await repository.getMonthlyTransactionsSummary(
    today.year,
    today.month,
  );
  
  // Get cash flow for this month
  final startOfMonth = DateTime(today.year, today.month, 1);
  final endOfMonth = DateTime(today.year, today.month + 1, 0);
  final cashFlow = await repository.getCashFlowReport(startOfMonth, endOfMonth);
  
  return {
    'today': todaySummary,
    'this_month': thisMonthSummary,
    'cash_flow': cashFlow,
  };
});

// Transaction validation provider
final transactionValidationProvider = Provider<TransactionValidation>((ref) {
  return TransactionValidation();
});

// Transaction validation class
class TransactionValidation {
  String? validateReferenceNumber(String? referenceNumber) {
    if (referenceNumber == null || referenceNumber.trim().isEmpty) {
      return 'رقم المرجع مطلوب';
    }
    return null;
  }

  String? validateAmount(String? amount) {
    if (amount == null || amount.trim().isEmpty) {
      return 'المبلغ مطلوب';
    }
    
    final double? parsedAmount = double.tryParse(amount.trim());
    if (parsedAmount == null) {
      return 'المبلغ غير صحيح';
    }
    
    if (parsedAmount <= 0) {
      return 'المبلغ يجب أن يكون أكبر من صفر';
    }
    
    return null;
  }

  String? validateType(String? type) {
    if (type == null || type.trim().isEmpty) {
      return 'نوع المعاملة مطلوب';
    }
    
    if (!TransactionType.allTypes.contains(type)) {
      return 'نوع المعاملة غير صحيح';
    }
    
    return null;
  }

  String? validatePaymentMethod(String? paymentMethod) {
    if (paymentMethod == null || paymentMethod.trim().isEmpty) {
      return 'طريقة الدفع مطلوبة';
    }
    
    if (!PaymentMethod.allMethods.contains(paymentMethod)) {
      return 'طريقة الدفع غير صحيحة';
    }
    
    return null;
  }

  String? validateDate(DateTime? date) {
    if (date == null) {
      return 'التاريخ مطلوب';
    }
    
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selectedDate = DateTime(date.year, date.month, date.day);
    
    if (selectedDate.isAfter(today)) {
      return 'لا يمكن أن يكون التاريخ في المستقبل';
    }
    
    return null;
  }

  String? validateDescription(String? description, {bool required = false}) {
    if (required && (description == null || description.trim().isEmpty)) {
      return 'الوصف مطلوب';
    }
    return null;
  }
}

// Recent transactions provider (last 10 transactions)
final recentTransactionsProvider = FutureProvider<List<Transactions>>((ref) async {
  final repository = ref.read(transactionRepositoryProvider);
  return await repository.getAllTransactions(limit: 10);
});

// Transaction types provider
final transactionTypesProvider = Provider<List<String>>((ref) {
  return TransactionType.allTypes;
});

// Payment methods provider
final paymentMethodsProvider = Provider<List<String>>((ref) {
  return PaymentMethod.allMethods;
});

// Transaction status provider
final transactionStatusProvider = Provider<List<String>>((ref) {
  return TransactionStatus.allStatuses;
});
