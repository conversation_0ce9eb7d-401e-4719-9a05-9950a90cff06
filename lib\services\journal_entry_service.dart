import '../data/local/dao/journal_entry_dao.dart';
import '../data/local/dao/chart_of_accounts_dao.dart';
import '../data/local/dao/audit_log_dao.dart';
import '../data/models/journal_entry.dart';
import '../data/models/chart_of_accounts.dart';
import 'accounting_service.dart';

/// خدمة إدارة القيود اليومية - Journal Entry Service
/// تدير عمليات القيود اليومية مع التحقق من الصحة والموافقات
/// Manages journal entry operations with validation and approvals
class JournalEntryService {
  final JournalEntryDao _journalEntryDao;
  final ChartOfAccountsDao _chartOfAccountsDao;
  final AuditLogDao _auditLogDao;
  final AccountingService _accountingService;

  JournalEntryService({
    required JournalEntryDao journalEntryDao,
    required ChartOfAccountsDao chartOfAccountsDao,
    required AuditLogDao auditLogDao,
    required AccountingService accountingService,
  })  : _journalEntryDao = journalEntryDao,
        _chartOfAccountsDao = chartOfAccountsDao,
        _auditLogDao = auditLogDao,
        _accountingService = accountingService;

  /// إنشاء قيد يومي جديد - Create new journal entry
  /// يستخدم AccountingService لضمان تطبيق منطق القيد المزدوج
  Future<String?> createEntry({
    required String debitAccountId,
    required String creditAccountId,
    required double amount,
    required String description,
    String? reference,
    DateTime? entryDate,
    String? userId,
    String? branchId,
    Map<String, dynamic>? metadata,
    bool requiresApproval = false,
  }) async {
    try {
      // التحقق من صحة البيانات
      await _validateEntryData(
        debitAccountId: debitAccountId,
        creditAccountId: creditAccountId,
        amount: amount,
        description: description,
      );

      // إنشاء القيد باستخدام AccountingService
      final entryId = await _accountingService.createJournalEntry(
        debitAccountId: debitAccountId,
        creditAccountId: creditAccountId,
        amount: amount,
        description: description,
        reference: reference,
        entryDate: entryDate,
        userId: userId,
        branchId: branchId,
        metadata: {
          ...?metadata,
          'requires_approval': requiresApproval,
          'created_by_service': 'journal_entry_service',
        },
      );

      // إذا كان القيد يتطلب موافقة، تحديث حالته
      if (entryId != null && requiresApproval) {
        await _journalEntryDao.updateApprovalStatus(entryId, 'pending', null);
      }

      return entryId;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'journal_entries',
        recordId: 'failed',
        action: 'create_entry',
        description: 'فشل في إنشاء قيد يومي من خلال الخدمة',
        userId: userId ?? 'system',
        category: 'journal_entry',
        severity: 'high',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      rethrow;
    }
  }

  /// التحقق من صحة بيانات القيد - Validate entry data
  Future<void> _validateEntryData({
    required String debitAccountId,
    required String creditAccountId,
    required double amount,
    required String description,
  }) async {
    // التحقق من أن الحسابين مختلفين
    if (debitAccountId == creditAccountId) {
      throw Exception('لا يمكن أن يكون الحساب المدين والدائن نفس الحساب');
    }

    // التحقق من صحة المبلغ
    if (amount <= 0) {
      throw Exception('المبلغ يجب أن يكون أكبر من صفر');
    }

    // التحقق من وجود الوصف
    if (description.trim().isEmpty) {
      throw Exception('وصف القيد مطلوب');
    }

    // التحقق من طول الوصف
    if (description.length > 500) {
      throw Exception('وصف القيد طويل جداً (الحد الأقصى 500 حرف)');
    }
  }

  /// الحصول على قيد بالمعرف - Get entry by ID
  Future<JournalEntry?> getEntryById(String entryId) async {
    try {
      return await _journalEntryDao.getById(entryId);
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'journal_entries',
        recordId: entryId,
        action: 'get_entry',
        description: 'فشل في الحصول على القيد',
        userId: 'system',
        category: 'journal_entry',
        severity: 'low',
        isSuccessful: false,
        errorMessage: e.toString(),
      );

      return null;
    }
  }

  /// الحصول على القيود حسب التاريخ - Get entries by date range
  Future<List<JournalEntry>> getEntriesByDateRange({
    required DateTime startDate,
    required DateTime endDate,
    String? branchId,
    String? userId,
  }) async {
    try {
      return await _journalEntryDao.getByDateRange(startDate, endDate);
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'journal_entries',
        recordId: 'date_range',
        action: 'get_entries',
        description: 'فشل في الحصول على القيود حسب التاريخ',
        userId: userId ?? 'system',
        category: 'journal_entry',
        severity: 'low',
        isSuccessful: false,
        errorMessage: e.toString(),
        metadata: {
          'start_date': startDate.toIso8601String(),
          'end_date': endDate.toIso8601String(),
        },
        branchId: branchId,
      );

      return [];
    }
  }

  /// الحصول على القيود المعلقة للموافقة - Get pending entries for approval
  Future<List<JournalEntry>> getPendingEntries({String? branchId}) async {
    try {
      return await _journalEntryDao.getPendingApprovalEntries();
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'journal_entries',
        recordId: 'pending',
        action: 'get_pending',
        description: 'فشل في الحصول على القيود المعلقة',
        userId: 'system',
        category: 'journal_entry',
        severity: 'low',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      return [];
    }
  }

  /// موافقة على قيد - Approve entry
  Future<bool> approveEntry({
    required String entryId,
    required String approverId,
    String? approvalNotes,
  }) async {
    try {
      // التحقق من وجود القيد
      final entry = await _journalEntryDao.getById(entryId);
      if (entry == null) {
        throw Exception('القيد غير موجود');
      }

      // التحقق من حالة القيد
      if (entry.approvalStatus == 'approved') {
        throw Exception('تم الموافقة على هذا القيد مسبقاً');
      }

      if (entry.approvalStatus == 'rejected') {
        throw Exception('تم رفض هذا القيد مسبقاً');
      }

      // الموافقة على القيد
      final success = await _journalEntryDao.approveEntry(entryId, approverId, approvalNotes);

      if (success) {
        // تسجيل العملية
        await _auditLogDao.logOperation(
          tableName: 'journal_entries',
          recordId: entryId,
          action: 'approve',
          description: 'الموافقة على القيد اليومي',
          userId: approverId,
          category: 'journal_entry',
          severity: 'medium',
          metadata: {
            'approval_notes': approvalNotes,
            'entry_description': entry.description,
          },
          branchId: entry.branchId,
        );
      }

      return success;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'journal_entries',
        recordId: entryId,
        action: 'approve',
        description: 'فشل في الموافقة على القيد',
        userId: approverId,
        category: 'journal_entry',
        severity: 'high',
        isSuccessful: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// رفض قيد - Reject entry
  Future<bool> rejectEntry({
    required String entryId,
    required String approverId,
    required String rejectionReason,
  }) async {
    try {
      // التحقق من وجود القيد
      final entry = await _journalEntryDao.getById(entryId);
      if (entry == null) {
        throw Exception('القيد غير موجود');
      }

      // التحقق من حالة القيد
      if (entry.approvalStatus == 'approved') {
        throw Exception('لا يمكن رفض قيد تم الموافقة عليه');
      }

      if (entry.approvalStatus == 'rejected') {
        throw Exception('تم رفض هذا القيد مسبقاً');
      }

      // رفض القيد
      final success = await _journalEntryDao.rejectEntry(entryId, approverId, rejectionReason);

      if (success) {
        // تسجيل العملية
        await _auditLogDao.logOperation(
          tableName: 'journal_entries',
          recordId: entryId,
          action: 'reject',
          description: 'رفض القيد اليومي',
          userId: approverId,
          category: 'journal_entry',
          severity: 'medium',
          metadata: {
            'rejection_reason': rejectionReason,
            'entry_description': entry.description,
          },
          branchId: entry.branchId,
        );
      }

      return success;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'journal_entries',
        recordId: entryId,
        action: 'reject',
        description: 'فشل في رفض القيد',
        userId: approverId,
        category: 'journal_entry',
        severity: 'high',
        isSuccessful: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// تعديل قيد (إذا لم تتم الموافقة عليه) - Edit entry (if not approved)
  Future<bool> editEntry({
    required String entryId,
    String? debitAccountId,
    String? creditAccountId,
    double? amount,
    String? description,
    String? reference,
    DateTime? entryDate,
    String? userId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // التحقق من وجود القيد
      final entry = await _journalEntryDao.getById(entryId);
      if (entry == null) {
        throw Exception('القيد غير موجود');
      }

      // التحقق من إمكانية التعديل
      if (entry.approvalStatus == 'approved') {
        throw Exception('لا يمكن تعديل قيد تم الموافقة عليه');
      }

      // إنشاء القيد المحدث
      final updatedEntry = entry.copyWith(
        debitAccountId: debitAccountId,
        creditAccountId: creditAccountId,
        debitAmount: amount,
        creditAmount: amount,
        description: description,
        reference: reference,
        entryDate: entryDate,
        metadata: metadata,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

      // التحقق من صحة البيانات الجديدة
      await _validateEntryData(
        debitAccountId: updatedEntry.debitAccountId,
        creditAccountId: updatedEntry.creditAccountId,
        amount: updatedEntry.debitAmount,
        description: updatedEntry.description,
      );

      // حفظ التحديثات
      final success = await _journalEntryDao.update(updatedEntry);

      if (success) {
        // تسجيل العملية
        await _auditLogDao.logOperation(
          tableName: 'journal_entries',
          recordId: entryId,
          action: 'edit',
          description: 'تعديل القيد اليومي',
          userId: userId ?? 'system',
          category: 'journal_entry',
          severity: 'medium',
          oldValues: entry.toMap().toString(),
          newValues: updatedEntry.toMap().toString(),
          branchId: entry.branchId,
        );
      }

      return success;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'journal_entries',
        recordId: entryId,
        action: 'edit',
        description: 'فشل في تعديل القيد',
        userId: userId ?? 'system',
        category: 'journal_entry',
        severity: 'high',
        isSuccessful: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// حذف قيد (حذف منطقي) - Delete entry (soft delete)
  Future<bool> deleteEntry({
    required String entryId,
    required String userId,
    String? reason,
  }) async {
    try {
      // التحقق من وجود القيد
      final entry = await _journalEntryDao.getById(entryId);
      if (entry == null) {
        throw Exception('القيد غير موجود');
      }

      // التحقق من إمكانية الحذف
      if (entry.approvalStatus == 'approved') {
        throw Exception('لا يمكن حذف قيد تم الموافقة عليه. استخدم العكس بدلاً من ذلك');
      }

      // حذف القيد
      final success = await _journalEntryDao.delete(entryId);

      if (success) {
        // تسجيل العملية
        await _auditLogDao.logOperation(
          tableName: 'journal_entries',
          recordId: entryId,
          action: 'delete',
          description: 'حذف القيد اليومي${reason != null ? ' - السبب: $reason' : ''}',
          userId: userId,
          category: 'journal_entry',
          severity: 'high',
          metadata: {
            'deletion_reason': reason,
            'entry_description': entry.description,
          },
          branchId: entry.branchId,
        );
      }

      return success;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'journal_entries',
        recordId: entryId,
        action: 'delete',
        description: 'فشل في حذف القيد',
        userId: userId,
        category: 'journal_entry',
        severity: 'high',
        isSuccessful: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// عكس قيد - Reverse entry
  Future<String?> reverseEntry({
    required String entryId,
    required String reason,
    required String userId,
  }) async {
    try {
      return await _accountingService.reverseJournalEntry(
        originalEntryId: entryId,
        reason: reason,
        userId: userId,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// البحث في القيود - Search entries
  Future<List<JournalEntry>> searchEntries({
    required String query,
    DateTime? startDate,
    DateTime? endDate,
    String? branchId,
  }) async {
    try {
      // البحث الأساسي
      List<JournalEntry> results = await _journalEntryDao.searchEntries(query);

      // تصفية حسب التاريخ إذا تم تحديده
      if (startDate != null && endDate != null) {
        results = results.where((entry) {
          return entry.entryDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
                 entry.entryDate.isBefore(endDate.add(const Duration(days: 1)));
        }).toList();
      }

      // تصفية حسب الفرع إذا تم تحديده
      if (branchId != null) {
        results = results.where((entry) => entry.branchId == branchId).toList();
      }

      return results;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'journal_entries',
        recordId: 'search',
        action: 'search',
        description: 'فشل في البحث في القيود',
        userId: 'system',
        category: 'journal_entry',
        severity: 'low',
        isSuccessful: false,
        errorMessage: e.toString(),
        metadata: {
          'search_query': query,
        },
        branchId: branchId,
      );

      return [];
    }
  }

  /// الحصول على إحصائيات القيود - Get entry statistics
  Future<Map<String, dynamic>> getEntryStatistics({
    DateTime? startDate,
    DateTime? endDate,
    String? branchId,
  }) async {
    try {
      return await _journalEntryDao.getJournalEntryStatistics(
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'journal_entries',
        recordId: 'statistics',
        action: 'get_statistics',
        description: 'فشل في الحصول على إحصائيات القيود',
        userId: 'system',
        category: 'journal_entry',
        severity: 'low',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      return {};
    }
  }
}
