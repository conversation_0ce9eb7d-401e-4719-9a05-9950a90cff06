import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tijari_tech/core/theme/colors.dart';
import 'package:tijari_tech/core/theme/text_styles.dart';
import 'package:tijari_tech/core/utils/extensions.dart';

import 'package:tijari_tech/data/models/product.dart';
import 'package:tijari_tech/data/models/warehouse.dart';
import 'package:tijari_tech/providers/stock_management_provider.dart';
import 'package:tijari_tech/providers/warehouse_provider.dart';

/// حوار إدارة المخزون للمنتج
class StockManagementDialog extends ConsumerStatefulWidget {
  final Product product;

  const StockManagementDialog({
    super.key,
    required this.product,
  });

  @override
  ConsumerState<StockManagementDialog> createState() =>
      _StockManagementDialogState();
}

class _StockManagementDialogState extends ConsumerState<StockManagementDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();

  // Controllers
  final _quantityController = TextEditingController();
  final _reasonController = TextEditingController();
  final _notesController = TextEditingController();

  // State
  bool _isLoading = false;
  String _operationType = 'add'; // add, subtract, set
  String _transactionType = 'adjustment'; // adjustment, purchase, sale, return
  String? _selectedWarehouseId; // إضافة دعم المخازن
  String _currentUserId = 'system'; // Use system user for now

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // تحميل المخازن عند فتح الحوار
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(warehouseManagementProvider.notifier).loadWarehouses();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _quantityController.dispose();
    _reasonController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600.w,
        height: 700.h,
        padding: EdgeInsets.all(24.r),
        child: Column(
          children: [
            // Header
            _buildHeader(),
            SizedBox(height: 20.h),

            // Current Stock Info
            _buildCurrentStockInfo(),
            SizedBox(height: 20.h),

            // Tabs
            _buildTabs(),
            SizedBox(height: 20.h),

            // Tab Content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildStockAdjustmentTab(),
                  _buildStockHistoryTab(),
                  _buildStockSettingsTab(),
                ],
              ),
            ),

            SizedBox(height: 20.h),

            // Action Buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// بناء رأس الحوار
  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.inventory_2,
          color: AppColors.primary,
          size: 28.r,
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'إدارة مخزون المنتج',
                style: AppTextStyles.titleLarge.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                widget.product.getDisplayName(),
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  /// بناء معلومات المخزون الحالي
  Widget _buildCurrentStockInfo() {
    final currentStock = widget.product.currentStock ?? 0;
    final minStock = widget.product.minStock;
    final maxStock = widget.product.maxStock;

    Color stockColor = AppColors.success;
    String stockStatus = 'متوفر';

    if (currentStock <= 0) {
      stockColor = AppColors.error;
      stockStatus = 'نفد المخزون';
    } else if (currentStock <= minStock) {
      stockColor = AppColors.warning;
      stockStatus = 'مخزون منخفض';
    }

    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: stockColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: stockColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.inventory,
            color: stockColor,
            size: 24.r,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الكمية الحالية: ${currentStock.toStringAsFixed(0)}',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: stockColor,
                  ),
                ),
                Text(
                  'الحالة: $stockStatus',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: stockColor,
                  ),
                ),
                if (widget.product.trackStock) ...[
                  SizedBox(height: 4.h),
                  Text(
                    'الحد الأدنى: ${minStock.toStringAsFixed(0)} | الحد الأقصى: ${maxStock.toStringAsFixed(0)}',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء التبويبات
  Widget _buildTabs() {
    return TabBar(
      controller: _tabController,
      labelColor: AppColors.primary,
      unselectedLabelColor: Colors.grey,
      indicatorColor: AppColors.primary,
      tabs: const [
        Tab(
          icon: Icon(Icons.edit),
          text: 'تعديل المخزون',
        ),
        Tab(
          icon: Icon(Icons.history),
          text: 'سجل الحركات',
        ),
        Tab(
          icon: Icon(Icons.settings),
          text: 'إعدادات المخزون',
        ),
      ],
    );
  }

  /// تبويب تعديل المخزون
  Widget _buildStockAdjustmentTab() {
    return Form(
      key: _formKey,
      child: ListView(
        children: [
          Text(
            'نوع العملية',
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),

          Row(
            children: [
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('إضافة'),
                  subtitle: const Text('زيادة الكمية'),
                  value: 'add',
                  groupValue: _operationType,
                  onChanged: (value) {
                    setState(() {
                      _operationType = value!;
                    });
                  },
                ),
              ),
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('خصم'),
                  subtitle: const Text('تقليل الكمية'),
                  value: 'subtract',
                  groupValue: _operationType,
                  onChanged: (value) {
                    setState(() {
                      _operationType = value!;
                    });
                  },
                ),
              ),
            ],
          ),

          RadioListTile<String>(
            title: const Text('تعيين'),
            subtitle: const Text('تحديد الكمية الإجمالية'),
            value: 'set',
            groupValue: _operationType,
            onChanged: (value) {
              setState(() {
                _operationType = value!;
              });
            },
          ),

          SizedBox(height: 20.h),

          // الكمية
          TextFormField(
            controller: _quantityController,
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
            ],
            decoration: InputDecoration(
              labelText:
                  _operationType == 'set' ? 'الكمية الإجمالية' : 'الكمية',
              hintText: '0.0',
              prefixIcon: Icon(
                _operationType == 'add'
                    ? Icons.add
                    : _operationType == 'subtract'
                        ? Icons.remove
                        : Icons.edit,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال الكمية';
              }
              final quantity = double.tryParse(value);
              if (quantity == null || quantity <= 0) {
                return 'يرجى إدخال كمية صحيحة';
              }
              return null;
            },
          ),

          SizedBox(height: 16.h),

          // سبب التعديل
          TextFormField(
            controller: _reasonController,
            decoration: InputDecoration(
              labelText: 'سبب التعديل *',
              hintText: 'أدخل سبب تعديل المخزون',
              prefixIcon: const Icon(Icons.description),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال سبب التعديل';
              }
              return null;
            },
          ),

          SizedBox(height: 16.h),

          // اختيار المخزن
          Text(
            'المخزن',
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),

          Consumer(
            builder: (context, ref, child) {
              final warehouseState = ref.watch(warehouseManagementProvider);

              if (warehouseState.isLoading) {
                return const CircularProgressIndicator();
              }

              final warehouses = warehouseState.warehouses;
              if (warehouses.isEmpty) {
                return Text(
                  'لا توجد مخازن متاحة',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.grey[600],
                  ),
                );
              }

              // تحديد المخزن الافتراضي إذا لم يتم اختيار مخزن
              if (_selectedWarehouseId == null) {
                final defaultWarehouse = warehouses.firstWhere(
                  (w) => w.isDefault,
                  orElse: () => warehouses.first,
                );
                _selectedWarehouseId = defaultWarehouse.id;
              }

              return DropdownButtonFormField<String>(
                value: _selectedWarehouseId,
                decoration: InputDecoration(
                  labelText: 'اختر المخزن',
                  prefixIcon: const Icon(Icons.warehouse),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
                items: warehouses.map((warehouse) {
                  return DropdownMenuItem<String>(
                    value: warehouse.id,
                    child: Container(
                      constraints: BoxConstraints(maxWidth: 400),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            warehouse.isDefault ? Icons.star : Icons.warehouse,
                            size: 16.r,
                            color: warehouse.isDefault
                                ? Colors.amber
                                : Colors.grey,
                          ),
                          SizedBox(width: 8.w),
                          Flexible(
                            child: Text(
                              warehouse.name,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (warehouse.isDefault)
                            Text(
                              '(افتراضي)',
                              style: AppTextStyles.bodySmall.copyWith(
                                color: Colors.amber[700],
                              ),
                            ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedWarehouseId = value;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى اختيار المخزن';
                  }
                  return null;
                },
              );
            },
          ),

          SizedBox(height: 16.h),

          // ملاحظات إضافية
          TextFormField(
            controller: _notesController,
            maxLines: 3,
            decoration: InputDecoration(
              labelText: 'ملاحظات إضافية',
              hintText: 'أدخل أي ملاحظات إضافية',
              prefixIcon: const Icon(Icons.note),
              alignLabelWithHint: true,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// تبويب سجل الحركات
  Widget _buildStockHistoryTab() {
    return Column(
      children: [
        Text(
          'سجل حركات المخزون',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        Flexible(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.history,
                  size: 64.r,
                  color: Colors.grey[400],
                ),
                SizedBox(height: 16.h),
                Text(
                  'سجل الحركات قيد التطوير',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'سيتم عرض جميع حركات المخزون هنا',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// تبويب إعدادات المخزون
  Widget _buildStockSettingsTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إعدادات تتبع المخزون',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),

          // معلومات الحدود الحالية
          Container(
            padding: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(
                color: AppColors.info.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الحدود الحالية',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.info,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                    'الحد الأدنى: ${widget.product.minStock.toStringAsFixed(0)}'),
                Text(
                    'الحد الأقصى: ${widget.product.maxStock.toStringAsFixed(0)}'),
                Text(
                    'تتبع المخزون: ${widget.product.trackStock ? "مفعل" : "معطل"}'),
              ],
            ),
          ),

          SizedBox(height: 20.h),

          Center(
            child: Column(
              children: [
                Icon(
                  Icons.settings,
                  size: 64.r,
                  color: Colors.grey[400],
                ),
                SizedBox(height: 16.h),
                Text(
                  'إعدادات المخزون المتقدمة',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'يمكن تعديل هذه الإعدادات من صفحة تعديل المنتج',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.grey[500],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveStockAdjustment,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: _isLoading
                ? SizedBox(
                    height: 20.h,
                    width: 20.w,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('حفظ التعديل'),
          ),
        ),
      ],
    );
  }

  /// حفظ تعديل المخزون
  Future<void> _saveStockAdjustment() async {
    if (_tabController.index != 0) return; // فقط في تبويب التعديل

    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final quantity = double.parse(_quantityController.text);
      final reason = _reasonController.text.trim();
      final notes = _notesController.text.trim().isEmpty
          ? null
          : _notesController.text.trim();

      await ref.read(stockManagementProvider.notifier).adjustStock(
            productId: widget.product.id,
            operationType: _operationType,
            quantity: quantity,
            reason: reason,
            notes: notes,
            transactionType: _transactionType,
            warehouseId: _selectedWarehouseId, // تمرير معرف المخزن
            userId: _currentUserId, // تمرير معرف المستخدم
          );

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('تم تعديل المخزون بنجاح'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ في تعديل المخزون: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
