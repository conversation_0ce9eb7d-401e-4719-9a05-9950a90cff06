import 'package:sqflite/sqflite.dart' as sql;
import 'package:tijari_tech/data/models/product_unit_price.dart';
import 'base_dao.dart';
import '../../../core/utils/app_utils.dart';

class ProductUnitPriceDao extends BaseDao<ProductUnitPrice> {
  @override
  String get tableName => 'product_unit_prices';

  @override
  ProductUnitPrice fromMap(Map<String, dynamic> map) => ProductUnitPrice.fromMap(map);

  @override
  Map<String, dynamic> toMap(ProductUnitPrice entity) => entity.toMap();

  // ------------------------------------------------------------------
  // Product Unit Price-specific queries
  // ------------------------------------------------------------------

  /// استرجاع أسعار منتج معين
  Future<List<ProductUnitPrice>> findByProductId(String productId) async {
    return await findWhere(
      where: 'product_id = ?',
      whereArgs: [productId],
      orderBy: 'is_default DESC, conversion_factor ASC',
    );
  }

  /// استرجاع السعر الافتراضي لمنتج
  Future<ProductUnitPrice?> findDefaultPriceByProductId(String productId) async {
    final prices = await findWhere(
      where: 'product_id = ? AND is_default = 1',
      whereArgs: [productId],
      limit: 1,
    );
    return prices.isNotEmpty ? prices.first : null;
  }

  /// استرجاع سعر منتج لوحدة معينة
  Future<ProductUnitPrice?> findByProductAndUnit(String productId, String unitId) async {
    final prices = await findWhere(
      where: 'product_id = ? AND unit_id = ?',
      whereArgs: [productId, unitId],
      limit: 1,
    );
    return prices.isNotEmpty ? prices.first : null;
  }

  /// استرجاع الأسعار الصالحة حالياً
  Future<List<ProductUnitPrice>> findValidPrices(String productId) async {
    final now = DateTime.now().toIso8601String();
    return await findWhere(
      where: '''
        product_id = ? AND 
        (valid_from IS NULL OR valid_from <= ?) AND 
        (valid_to IS NULL OR valid_to >= ?)
      ''',
      whereArgs: [productId, now, now],
      orderBy: 'is_default DESC, conversion_factor ASC',
    );
  }

  /// تعيين سعر كسعر افتراضي
  Future<bool> setDefaultPrice(String productId, String priceId) async {
    try {
      final db = await database;
      
      return await db.transaction((txn) async {
        // إلغاء تعيين جميع الأسعار كافتراضية
        await txn.update(
          tableName,
          {'is_default': 0, 'updated_at': DateTime.now().toIso8601String()},
          where: 'product_id = ? AND deleted_at IS NULL',
          whereArgs: [productId],
        );
        
        // تعيين السعر المحدد كافتراضي
        final count = await txn.update(
          tableName,
          {'is_default': 1, 'updated_at': DateTime.now().toIso8601String()},
          where: 'id = ? AND product_id = ? AND deleted_at IS NULL',
          whereArgs: [priceId, productId],
        );
        
        return count > 0;
      });
    } catch (e) {
      AppUtils.logError('Error setting default price', e);
      return false;
    }
  }

  /// تحديث أسعار متعددة لمنتج
  Future<bool> bulkUpdatePrices(String productId, List<ProductUnitPrice> prices) async {
    try {
      final db = await database;
      
      return await db.transaction((txn) async {
        // حذف الأسعار الحالية
        await txn.update(
          tableName,
          {
            'deleted_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'product_id = ? AND deleted_at IS NULL',
          whereArgs: [productId],
        );
        
        // إدراج الأسعار الجديدة
        for (final price in prices) {
          final priceMap = price.copyWith(productId: productId).toMap();
          priceMap['created_at'] = DateTime.now().toIso8601String();
          priceMap['updated_at'] = DateTime.now().toIso8601String();
          await txn.insert(tableName, priceMap);
        }
        
        return true;
      });
    } catch (e) {
      AppUtils.logError('Error bulk updating prices', e);
      return false;
    }
  }

  /// حذف جميع أسعار منتج معين
  Future<bool> deleteByProductId(String productId) async {
    try {
      final db = await database;
      final now = DateTime.now().toIso8601String();
      
      final count = await db.update(
        tableName,
        {
          'deleted_at': now,
          'updated_at': now,
        },
        where: 'product_id = ? AND deleted_at IS NULL',
        whereArgs: [productId],
      );
      
      AppUtils.logInfo('Soft deleted $count prices for product: $productId');
      return count > 0;
    } catch (e) {
      AppUtils.logError('Error deleting prices by product ID', e);
      return false;
    }
  }

  /// استرجاع الأسعار مع معلومات الوحدة
  Future<List<Map<String, dynamic>>> getPricesWithUnitInfo(String productId) async {
    final sql = '''
      SELECT 
        pup.*,
        u.name as unit_name,
        u.symbol as unit_symbol,
        u.factor as unit_factor
      FROM $tableName pup
      LEFT JOIN units u ON pup.unit_id = u.id
      WHERE pup.product_id = ? AND pup.deleted_at IS NULL
      ORDER BY pup.is_default DESC, pup.conversion_factor ASC
    ''';

    return await rawQuery(sql, [productId]);
  }

  /// حساب السعر لكمية معينة
  Future<double?> calculatePrice(
    String productId,
    String unitId,
    double quantity, {
    String priceType = 'selling', // selling, wholesale, retail
  }) async {
    try {
      final price = await findByProductAndUnit(productId, unitId);
      if (price == null) return null;
      
      double unitPrice;
      switch (priceType) {
        case 'wholesale':
          unitPrice = price.wholesalePrice ?? price.sellingPrice;
          break;
        case 'retail':
          unitPrice = price.retailPrice ?? price.sellingPrice;
          break;
        default:
          unitPrice = price.sellingPrice;
      }
      
      return unitPrice * quantity;
    } catch (e) {
      AppUtils.logError('Error calculating price', e);
      return null;
    }
  }

  /// استرجاع تاريخ أسعار منتج
  Future<List<ProductUnitPrice>> getPriceHistory(
    String productId, {
    String? unitId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    String whereClause = 'product_id = ?';
    List<dynamic> whereArgs = [productId];
    
    if (unitId != null) {
      whereClause += ' AND unit_id = ?';
      whereArgs.add(unitId);
    }
    
    if (fromDate != null) {
      whereClause += ' AND created_at >= ?';
      whereArgs.add(fromDate.toIso8601String());
    }
    
    if (toDate != null) {
      whereClause += ' AND created_at <= ?';
      whereArgs.add(toDate.toIso8601String());
    }
    
    return await findWhere(
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'created_at DESC',
    );
  }

  /// استرجاع الأسعار المنتهية الصلاحية
  Future<List<ProductUnitPrice>> getExpiredPrices() async {
    final now = DateTime.now().toIso8601String();
    return await findWhere(
      where: 'valid_to IS NOT NULL AND valid_to < ?',
      whereArgs: [now],
      orderBy: 'valid_to DESC',
    );
  }

  /// استرجاع الأسعار التي ستنتهي صلاحيتها قريباً
  Future<List<ProductUnitPrice>> getExpiringPrices({int daysAhead = 7}) async {
    final now = DateTime.now();
    final futureDate = now.add(Duration(days: daysAhead));
    
    return await findWhere(
      where: '''
        valid_to IS NOT NULL AND 
        valid_to > ? AND 
        valid_to <= ?
      ''',
      whereArgs: [now.toIso8601String(), futureDate.toIso8601String()],
      orderBy: 'valid_to ASC',
    );
  }

  /// تحديث صلاحية الأسعار
  Future<bool> updatePriceValidity(
    String priceId,
    DateTime? validFrom,
    DateTime? validTo,
  ) async {
    try {
      final db = await database;
      
      final count = await db.update(
        tableName,
        {
          'valid_from': validFrom?.toIso8601String(),
          'valid_to': validTo?.toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ? AND deleted_at IS NULL',
        whereArgs: [priceId],
      );
      
      return count > 0;
    } catch (e) {
      AppUtils.logError('Error updating price validity', e);
      return false;
    }
  }

  /// استرجاع إحصائيات الأسعار
  Future<Map<String, dynamic>> getPricesStatistics() async {
    final sql = '''
      SELECT 
        COUNT(*) as total_prices,
        COUNT(CASE WHEN is_default = 1 THEN 1 END) as default_prices,
        COUNT(CASE WHEN wholesale_price IS NOT NULL THEN 1 END) as wholesale_prices,
        COUNT(CASE WHEN retail_price IS NOT NULL THEN 1 END) as retail_prices,
        COUNT(CASE WHEN valid_to IS NOT NULL AND valid_to < datetime('now') THEN 1 END) as expired_prices,
        AVG(selling_price) as avg_selling_price,
        AVG(cost_price) as avg_cost_price,
        AVG((selling_price - cost_price) / cost_price * 100) as avg_profit_margin
      FROM $tableName
      WHERE deleted_at IS NULL
    ''';

    final result = await rawQuery(sql);
    return result.isNotEmpty ? result.first : {};
  }

  /// تصدير الأسعار
  Future<List<Map<String, dynamic>>> exportPrices({String? productId}) async {
    try {
      String whereClause = 'deleted_at IS NULL';
      List<dynamic> whereArgs = [];
      
      if (productId != null) {
        whereClause += ' AND product_id = ?';
        whereArgs.add(productId);
      }
      
      final sql = '''
        SELECT pup.*, u.name as unit_name, u.symbol as unit_symbol
        FROM $tableName pup
        LEFT JOIN units u ON pup.unit_id = u.id
        WHERE $whereClause 
        ORDER BY pup.product_id, pup.is_default DESC
      ''';
      
      return await rawQuery(sql, whereArgs.isEmpty ? null : whereArgs);
    } catch (e) {
      AppUtils.logError('Error exporting prices', e);
      return [];
    }
  }

  /// تنظيف الأسعار المنتهية الصلاحية
  Future<int> cleanupExpiredPrices({int daysOld = 90}) async {
    try {
      final db = await database;
      final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));
      
      final count = await db.update(
        tableName,
        {
          'deleted_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: '''
          valid_to IS NOT NULL AND 
          valid_to < ? AND 
          deleted_at IS NULL
        ''',
        whereArgs: [cutoffDate.toIso8601String()],
      );
      
      AppUtils.logInfo('Cleaned up $count expired prices');
      return count;
    } catch (e) {
      AppUtils.logError('Error cleaning up expired prices', e);
      return 0;
    }
  }
}
