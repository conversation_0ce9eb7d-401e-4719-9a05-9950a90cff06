import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:sqflite/sqflite.dart';

import '../data/api/api_client.dart';
import '../data/repositories/user_repository.dart';
import '../data/repositories/transaction_repository.dart';
import '../data/local/database.dart';

class SyncService {
  static final SyncService _instance = SyncService._internal();
  factory SyncService() => _instance;
  SyncService._internal();

  final ApiClient _apiClient = ApiClient(Dio());
  final UserRepository _userRepository = UserRepository();
  final TransactionRepository _transactionRepository = TransactionRepository();
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  Timer? _syncTimer;
  bool _isSyncing = false;
  final StreamController<SyncStatus> _syncStatusController =
      StreamController<SyncStatus>.broadcast();

  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;

  // Initialize sync service
  Future<void> initialize() async {
    await _startPeriodicSync();
    await _listenToConnectivityChanges();
  }

  // Start periodic sync
  Future<void> _startPeriodicSync() async {
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _performSync();
    });
  }

  // Listen to connectivity changes
  Future<void> _listenToConnectivityChanges() async {
    Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
          if (result != ConnectivityResult.none) {
            _performSync();
          }
        } as void Function(List<ConnectivityResult> event)?);
  }

  // Perform full sync
  Future<void> performFullSync() async {
    if (_isSyncing) return;

    _isSyncing = true;
    try {
      await _performSync();
    } finally {
      _isSyncing = false;
    }
  }

  // Internal sync method
  Future<void> _performSync() async {
    if (_isSyncing) return;

    _isSyncing = true;
    try {
      final bool isOnline = await _checkConnectivity();
      if (!isOnline) {
        _updateSyncStatus(isOnline: false);
        return;
      }

      // Get last sync timestamp
      final String lastSyncTimestamp = await _getLastSyncTimestamp();

      // Upload local changes
      await _uploadLocalChanges(lastSyncTimestamp);

      // Download remote changes
      await _downloadRemoteChanges(lastSyncTimestamp);

      // Update last sync timestamp
      await _updateLastSyncTimestamp();

      _updateSyncStatus(isOnline: true);
    } catch (e) {
      print('Sync error: $e');
      _updateSyncStatus(isOnline: true, hasError: true);
    } finally {
      _isSyncing = false;
    }
  }

  // Upload local changes to server
  Future<void> _uploadLocalChanges(String lastSyncTimestamp) async {
    try {
      // Get local changes since last sync
      final Map<String, List<Map<String, dynamic>>> localChanges =
          await _getLocalChangesSince(lastSyncTimestamp);

      if (localChanges.isEmpty) return;

      // Upload to server
      final SyncUploadRequest request = SyncUploadRequest(
        users: localChanges['users'] ?? [],
        transactions: localChanges['transactions'] ?? [],
        stockAdjustments: localChanges['stock_adjustments'] ?? [],
        lastSyncTimestamp: lastSyncTimestamp,
      );

      final ApiResponse<SyncResponse> response =
          await _apiClient.uploadData(request);

      if (response.success && response.data != null) {
        // Mark uploaded records as synced
        await _markRecordsAsSynced(localChanges);
        print('Uploaded ${response.data!.uploadedRecords} records');
      }
    } catch (e) {
      print('Upload error: $e');
      rethrow;
    }
  }

  // Download remote changes from server
  Future<void> _downloadRemoteChanges(String lastSyncTimestamp) async {
    try {
      final SyncDownloadRequest request = SyncDownloadRequest(
        lastSyncTimestamp: lastSyncTimestamp,
        tables: ['users', 'transactions', 'stock_adjustments'],
      );

      final ApiResponse<SyncDownloadResponse> response =
          await _apiClient.downloadData(request);

      if (response.success && response.data != null) {
        // Apply remote changes to local database
        await _applyRemoteChanges(response.data!.data);
        print('Downloaded remote changes');
      }
    } catch (e) {
      print('Download error: $e');
      rethrow;
    }
  }

  // Get local changes since last sync
  Future<Map<String, List<Map<String, dynamic>>>> _getLocalChangesSince(
    String lastSyncTimestamp,
  ) async {
    final Map<String, List<Map<String, dynamic>>> changes = {};
    final DateTime lastSync = DateTime.parse(lastSyncTimestamp);

    // Get user changes
    final List<Map<String, dynamic>> userChanges =
        await _getUserChangesSince(lastSync);
    if (userChanges.isNotEmpty) {
      changes['users'] = userChanges;
    }

    // Get transaction changes
    final List<Map<String, dynamic>> transactionChanges =
        await _getTransactionChangesSince(lastSync);
    if (transactionChanges.isNotEmpty) {
      changes['transactions'] = transactionChanges;
    }

    // Get stock adjustment changes
    final List<Map<String, dynamic>> stockAdjustmentChanges =
        await _getStockAdjustmentChangesSince(lastSync);
    if (stockAdjustmentChanges.isNotEmpty) {
      changes['stock_adjustments'] = stockAdjustmentChanges;
    }

    return changes;
  }

  // Get user changes since timestamp
  Future<List<Map<String, dynamic>>> _getUserChangesSince(
      DateTime timestamp) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> results = await db.query(
      'users',
      where: 'updated_at > ? OR created_at > ?',
      whereArgs: [timestamp.toIso8601String(), timestamp.toIso8601String()],
    );
    return results;
  }

  // Get transaction changes since timestamp
  Future<List<Map<String, dynamic>>> _getTransactionChangesSince(
      DateTime timestamp) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> results = await db.query(
      'transactions',
      where: 'updated_at > ? OR created_at > ?',
      whereArgs: [timestamp.toIso8601String(), timestamp.toIso8601String()],
    );
    return results;
  }

  // Get stock adjustment changes since timestamp
  Future<List<Map<String, dynamic>>> _getStockAdjustmentChangesSince(
      DateTime timestamp) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> results = await db.query(
      'stock_adjustments',
      where: 'updated_at > ? OR created_at > ?',
      whereArgs: [timestamp.toIso8601String(), timestamp.toIso8601String()],
    );
    return results;
  }

  // Apply remote changes to local database
  Future<void> _applyRemoteChanges(
      Map<String, List<Map<String, dynamic>>> remoteData) async {
    final db = await _databaseHelper.database;

    // Apply user changes
    if (remoteData.containsKey('users')) {
      for (final userData in remoteData['users']!) {
        await db.insert(
          'users',
          userData,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    }

    // Apply transaction changes
    if (remoteData.containsKey('transactions')) {
      for (final transactionData in remoteData['transactions']!) {
        await db.insert(
          'transactions',
          transactionData,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    }

    // Apply stock adjustment changes
    if (remoteData.containsKey('stock_adjustments')) {
      for (final adjustmentData in remoteData['stock_adjustments']!) {
        await db.insert(
          'stock_adjustments',
          adjustmentData,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    }
  }

  // Mark records as synced
  Future<void> _markRecordsAsSynced(
      Map<String, List<Map<String, dynamic>>> records) async {
    // In a real implementation, you might add a 'synced' flag to track sync status
    // For now, we'll just log the operation
    print('Marked ${records.length} record types as synced');
  }

  // Check internet connectivity
  Future<bool> _checkConnectivity() async {
    final ConnectivityResult connectivityResult =
        (await Connectivity().checkConnectivity()) as ConnectivityResult;
    return connectivityResult != ConnectivityResult.none;
  }

  // Get last sync timestamp
  Future<String> _getLastSyncTimestamp() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString('last_sync_timestamp') ??
        DateTime.now().subtract(const Duration(days: 30)).toIso8601String();
  }

  // Update last sync timestamp
  Future<void> _updateLastSyncTimestamp() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(
        'last_sync_timestamp', DateTime.now().toIso8601String());
  }

  // Update sync status
  void _updateSyncStatus({
    required bool isOnline,
    bool hasError = false,
  }) {
    final status = SyncStatus(
      lastSyncTimestamp: DateTime.now().toIso8601String(),
      isOnline: isOnline,
      pendingUploads: 0, // Calculate actual pending uploads
      pendingDownloads: 0, // Calculate actual pending downloads
    );

    _syncStatusController.add(status);
  }

  // Get sync statistics
  Future<Map<String, dynamic>> getSyncStatistics() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String lastSync = prefs.getString('last_sync_timestamp') ?? 'Never';
    final bool isOnline = await _checkConnectivity();

    return {
      'last_sync': lastSync,
      'is_online': isOnline,
      'is_syncing': _isSyncing,
      'pending_uploads': await _getPendingUploadsCount(),
      'pending_downloads':
          0, // This would be calculated based on server comparison
    };
  }

  // Get pending uploads count
  Future<int> _getPendingUploadsCount() async {
    final String lastSyncTimestamp = await _getLastSyncTimestamp();
    final Map<String, List<Map<String, dynamic>>> changes =
        await _getLocalChangesSince(lastSyncTimestamp);

    int count = 0;
    for (final changeList in changes.values) {
      count += changeList.length;
    }
    return count;
  }

  // Force sync now
  Future<void> forceSyncNow() async {
    await _performSync();
  }

  // Reset sync data
  Future<void> resetSyncData() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('last_sync_timestamp');
  }

  // Dispose resources
  void dispose() {
    _syncTimer?.cancel();
    _syncStatusController.close();
  }
}
