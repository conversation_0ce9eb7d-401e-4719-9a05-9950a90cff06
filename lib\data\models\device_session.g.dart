// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_session.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeviceSession _$DeviceSessionFromJson(Map<String, dynamic> json) =>
    DeviceSession(
      id: json['id'] as String,
      userId: json['userId'] as String,
      deviceName: json['deviceName'] as String?,
      deviceOs: json['deviceOs'] as String?,
      appVersion: json['appVersion'] as String?,
      ipAddress: json['ipAddress'] as String?,
      token: json['token'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      lastLoginAt: json['lastLoginAt'] == null
          ? null
          : DateTime.parse(json['lastLoginAt'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$DeviceSessionToJson(DeviceSession instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'deviceName': instance.deviceName,
      'deviceOs': instance.deviceOs,
      'appVersion': instance.appVersion,
      'ipAddress': instance.ipAddress,
      'token': instance.token,
      'isActive': instance.isActive,
      'lastLoginAt': instance.lastLoginAt?.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };
