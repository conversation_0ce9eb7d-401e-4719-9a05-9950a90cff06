import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/inventory_report.dart';
import '../services/inventory_report_service.dart';
import '../core/utils/app_utils.dart';
import '../core/utils/error_handler.dart';
import 'database_provider.dart';

// Inventory report service provider
final inventoryReportServiceProvider = Provider<InventoryReportService>((ref) {
  return InventoryReportService();
});

// Inventory report management provider
final inventoryReportManagementProvider = StateNotifierProvider<
    InventoryReportManagementNotifier, InventoryReportManagementState>((ref) {
  return InventoryReportManagementNotifier(ref);
});

// Inventory report management state
class InventoryReportManagementState {
  final bool isLoading;
  final String? error;
  final String? currentReportType;
  final Map<String, dynamic> currentReportData;
  final Map<String, dynamic> reportParameters;
  final List<InventoryReport> savedReports;
  final StockSummaryData? stockSummary;
  final Map<String, dynamic> movementAnalysis;
  final Map<String, dynamic> lowStockReport;
  final Map<String, dynamic> abcAnalysis;

  const InventoryReportManagementState({
    this.isLoading = false,
    this.error,
    this.currentReportType,
    this.currentReportData = const {},
    this.reportParameters = const {},
    this.savedReports = const [],
    this.stockSummary,
    this.movementAnalysis = const {},
    this.lowStockReport = const {},
    this.abcAnalysis = const {},
  });

  InventoryReportManagementState copyWith({
    bool? isLoading,
    String? error,
    String? currentReportType,
    Map<String, dynamic>? currentReportData,
    Map<String, dynamic>? reportParameters,
    List<InventoryReport>? savedReports,
    StockSummaryData? stockSummary,
    Map<String, dynamic>? movementAnalysis,
    Map<String, dynamic>? lowStockReport,
    Map<String, dynamic>? abcAnalysis,
  }) {
    return InventoryReportManagementState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      currentReportType: currentReportType ?? this.currentReportType,
      currentReportData: currentReportData ?? this.currentReportData,
      reportParameters: reportParameters ?? this.reportParameters,
      savedReports: savedReports ?? this.savedReports,
      stockSummary: stockSummary ?? this.stockSummary,
      movementAnalysis: movementAnalysis ?? this.movementAnalysis,
      lowStockReport: lowStockReport ?? this.lowStockReport,
      abcAnalysis: abcAnalysis ?? this.abcAnalysis,
    );
  }
}

// Inventory report management notifier
class InventoryReportManagementNotifier extends StateNotifier<InventoryReportManagementState> {
  final Ref _ref;

  InventoryReportManagementNotifier(this._ref) : super(const InventoryReportManagementState());

  InventoryReportService get _service => _ref.read(inventoryReportServiceProvider);
  DatabaseOperations get _dbOperations => _ref.read(databaseOperationsProvider);

  // Generate stock summary report
  Future<bool> generateStockSummaryReport({
    String? warehouseId,
    String? categoryId,
    DateTime? asOfDate,
  }) async {
    try {
      state = state.copyWith(
        isLoading: true,
        error: null,
        currentReportType: 'stock_summary',
      );

      final summaryData = await _dbOperations.executeWithReadyCheck(() async {
        return await _service.generateStockSummaryReport(
          warehouseId: warehouseId,
          categoryId: categoryId,
          asOfDate: asOfDate,
        );
      });

      state = state.copyWith(
        isLoading: false,
        stockSummary: summaryData,
        currentReportData: summaryData.toMap(),
        reportParameters: {
          'warehouse_id': warehouseId,
          'category_id': categoryId,
          'as_of_date': asOfDate?.toIso8601String(),
        },
      );

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error generating stock summary report', e);
      return false;
    }
  }

  // Generate movement analysis report
  Future<bool> generateMovementAnalysisReport({
    String? warehouseId,
    String? productId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      state = state.copyWith(
        isLoading: true,
        error: null,
        currentReportType: 'movement_analysis',
      );

      final analysisData = await _dbOperations.executeWithReadyCheck(() async {
        return await _service.generateMovementAnalysisReport(
          warehouseId: warehouseId,
          productId: productId,
          fromDate: fromDate,
          toDate: toDate,
        );
      });

      state = state.copyWith(
        isLoading: false,
        movementAnalysis: analysisData,
        currentReportData: analysisData,
        reportParameters: {
          'warehouse_id': warehouseId,
          'product_id': productId,
          'from_date': fromDate?.toIso8601String(),
          'to_date': toDate?.toIso8601String(),
        },
      );

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error generating movement analysis report', e);
      return false;
    }
  }

  // Generate low stock report
  Future<bool> generateLowStockReport({
    String? warehouseId,
    String? categoryId,
    double? threshold,
  }) async {
    try {
      state = state.copyWith(
        isLoading: true,
        error: null,
        currentReportType: 'low_stock',
      );

      final reportData = await _dbOperations.executeWithReadyCheck(() async {
        return await _service.generateLowStockReport(
          warehouseId: warehouseId,
          categoryId: categoryId,
          threshold: threshold,
        );
      });

      state = state.copyWith(
        isLoading: false,
        lowStockReport: reportData,
        currentReportData: reportData,
        reportParameters: {
          'warehouse_id': warehouseId,
          'category_id': categoryId,
          'threshold': threshold,
        },
      );

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error generating low stock report', e);
      return false;
    }
  }

  // Generate ABC analysis report
  Future<bool> generateABCAnalysisReport({
    String? warehouseId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      state = state.copyWith(
        isLoading: true,
        error: null,
        currentReportType: 'abc_analysis',
      );

      final analysisData = await _dbOperations.executeWithReadyCheck(() async {
        return await _service.generateABCAnalysisReport(
          warehouseId: warehouseId,
          fromDate: fromDate,
          toDate: toDate,
        );
      });

      state = state.copyWith(
        isLoading: false,
        abcAnalysis: analysisData,
        currentReportData: analysisData,
        reportParameters: {
          'warehouse_id': warehouseId,
          'from_date': fromDate?.toIso8601String(),
          'to_date': toDate?.toIso8601String(),
        },
      );

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error generating ABC analysis report', e);
      return false;
    }
  }

  // Set report parameters
  void setReportParameters(Map<String, dynamic> parameters) {
    state = state.copyWith(reportParameters: parameters);
  }

  // Clear current report
  void clearCurrentReport() {
    state = state.copyWith(
      currentReportType: null,
      currentReportData: {},
      reportParameters: {},
      stockSummary: null,
      movementAnalysis: {},
      lowStockReport: {},
      abcAnalysis: {},
      error: null,
    );
  }

  // Export report data
  Map<String, dynamic> exportCurrentReport() {
    return {
      'report_type': state.currentReportType,
      'parameters': state.reportParameters,
      'data': state.currentReportData,
      'generated_at': DateTime.now().toIso8601String(),
    };
  }

  // Get report summary
  Map<String, dynamic> getCurrentReportSummary() {
    switch (state.currentReportType) {
      case 'stock_summary':
        if (state.stockSummary != null) {
          return {
            'total_products': state.stockSummary!.totalProducts,
            'total_warehouses': state.stockSummary!.totalWarehouses,
            'total_value': state.stockSummary!.totalValue,
            'low_stock_items': state.stockSummary!.lowStockItems,
            'out_of_stock_items': state.stockSummary!.outOfStockItems,
          };
        }
        break;
      case 'movement_analysis':
        if (state.movementAnalysis.isNotEmpty) {
          final summary = state.movementAnalysis['summary'] as Map<String, dynamic>?;
          return summary ?? {};
        }
        break;
      case 'low_stock':
        if (state.lowStockReport.isNotEmpty) {
          final summary = state.lowStockReport['summary'] as Map<String, dynamic>?;
          return summary ?? {};
        }
        break;
      case 'abc_analysis':
        if (state.abcAnalysis.isNotEmpty) {
          final summary = state.abcAnalysis['summary'] as Map<String, dynamic>?;
          return summary ?? {};
        }
        break;
    }
    return {};
  }

  // Get chart data for current report
  Map<String, dynamic> getChartData() {
    switch (state.currentReportType) {
      case 'stock_summary':
        if (state.stockSummary != null) {
          return {
            'warehouse_distribution': state.stockSummary!.warehouseSummaries
                .map((w) => {'name': w.warehouseName, 'value': w.totalValue})
                .toList(),
            'stock_status': [
              {'name': 'مخزون طبيعي', 'value': state.stockSummary!.totalProducts - state.stockSummary!.lowStockItems - state.stockSummary!.outOfStockItems},
              {'name': 'مخزون منخفض', 'value': state.stockSummary!.lowStockItems},
              {'name': 'نفاد المخزون', 'value': state.stockSummary!.outOfStockItems},
            ],
          };
        }
        break;
      case 'movement_analysis':
        if (state.movementAnalysis.isNotEmpty) {
          final summary = state.movementAnalysis['summary'] as Map<String, dynamic>?;
          if (summary != null) {
            return {
              'movement_types': [
                {'name': 'دخول', 'value': summary['in_movements']},
                {'name': 'خروج', 'value': summary['out_movements']},
                {'name': 'نقل', 'value': summary['transfer_movements']},
                {'name': 'تسوية', 'value': summary['adjustment_movements']},
              ],
              'daily_movements': state.movementAnalysis['daily_analysis'] ?? {},
            };
          }
        }
        break;
      case 'abc_analysis':
        if (state.abcAnalysis.isNotEmpty) {
          final summary = state.abcAnalysis['summary'] as Map<String, dynamic>?;
          if (summary != null) {
            return {
              'abc_distribution': [
                {'name': 'فئة A', 'value': summary['a_products_count']},
                {'name': 'فئة B', 'value': summary['b_products_count']},
                {'name': 'فئة C', 'value': summary['c_products_count']},
              ],
              'value_distribution': [
                {'name': 'فئة A', 'value': summary['a_products_value']},
                {'name': 'فئة B', 'value': summary['b_products_value']},
                {'name': 'فئة C', 'value': summary['c_products_value']},
              ],
            };
          }
        }
        break;
    }
    return {};
  }
}
