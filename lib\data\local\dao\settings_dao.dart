import '../../../core/constants/database_constants.dart';
import '../../../core/utils/app_utils.dart';
import '../../models/settings.dart';
import 'base_dao.dart';

/// DAO للإعدادات - Settings Data Access Object
/// يدير عمليات قاعدة البيانات للإعدادات
class SettingsDao extends BaseDao<Settings> {
  @override
  String get tableName => DatabaseConstants.tableSettings;

  @override
  Settings fromMap(Map<String, dynamic> map) => Settings.fromMap(map);

  @override
  Map<String, dynamic> toMap(Settings entity) => entity.toMap();

  /// الحصول على إعداد بالمفتاح - Get setting by key
  Future<Settings?> getByKey(String key,
      {String? scope, String? branchId, String? userId}) async {
    try {
      String whereClause =
          '${DatabaseConstants.columnSettingsKey} = ? AND deleted_at IS NULL';
      List<dynamic> whereArgs = [key];

      if (scope != null) {
        if (scope == 'global') {
          whereClause += ' AND branch_id IS NULL AND user_id IS NULL';
        } else if (scope == 'branch' && branchId != null) {
          whereClause += ' AND branch_id = ? AND user_id IS NULL';
          whereArgs.add(branchId);
        } else if (scope == 'user' && userId != null) {
          whereClause += ' AND user_id = ?';
          whereArgs.add(userId);
        }
      } else {
        // البحث بدون تحديد نطاق
        if (branchId != null) {
          whereClause += ' AND branch_id = ?';
          whereArgs.add(branchId);
        } else {
          whereClause += ' AND branch_id IS NULL';
        }

        if (userId != null) {
          whereClause += ' AND user_id = ?';
          whereArgs.add(userId);
        } else {
          whereClause += ' AND user_id IS NULL';
        }
      }

      final results = await findWhere(
        where: whereClause,
        whereArgs: whereArgs,
        limit: 1,
      );

      return results.isNotEmpty ? results.first : null;
    } catch (e) {
      print('خطأ في الحصول على الإعداد: $e');
      return null;
    }
  }

  /// تحديث قيمة إعداد - Update setting value
  Future<bool> updateValue(String settingId, String value) async {
    try {
      final setting = await findById(settingId);
      if (setting == null) return false;

      final updatedSetting = setting.copyWith(
        value: value,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

      return await update(updatedSetting.id, updatedSetting);
    } catch (e) {
      print('خطأ في تحديث قيمة الإعداد: $e');
      return false;
    }
  }

  /// الحصول على الإعدادات حسب النطاق - Get settings by scope
  Future<List<Settings>> getByScope(String scope,
      {String? branchId, String? userId}) async {
    try {
      String whereClause = 'deleted_at IS NULL';
      List<dynamic> whereArgs = [];

      if (scope == 'global') {
        whereClause += ' AND branch_id IS NULL AND user_id IS NULL';
      } else if (scope == 'branch' && branchId != null) {
        whereClause += ' AND branch_id = ? AND user_id IS NULL';
        whereArgs.add(branchId);
      } else if (scope == 'user' && userId != null) {
        whereClause += ' AND user_id = ?';
        whereArgs.add(userId);
      }

      return await findWhere(
        where: whereClause,
        whereArgs: whereArgs,
        orderBy:
            '${DatabaseConstants.columnSettingsCategory}, ${DatabaseConstants.columnSettingsKey}',
      );
    } catch (e) {
      print('خطأ في الحصول على الإعدادات حسب النطاق: $e');
      return [];
    }
  }

  /// الحصول على الإعدادات حسب الفئة - Get settings by category
  Future<List<Settings>> getByCategory(String category,
      {String? scope, String? branchId, String? userId}) async {
    try {
      String whereClause =
          '${DatabaseConstants.columnSettingsCategory} = ? AND deleted_at IS NULL';
      List<dynamic> whereArgs = [category];

      if (scope != null) {
        if (scope == 'global') {
          whereClause += ' AND branch_id IS NULL AND user_id IS NULL';
        } else if (scope == 'branch' && branchId != null) {
          whereClause += ' AND branch_id = ? AND user_id IS NULL';
          whereArgs.add(branchId);
        } else if (scope == 'user' && userId != null) {
          whereClause += ' AND user_id = ?';
          whereArgs.add(userId);
        }
      }

      return await findWhere(
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: DatabaseConstants.columnSettingsKey,
      );
    } catch (e) {
      print('خطأ في الحصول على الإعدادات حسب الفئة: $e');
      return [];
    }
  }

  /// البحث في الإعدادات - Search settings
  Future<List<Settings>> searchSettings(String query) async {
    try {
      final whereClause = '''
        (${DatabaseConstants.columnSettingsKey} LIKE ? 
        OR ${DatabaseConstants.columnSettingsDescription} LIKE ? 
        OR ${DatabaseConstants.columnSettingsValue} LIKE ?) 
        AND deleted_at IS NULL
      ''';

      final whereArgs = ['%$query%', '%$query%', '%$query%'];

      return await findWhere(
        where: whereClause,
        whereArgs: whereArgs,
        orderBy:
            '${DatabaseConstants.columnSettingsCategory}, ${DatabaseConstants.columnSettingsKey}',
      );
    } catch (e) {
      print('خطأ في البحث في الإعدادات: $e');
      return [];
    }
  }

  /// الحصول على الفئات المتاحة - Get available categories
  Future<List<String>> getCategories() async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.rawQuery(
          'SELECT DISTINCT ${DatabaseConstants.columnSettingsCategory} FROM $tableName WHERE deleted_at IS NULL ORDER BY ${DatabaseConstants.columnSettingsCategory}');

      return maps
          .map((map) => map[DatabaseConstants.columnSettingsCategory] as String)
          .toList();
    } catch (e) {
      print('خطأ في الحصول على الفئات: $e');
      return [];
    }
  }

  /// تصدير الإعدادات - Export settings
  Future<Map<String, String>> exportSettings(
      {String? branchId, String? userId, String? category}) async {
    try {
      String whereClause = 'deleted_at IS NULL';
      List<dynamic> whereArgs = [];

      if (branchId != null) {
        whereClause += ' AND branch_id = ?';
        whereArgs.add(branchId);
      }

      if (userId != null) {
        whereClause += ' AND user_id = ?';
        whereArgs.add(userId);
      }

      if (category != null) {
        whereClause += ' AND ${DatabaseConstants.columnSettingsCategory} = ?';
        whereArgs.add(category);
      }

      final settings = await findWhere(
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: DatabaseConstants.columnSettingsKey,
      );

      Map<String, String> exportMap = {};
      for (var setting in settings) {
        exportMap[setting.key] = setting.value;
      }

      return exportMap;
    } catch (e) {
      print('خطأ في تصدير الإعدادات: $e');
      return {};
    }
  }

  /// استيراد الإعدادات - Import settings
  Future<bool> importSettings(Map<String, String> settings,
      {String? branchId, String? userId}) async {
    try {
      bool success = true;
      for (var entry in settings.entries) {
        final existingSetting =
            await getByKey(entry.key, branchId: branchId, userId: userId);

        if (existingSetting != null) {
          // تحديث الإعداد الموجود
          final updated = await updateValue(existingSetting.id, entry.value);
          if (!updated) success = false;
        } else {
          // إنشاء إعداد جديد
          final newSetting = Settings.create(
            id: AppUtils.generateId(),
            key: entry.key,
            value: entry.value,
            branchId: branchId,
            userId: userId,
          );

          final inserted = await insert(newSetting);
          if (inserted.isEmpty) success = false;
        }
      }
      return success;
    } catch (e) {
      print('خطأ في استيراد الإعدادات: $e');
      return false;
    }
  }

  /// إحصائيات الإعدادات - Settings statistics
  Future<Map<String, dynamic>> getSettingsStatistics() async {
    try {
      final db = await database;

      final totalResult = await db.rawQuery(
          'SELECT COUNT(*) as count FROM $tableName WHERE deleted_at IS NULL');

      final systemResult = await db.rawQuery(
          'SELECT COUNT(*) as count FROM $tableName WHERE ${DatabaseConstants.columnSettingsIsSystem} = 1 AND deleted_at IS NULL');

      final globalResult = await db.rawQuery(
          'SELECT COUNT(*) as count FROM $tableName WHERE branch_id IS NULL AND user_id IS NULL AND deleted_at IS NULL');

      final categoryResult = await db.rawQuery(
          'SELECT ${DatabaseConstants.columnSettingsCategory}, COUNT(*) as count FROM $tableName WHERE deleted_at IS NULL GROUP BY ${DatabaseConstants.columnSettingsCategory}');

      final dataTypeResult = await db.rawQuery(
          'SELECT ${DatabaseConstants.columnSettingsDataType}, COUNT(*) as count FROM $tableName WHERE deleted_at IS NULL GROUP BY ${DatabaseConstants.columnSettingsDataType}');

      Map<String, dynamic> stats = {
        'total': totalResult.first['count'] as int,
        'system': systemResult.first['count'] as int,
        'global': globalResult.first['count'] as int,
        'user_specific': (totalResult.first['count'] as int) -
            (globalResult.first['count'] as int),
        'categories': {},
        'data_types': {},
      };

      // إحصائيات الفئات
      for (var row in categoryResult) {
        stats['categories'][row[DatabaseConstants.columnSettingsCategory]] =
            row['count'];
      }

      // إحصائيات أنواع البيانات
      for (var row in dataTypeResult) {
        stats['data_types'][row[DatabaseConstants.columnSettingsDataType]] =
            row['count'];
      }

      return stats;
    } catch (e) {
      print('خطأ في الحصول على إحصائيات الإعدادات: $e');
      return {
        'total': 0,
        'system': 0,
        'global': 0,
        'user_specific': 0,
        'categories': {},
        'data_types': {},
      };
    }
  }
}
