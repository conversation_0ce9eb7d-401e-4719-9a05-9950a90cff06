// import 'package:flutter_test/flutter_test.dart';
// import 'package:sqflite_common_ffi/sqflite_ffi.dart';
// import '../lib/data/local/database.dart';
// import '../lib/data/local/dao/sale_dao.dart';
// import '../lib/data/models/sale.dart';
// import '../lib/data/models/sale_item.dart';
// import '../lib/core/utils/app_utils.dart';
// import '../lib/core/constants/database_constants.dart';

// void main() {
//   late Database database;
//   late SaleDao saleDao;

//   setUpAll(() {
//     // Initialize FFI
//     sqfliteFfiInit();
//     // Change the default factory
//     databaseFactory = databaseFactoryFfi;
//   });

//   setUp(() async {
//     // Create in-memory database for testing
//     database = await openDatabase(
//       ':memory:',
//       version: 1,
//       onCreate: (db, version) async {
//         final dbHelper = DatabaseHelper();
//         await dbHelper.createDatabase(db, version);
//       },
//     );
    
//     saleDao = SaleDao(database);
//   });

//   tearDown(() async {
//     await database.close();
//   });

//   group('Foreign Key Constraint Tests', () {
//     test('should verify all required foreign key records exist', () async {
//       // التحقق من وجود الوحدة الافتراضية
//       final units = await database.query(
//         DatabaseConstants.tableUnits,
//         where: '${DatabaseConstants.columnUnitId} = ?',
//         whereArgs: ['unit_piece'],
//       );
//       expect(units.isNotEmpty, true, reason: 'Default unit "unit_piece" should exist');

//       // التحقق من وجود المخزن الافتراضي
//       final warehouses = await database.query(
//         DatabaseConstants.tableWarehouses,
//         where: '${DatabaseConstants.columnWarehouseId} = ?',
//         whereArgs: ['main-warehouse'],
//       );
//       expect(warehouses.isNotEmpty, true, reason: 'Default warehouse "main-warehouse" should exist');

//       // التحقق من وجود المستخدم النظام
//       final users = await database.query(
//         DatabaseConstants.tableUsers,
//         where: '${DatabaseConstants.columnUserId} = ?',
//         whereArgs: ['system'],
//       );
//       expect(users.isNotEmpty, true, reason: 'System user should exist');
//     });

//     test('should create sale with valid foreign keys', () async {
//       // إنشاء منتج للاختبار
//       final productId = AppUtils.generateId();
//       await database.insert(DatabaseConstants.tableProducts, {
//         DatabaseConstants.columnProductId: productId,
//         DatabaseConstants.columnProductNameAr: 'منتج تجريبي',
//         DatabaseConstants.columnProductNameEn: 'Test Product',
//         DatabaseConstants.columnProductBarcode: '123456789',
//         DatabaseConstants.columnProductSellingPrice: 100.0,
//         DatabaseConstants.columnProductCostPrice: 80.0,
//         DatabaseConstants.columnProductBaseUnitId: 'unit_piece',
//         DatabaseConstants.columnProductCategoryId: 'default-category',
//         DatabaseConstants.columnProductTrackStock: 1,
//         DatabaseConstants.columnProductIsActive: 1,
//         DatabaseConstants.columnProductCreatedAt: DateTime.now().toIso8601String(),
//         DatabaseConstants.columnProductIsSynced: 0,
//       });

//       // إنشاء عميل للاختبار
//       final customerId = AppUtils.generateId();
//       await database.insert(DatabaseConstants.tableCustomers, {
//         DatabaseConstants.columnCustomerId: customerId,
//         DatabaseConstants.columnCustomerName: 'عميل تجريبي',
//         DatabaseConstants.columnCustomerPhone: '123456789',
//         DatabaseConstants.columnCustomerEmail: '<EMAIL>',
//         DatabaseConstants.columnCustomerBalance: 0.0,
//         DatabaseConstants.columnCustomerCreatedAt: DateTime.now().toIso8601String(),
//         DatabaseConstants.columnCustomerIsSynced: 0,
//       });

//       // إنشاء مبيعة
//       final sale = Sale.create(
//         id: AppUtils.generateId(),
//         customerId: customerId,
//         invoiceNo: 'TEST-001',
//         branchId: 'default-branch',
//         date: DateTime.now(),
//         total: 100.0,
//         paid: 100.0,
//         notes: 'اختبار المبيعة',
//       );

//       // إنشاء عنصر مبيعة
//       final saleItem = SaleItem.create(
//         id: AppUtils.generateId(),
//         saleId: sale.id,
//         productId: productId,
//         unitId: 'unit_piece', // استخدام الوحدة الافتراضية
//         qty: 1.0,
//         unitPrice: 100.0,
//       );

//       // محاولة إنشاء المبيعة
//       expect(() async {
//         await saleDao.createSale(sale, [saleItem]);
//       }, returnsNormally);
//     });

//     test('should handle empty unitId gracefully', () async {
//       // إنشاء منتج للاختبار
//       final productId = AppUtils.generateId();
//       await database.insert(DatabaseConstants.tableProducts, {
//         DatabaseConstants.columnProductId: productId,
//         DatabaseConstants.columnProductNameAr: 'منتج تجريبي 2',
//         DatabaseConstants.columnProductNameEn: 'Test Product 2',
//         DatabaseConstants.columnProductBarcode: '123456790',
//         DatabaseConstants.columnProductSellingPrice: 50.0,
//         DatabaseConstants.columnProductCostPrice: 40.0,
//         DatabaseConstants.columnProductBaseUnitId: 'unit_piece',
//         DatabaseConstants.columnProductCategoryId: 'default-category',
//         DatabaseConstants.columnProductTrackStock: 1,
//         DatabaseConstants.columnProductIsActive: 1,
//         DatabaseConstants.columnProductCreatedAt: DateTime.now().toIso8601String(),
//         DatabaseConstants.columnProductIsSynced: 0,
//       });

//       // إنشاء عميل للاختبار
//       final customerId = AppUtils.generateId();
//       await database.insert(DatabaseConstants.tableCustomers, {
//         DatabaseConstants.columnCustomerId: customerId,
//         DatabaseConstants.columnCustomerName: 'عميل تجريبي 2',
//         DatabaseConstants.columnCustomerPhone: '123456790',
//         DatabaseConstants.columnCustomerEmail: '<EMAIL>',
//         DatabaseConstants.columnCustomerBalance: 0.0,
//         DatabaseConstants.columnCustomerCreatedAt: DateTime.now().toIso8601String(),
//         DatabaseConstants.columnCustomerIsSynced: 0,
//       });

//       // إنشاء مبيعة
//       final sale = Sale.create(
//         id: AppUtils.generateId(),
//         customerId: customerId,
//         invoiceNo: 'TEST-002',
//         branchId: 'default-branch',
//         date: DateTime.now(),
//         total: 50.0,
//         paid: 50.0,
//         notes: 'اختبار unitId فارغ',
//       );

//       // إنشاء عنصر مبيعة بـ unitId فارغ
//       final saleItem = SaleItem.create(
//         id: AppUtils.generateId(),
//         saleId: sale.id,
//         productId: productId,
//         unitId: '', // unitId فارغ
//         qty: 1.0,
//         unitPrice: 50.0,
//       );

//       // يجب أن تنجح العملية لأن الكود سيستخدم الوحدة الافتراضية
//       expect(() async {
//         await saleDao.createSale(sale, [saleItem]);
//       }, returnsNormally);

//       // التحقق من أن حركة المخزون تم إنشاؤها بالوحدة الافتراضية
//       final stockMovements = await database.query(
//         DatabaseConstants.tableStockMovements,
//         where: '${DatabaseConstants.columnStockMovementReferenceId} = ?',
//         whereArgs: [sale.id],
//       );

//       expect(stockMovements.isNotEmpty, true);
//       expect(stockMovements.first[DatabaseConstants.columnStockMovementUnitId], 'unit_piece');
//     });

//     test('should verify stock movement creation with correct foreign keys', () async {
//       // إنشاء منتج للاختبار
//       final productId = AppUtils.generateId();
//       await database.insert(DatabaseConstants.tableProducts, {
//         DatabaseConstants.columnProductId: productId,
//         DatabaseConstants.columnProductNameAr: 'منتج تجريبي 3',
//         DatabaseConstants.columnProductNameEn: 'Test Product 3',
//         DatabaseConstants.columnProductBarcode: '123456791',
//         DatabaseConstants.columnProductSellingPrice: 75.0,
//         DatabaseConstants.columnProductCostPrice: 60.0,
//         DatabaseConstants.columnProductBaseUnitId: 'unit_piece',
//         DatabaseConstants.columnProductCategoryId: 'default-category',
//         DatabaseConstants.columnProductTrackStock: 1,
//         DatabaseConstants.columnProductIsActive: 1,
//         DatabaseConstants.columnProductCreatedAt: DateTime.now().toIso8601String(),
//         DatabaseConstants.columnProductIsSynced: 0,
//       });

//       // إضافة مخزون أولي
//       await database.insert(DatabaseConstants.tableStocks, {
//         DatabaseConstants.columnStockId: AppUtils.generateId(),
//         DatabaseConstants.columnStockProductId: productId,
//         DatabaseConstants.columnStockWarehouseId: 'main-warehouse',
//         DatabaseConstants.columnStockQuantity: 10.0,
//         DatabaseConstants.columnStockReorderPoint: 5.0,
//         DatabaseConstants.columnStockCreatedAt: DateTime.now().toIso8601String(),
//         DatabaseConstants.columnStockIsSynced: 0,
//       });

//       // إنشاء عميل للاختبار
//       final customerId = AppUtils.generateId();
//       await database.insert(DatabaseConstants.tableCustomers, {
//         DatabaseConstants.columnCustomerId: customerId,
//         DatabaseConstants.columnCustomerName: 'عميل تجريبي 3',
//         DatabaseConstants.columnCustomerPhone: '123456791',
//         DatabaseConstants.columnCustomerEmail: '<EMAIL>',
//         DatabaseConstants.columnCustomerBalance: 0.0,
//         DatabaseConstants.columnCustomerCreatedAt: DateTime.now().toIso8601String(),
//         DatabaseConstants.columnCustomerIsSynced: 0,
//       });

//       // إنشاء مبيعة
//       final sale = Sale.create(
//         id: AppUtils.generateId(),
//         customerId: customerId,
//         invoiceNo: 'TEST-003',
//         branchId: 'default-branch',
//         date: DateTime.now(),
//         total: 150.0,
//         paid: 150.0,
//         notes: 'اختبار حركة المخزون',
//       );

//       // إنشاء عنصر مبيعة
//       final saleItem = SaleItem.create(
//         id: AppUtils.generateId(),
//         saleId: sale.id,
//         productId: productId,
//         unitId: 'unit_piece',
//         qty: 2.0,
//         unitPrice: 75.0,
//       );

//       // إنشاء المبيعة
//       await saleDao.createSale(sale, [saleItem]);

//       // التحقق من إنشاء حركة المخزون
//       final stockMovements = await database.query(
//         DatabaseConstants.tableStockMovements,
//         where: '${DatabaseConstants.columnStockMovementReferenceId} = ?',
//         whereArgs: [sale.id],
//       );

//       expect(stockMovements.length, 1);
      
//       final movement = stockMovements.first;
//       expect(movement[DatabaseConstants.columnStockMovementProductId], productId);
//       expect(movement[DatabaseConstants.columnStockMovementWarehouseId], 'main-warehouse');
//       expect(movement[DatabaseConstants.columnStockMovementUnitId], 'unit_piece');
//       expect(movement[DatabaseConstants.columnStockMovementCreatedBy], 'system');
//       expect(movement[DatabaseConstants.columnStockMovementType], 'out');
//       expect(movement[DatabaseConstants.columnStockMovementQuantity], 2.0);
//     });
//   });
// }
