import 'package:json_annotation/json_annotation.dart';
import '../../core/constants/database_constants.dart';

part 'chart_of_accounts.g.dart';

/// نموذج دليل الحسابات - Chart of Accounts Model
@JsonSerializable()
class ChartOfAccounts {
  final String id;
  final String accountCode;   // رمز الحساب
  final String accountNameAr; // اسم الحساب بالعربية
  final String accountNameEn; // اسم الحساب بالإنجليزية
  final String accountType;   // نوع الحساب
  final String? parentId;     // معرف الحساب الأب
  final int level;            // مستوى الحساب
  final bool isActive;        // نشط؟
  final bool isSystem;        // حساب نظام؟
  final double balance;       // الرصيد
  final String? description;  // وصف
  final String? notes;        // ملاحظات
  final String createdBy;     // منشئ الحساب
  final DateTime createdAt;   // تاريخ الإنشاء
  final DateTime? updatedAt;  // تاريخ التحديث
  final DateTime? deletedAt;  // تاريخ الحذف
  final bool isSynced;        // مزامنة؟

  const ChartOfAccounts({
    required this.id,
    required this.accountCode,
    required this.accountNameAr,
    required this.accountNameEn,
    required this.accountType,
    this.parentId,
    this.level = 1,
    this.isActive = true,
    this.isSystem = false,
    this.balance = 0.0,
    this.description,
    this.notes,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  factory ChartOfAccounts.fromJson(Map<String, dynamic> json) =>
      _$ChartOfAccountsFromJson(json);
  Map<String, dynamic> toJson() => _$ChartOfAccountsToJson(this);

  factory ChartOfAccounts.fromMap(Map<String, dynamic> map) {
    return ChartOfAccounts(
      id: map[DatabaseConstants.columnChartOfAccountsId] as String,
      accountCode: map[DatabaseConstants.columnChartOfAccountsAccountCode] as String,
      accountNameAr: map[DatabaseConstants.columnChartOfAccountsAccountNameAr] as String,
      accountNameEn: map[DatabaseConstants.columnChartOfAccountsAccountNameEn] as String,
      accountType: map[DatabaseConstants.columnChartOfAccountsAccountType] as String,
      parentId: map[DatabaseConstants.columnChartOfAccountsParentId] as String?,
      level: (map[DatabaseConstants.columnChartOfAccountsLevel] as int?) ?? 1,
      isActive: (map[DatabaseConstants.columnChartOfAccountsIsActive] as int?) == 1,
      isSystem: (map[DatabaseConstants.columnChartOfAccountsIsSystem] as int?) == 1,
      balance: (map[DatabaseConstants.columnChartOfAccountsBalance] as num?)?.toDouble() ?? 0.0,
      description: map[DatabaseConstants.columnChartOfAccountsDescription] as String?,
      notes: map[DatabaseConstants.columnChartOfAccountsNotes] as String?,
      createdBy: map[DatabaseConstants.columnChartOfAccountsCreatedBy] as String,
      createdAt: DateTime.parse(map[DatabaseConstants.columnChartOfAccountsCreatedAt] as String),
      updatedAt: map[DatabaseConstants.columnChartOfAccountsUpdatedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnChartOfAccountsUpdatedAt] as String)
          : null,
      deletedAt: map[DatabaseConstants.columnChartOfAccountsDeletedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnChartOfAccountsDeletedAt] as String)
          : null,
      isSynced: (map[DatabaseConstants.columnChartOfAccountsIsSynced] as int) == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnChartOfAccountsId: id,
      DatabaseConstants.columnChartOfAccountsAccountCode: accountCode,
      DatabaseConstants.columnChartOfAccountsAccountNameAr: accountNameAr,
      DatabaseConstants.columnChartOfAccountsAccountNameEn: accountNameEn,
      DatabaseConstants.columnChartOfAccountsAccountType: accountType,
      DatabaseConstants.columnChartOfAccountsParentId: parentId,
      DatabaseConstants.columnChartOfAccountsLevel: level,
      DatabaseConstants.columnChartOfAccountsIsActive: isActive ? 1 : 0,
      DatabaseConstants.columnChartOfAccountsIsSystem: isSystem ? 1 : 0,
      DatabaseConstants.columnChartOfAccountsBalance: balance,
      DatabaseConstants.columnChartOfAccountsDescription: description,
      DatabaseConstants.columnChartOfAccountsNotes: notes,
      DatabaseConstants.columnChartOfAccountsCreatedBy: createdBy,
      DatabaseConstants.columnChartOfAccountsCreatedAt: createdAt.toIso8601String(),
      DatabaseConstants.columnChartOfAccountsUpdatedAt: updatedAt?.toIso8601String(),
      DatabaseConstants.columnChartOfAccountsDeletedAt: deletedAt?.toIso8601String(),
      DatabaseConstants.columnChartOfAccountsIsSynced: isSynced ? 1 : 0,
    };
  }

  ChartOfAccounts copyWith({
    String? id,
    String? accountCode,
    String? accountNameAr,
    String? accountNameEn,
    String? accountType,
    String? parentId,
    int? level,
    bool? isActive,
    bool? isSystem,
    double? balance,
    String? description,
    String? notes,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) =>
      ChartOfAccounts(
        id: id ?? this.id,
        accountCode: accountCode ?? this.accountCode,
        accountNameAr: accountNameAr ?? this.accountNameAr,
        accountNameEn: accountNameEn ?? this.accountNameEn,
        accountType: accountType ?? this.accountType,
        parentId: parentId ?? this.parentId,
        level: level ?? this.level,
        isActive: isActive ?? this.isActive,
        isSystem: isSystem ?? this.isSystem,
        balance: balance ?? this.balance,
        description: description ?? this.description,
        notes: notes ?? this.notes,
        createdBy: createdBy ?? this.createdBy,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        isSynced: isSynced ?? this.isSynced,
      );
}