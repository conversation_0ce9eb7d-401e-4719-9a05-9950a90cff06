import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:tijari_tech/core/utils/formatters.dart';
import 'package:tijari_tech/shared/widgets/loading_widget.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../data/models/purchase.dart';
import '../../data/models/purchase_item.dart';
import '../../data/repositories/purchase_repository.dart';
import '../../data/local/dao/supplier_dao.dart';
import '../../router/routes.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/custom_card.dart';
import '../../widgets/error_widget.dart';

class PurchaseViewPage extends ConsumerStatefulWidget {
  final String purchaseId;

  const PurchaseViewPage({
    super.key,
    required this.purchaseId,
  });

  @override
  ConsumerState<PurchaseViewPage> createState() => _PurchaseViewPageState();
}

class _PurchaseViewPageState extends ConsumerState<PurchaseViewPage> {
  Purchase? purchase;
  List<PurchaseItem> items = [];
  dynamic supplier;
  bool isLoading = true;
  String? error;

  @override
  void initState() {
    super.initState();
    _loadPurchaseData();
  }

  Future<void> _loadPurchaseData() async {
    try {
      setState(() {
        isLoading = true;
        error = null;
      });

      final purchaseRepository = PurchaseRepository();

      // Load purchase with items
      final purchaseData =
          await purchaseRepository.getPurchaseById(widget.purchaseId);
      if (purchaseData != null) {
        purchase =
            Purchase.fromMap(purchaseData['purchase'] as Map<String, dynamic>);

        // Get items from the data
        final itemsData = purchaseData['items'] as List<dynamic>;
        items = itemsData
            .map((item) => PurchaseItem.fromMap(item as Map<String, dynamic>))
            .toList();

        // Load supplier if exists (we'll skip this for now to avoid complexity)
        // if (purchase!.supplierId != null) {
        //   supplier = await supplierRepository.getSupplierById(purchase!.supplierId!);
        // }
      }

      setState(() => isLoading = false);
    } catch (e) {
      setState(() {
        error = e.toString();
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const MainLayout(
        title: 'تفاصيل فاتورة الشراء',
        child: LoadingWidget(),
      );
    }

    if (error != null || purchase == null) {
      return MainLayout(
        title: 'تفاصيل فاتورة الشراء',
        child: CustomErrorWidget(
          message: error ?? 'فاتورة الشراء غير موجودة',
          onRetry: _loadPurchaseData,
        ),
      );
    }

    return MainLayout(
      title: 'فاتورة شراء ${purchase!.invoiceNo}',
      actions: [
        IconButton(
          onPressed: () =>
              context.go('${AppRoutes.purchases}/${widget.purchaseId}/edit'),
          icon: const Icon(Icons.edit),
          tooltip: 'تعديل',
        ),
        IconButton(
          onPressed: _printInvoice,
          icon: const Icon(Icons.print),
          tooltip: 'طباعة',
        ),
      ],
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Invoice Header
            _buildInvoiceHeader(),
            SizedBox(height: 24.h),

            // Supplier Information
            _buildSupplierInfo(),
            SizedBox(height: 24.h),

            // Items List
            _buildItemsList(),
            SizedBox(height: 24.h),

            // Invoice Summary
            _buildInvoiceSummary(),
            SizedBox(height: 24.h),

            // Payment Information
            _buildPaymentInfo(),

            if (purchase!.notes != null && purchase!.notes!.isNotEmpty) ...[
              SizedBox(height: 24.h),
              _buildNotes(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceHeader() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(20.r),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(16.r),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Icon(
                Icons.shopping_cart,
                size: 32.r,
                color: AppColors.primary,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'فاتورة شراء',
                    style: AppTextStyles.headlineSmall.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    purchase!.invoiceNo ?? 'غير محدد',
                    style: AppTextStyles.titleLarge.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Row(
                    children: [
                      Icon(Icons.calendar_today,
                          size: 16.r, color: AppColors.onSecondary),
                      SizedBox(width: 8.w),
                      Text(
                        AppFormatters.formatDate(
                            purchase!.createdAt ?? DateTime.now()),
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.onSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            _buildStatusChip(purchase!.paymentStatus),
          ],
        ),
      ),
    );
  }

  Widget _buildSupplierInfo() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(20.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.business, color: AppColors.primary, size: 20.r),
                SizedBox(width: 8.w),
                Text(
                  'معلومات المورد',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            if (supplier != null) ...[
              _buildInfoRow('اسم المورد', supplier.name),
              if (supplier.phone != null && supplier.phone!.isNotEmpty)
                _buildInfoRow('الهاتف', supplier.phone!),
              if (supplier.email != null && supplier.email!.isNotEmpty)
                _buildInfoRow('البريد الإلكتروني', supplier.email!),
              if (supplier.address != null && supplier.address!.isNotEmpty)
                _buildInfoRow('العنوان', supplier.address!),
            ] else ...[
              Text(
                'مورد نقدي',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.onSecondary,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildItemsList() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(20.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.inventory_2, color: AppColors.primary, size: 20.r),
                SizedBox(width: 8.w),
                Text(
                  'عناصر الفاتورة',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  child: Text(
                    '${items.length} عنصر',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: items.length,
              separatorBuilder: (context, index) => Divider(height: 24.h),
              itemBuilder: (context, index) {
                final item = items[index];
                return _buildItemRow(item);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemRow(PurchaseItem item) {
    return Row(
      children: [
        Container(
          width: 40.w,
          height: 40.w,
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(
            Icons.inventory,
            color: AppColors.primary,
            size: 20.r,
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'منتج ${item.productId}', // TODO: Get actual product name
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                'الكمية: ${AppFormatters.formatNumber(item.qty)} • السعر: ${AppFormatters.formatCurrency(item.unitPrice)}',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.onSecondary,
                ),
              ),
            ],
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              AppFormatters.formatCurrency(item.totalPrice),
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInvoiceSummary() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(20.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.calculate, color: AppColors.primary, size: 20.r),
                SizedBox(width: 8.w),
                Text(
                  'ملخص الفاتورة',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            _buildSummaryRow('الإجمالي', purchase!.totalAmount, isTotal: true),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInfo() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(20.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.payment, color: AppColors.primary, size: 20.r),
                SizedBox(width: 8.w),
                Text(
                  'معلومات الدفع',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            _buildInfoRow('طريقة الدفع', 'نقدي'), // Default payment method
            _buildSummaryRow('المدفوع', purchase!.paidAmount,
                color: AppColors.success),
            if (purchase!.dueAmount > 0)
              _buildSummaryRow('المستحق', purchase!.dueAmount,
                  color: AppColors.warning),
          ],
        ),
      ),
    );
  }

  Widget _buildNotes() {
    return CustomCard(
      child: Padding(
        padding: EdgeInsets.all(20.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.note, color: AppColors.primary, size: 20.r),
                SizedBox(width: 8.w),
                Text(
                  'ملاحظات',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.r),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                purchase!.notes!,
                style: AppTextStyles.bodyMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    final color = _getStatusColor(status);
    final text = _getStatusText(status);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: AppTextStyles.bodyMedium.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120.w,
            child: Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.onSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, double amount,
      {bool isTotal = false, bool isDiscount = false, Color? color}) {
    return Padding(
      padding: EdgeInsets.only(bottom: isTotal ? 0 : 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: isTotal
                ? AppTextStyles.titleMedium
                    .copyWith(fontWeight: FontWeight.bold)
                : AppTextStyles.bodyMedium
                    .copyWith(color: AppColors.onSecondary),
          ),
          Text(
            AppFormatters.formatCurrency(amount.abs()),
            style: isTotal
                ? AppTextStyles.titleLarge.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color ?? AppColors.primary,
                  )
                : AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: color ??
                        (isDiscount ? AppColors.success : AppColors.onSurface),
                  ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'completed':
        return AppColors.success;
      case 'pending':
        return AppColors.warning;
      case 'cancelled':
        return AppColors.error;
      default:
        return AppColors.onSecondary;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'completed':
        return 'مكتملة';
      case 'pending':
        return 'معلقة';
      case 'cancelled':
        return 'ملغية';
      default:
        return 'غير محدد';
    }
  }

  void _printInvoice() {
    // TODO: Implement print functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تنفيذ الطباعة قريباً')),
    );
  }
}
