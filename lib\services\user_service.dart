import '../data/local/dao/user_dao.dart';
import '../data/models/user.dart';
import '../core/utils/app_utils.dart';

/// خدمة إدارة المستخدمين - User Service
/// تدير المستخدمين والصلاحيات والمصادقة
/// Manages users, permissions, and authentication
class UserService {
  final UserDao _userDao;
  final AuditLogDao _auditLogDao;

  UserService({
    required UserDao userDao,
    required AuditLogDao auditLogDao,
  })  : _userDao = userDao,
        _auditLogDao = auditLogDao;

  /// إنشاء مستخدم جديد - Create new user
  /// ينشئ مستخدم جديد مع التحقق من صحة البيانات
  Future<String?> createUser({
    required String username,
    required String email,
    required String password,
    required String fullName,
    required String role,
    String? phoneNumber,
    String? branchId,
    bool isActive = true,
    String? createdByUserId,
    Map<String, dynamic>? permissions,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // التحقق من صحة البيانات
      await _validateUserData(
        username: username,
        email: email,
        password: password,
        fullName: fullName,
        role: role,
      );

      // التحقق من عدم تكرار اسم المستخدم والبريد الإلكتروني
      final existingUserByUsername = await _userDao.getByUsername(username);
      if (existingUserByUsername != null) {
        throw Exception('اسم المستخدم موجود مسبقاً: $username');
      }

      final existingUserByEmail = await _userDao.getByEmail(email);
      if (existingUserByEmail != null) {
        throw Exception('البريد الإلكتروني موجود مسبقاً: $email');
      }

      // تشفير كلمة المرور
      final hashedPassword = _hashPassword(password);

      // إنشاء المستخدم الجديد
      final user = User.create(
        id: AppUtils.generateId(),
        username: username,
        email: email,
        passwordHash: hashedPassword,
        fullName: fullName,
        role: role,
        phoneNumber: phoneNumber,
        branchId: branchId,
        isActive: isActive,
        permissions: permissions ?? _getDefaultPermissions(role),
        metadata: metadata,
      );

      // حفظ المستخدم
      final userId = await _userDao.insert(user);
      if (userId == null) {
        throw Exception('فشل في حفظ المستخدم الجديد');
      }

      // تسجيل العملية
      await _auditLogDao.logOperation(
        tableName: 'users',
        recordId: userId,
        action: 'create',
        description: 'إنشاء مستخدم جديد: $fullName',
        userId: createdByUserId ?? 'system',
        category: 'user_management',
        severity: 'medium',
        metadata: {
          'username': username,
          'email': email,
          'role': role,
          'branch_id': branchId,
        },
        branchId: branchId,
      );

      return userId;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'users',
        recordId: 'failed',
        action: 'create',
        description: 'فشل في إنشاء مستخدم جديد: $fullName',
        userId: createdByUserId ?? 'system',
        category: 'user_management',
        severity: 'high',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      rethrow;
    }
  }

  /// التحقق من صحة بيانات المستخدم - Validate user data
  Future<void> _validateUserData({
    required String username,
    required String email,
    required String password,
    required String fullName,
    required String role,
  }) async {
    // التحقق من اسم المستخدم
    if (username.trim().isEmpty) {
      throw Exception('اسم المستخدم مطلوب');
    }

    if (username.length < 3) {
      throw Exception('اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
    }

    if (username.length > 50) {
      throw Exception('اسم المستخدم طويل جداً (الحد الأقصى 50 حرف)');
    }

    // التحقق من البريد الإلكتروني
    if (email.trim().isEmpty) {
      throw Exception('البريد الإلكتروني مطلوب');
    }

    if (!_isValidEmail(email)) {
      throw Exception('البريد الإلكتروني غير صحيح');
    }

    // التحقق من كلمة المرور
    if (password.length < 6) {
      throw Exception('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
    }

    // التحقق من الاسم الكامل
    if (fullName.trim().isEmpty) {
      throw Exception('الاسم الكامل مطلوب');
    }

    if (fullName.length > 100) {
      throw Exception('الاسم الكامل طويل جداً (الحد الأقصى 100 حرف)');
    }

    // التحقق من الدور
    const validRoles = ['مدير النظام', 'مدير', 'محاسب', 'مستخدم'];
    if (!validRoles.contains(role)) {
      throw Exception('الدور غير صحيح. الأدوار المسموحة: ${validRoles.join(', ')}');
    }
  }

  /// التحقق من صحة البريد الإلكتروني - Validate email format
  bool _isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email);
  }

  /// تشفير كلمة المرور - Hash password
  String _hashPassword(String password) {
    // في التطبيق الحقيقي، استخدم مكتبة تشفير قوية مثل bcrypt
    // هنا نستخدم تشفير بسيط للتطوير
    return password.hashCode.toString();
  }

  /// الحصول على الصلاحيات الافتراضية - Get default permissions
  Map<String, dynamic> _getDefaultPermissions(String role) {
    switch (role) {
      case 'مدير النظام':
        return {
          'can_create': true,
          'can_read': true,
          'can_update': true,
          'can_delete': true,
          'can_approve': true,
          'can_manage_users': true,
          'can_manage_settings': true,
          'can_view_reports': true,
          'can_export_data': true,
        };
      case 'مدير':
        return {
          'can_create': true,
          'can_read': true,
          'can_update': true,
          'can_delete': true,
          'can_approve': true,
          'can_manage_users': false,
          'can_manage_settings': false,
          'can_view_reports': true,
          'can_export_data': true,
        };
      case 'محاسب':
        return {
          'can_create': true,
          'can_read': true,
          'can_update': true,
          'can_delete': false,
          'can_approve': false,
          'can_manage_users': false,
          'can_manage_settings': false,
          'can_view_reports': true,
          'can_export_data': false,
        };
      case 'مستخدم':
      default:
        return {
          'can_create': false,
          'can_read': true,
          'can_update': false,
          'can_delete': false,
          'can_approve': false,
          'can_manage_users': false,
          'can_manage_settings': false,
          'can_view_reports': false,
          'can_export_data': false,
        };
    }
  }

  /// تسجيل الدخول - User login
  Future<User?> login({
    required String usernameOrEmail,
    required String password,
    String? ipAddress,
    String? userAgent,
  }) async {
    try {
      // البحث عن المستخدم بالاسم أو البريد الإلكتروني
      User? user;
      if (_isValidEmail(usernameOrEmail)) {
        user = await _userDao.getByEmail(usernameOrEmail);
      } else {
        user = await _userDao.getByUsername(usernameOrEmail);
      }

      if (user == null) {
        throw Exception('اسم المستخدم أو البريد الإلكتروني غير صحيح');
      }

      // التحقق من حالة المستخدم
      if (!user.isActive) {
        throw Exception('الحساب غير نشط');
      }

      // التحقق من كلمة المرور
      final hashedPassword = _hashPassword(password);
      if (user.passwordHash != hashedPassword) {
        // تسجيل محاولة دخول فاشلة
        await _auditLogDao.logOperation(
          tableName: 'users',
          recordId: user.id,
          action: 'login_failed',
          description: 'محاولة دخول فاشلة: كلمة مرور خاطئة',
          userId: user.id,
          category: 'authentication',
          severity: 'medium',
          isSuccessful: false,
          ipAddress: ipAddress,
          metadata: {
            'username_or_email': usernameOrEmail,
            'user_agent': userAgent,
          },
          branchId: user.branchId,
        );

        throw Exception('كلمة المرور غير صحيحة');
      }

      // تحديث آخر تسجيل دخول
      await _userDao.updateLastLogin(user.id);

      // تسجيل تسجيل الدخول الناجح
      await _auditLogDao.logOperation(
        tableName: 'users',
        recordId: user.id,
        action: 'login_success',
        description: 'تسجيل دخول ناجح',
        userId: user.id,
        category: 'authentication',
        severity: 'low',
        ipAddress: ipAddress,
        metadata: {
          'username_or_email': usernameOrEmail,
          'user_agent': userAgent,
        },
        branchId: user.branchId,
      );

      return user;
    } catch (e) {
      // تسجيل محاولة دخول فاشلة عامة
      await _auditLogDao.logOperation(
        tableName: 'users',
        recordId: 'unknown',
        action: 'login_failed',
        description: 'محاولة دخول فاشلة: ${e.toString()}',
        userId: 'anonymous',
        category: 'authentication',
        severity: 'high',
        isSuccessful: false,
        errorMessage: e.toString(),
        ipAddress: ipAddress,
        metadata: {
          'username_or_email': usernameOrEmail,
          'user_agent': userAgent,
        },
      );

      rethrow;
    }
  }

  /// تحديث معلومات المستخدم - Update user information
  Future<bool> updateUser({
    required String userId,
    String? username,
    String? email,
    String? fullName,
    String? phoneNumber,
    String? role,
    String? branchId,
    bool? isActive,
    Map<String, dynamic>? permissions,
    String? updatedByUserId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // الحصول على المستخدم الحالي
      final currentUser = await _userDao.getById(userId);
      if (currentUser == null) {
        throw Exception('المستخدم غير موجود');
      }

      // التحقق من التحديثات المطلوبة
      if (username != null || email != null || fullName != null || role != null) {
        await _validateUserData(
          username: username ?? currentUser.username,
          email: email ?? currentUser.email,
          password: 'dummy', // كلمة مرور وهمية للتحقق
          fullName: fullName ?? currentUser.fullName,
          role: role ?? currentUser.role,
        );
      }

      // التحقق من عدم تكرار اسم المستخدم الجديد
      if (username != null && username != currentUser.username) {
        final existingUser = await _userDao.getByUsername(username);
        if (existingUser != null) {
          throw Exception('اسم المستخدم موجود مسبقاً: $username');
        }
      }

      // التحقق من عدم تكرار البريد الإلكتروني الجديد
      if (email != null && email != currentUser.email) {
        final existingUser = await _userDao.getByEmail(email);
        if (existingUser != null) {
          throw Exception('البريد الإلكتروني موجود مسبقاً: $email');
        }
      }

      // إنشاء المستخدم المحدث
      final updatedUser = currentUser.copyWith(
        username: username,
        email: email,
        fullName: fullName,
        phoneNumber: phoneNumber,
        role: role,
        branchId: branchId,
        isActive: isActive,
        permissions: permissions,
        metadata: metadata,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

      // حفظ التحديثات
      final success = await _userDao.update(updatedUser);

      if (success) {
        // تسجيل العملية
        await _auditLogDao.logOperation(
          tableName: 'users',
          recordId: userId,
          action: 'update',
          description: 'تحديث معلومات المستخدم: ${updatedUser.fullName}',
          userId: updatedByUserId ?? 'system',
          category: 'user_management',
          severity: 'medium',
          oldValues: currentUser.toMap().toString(),
          newValues: updatedUser.toMap().toString(),
          branchId: currentUser.branchId,
        );
      }

      return success;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'users',
        recordId: userId,
        action: 'update',
        description: 'فشل في تحديث معلومات المستخدم',
        userId: updatedByUserId ?? 'system',
        category: 'user_management',
        severity: 'high',
        isSuccessful: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// تغيير كلمة المرور - Change password
  Future<bool> changePassword({
    required String userId,
    required String currentPassword,
    required String newPassword,
    String? changedByUserId,
  }) async {
    try {
      // الحصول على المستخدم
      final user = await _userDao.getById(userId);
      if (user == null) {
        throw Exception('المستخدم غير موجود');
      }

      // التحقق من كلمة المرور الحالية
      final currentHashedPassword = _hashPassword(currentPassword);
      if (user.passwordHash != currentHashedPassword) {
        throw Exception('كلمة المرور الحالية غير صحيحة');
      }

      // التحقق من كلمة المرور الجديدة
      if (newPassword.length < 6) {
        throw Exception('كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل');
      }

      // تشفير كلمة المرور الجديدة
      final newHashedPassword = _hashPassword(newPassword);

      // تحديث كلمة المرور
      final success = await _userDao.updatePassword(userId, newHashedPassword);

      if (success) {
        // تسجيل العملية
        await _auditLogDao.logOperation(
          tableName: 'users',
          recordId: userId,
          action: 'change_password',
          description: 'تغيير كلمة المرور للمستخدم: ${user.fullName}',
          userId: changedByUserId ?? userId,
          category: 'user_management',
          severity: 'medium',
          branchId: user.branchId,
        );
      }

      return success;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'users',
        recordId: userId,
        action: 'change_password',
        description: 'فشل في تغيير كلمة المرور',
        userId: changedByUserId ?? userId,
        category: 'user_management',
        severity: 'high',
        isSuccessful: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// تحديث صلاحيات المستخدم - Update user permissions
  Future<bool> updateUserPermissions({
    required String userId,
    required Map<String, dynamic> permissions,
    required String updatedByUserId,
  }) async {
    try {
      final user = await _userDao.getById(userId);
      if (user == null) {
        throw Exception('المستخدم غير موجود');
      }

      final success = await _userDao.updatePermissions(userId, permissions);

      if (success) {
        // تسجيل العملية
        await _auditLogDao.logOperation(
          tableName: 'users',
          recordId: userId,
          action: 'update_permissions',
          description: 'تحديث صلاحيات المستخدم: ${user.fullName}',
          userId: updatedByUserId,
          category: 'user_management',
          severity: 'high',
          oldValues: user.permissions.toString(),
          newValues: permissions.toString(),
          branchId: user.branchId,
        );
      }

      return success;
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'users',
        recordId: userId,
        action: 'update_permissions',
        description: 'فشل في تحديث صلاحيات المستخدم',
        userId: updatedByUserId,
        category: 'user_management',
        severity: 'high',
        isSuccessful: false,
        errorMessage: e.toString(),
      );

      rethrow;
    }
  }

  /// التحقق من صلاحية المستخدم - Check user permission
  Future<bool> hasPermission({
    required String userId,
    required String permission,
  }) async {
    try {
      final user = await _userDao.getById(userId);
      if (user == null || !user.isActive) {
        return false;
      }

      // مدير النظام له جميع الصلاحيات
      if (user.role == 'مدير النظام') {
        return true;
      }

      return user.permissions[permission] == true;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على المستخدمين حسب الفرع - Get users by branch
  Future<List<User>> getUsersByBranch({
    required String branchId,
    bool activeOnly = true,
  }) async {
    try {
      return await _userDao.getByBranch(branchId, activeOnly: activeOnly);
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'users',
        recordId: 'by_branch',
        action: 'get_users_by_branch',
        description: 'فشل في الحصول على المستخدمين حسب الفرع',
        userId: 'system',
        category: 'user_management',
        severity: 'low',
        isSuccessful: false,
        errorMessage: e.toString(),
        branchId: branchId,
      );

      return [];
    }
  }

  /// الحصول على المستخدمين حسب الدور - Get users by role
  Future<List<User>> getUsersByRole({
    required String role,
    bool activeOnly = true,
  }) async {
    try {
      return await _userDao.getByRole(role, activeOnly: activeOnly);
    } catch (e) {
      await _auditLogDao.logOperation(
        tableName: 'users',
        recordId: 'by_role',
        action: 'get_users_by_role',
        description: 'فشل في الحصول على المستخدمين حسب الدور',
        userId: 'system',
        category: 'user_management',
        severity: 'low',
        isSuccessful: false,
        errorMessage: e.toString(),
        metadata: {
          'role': role,
        },
      );

      return [];
    }
  }
}
