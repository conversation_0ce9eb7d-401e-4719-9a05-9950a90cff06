import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:tijari_tech/core/utils/formatters.dart';
import 'package:tijari_tech/widgets/custom_drowpdown.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../data/models/purchase.dart';
import '../../router/routes.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/custom_card.dart';
import '../../widgets/empty_state_widget.dart';

class PurchasesListPage extends ConsumerStatefulWidget {
  const PurchasesListPage({super.key});

  @override
  ConsumerState<PurchasesListPage> createState() => _PurchasesListPageState();
}

class _PurchasesListPageState extends ConsumerState<PurchasesListPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedStatus = 'all';
  String _selectedSupplier = 'all';
  DateTimeRange? _dateRange;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // We'll load data manually for now
      _loadPurchasesData();
    });
  }

  Future<void> _loadPurchasesData() async {
    // TODO: Implement data loading
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // For now, we'll use empty data until we implement proper providers
    final purchases = <Purchase>[];
    final suppliers = <dynamic>[];

    return MainLayout(
      title: 'قائمة المشتريات',
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => context.go(AppRoutes.purchaseAdd),
        icon: const Icon(Icons.add_shopping_cart),
        label: const Text('فاتورة جديدة'),
      ),
      child: Column(
        children: [
          // Statistics Cards
          _buildStatisticsCards(purchases),

          // Search and Filters
          _buildSearchAndFilters(suppliers),

          // Purchases List
          Expanded(
            child: _buildPurchasesList(purchases),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsCards(List<Purchase> purchases) {
    final totalPurchases = purchases.length;
    final totalAmount = purchases.fold<double>(
        0, (sum, purchase) => sum + purchase.totalAmount);
    final paidAmount =
        purchases.fold<double>(0, (sum, purchase) => sum + purchase.paidAmount);
    final dueAmount = totalAmount - paidAmount;
    final completedPurchases = purchases.where((p) => p.dueAmount == 0).length;

    return Container(
      padding: EdgeInsets.all(16.r),
      child: Row(
        children: [
          Expanded(
            child: CustomCard(
              child: Column(
                children: [
                  Icon(Icons.shopping_cart,
                      size: 32.r, color: AppColors.primary),
                  SizedBox(height: 8.h),
                  Text(
                    totalPurchases.toString(),
                    style: AppTextStyles.headlineMedium.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text('إجمالي الفواتير', style: AppTextStyles.bodySmall),
                ],
              ),
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: CustomCard(
              child: Column(
                children: [
                  Icon(Icons.attach_money,
                      size: 32.r, color: AppColors.success),
                  SizedBox(height: 8.h),
                  Text(
                    AppFormatters.formatCurrency(totalAmount),
                    style: AppTextStyles.titleMedium.copyWith(
                      color: AppColors.success,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text('إجمالي المبلغ', style: AppTextStyles.bodySmall),
                ],
              ),
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: CustomCard(
              child: Column(
                children: [
                  Icon(Icons.payment, size: 32.r, color: AppColors.info),
                  SizedBox(height: 8.h),
                  Text(
                    AppFormatters.formatCurrency(paidAmount),
                    style: AppTextStyles.titleMedium.copyWith(
                      color: AppColors.info,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text('المدفوع', style: AppTextStyles.bodySmall),
                ],
              ),
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: CustomCard(
              child: Column(
                children: [
                  Icon(Icons.pending_actions,
                      size: 32.r, color: AppColors.warning),
                  SizedBox(height: 8.h),
                  Text(
                    AppFormatters.formatCurrency(dueAmount),
                    style: AppTextStyles.titleMedium.copyWith(
                      color: AppColors.warning,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text('المستحق', style: AppTextStyles.bodySmall),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters(List<dynamic> suppliers) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Column(
        children: [
          // Search Bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في الفواتير...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _searchQuery = '');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            onChanged: (value) => setState(() => _searchQuery = value),
          ),

          SizedBox(height: 12.h),

          // Filters Row - Scrollable
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Status Filter
            
              Expanded(
                child: AppFilterDropdown(
                 label: 'الحالة',
                  value: _selectedStatus,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                    DropdownMenuItem(value: 'pending', child: Text('معلقة')),
                    DropdownMenuItem(value: 'completed', child: Text('مكتملة')),
                    DropdownMenuItem(value: 'cancelled', child: Text('ملغية')),
                  ],
                  onChanged: (value) =>
                      setState(() => _selectedStatus = value!),
                ),
              ),
              SizedBox(width: 5.w),

              // Expanded(
              //   child: _buildFilterDropdown(
              //     label: 'المورد',
              //     value: _selectedSupplier,
              //     items: [
              //       DropdownMenuItem(
              //           value: 'all', child: Text('جميع الموردين')),
              //       ...suppliers.map((supplier) => DropdownMenuItem(
              //             value: supplier.id,
              //             child: Text(supplier.name),
              //           )),
              //     ],
              //     onChanged: (value) =>
              //         setState(() => _selectedSupplier = value!),
              //   ),
              // ),
              // Expanded(
              //   child: _buildFilterDropdown(
              //     label: 'المورد',
              //     value: _selectedSupplier,
              //     items: [
              //       DropdownMenuItem(
              //           value: 'all', child: Text('جميع الموردين')),
              //       ...suppliers.map((supplier) => DropdownMenuItem(
              //             value: supplier.id,
              //             child: Text(supplier.name),
              //           )),
              //     ],
              //     onChanged: (value) =>
              //         setState(() => _selectedSupplier = value!),
              //   ),
              // ),
              Expanded(
                child: AppFilterDropdown(
                  label: 'المورد',
                  value: _selectedSupplier,
                  items: [
                    DropdownMenuItem(
                        value: 'all', child: Text('جميع الموردين')),
                    ...suppliers.map((supplier) => DropdownMenuItem(
                          value: supplier.id,
                          child: Text(supplier.name),
                        )),
                  ],
                  onChanged: (value) =>
                      setState(() => _selectedSupplier = value!),
                ),
              ),

              // Supplier Filter

              SizedBox(width: 12.w),

              // Date Range Filter
              OutlinedButton.icon(
                onPressed: _selectDateRange,
                icon: const Icon(Icons.date_range),
                label: Text(_dateRange == null ? 'التاريخ' : 'مفلتر'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildFilterDropdown({
    required String label,
    required String value,
    required List<DropdownMenuItem<String>> items,
    required ValueChanged<String?> onChanged,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
        contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      ),
      items: items,
      onChanged: onChanged,
      isExpanded: true,
    );
  }

  Widget _buildPurchasesList(List<Purchase> purchases) {
    final filteredPurchases = _filterPurchases(purchases);

    if (filteredPurchases.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.shopping_cart_outlined,
        title: 'لا توجد فواتير مشتريات',
        subtitle: 'ابدأ بإضافة فاتورة مشتريات جديدة',
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      itemCount: filteredPurchases.length,
      itemBuilder: (context, index) {
        final purchase = filteredPurchases[index];
        return _buildPurchaseCard(purchase);
      },
    );
  }

  Widget _buildPurchaseCard(Purchase purchase) {
    return CustomCard(
      margin: EdgeInsets.only(bottom: 12.h),
      child: ListTile(
        contentPadding: EdgeInsets.all(16.r),
        leading: CircleAvatar(
          backgroundColor:
              _getStatusColor(purchase.paymentStatus).withOpacity(0.1),
          child: Icon(
            Icons.shopping_cart,
            color: _getStatusColor(purchase.paymentStatus),
          ),
        ),
        title: Row(
          children: [
            Text(
              purchase.invoiceNo ?? 'غير محدد',
              style: AppTextStyles.titleMedium
                  .copyWith(fontWeight: FontWeight.bold),
            ),
            const Spacer(),
            _buildStatusChip(purchase.paymentStatus),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 8.h),
            Text(
                'التاريخ: ${AppFormatters.formatDate(purchase.createdAt ?? DateTime.now())}'),
            Text(
                'المبلغ: ${AppFormatters.formatCurrency(purchase.totalAmount)}'),
            if (purchase.dueAmount > 0)
              Text(
                'المستحق: ${AppFormatters.formatCurrency(purchase.dueAmount)}',
                style: TextStyle(color: AppColors.error),
              ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(value, purchase),
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'view', child: Text('عرض')),
            const PopupMenuItem(value: 'edit', child: Text('تعديل')),
            const PopupMenuItem(value: 'delete', child: Text('حذف')),
          ],
        ),
        onTap: () => context.go('${AppRoutes.purchases}/${purchase.id}'),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    final color = _getStatusColor(status);
    final text = _getStatusText(status);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: AppTextStyles.bodySmall.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'completed':
        return AppColors.success;
      case 'pending':
        return AppColors.warning;
      case 'cancelled':
        return AppColors.error;
      default:
        return AppColors.onSecondary;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'completed':
        return 'مكتملة';
      case 'pending':
        return 'معلقة';
      case 'cancelled':
        return 'ملغية';
      default:
        return 'غير محدد';
    }
  }

  List<Purchase> _filterPurchases(List<Purchase> purchases) {
    return purchases.where((purchase) {
      // Search filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!(purchase.invoiceNo?.toLowerCase().contains(query) ?? false) &&
            !(purchase.notes?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // Status filter (using payment status)
      if (_selectedStatus != 'all' &&
          purchase.paymentStatus != _selectedStatus) {
        return false;
      }

      // Supplier filter
      if (_selectedSupplier != 'all' &&
          purchase.supplierId != _selectedSupplier) {
        return false;
      }

      // Date range filter
      if (_dateRange != null) {
        final purchaseDate = purchase.createdAt;
        if (purchaseDate != null &&
            (purchaseDate.isBefore(_dateRange!.start) ||
                purchaseDate
                    .isAfter(_dateRange!.end.add(const Duration(days: 1))))) {
          return false;
        }
      }

      return true;
    }).toList();
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _dateRange,
    );

    if (picked != null) {
      setState(() => _dateRange = picked);
    }
  }

  void _handleMenuAction(String action, Purchase purchase) {
    switch (action) {
      case 'view':
        context.go('${AppRoutes.purchases}/${purchase.id}');
        break;
      case 'edit':
        context.go('${AppRoutes.purchases}/${purchase.id}/edit');
        break;
      case 'delete':
        _showDeleteDialog(purchase);
        break;
    }
  }

  void _showDeleteDialog(Purchase purchase) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content:
            Text('هل أنت متأكد من حذف فاتورة المشتريات ${purchase.invoiceNo}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement delete functionality
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
