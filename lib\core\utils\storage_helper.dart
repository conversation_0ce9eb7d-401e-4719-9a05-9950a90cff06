import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';
import 'app_utils.dart';

class StorageHelper {
  static SharedPreferences? _prefs;
  
  // Initialize shared preferences
  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }
  
  // Ensure preferences are initialized
  static Future<SharedPreferences> get _preferences async {
    if (_prefs == null) {
      await init();
    }
    return _prefs!;
  }
  
  // String operations
  static Future<bool> setString(String key, String value) async {
    try {
      final prefs = await _preferences;
      return await prefs.setString(key, value);
    } catch (e) {
      AppUtils.logError('Error setting string value', e);
      return false;
    }
  }
  
  static Future<String?> getString(String key, [String? defaultValue]) async {
    try {
      final prefs = await _preferences;
      return prefs.getString(key) ?? defaultValue;
    } catch (e) {
      AppUtils.logError('Error getting string value', e);
      return defaultValue;
    }
  }
  
  // Integer operations
  static Future<bool> setInt(String key, int value) async {
    try {
      final prefs = await _preferences;
      return await prefs.setInt(key, value);
    } catch (e) {
      AppUtils.logError('Error setting int value', e);
      return false;
    }
  }
  
  static Future<int> getInt(String key, [int defaultValue = 0]) async {
    try {
      final prefs = await _preferences;
      return prefs.getInt(key) ?? defaultValue;
    } catch (e) {
      AppUtils.logError('Error getting int value', e);
      return defaultValue;
    }
  }
  
  // Double operations
  static Future<bool> setDouble(String key, double value) async {
    try {
      final prefs = await _preferences;
      return await prefs.setDouble(key, value);
    } catch (e) {
      AppUtils.logError('Error setting double value', e);
      return false;
    }
  }
  
  static Future<double> getDouble(String key, [double defaultValue = 0.0]) async {
    try {
      final prefs = await _preferences;
      return prefs.getDouble(key) ?? defaultValue;
    } catch (e) {
      AppUtils.logError('Error getting double value', e);
      return defaultValue;
    }
  }
  
  // Boolean operations
  static Future<bool> setBool(String key, bool value) async {
    try {
      final prefs = await _preferences;
      return await prefs.setBool(key, value);
    } catch (e) {
      AppUtils.logError('Error setting bool value', e);
      return false;
    }
  }
  
  static Future<bool> getBool(String key, [bool defaultValue = false]) async {
    try {
      final prefs = await _preferences;
      return prefs.getBool(key) ?? defaultValue;
    } catch (e) {
      AppUtils.logError('Error getting bool value', e);
      return defaultValue;
    }
  }
  
  // List operations
  static Future<bool> setStringList(String key, List<String> value) async {
    try {
      final prefs = await _preferences;
      return await prefs.setStringList(key, value);
    } catch (e) {
      AppUtils.logError('Error setting string list value', e);
      return false;
    }
  }
  
  static Future<List<String>> getStringList(String key, [List<String>? defaultValue]) async {
    try {
      final prefs = await _preferences;
      return prefs.getStringList(key) ?? defaultValue ?? [];
    } catch (e) {
      AppUtils.logError('Error getting string list value', e);
      return defaultValue ?? [];
    }
  }
  
  // JSON operations
  static Future<bool> setJson(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = jsonEncode(value);
      return await setString(key, jsonString);
    } catch (e) {
      AppUtils.logError('Error setting JSON value', e);
      return false;
    }
  }
  
  static Future<Map<String, dynamic>?> getJson(String key) async {
    try {
      final jsonString = await getString(key);
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      AppUtils.logError('Error getting JSON value', e);
      return null;
    }
  }
  
  // Object operations (using JSON serialization)
  static Future<bool> setObject<T>(String key, T object, Map<String, dynamic> Function(T) toJson) async {
    try {
      final jsonMap = toJson(object);
      return await setJson(key, jsonMap);
    } catch (e) {
      AppUtils.logError('Error setting object value', e);
      return false;
    }
  }
  
  static Future<T?> getObject<T>(String key, T Function(Map<String, dynamic>) fromJson) async {
    try {
      final jsonMap = await getJson(key);
      if (jsonMap != null) {
        return fromJson(jsonMap);
      }
      return null;
    } catch (e) {
      AppUtils.logError('Error getting object value', e);
      return null;
    }
  }
  
  // Remove operations
  static Future<bool> remove(String key) async {
    try {
      final prefs = await _preferences;
      return await prefs.remove(key);
    } catch (e) {
      AppUtils.logError('Error removing value', e);
      return false;
    }
  }
  
  static Future<bool> clear() async {
    try {
      final prefs = await _preferences;
      return await prefs.clear();
    } catch (e) {
      AppUtils.logError('Error clearing preferences', e);
      return false;
    }
  }
  
  // Check if key exists
  static Future<bool> containsKey(String key) async {
    try {
      final prefs = await _preferences;
      return prefs.containsKey(key);
    } catch (e) {
      AppUtils.logError('Error checking key existence', e);
      return false;
    }
  }
  
  // Get all keys
  static Future<Set<String>> getKeys() async {
    try {
      final prefs = await _preferences;
      return prefs.getKeys();
    } catch (e) {
      AppUtils.logError('Error getting keys', e);
      return <String>{};
    }
  }
  
  // App-specific helper methods
  
  // Theme mode
  static Future<bool> setThemeMode(String themeMode) async {
    return await setString(AppConstants.keyThemeMode, themeMode);
  }
  
  static Future<String> getThemeMode() async {
    return await getString(AppConstants.keyThemeMode, 'system') ?? 'system';
  }
  
  // Language
  static Future<bool> setLanguage(String language) async {
    return await setString(AppConstants.keyLanguage, language);
  }
  
  static Future<String> getLanguage() async {
    return await getString(AppConstants.keyLanguage, AppConstants.defaultLocale) ?? AppConstants.defaultLocale;
  }
  
  // User info
  static Future<bool> setUserId(String userId) async {
    return await setString(AppConstants.keyUserId, userId);
  }
  
  static Future<String?> getUserId() async {
    return await getString(AppConstants.keyUserId);
  }
  
  static Future<bool> setUserName(String userName) async {
    return await setString(AppConstants.keyUserName, userName);
  }
  
  static Future<String?> getUserName() async {
    return await getString(AppConstants.keyUserName);
  }
  
  // Branch info
  static Future<bool> setBranchId(String branchId) async {
    return await setString(AppConstants.keyBranchId, branchId);
  }
  
  static Future<String?> getBranchId() async {
    return await getString(AppConstants.keyBranchId);
  }
  
  // Sync settings
  static Future<bool> setLastSyncTime(DateTime dateTime) async {
    return await setString(AppConstants.keyLastSyncTime, dateTime.toIso8601String());
  }
  
  static Future<DateTime?> getLastSyncTime() async {
    final dateString = await getString(AppConstants.keyLastSyncTime);
    if (dateString != null) {
      return DateTime.tryParse(dateString);
    }
    return null;
  }
  
  static Future<bool> setAutoSync(bool autoSync) async {
    return await setBool(AppConstants.keyAutoSync, autoSync);
  }
  
  static Future<bool> getAutoSync() async {
    return await getBool(AppConstants.keyAutoSync, true);
  }
  
  // Printer settings
  static Future<bool> setPrinterSettings(Map<String, dynamic> settings) async {
    return await setJson(AppConstants.keyPrinterSettings, settings);
  }
  
  static Future<Map<String, dynamic>?> getPrinterSettings() async {
    return await getJson(AppConstants.keyPrinterSettings);
  }
  
  // Tax rate
  static Future<bool> setTaxRate(double taxRate) async {
    return await setDouble(AppConstants.keyTaxRate, taxRate);
  }
  
  static Future<double> getTaxRate() async {
    return await getDouble(AppConstants.keyTaxRate, AppConstants.defaultTaxRate);
  }
  
  // Company info
  static Future<bool> setCompanyInfo(Map<String, dynamic> companyInfo) async {
    return await setJson(AppConstants.keyCompanyInfo, companyInfo);
  }
  
  static Future<Map<String, dynamic>?> getCompanyInfo() async {
    return await getJson(AppConstants.keyCompanyInfo);
  }
  
  // Clear user data (for logout)
  static Future<void> clearUserData() async {
    await remove(AppConstants.keyUserId);
    await remove(AppConstants.keyUserName);
    await remove(AppConstants.keyBranchId);
    await remove(AppConstants.keyLastSyncTime);
  }
  
  // Clear all app data (for reset)
  static Future<void> clearAllData() async {
    await clear();
  }
}
