import '../../core/utils/app_utils.dart';
import '../local/dao/supplier_dao.dart';
import '../models/supplier.dart';

class SupplierRepository {
  final SupplierDao _supplierDao;

  SupplierRepository(this._supplierDao);

  // ------------------------------------------------------------------
  // Basic Operations
  // ------------------------------------------------------------------

  Future<String> createSupplier(Supplier supplier) async {
    try {
      AppUtils.logInfo('Creating supplier: ${supplier.name}');

      await _validateSupplier(supplier);
      await _checkDuplicateName(supplier.name, excludeId: supplier.id);

      final supplierId = await _supplierDao.insert(supplier);
      AppUtils.logInfo('Supplier created successfully - ID: $supplierId');
      return supplierId;
    } catch (e) {
      AppUtils.logError('Error creating supplier', e);
      rethrow;
    }
  }

  Future<void> updateSupplier(Supplier supplier) async {
    try {
      AppUtils.logInfo('Updating supplier: ${supplier.name}');

      await _validateSupplier(supplier);
      final existingSupplier = await _supplierDao.findById(supplier.id);
      if (existingSupplier == null) {
        throw Exception('Supplier not found');
      }

      await _checkDuplicateName(supplier.name, excludeId: supplier.id);
      await _supplierDao.update(supplier);

      AppUtils.logInfo('Supplier updated successfully - ID: ${supplier.id}');
    } catch (e) {
      AppUtils.logError('Error updating supplier', e);
      rethrow;
    }
  }

  Future<void> deleteSupplier(String id) async {
    try {
      AppUtils.logInfo('Deleting supplier - ID: $id');

      final supplier = await _supplierDao.findById(id);
      if (supplier == null) {
        throw Exception('Supplier not found');
      }

      await _checkCanDelete(id);
      await _supplierDao.delete(id);

      AppUtils.logInfo('Supplier deleted successfully - ID: $id');
    } catch (e) {
      AppUtils.logError('Error deleting supplier', e);
      rethrow;
    }
  }

  Future<void> restoreSupplier(String id) async {
    try {
      AppUtils.logInfo('Restoring supplier - ID: $id');

      final supplier = await _supplierDao.findById(id);
      if (supplier == null) {
        throw Exception('Supplier not found');
      }

      if (!supplier.isDeleted) {
        throw Exception('Supplier is not deleted');
      }

      final restoredSupplier = supplier.copyWith(
        deletedAt: null,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

      await _supplierDao.update(restoredSupplier);
      AppUtils.logInfo('Supplier restored successfully - ID: $id');
    } catch (e) {
      AppUtils.logError('Error restoring supplier', e);
      rethrow;
    }
  }

  Future<void> deleteSupplierForever(String id) async {
    try {
      AppUtils.logInfo('Permanently deleting supplier - ID: $id');
      await _checkCanDeleteForever(id);
      await _supplierDao.delete(id);
      AppUtils.logInfo('Supplier permanently deleted - ID: $id');
    } catch (e) {
      AppUtils.logError('Error permanently deleting supplier', e);
      rethrow;
    }
  }

  // ------------------------------------------------------------------
  // Query Operations
  // ------------------------------------------------------------------

  Future<Supplier?> getSupplierById(String id) async {
    try {
      return await _supplierDao.findById(id);
    } catch (e) {
      AppUtils.logError('Error retrieving supplier', e);
      rethrow;
    }
  }

  Future<List<Supplier>> getAllSuppliers({bool includeDeleted = false}) async {
    try {
      return await _supplierDao.findAll(includeDeleted: includeDeleted);
    } catch (e) {
      AppUtils.logError('Error retrieving suppliers', e);
      rethrow;
    }
  }

  Future<List<Supplier>> searchSuppliers(String query, {bool includeDeleted = false}) async {
    try {
      if (query.trim().isEmpty) {
        return await getAllSuppliers(includeDeleted: includeDeleted);
      }
      return await _supplierDao.search(query.trim(), includeDeleted: includeDeleted);
    } catch (e) {
      AppUtils.logError('Error searching suppliers', e);
      rethrow;
    }
  }

  Future<List<Supplier>> getSuppliersByBalanceStatus(
    SupplierBalanceStatus status, {
    bool includeDeleted = false,
  }) async {
    try {
      return await _supplierDao.findByBalanceStatus(status, includeDeleted: includeDeleted);
    } catch (e) {
      AppUtils.logError('Error retrieving suppliers by balance status', e);
      rethrow;
    }
  }

  Future<List<Supplier>> getWeOweSuppliers() async {
    return await getSuppliersByBalanceStatus(SupplierBalanceStatus.weOwe);
  }

  Future<List<Supplier>> getTheyOweSuppliers() async {
    return await getSuppliersByBalanceStatus(SupplierBalanceStatus.theyOwe);
  }

  Future<List<Supplier>> getBalancedSuppliers() async {
    return await getSuppliersByBalanceStatus(SupplierBalanceStatus.balanced);
  }

  // ------------------------------------------------------------------
  // Balance Operations
  // ------------------------------------------------------------------

  Future<void> updateSupplierBalance(String id, double newBalance) async {
    try {
      AppUtils.logInfo('Updating supplier balance - ID: $id, New balance: $newBalance');

      final supplier = await _supplierDao.findById(id);
      if (supplier == null) {
        throw Exception('Supplier not found');
      }

      final updatedSupplier = supplier.copyWith(
        balance: newBalance,
        updatedAt: DateTime.now(),
        isSynced: false,
      );
      
      await _supplierDao.update(updatedSupplier);
      AppUtils.logInfo('Supplier balance updated successfully');
    } catch (e) {
      AppUtils.logError('Error updating supplier balance', e);
      rethrow;
    }
  }

  Future<void> addToSupplierBalance(String id, double amount) async {
    try {
      AppUtils.logInfo('Adding to supplier balance - ID: $id, Amount: $amount');

      final supplier = await _supplierDao.findById(id);
      if (supplier == null) {
        throw Exception('Supplier not found');
      }

      final updatedSupplier = supplier.addToBalance(amount);
      await _supplierDao.update(updatedSupplier);

      AppUtils.logInfo('Amount added to supplier balance successfully');
    } catch (e) {
      AppUtils.logError('Error adding to supplier balance', e);
      rethrow;
    }
  }

  Future<void> subtractFromSupplierBalance(String id, double amount) async {
    try {
      AppUtils.logInfo('Subtracting from supplier balance - ID: $id, Amount: $amount');

      final supplier = await _supplierDao.findById(id);
      if (supplier == null) {
        throw Exception('Supplier not found');
      }

      final updatedSupplier = supplier.subtractFromBalance(amount);
      await _supplierDao.update(updatedSupplier);

      AppUtils.logInfo('Amount subtracted from supplier balance successfully');
    } catch (e) {
      AppUtils.logError('Error subtracting from supplier balance', e);
      rethrow;
    }
  }

  Future<void> clearSupplierBalance(String id) async {
    try {
      AppUtils.logInfo('Clearing supplier balance - ID: $id');

      final supplier = await _supplierDao.findById(id);
      if (supplier == null) {
        throw Exception('Supplier not found');
      }

      final updatedSupplier = supplier.clearBalance();
      await _supplierDao.update(updatedSupplier);

      AppUtils.logInfo('Supplier balance cleared successfully');
    } catch (e) {
      AppUtils.logError('Error clearing supplier balance', e);
      rethrow;
    }
  }

  // ------------------------------------------------------------------
  // Statistics
  // ------------------------------------------------------------------

  Future<Map<String, dynamic>> getSupplierStatistics() async {
    try {
      return await _supplierDao.getSupplierStats();
    } catch (e) {
      AppUtils.logError('Error retrieving supplier statistics', e);
      rethrow;
    }
  }

  // ------------------------------------------------------------------
  // Synchronization
  // ------------------------------------------------------------------

  Future<List<Supplier>> getUnsyncedSuppliers() async {
    try {
      return await _supplierDao.getUnsyncedSuppliers();
    } catch (e) {
      AppUtils.logError('Error retrieving unsynced suppliers', e);
      rethrow;
    }
  }

  Future<void> markSupplierAsSynced(String id) async {
    try {
      await _supplierDao.markAsSynced(id);
    } catch (e) {
      AppUtils.logError('Error marking supplier as synced', e);
      rethrow;
    }
  }

  // ------------------------------------------------------------------
  // Private Validation Methods
  // ------------------------------------------------------------------

  Future<void> _validateSupplier(Supplier supplier) async {
    if (supplier.name.trim().isEmpty) {
      throw Exception('Supplier name is required');
    }

    if (supplier.name.trim().length < 2) {
      throw Exception('Supplier name must be more than one character');
    }

    if (supplier.phone != null && supplier.phone!.isNotEmpty) {
      if (!RegExp(r'^[0-9+\-\s()]+$').hasMatch(supplier.phone!)) {
        throw Exception('Invalid phone number');
      }
    }

    if (supplier.email != null && supplier.email!.isNotEmpty) {
      if (!RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(supplier.email!)) {
        throw Exception('Invalid email address');
      }
    }
  }

  Future<void> _checkDuplicateName(String name, {String? excludeId}) async {
    try {
      AppUtils.logInfo('Checking for duplicate name: $name');
      final suppliers = await _supplierDao.findAll();
      final duplicateSupplier = suppliers
          .where((supplier) =>
              supplier.name.toLowerCase() == name.toLowerCase() &&
              supplier.id != excludeId)
          .firstOrNull;

      if (duplicateSupplier != null) {
        throw Exception('Another supplier with the same name exists');
      }
      AppUtils.logInfo('No duplicate name found');
    } catch (e) {
      AppUtils.logError('Error checking for duplicate name', e);
      AppUtils.logInfo('Ignoring duplicate check error and proceeding');
    }
  }

  Future<void> _checkCanDelete(String id) async {
    final supplier = await _supplierDao.findById(id);
    if (supplier != null && supplier.balance != 0) {
      throw Exception('Cannot delete supplier with non-zero balance');
    }
  }

  Future<void> _checkCanDeleteForever(String id) async {
    await _checkCanDelete(id);
    final supplier = await _supplierDao.findById(id);
    if (supplier != null && !supplier.isDeleted) {
      throw Exception('Supplier must be deleted first before permanent deletion');
    }
  }
}