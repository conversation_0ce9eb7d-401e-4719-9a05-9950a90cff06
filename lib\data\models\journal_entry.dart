import 'package:json_annotation/json_annotation.dart';
import '../../core/constants/database_constants.dart';

part 'journal_entry.g.dart';

/// نموذج القيد اليومي - Journal Entry Model
/// يمثل القيود المحاسبية في النظام المحاسبي
@JsonSerializable()
class JournalEntry {
  final String id;
  final String debitAccount;   // الحساب المدين
  final String creditAccount;  // الحساب الدائن
  final double amount;         // المبلغ
  final DateTime date;         // تاريخ القيد
  final String? notes;         // ملاحظات
  final String? referenceType; // نوع المرجع (sale, purchase, adjustment, etc.)
  final String? referenceId;   // معرف المرجع
  final String? referenceNumber; // رقم المرجع
  final String createdBy;      // من أنشأ القيد
  final DateTime createdAt;    // تاريخ الإنشاء
  final DateTime? updatedAt;   // تاريخ التحديث
  final DateTime? deletedAt;   // تاريخ الحذف (soft delete)
  final bool isSynced;         // هل تم المزامنة

  const JournalEntry({
    required this.id,
    required this.debitAccount,
    required this.creditAccount,
    required this.amount,
    required this.date,
    this.notes,
    this.referenceType,
    this.referenceId,
    this.referenceNumber,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  /// إنشاء قيد يومي من JSON
  factory JournalEntry.fromJson(Map<String, dynamic> json) =>
      _$JournalEntryFromJson(json);

  /// تحويل القيد اليومي إلى JSON
  Map<String, dynamic> toJson() => _$JournalEntryToJson(this);
/// إنشاء قيد يومي من Map قاعدة البيانات
factory JournalEntry.fromMap(Map<String, dynamic> map) {
  return JournalEntry(
    id: map[DatabaseConstants.columnJournalEntryId] as String,
    debitAccount: map[DatabaseConstants.columnJournalEntryDebitAccount] as String,
    creditAccount: map[DatabaseConstants.columnJournalEntryCreditAccount] as String,
    amount: (map[DatabaseConstants.columnJournalEntryAmount] as num).toDouble(),
    date: DateTime.parse(map[DatabaseConstants.columnJournalEntryDate] as String),
    notes: map[DatabaseConstants.columnJournalEntryNotes] as String?,
    referenceType: map[DatabaseConstants.columnJournalEntryReferenceType] as String?,
    referenceId: map[DatabaseConstants.columnJournalEntryReferenceId] as String?,
    referenceNumber: map[DatabaseConstants.columnJournalEntryReferenceNo] as String?,
    createdBy: map[DatabaseConstants.columnJournalEntryCreatedBy] as String,
    createdAt: DateTime.parse(map[DatabaseConstants.columnJournalEntryCreatedAt] as String),
    updatedAt: map[DatabaseConstants.columnJournalEntryUpdatedAt] != null
        ? DateTime.parse(map[DatabaseConstants.columnJournalEntryUpdatedAt] as String)
        : null,
    deletedAt: map[DatabaseConstants.columnJournalEntryDeletedAt] != null
        ? DateTime.parse(map[DatabaseConstants.columnJournalEntryDeletedAt] as String)
        : null,
    isSynced: (map[DatabaseConstants.columnJournalEntryIsSynced] as int) == 1,
  );
}



/// تحويل القيد اليومي إلى Map لقاعدة البيانات
Map<String, dynamic> toMap() {
  return {
    DatabaseConstants.columnJournalEntryId: id,
    DatabaseConstants.columnJournalEntryDebitAccount: debitAccount,
    DatabaseConstants.columnJournalEntryCreditAccount: creditAccount,
    DatabaseConstants.columnJournalEntryAmount: amount,
    DatabaseConstants.columnJournalEntryDate: date.toIso8601String(),
    DatabaseConstants.columnJournalEntryNotes: notes,
    DatabaseConstants.columnJournalEntryReferenceType: referenceType,
    DatabaseConstants.columnJournalEntryReferenceId: referenceId,
    DatabaseConstants.columnJournalEntryReferenceNo: referenceNumber,
    DatabaseConstants.columnJournalEntryCreatedBy: createdBy,
    DatabaseConstants.columnJournalEntryCreatedAt: createdAt.toIso8601String(),
    DatabaseConstants.columnJournalEntryUpdatedAt: updatedAt?.toIso8601String(),
    DatabaseConstants.columnJournalEntryDeletedAt: deletedAt?.toIso8601String(),
    DatabaseConstants.columnJournalEntryIsSynced: isSynced ? 1 : 0,
  };
}

  /// نسخ القيد مع تعديل بعض الخصائص
  JournalEntry copyWith({
    String? id,
    String? debitAccount,
    String? creditAccount,
    double? amount,
    DateTime? date,
    String? notes,
    String? referenceType,
    String? referenceId,
    String? referenceNumber,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return JournalEntry(
      id: id ?? this.id,
      debitAccount: debitAccount ?? this.debitAccount,
      creditAccount: creditAccount ?? this.creditAccount,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      notes: notes ?? this.notes,
      referenceType: referenceType ?? this.referenceType,
      referenceId: referenceId ?? this.referenceId,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  /// التحقق من صحة القيد
  bool get isValid {
    return id.isNotEmpty &&
        debitAccount.isNotEmpty &&
        creditAccount.isNotEmpty &&
        amount > 0 &&
        createdBy.isNotEmpty;
  }

  /// هل القيد محذوف؟
  bool get isDeleted => deletedAt != null;

  /// هل القيد متوازن؟ (المبلغ المدين = المبلغ الدائن)
  bool get isBalanced => amount > 0; // في هذا النموذج البسيط، كل قيد متوازن

  @override
  String toString() {
    return 'JournalEntry(id: $id, debitAccount: $debitAccount, creditAccount: $creditAccount, amount: $amount, date: $date)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JournalEntry && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
