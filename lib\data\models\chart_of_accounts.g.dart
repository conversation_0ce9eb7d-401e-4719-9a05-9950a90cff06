// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chart_of_accounts.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChartOfAccounts _$ChartOfAccountsFromJson(Map<String, dynamic> json) =>
    ChartOfAccounts(
      id: json['id'] as String,
      accountCode: json['accountCode'] as String,
      accountNameAr: json['accountNameAr'] as String,
      accountNameEn: json['accountNameEn'] as String,
      accountType: json['accountType'] as String,
      parentId: json['parentId'] as String?,
      level: (json['level'] as num?)?.toInt() ?? 1,
      isActive: json['isActive'] as bool? ?? true,
      isSystem: json['isSystem'] as bool? ?? false,
      balance: (json['balance'] as num?)?.toDouble() ?? 0.0,
      description: json['description'] as String?,
      notes: json['notes'] as String?,
      createdBy: json['createdBy'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      isSynced: json['isSynced'] as bool? ?? false,
    );

Map<String, dynamic> _$ChartOfAccountsToJson(ChartOfAccounts instance) =>
    <String, dynamic>{
      'id': instance.id,
      'accountCode': instance.accountCode,
      'accountNameAr': instance.accountNameAr,
      'accountNameEn': instance.accountNameEn,
      'accountType': instance.accountType,
      'parentId': instance.parentId,
      'level': instance.level,
      'isActive': instance.isActive,
      'isSystem': instance.isSystem,
      'balance': instance.balance,
      'description': instance.description,
      'notes': instance.notes,
      'createdBy': instance.createdBy,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'isSynced': instance.isSynced,
    };
