// في ملف lib/providers/unit_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tijari_tech/core/utils/error_handler.dart';
import 'package:tijari_tech/data/models/unit.dart';
import 'package:tijari_tech/data/repositories/unit_repository.dart';
import 'package:tijari_tech/data/local/dao/unit_dao.dart';
import '../core/utils/app_utils.dart';
import 'database_provider.dart';

// Unit DAO provider
final unitDaoProvider = Provider<UnitDao>((ref) {
  return UnitDao();
});

// Unit repository provider
final unitRepositoryProvider = Provider<UnitRepository>((ref) {
  final unitDao = ref.watch(unitDaoProvider);
  return UnitRepository(unitDao: unitDao);
});

// Unit management state
class UnitManagementState {
  final bool isLoading;
  final String? error;
  final List<Unit> units;
  final List<Unit> filteredUnits;
  final String searchQuery;
  final String? selectedUnitType;
  final bool showInactive;
  final Unit? selectedUnit;
  final Map<String, dynamic> statistics;
  final Map<String, bool> expandedUnits;
  final Map<String, List<Unit>> unitsByType;
  final Map<String, int> branchUsageCount;
  final Map<String, int> categoryUsageCount;

  const UnitManagementState({
    this.isLoading = false,
    this.error,
    this.units = const [],
    this.filteredUnits = const [],
    this.searchQuery = '',
    this.selectedUnitType,
    this.showInactive = false,
    this.selectedUnit,
    this.statistics = const {},
    this.expandedUnits = const {},
    this.unitsByType = const {},
    this.branchUsageCount = const {},
    this.categoryUsageCount = const {},
  });

  UnitManagementState copyWith({
    bool? isLoading,
    String? error,
    List<Unit>? units,
    List<Unit>? filteredUnits,
    String? searchQuery,
    String? selectedUnitType,
    bool? showInactive,
    Unit? selectedUnit,
    Map<String, dynamic>? statistics,
    Map<String, bool>? expandedUnits,
    Map<String, List<Unit>>? unitsByType,
    Map<String, int>? branchUsageCount,
    Map<String, int>? categoryUsageCount,
  }) {
    return UnitManagementState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      units: units ?? this.units,
      filteredUnits: filteredUnits ?? this.filteredUnits,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedUnitType: selectedUnitType ?? this.selectedUnitType,
      showInactive: showInactive ?? this.showInactive,
      selectedUnit: selectedUnit ?? this.selectedUnit,
      statistics: statistics ?? this.statistics,
      expandedUnits: expandedUnits ?? this.expandedUnits,
      unitsByType: unitsByType ?? this.unitsByType,
      branchUsageCount: branchUsageCount ?? this.branchUsageCount,
      categoryUsageCount: categoryUsageCount ?? this.categoryUsageCount,
    );
  }
}

// Unit management notifier
class UnitManagementNotifier extends StateNotifier<UnitManagementState> {
  final Ref _ref;

  UnitManagementNotifier(this._ref) : super(const UnitManagementState()) {
    _loadUnits();
  }

  UnitRepository get _repository => _ref.read(unitRepositoryProvider);
  DatabaseOperations get _dbOperations => _ref.read(databaseOperationsProvider);

  // Load all units
  Future<void> _loadUnits() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final units = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getUnitTree();
      });

      final statistics = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getEnhancedUnitsStatistics();
      });

      // تجميع معلومات الفروع والتصنيفات
      final branchUsageCount = <String, int>{};
      final categoryUsageCount = <String, int>{};

      for (final unit in units) {
        branchUsageCount[unit.id] = unit.branchCount ?? 0;
        categoryUsageCount[unit.id] = unit.categoryCount ?? 0;
      }

      // تجميع الوحدات حسب النوع
      final unitsByType = <String, List<Unit>>{};
      for (final unit in units) {
        unitsByType.putIfAbsent(unit.unitType, () => []).add(unit);
      }

      state = state.copyWith(
        isLoading: false,
        units: units,
        filteredUnits: _filterUnits(units),
        statistics: statistics,
        unitsByType: unitsByType,
        branchUsageCount: branchUsageCount,
        categoryUsageCount: categoryUsageCount,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error loading units', e);
    }
  }

  // Filter units based on search query, type, and show inactive setting
  List<Unit> _filterUnits(List<Unit> units) {
    var filtered = units;

    // Filter by search query
    if (state.searchQuery.isNotEmpty) {
      final query = state.searchQuery.toLowerCase();
      filtered = filtered.where((unit) {
        return unit.name.toLowerCase().contains(query) ||
            (unit.symbol?.toLowerCase().contains(query) ?? false) ||
            (unit.abbreviation?.toLowerCase().contains(query) ?? false) ||
            (unit.description?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    // Filter by unit type
    if (state.selectedUnitType != null) {
      filtered = filtered
          .where((unit) => unit.unitType == state.selectedUnitType)
          .toList();
    }

    // Filter by active status
    if (!state.showInactive) {
      filtered = filtered.where((unit) => unit.isActiveUnit).toList();
    }

    return filtered;
  }

  // Search units
  void searchUnits(String query) {
    state = state.copyWith(
      searchQuery: query,
      filteredUnits: _filterUnits(state.units),
    );
  }

  // Filter by unit type
  void filterByUnitType(String? unitType) {
    state = state.copyWith(
      selectedUnitType: unitType,
      filteredUnits: _filterUnits(state.units),
    );
  }

  // Toggle show inactive units
  void toggleShowInactive(bool showInactive) {
    state = state.copyWith(
      showInactive: showInactive,
      filteredUnits: _filterUnits(state.units),
    );
  }

  // Select unit
  void selectUnit(Unit? unit) {
    state = state.copyWith(selectedUnit: unit);
  }

  // Toggle unit expansion
  void toggleUnitExpansion(String unitId) {
    final expandedUnits = Map<String, bool>.from(state.expandedUnits);
    expandedUnits[unitId] = !(expandedUnits[unitId] ?? false);
    state = state.copyWith(expandedUnits: expandedUnits);
  }

  // Expand all units
  void expandAllUnits() {
    final expandedUnits = <String, bool>{};
    _expandAllUnitsRecursive(state.units, expandedUnits);
    state = state.copyWith(expandedUnits: expandedUnits);
  }

  // Collapse all units
  void collapseAllUnits() {
    state = state.copyWith(expandedUnits: {});
  }

  // Recursive function to expand all units
  void _expandAllUnitsRecursive(
      List<Unit> units, Map<String, bool> expandedUnits) {
    for (final unit in units) {
      expandedUnits[unit.id] = true;
      if (unit.derivedUnits != null && unit.derivedUnits!.isNotEmpty) {
        _expandAllUnitsRecursive(unit.derivedUnits!, expandedUnits);
      }
    }

    state = state.copyWith(expandedUnits: expandedUnits);
  }

  // Refresh units
  Future<void> refresh() async {
    await _loadUnits();
  }

  // Create unit
  Future<bool> createUnit({
    required String name,
    String? symbol,
    String? abbreviation,
    required String unitType,
    bool isBaseUnit = true,
    String? baseUnitId,
    double? factor,
    String? description,
    bool isActive = true,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final unit = Unit.create(
        id: AppUtils.generateId(),
        name: name,
        symbol: symbol,
        abbreviation: abbreviation,
        factor: factor ?? (isBaseUnit ? 1.0 : 0.0),
        baseUnitId: baseUnitId,
        unitType: unitType,
        isBaseUnit: isBaseUnit,
        isActive: isActive,
        description: description,
      );

      await _repository.createUnit(unit);
      await refresh();
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      return false;
    }
  }

  // Create derived unit
  Future<bool> createDerivedUnit({
    required String name,
    String? symbol,
    String? abbreviation,
    required String baseUnitId,
    required double factor,
    String? description,
    bool isActive = true,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _repository.createDerivedUnit(
        name: name,
        symbol: symbol,
        abbreviation: abbreviation,
        baseUnitId: baseUnitId,
        factor: factor,
        description: description,
      );

      await refresh();
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      return false;
    }
  }

  // Create unit tree
  Future<bool> createUnitTree({
    required String baseName,
    String? baseSymbol,
    String? baseAbbreviation,
    required String unitType,
    required List<Map<String, dynamic>> derivedUnitsData,
    String? description,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _repository.createUnitTree(
        baseName: baseName,
        baseSymbol: baseSymbol,
        baseAbbreviation: baseAbbreviation,
        unitType: unitType,
        derivedUnitsData: derivedUnitsData,
        description: description,
      );

      await refresh();
      AppUtils.logInfo('Unit tree created successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error creating unit tree', e);
      return false;
    }
  }

  // Update unit
  Future<bool> updateUnit({
    required String id,
    String? name,
    String? symbol,
    String? abbreviation,
    double? factor,
    String? baseUnitId,
    String? unitType,
    bool? isBaseUnit,
    bool? isActive,
    String? description,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final existingUnit = await _repository.getUnitWithDetails(id);
      if (existingUnit == null) {
        throw Exception('Unit not found');
      }

      final updatedUnit = existingUnit.update(
        name: name,
        symbol: symbol,
        abbreviation: abbreviation,
        factor: factor,
        baseUnitId: baseUnitId,
        unitType: unitType,
        isBaseUnit: isBaseUnit,
        isActive: isActive,
        description: description,
      );

      await _repository.updateUnit(id, updatedUnit);
      await refresh();
      AppUtils.logInfo('Unit updated successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error updating unit', e);
      return false;
    }
  }

  // Delete unit
  Future<bool> deleteUnit(String id) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _repository.deleteUnit(id);
      await refresh();
      AppUtils.logInfo('Unit deleted successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error deleting unit', e);
      return false;
    }
  }

  // Create default units
  Future<bool> createDefaultUnits() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // إنشاء وحدات الوزن
      await _repository.createUnitTree(
        baseName: 'جرام',
        baseSymbol: 'جم',
        baseAbbreviation: 'g',
        unitType: 'weight',
        derivedUnitsData: [
          {
            'name': 'كيلوجرام',
            'symbol': 'كجم',
            'abbreviation': 'kg',
            'factor': 1000.0
          },
          {
            'name': 'طن',
            'symbol': 'طن',
            'abbreviation': 't',
            'factor': 1000000.0
          },
        ],
      );

      // إنشاء وحدات الطول
      await _repository.createUnitTree(
        baseName: 'سنتيمتر',
        baseSymbol: 'سم',
        baseAbbreviation: 'cm',
        unitType: 'length',
        derivedUnitsData: [
          {'name': 'متر', 'symbol': 'م', 'abbreviation': 'm', 'factor': 100.0},
        ],
      );

      // إنشاء وحدات الحجم
      await _repository.createUnitTree(
        baseName: 'مليلتر',
        baseSymbol: 'مل',
        baseAbbreviation: 'mL',
        unitType: 'volume',
        derivedUnitsData: [
          {
            'name': 'لتر',
            'symbol': 'لتر',
            'abbreviation': 'L',
            'factor': 1000.0
          },
        ],
      );
      List<Unit> units = [
        Unit.create(
          id: 'unit_piece',
          name: 'قطعة',
          symbol: 'قطعة',
          abbreviation: 'pc',
          factor: 1.0,
          unitType: 'count',
          isBaseUnit: true,
        ),
      ];
      await _repository.createUnit(units as Unit);
      // إنشاء وحدة العدد
      // await _repository.createUnit(
      //   name: 'قطعة',
      //   nameEn: 'Piece',
      //   symbol: 'قطعة',
      //   abbreviation: 'pc',
      //   unitType: 'count',
      //   isBaseUnit: true,
      // );

      await refresh();
      AppUtils.logInfo('Default units created successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error creating default units', e);
      return false;
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Unit management provider
final unitManagementProvider =
    StateNotifierProvider<UnitManagementNotifier, UnitManagementState>((ref) {
  return UnitManagementNotifier(ref);
});

// Unit by ID provider
final unitByIdProvider = FutureProvider.family<Unit?, String>((ref, id) async {
  final repository = ref.read(unitRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);
  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getUnitWithDetails(id);
  });
});

// Base units provider
final baseUnitsProvider = FutureProvider<List<Unit>>((ref) async {
  final repository = ref.read(unitRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);
  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getBaseUnits();
  });
});

// Unit types provider
final unitTypesProvider = FutureProvider<List<String>>((ref) async {
  final repository = ref.read(unitRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);
  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getUnitTypes();
  });
});

// Units statistics provider
final unitsStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.read(unitRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);
  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getEnhancedUnitsStatistics();
  });
});

// Units by type provider
final unitsByTypeProvider =
    FutureProvider.family<List<Unit>, String>((ref, unitType) async {
  final repository = ref.read(unitRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);
  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getUnitsByType(unitType);
  });
});
