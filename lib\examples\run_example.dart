import 'business_operations_example.dart';
import '../data/local/database.dart';
import '../core/utils/app_utils.dart';

/// ملف تشغيل الأمثلة العملية
/// يوضح كيفية استخدام النظام التجاري المتكامل
void main() async {
  print('🚀 بدء تشغيل أمثلة النظام التجاري المتكامل');
  print('=' * 60);

  try {
    // تهيئة قاعدة البيانات
    await _initializeDatabase();

    // إنشاء مثيل من الأمثلة
    final example = BusinessOperationsExample();

    // تشغيل المثال الشامل
    await example.runCompleteExample();

    // تشغيل أمثلة إضافية
    await _runAdditionalExamples(example);

    print('\n🎉 تم تشغيل جميع الأمثلة بنجاح!');
    print('=' * 60);

  } catch (e) {
    print('❌ خطأ في تشغيل الأمثلة: $e');
    print('تفاصيل الخطأ: ${e.toString()}');
  }
}

/// تهيئة قاعدة البيانات
Future<void> _initializeDatabase() async {
  print('🗄️ تهيئة قاعدة البيانات...');
  
  try {
    final databaseHelper = DatabaseHelper();
    final database = await databaseHelper.database;
    
    print('✅ تم تهيئة قاعدة البيانات بنجاح');
    print('📊 حجم قاعدة البيانات: ${await databaseHelper.getDatabaseSize()} بايت');
    
  } catch (e) {
    print('❌ خطأ في تهيئة قاعدة البيانات: $e');
    rethrow;
  }
}

/// تشغيل أمثلة إضافية
Future<void> _runAdditionalExamples(BusinessOperationsExample example) async {
  print('\n🔧 تشغيل أمثلة إضافية...');
  
  try {
    // مثال تحديث المخزون
    await example.updateStockExample();
    
    // مثال البحث في المنتجات
    await example.searchProductsExample();
    
    // مثال تحديث الأسعار
    await _priceUpdateExample();
    
    // مثال التقارير المتقدمة
    await _advancedReportsExample();
    
  } catch (e) {
    print('❌ خطأ في الأمثلة الإضافية: $e');
  }
}

/// مثال تحديث الأسعار بالجملة
Future<void> _priceUpdateExample() async {
  print('\n💰 مثال تحديث الأسعار بالجملة...');
  
  try {
    // هذا مثال تجريبي - في التطبيق الحقيقي ستحصل على IDs حقيقية
    final priceUpdates = <String, Map<String, double>>{
      'product-id-1': {
        'cost_price': 3600.0,
        'selling_price': 4300.0,
      },
      'product-id-2': {
        'cost_price': 50.0,
        'selling_price': 70.0,
      },
    };
    
    print('📝 محاولة تحديث أسعار ${priceUpdates.length} منتج...');
    print('ℹ️ ملاحظة: هذا مثال تجريبي قد لا يعمل بدون منتجات حقيقية');
    
  } catch (e) {
    print('❌ خطأ في تحديث الأسعار: $e');
  }
}

/// مثال التقارير المتقدمة
Future<void> _advancedReportsExample() async {
  print('\n📊 مثال التقارير المتقدمة...');
  
  try {
    print('📈 تقارير متاحة في النظام:');
    print('   - تقرير المبيعات اليومية');
    print('   - تقرير المشتريات اليومية');
    print('   - تقرير المنتجات الأكثر مبيعاً');
    print('   - تقرير المنتجات الراكدة');
    print('   - تقرير المخزون المنخفض');
    print('   - تقرير الأرباح والخسائر');
    print('   - تقرير العملاء الأكثر شراءً');
    print('   - تقرير الموردين');
    print('   - تقرير المدفوعات المعلقة');
    
    print('\n💡 يمكن الوصول لهذه التقارير من خلال:');
    print('   - BusinessService للتقارير الأساسية');
    print('   - ProductRepository للتقارير المتعلقة بالمنتجات');
    print('   - SaleRepository للتقارير المتعلقة بالمبيعات');
    print('   - PurchaseRepository للتقارير المتعلقة بالمشتريات');
    
  } catch (e) {
    print('❌ خطأ في التقارير المتقدمة: $e');
  }
}

/// معلومات النظام
void _showSystemInfo() {
  print('\n📋 معلومات النظام:');
  print('=' * 40);
  print('🏢 اسم النظام: نظام تجاري تك المحاسبي');
  print('📅 الإصدار: 1.0.0');
  print('🛠️ المطور: فريق تجاري تك');
  print('📱 المنصة: Flutter/Dart');
  print('🗄️ قاعدة البيانات: SQLite');
  
  print('\n🎯 الميزات المتاحة:');
  print('   ✅ إدارة المنتجات والفئات والوحدات');
  print('   ✅ عمليات الشراء والبيع');
  print('   ✅ إدارة المخزون والمستودعات');
  print('   ✅ إدارة العملاء والموردين');
  print('   ✅ النظام المحاسبي والمعاملات المالية');
  print('   ✅ التقارير والإحصائيات');
  print('   ✅ نظام الإشعارات');
  print('   ✅ إدارة المستخدمين والصلاحيات');
  print('   ✅ المرفقات والملفات');
  print('   ✅ تسويات المخزون');
  print('   ✅ الذكاء الاصطناعي والتنبؤات');
  print('   ✅ المزامنة مع الخادم');
  print('   ✅ سجل المراجعة والتدقيق');
  
  print('\n🔧 البنية التقنية:');
  print('   📁 DAO Pattern للوصول لقاعدة البيانات');
  print('   📁 Repository Pattern لطبقة البيانات');
  print('   📁 Service Layer للمنطق التجاري');
  print('   📁 Exception Handling للتعامل مع الأخطاء');
  print('   📁 Logging System لتسجيل العمليات');
  print('   📁 Validation Layer للتحقق من البيانات');
  
  print('=' * 40);
}

/// تشغيل معلومات النظام
void runSystemInfo() {
  _showSystemInfo();
}
