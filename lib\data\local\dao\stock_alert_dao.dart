import 'package:tijari_tech/core/constants/database_constants.dart';
import 'base_dao.dart';
import '../../models/stock_alert.dart';
import '../../../core/utils/app_utils.dart';

class StockAlertDao extends BaseDao<StockAlert> {
  @override
  String get tableName => 'stock_alerts'; // Will be added to database constants

  @override
  StockAlert fromMap(Map<String, dynamic> map) => StockAlert.fromMap(map);

  @override
  Map<String, dynamic> toMap(StockAlert entity) => entity.toMap();

  // ------------------------------------------------------------------
  // StockAlert-specific queries
  // ------------------------------------------------------------------

  /// استرجاع التنبيهات النشطة
  Future<List<StockAlert>> getActiveAlerts({
    String? warehouseId,
    String? productId,
    String? alertType,
    String? severity,
    bool? isRead,
    String? orderBy,
    bool ascending = false,
    int? limit,
    int? offset,
  }) async {
    final conditions = <String>[];
    final args = <dynamic>[];

    // Always exclude deleted and resolved alerts
    conditions.add('deleted_at IS NULL');
    conditions.add('is_resolved = 0');

    if (warehouseId != null) {
      conditions.add('warehouse_id = ?');
      args.add(warehouseId);
    }

    if (productId != null) {
      conditions.add('product_id = ?');
      args.add(productId);
    }

    if (alertType != null) {
      conditions.add('alert_type = ?');
      args.add(alertType);
    }

    if (severity != null) {
      conditions.add('severity = ?');
      args.add(severity);
    }

    if (isRead != null) {
      conditions.add('is_read = ?');
      args.add(isRead ? 1 : 0);
    }

    final whereClause = conditions.join(' AND ');
    final orderByClause = orderBy ?? 'created_at';
    final direction = ascending ? 'ASC' : 'DESC';

    final sqlQuery = '''
      SELECT 
        sa.*,
        p.name_ar as product_name,
        w.name as warehouse_name
      FROM $tableName sa
      LEFT JOIN ${DatabaseConstants.tableProducts} p 
        ON sa.product_id = p.${DatabaseConstants.columnProductId}
      LEFT JOIN ${DatabaseConstants.tableWarehouses} w 
        ON sa.warehouse_id = w.${DatabaseConstants.columnWarehouseId}
      WHERE $whereClause
      ORDER BY $orderByClause $direction
      ${limit != null ? 'LIMIT $limit' : ''}
      ${offset != null ? 'OFFSET $offset' : ''}
    ''';

    final result = await rawQuery(sqlQuery, args);
    return result.map((row) => StockAlert.fromMap(row)).toList();
  }

  /// استرجاع التنبيهات غير المقروءة
  Future<List<StockAlert>> getUnreadAlerts({
    String? warehouseId,
    String? productId,
    int? limit,
  }) async {
    return await getActiveAlerts(
      warehouseId: warehouseId,
      productId: productId,
      isRead: false,
      orderBy: 'created_at',
      ascending: false,
      limit: limit,
    );
  }

  /// استرجاع التنبيهات الحرجة
  Future<List<StockAlert>> getCriticalAlerts({
    String? warehouseId,
    String? productId,
    int? limit,
  }) async {
    return await getActiveAlerts(
      warehouseId: warehouseId,
      productId: productId,
      severity: 'critical',
      orderBy: 'created_at',
      ascending: false,
      limit: limit,
    );
  }

  /// استرجاع إحصائيات التنبيهات
  Future<Map<String, dynamic>> getAlertStatistics({
    String? warehouseId,
    String? productId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    final conditions = <String>[];
    final args = <dynamic>[];

    conditions.add('deleted_at IS NULL');

    if (warehouseId != null) {
      conditions.add('warehouse_id = ?');
      args.add(warehouseId);
    }

    if (productId != null) {
      conditions.add('product_id = ?');
      args.add(productId);
    }

    if (fromDate != null) {
      conditions.add('created_at >= ?');
      args.add(fromDate.toIso8601String());
    }

    if (toDate != null) {
      conditions.add('created_at <= ?');
      args.add(toDate.toIso8601String());
    }

    final whereClause = conditions.join(' AND ');

    final sqlQuery = '''
      SELECT 
        COUNT(*) as total_alerts,
        COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread_alerts,
        COUNT(CASE WHEN is_resolved = 0 THEN 1 END) as active_alerts,
        COUNT(CASE WHEN severity = 'critical' THEN 1 END) as critical_alerts,
        COUNT(CASE WHEN severity = 'high' THEN 1 END) as high_alerts,
        COUNT(CASE WHEN severity = 'medium' THEN 1 END) as medium_alerts,
        COUNT(CASE WHEN severity = 'low' THEN 1 END) as low_alerts,
        COUNT(CASE WHEN alert_type = 'low_stock' THEN 1 END) as low_stock_alerts,
        COUNT(CASE WHEN alert_type = 'out_of_stock' THEN 1 END) as out_of_stock_alerts,
        COUNT(CASE WHEN alert_type = 'expired' THEN 1 END) as expired_alerts,
        COUNT(CASE WHEN alert_type = 'near_expiry' THEN 1 END) as near_expiry_alerts
      FROM $tableName
      WHERE $whereClause
    ''';

    final result = await rawQuery(sqlQuery, args);
    return result.isNotEmpty ? result.first : {};
  }

  /// تحديد جميع التنبيهات كمقروءة
  Future<void> markAllAsRead({
    String? warehouseId,
    String? productId,
  }) async {
    final conditions = <String>[];
    final args = <dynamic>[];

    conditions.add('is_read = 0');
    conditions.add('deleted_at IS NULL');

    if (warehouseId != null) {
      conditions.add('warehouse_id = ?');
      args.add(warehouseId);
    }

    if (productId != null) {
      conditions.add('product_id = ?');
      args.add(productId);
    }

    final whereClause = conditions.join(' AND ');

    await rawQuery('''
      UPDATE $tableName 
      SET is_read = 1, updated_at = ?
      WHERE $whereClause
    ''', [DateTime.now().toIso8601String(), ...args]);
  }

  /// حل جميع التنبيهات
  Future<void> resolveAllAlerts({
    String? warehouseId,
    String? productId,
    String? alertType,
    required String resolvedBy,
  }) async {
    final conditions = <String>[];
    final args = <dynamic>[];

    conditions.add('is_resolved = 0');
    conditions.add('deleted_at IS NULL');

    if (warehouseId != null) {
      conditions.add('warehouse_id = ?');
      args.add(warehouseId);
    }

    if (productId != null) {
      conditions.add('product_id = ?');
      args.add(productId);
    }

    if (alertType != null) {
      conditions.add('alert_type = ?');
      args.add(alertType);
    }

    final whereClause = conditions.join(' AND ');
    final now = DateTime.now().toIso8601String();

    await rawQuery('''
      UPDATE $tableName 
      SET is_resolved = 1, resolved_at = ?, resolved_by = ?, updated_at = ?
      WHERE $whereClause
    ''', [now, resolvedBy, now, ...args]);
  }

  /// حذف التنبيهات القديمة
  Future<void> deleteOldAlerts(int daysOld) async {
    final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));
    
    await rawQuery('''
      UPDATE $tableName 
      SET deleted_at = ?, updated_at = ?
      WHERE created_at < ? AND (is_resolved = 1 OR deleted_at IS NOT NULL)
    ''', [
      DateTime.now().toIso8601String(),
      DateTime.now().toIso8601String(),
      cutoffDate.toIso8601String(),
    ]);
  }

  /// البحث في التنبيهات
  Future<List<StockAlert>> searchAlerts({
    String? searchTerm,
    String? warehouseId,
    String? alertType,
    String? severity,
    bool? isRead,
    bool? isResolved,
    DateTime? fromDate,
    DateTime? toDate,
    String? orderBy,
    bool ascending = false,
    int? limit,
    int? offset,
  }) async {
    final conditions = <String>[];
    final args = <dynamic>[];

    conditions.add('sa.deleted_at IS NULL');

    if (searchTerm != null && searchTerm.isNotEmpty) {
      conditions.add('''
        (sa.title LIKE ? OR sa.message LIKE ? OR p.name_ar LIKE ?)
      ''');
      final searchPattern = '%$searchTerm%';
      args.addAll([searchPattern, searchPattern, searchPattern]);
    }

    if (warehouseId != null) {
      conditions.add('sa.warehouse_id = ?');
      args.add(warehouseId);
    }

    if (alertType != null) {
      conditions.add('sa.alert_type = ?');
      args.add(alertType);
    }

    if (severity != null) {
      conditions.add('sa.severity = ?');
      args.add(severity);
    }

    if (isRead != null) {
      conditions.add('sa.is_read = ?');
      args.add(isRead ? 1 : 0);
    }

    if (isResolved != null) {
      conditions.add('sa.is_resolved = ?');
      args.add(isResolved ? 1 : 0);
    }

    if (fromDate != null) {
      conditions.add('sa.created_at >= ?');
      args.add(fromDate.toIso8601String());
    }

    if (toDate != null) {
      conditions.add('sa.created_at <= ?');
      args.add(toDate.toIso8601String());
    }

    final whereClause = conditions.join(' AND ');
    final orderByClause = orderBy ?? 'sa.created_at';
    final direction = ascending ? 'ASC' : 'DESC';

    final sqlQuery = '''
      SELECT 
        sa.*,
        p.name_ar as product_name,
        w.name as warehouse_name
      FROM $tableName sa
      LEFT JOIN ${DatabaseConstants.tableProducts} p 
        ON sa.product_id = p.${DatabaseConstants.columnProductId}
      LEFT JOIN ${DatabaseConstants.tableWarehouses} w 
        ON sa.warehouse_id = w.${DatabaseConstants.columnWarehouseId}
      WHERE $whereClause
      ORDER BY $orderByClause $direction
      ${limit != null ? 'LIMIT $limit' : ''}
      ${offset != null ? 'OFFSET $offset' : ''}
    ''';

    final result = await rawQuery(sqlQuery, args);
    return result.map((row) => StockAlert.fromMap(row)).toList();
  }
}

class StockAlertConfigDao extends BaseDao<StockAlertConfig> {
  @override
  String get tableName => 'stock_alert_configs'; // Will be added to database constants

  @override
  StockAlertConfig fromMap(Map<String, dynamic> map) => StockAlertConfig.fromMap(map);

  @override
  Map<String, dynamic> toMap(StockAlertConfig entity) => entity.toMap();

  // ------------------------------------------------------------------
  // StockAlertConfig-specific queries
  // ------------------------------------------------------------------

  /// استرجاع إعدادات التنبيهات للمنتج
  Future<List<StockAlertConfig>> getConfigsForProduct(String productId) async {
    return await findWhere(
      where: 'product_id = ? AND deleted_at IS NULL',
      whereArgs: [productId],
      orderBy: 'created_at DESC',
    );
  }

  /// استرجاع إعدادات التنبيهات للمخزن
  Future<List<StockAlertConfig>> getConfigsForWarehouse(String warehouseId) async {
    return await findWhere(
      where: 'warehouse_id = ? AND deleted_at IS NULL',
      whereArgs: [warehouseId],
      orderBy: 'created_at DESC',
    );
  }

  /// استرجاع إعدادات التنبيهات للمنتج في مخزن محدد
  Future<StockAlertConfig?> getConfigForProductInWarehouse(
    String productId,
    String warehouseId,
  ) async {
    final configs = await findWhere(
      where: 'product_id = ? AND warehouse_id = ? AND deleted_at IS NULL',
      whereArgs: [productId, warehouseId],
      limit: 1,
    );
    return configs.isNotEmpty ? configs.first : null;
  }

  /// استرجاع الإعدادات العامة للمنتج (لجميع المخازن)
  Future<StockAlertConfig?> getGlobalConfigForProduct(String productId) async {
    final configs = await findWhere(
      where: 'product_id = ? AND warehouse_id IS NULL AND deleted_at IS NULL',
      whereArgs: [productId],
      limit: 1,
    );
    return configs.isNotEmpty ? configs.first : null;
  }
}
