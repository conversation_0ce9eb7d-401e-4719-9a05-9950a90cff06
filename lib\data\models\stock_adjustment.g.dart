// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stock_adjustment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StockAdjustment _$StockAdjustmentFromJson(Map<String, dynamic> json) =>
    StockAdjustment(
      id: json['id'] as String,
      referenceNumber: json['referenceNumber'] as String,
      adjustmentDate: DateTime.parse(json['adjustmentDate'] as String),
      warehouseId: json['warehouseId'] as String,
      reason: json['reason'] as String,
      notes: json['notes'] as String,
      status: json['status'] as String,
      createdBy: json['createdBy'] as String,
      approvedBy: json['approvedBy'] as String?,
      approvedAt: json['approvedAt'] == null
          ? null
          : DateTime.parse(json['approvedAt'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      isSynced: json['isSynced'] as bool,
    );

Map<String, dynamic> _$StockAdjustmentToJson(StockAdjustment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'referenceNumber': instance.referenceNumber,
      'adjustmentDate': instance.adjustmentDate.toIso8601String(),
      'warehouseId': instance.warehouseId,
      'reason': instance.reason,
      'notes': instance.notes,
      'status': instance.status,
      'createdBy': instance.createdBy,
      'approvedBy': instance.approvedBy,
      'approvedAt': instance.approvedAt?.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'isSynced': instance.isSynced,
    };

StockAdjustmentItem _$StockAdjustmentItemFromJson(Map<String, dynamic> json) =>
    StockAdjustmentItem(
      id: json['id'] as String,
      adjustmentId: json['adjustmentId'] as String,
      productId: json['productId'] as String,
      currentQuantity: (json['currentQuantity'] as num).toDouble(),
      adjustmentQuantity: (json['adjustmentQuantity'] as num).toDouble(),
      newQuantity: (json['newQuantity'] as num).toDouble(),
      adjustmentType: json['adjustmentType'] as String,
      reason: json['reason'] as String,
      notes: json['notes'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      isSynced: json['isSynced'] as bool,
    );

Map<String, dynamic> _$StockAdjustmentItemToJson(
        StockAdjustmentItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'adjustmentId': instance.adjustmentId,
      'productId': instance.productId,
      'currentQuantity': instance.currentQuantity,
      'adjustmentQuantity': instance.adjustmentQuantity,
      'newQuantity': instance.newQuantity,
      'adjustmentType': instance.adjustmentType,
      'reason': instance.reason,
      'notes': instance.notes,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'isSynced': instance.isSynced,
    };
