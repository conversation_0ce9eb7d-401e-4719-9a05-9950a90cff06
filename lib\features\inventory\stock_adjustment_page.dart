import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:tijari_tech/widgets/custom_drowpdown.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../widgets/main_layout.dart';
import '../../providers/stock_adjustment_provider.dart';
import '../../core/utils/app_utils.dart';
import 'widgets/stock_adjustment_item_dialog.dart';

class StockAdjustmentPage extends ConsumerStatefulWidget {
  const StockAdjustmentPage({super.key});

  @override
  ConsumerState<StockAdjustmentPage> createState() =>
      _StockAdjustmentPageState();
}

class _StockAdjustmentPageState extends ConsumerState<StockAdjustmentPage> {
  final _formKey = GlobalKey<FormState>();
  final _referenceController = TextEditingController();
  final _notesController = TextEditingController();

  String _selectedWarehouse = 'المخزن الرئيسي';
  String _selectedReason = 'تالف';
  DateTime _selectedDate = DateTime.now();

  final List<Map<String, dynamic>> _adjustmentItems = [];

  @override
  void dispose() {
    _referenceController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'تسوية المخزون',
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            // Header Section
            _buildHeaderSection(),

            // Items Section
            Expanded(
              child: _buildItemsSection(),
            ),

            // Action Buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: EdgeInsets.all(16.r),
      color: Colors.grey[50],
      child: Column(
        children: [
          SizedBox(height: 16.h),

          Row(
            children: [
              // Reference Number
              Expanded(
                child: TextFormField(
                  controller: _referenceController,
                  decoration: const InputDecoration(
                    labelText: 'رقم المرجع',
                    hintText: 'ADJ-001',
                    prefixIcon: Icon(Icons.numbers),
                  ),
                ),
              ),
              SizedBox(width: 16.w),

              // Date
              Expanded(
                child: InkWell(
                  onTap: _selectDate,
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'التاريخ',
                      prefixIcon: Icon(Icons.calendar_today),
                    ),
                    child: Text(
                      '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          Row(
            children: [
              // Warehouse
              Expanded(
                  child: AppFilterDropdown(
                      label: 'الحالة',
                      value: _selectedWarehouse,
                      items: [
                        'المخزن الرئيسي',
                        'المخزن الفرعي',
                        'مخزن الفرع الثاني'
                      ].map((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        setState(() {
                          _selectedWarehouse = newValue!;
                        });
                      })),
              SizedBox(width: 16.w),

              // Reason
              Expanded(
                  child: AppFilterDropdown(
                label: 'سبب التسوية',
                value: _selectedReason,
                //  : Icon(Icons.info_outline),
                items: [
                  'تالف',
                  'فاقد',
                  'هدية',
                  'عينة',
                  'انتهاء صلاحية',
                  'خطأ جرد',
                  'أخرى'
                ].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedReason = newValue!;
                  });
                },
              ))
            ],
          ),
          SizedBox(height: 16.h),

          // Notes
          TextFormField(
            controller: _notesController,
            maxLines: 2,
            decoration: const InputDecoration(
              labelText: 'ملاحظات',
              hintText: 'أدخل ملاحظات إضافية...',
              prefixIcon: Icon(Icons.note_outlined),
              alignLabelWithHint: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemsSection() {
    return Column(
      children: [
        // Add Item Button
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.r),
          child: ElevatedButton.icon(
            onPressed: _showAddItemDialog,
            icon: const Icon(Icons.add),
            label: const Text('إضافة صنف'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ),

        // Items List
        Expanded(
          child:
              _adjustmentItems.isEmpty ? _buildEmptyState() : _buildItemsList(),
        ),

        // Summary
        if (_adjustmentItems.isNotEmpty) _buildSummary(),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 64.r,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد أصناف',
            style: AppTextStyles.titleMedium.copyWith(
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'اضغط على "إضافة صنف" لبدء التسوية',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemsList() {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16.r),
      itemCount: _adjustmentItems.length,
      itemBuilder: (context, index) {
        final item = _adjustmentItems[index];
        return Card(
          margin: EdgeInsets.only(bottom: 8.h),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: item['type'] == 'increase'
                  ? AppColors.success.withValues(alpha: 0.1)
                  : AppColors.error.withValues(alpha: 0.1),
              child: Icon(
                item['type'] == 'increase' ? Icons.add : Icons.remove,
                color: item['type'] == 'increase'
                    ? AppColors.success
                    : AppColors.error,
              ),
            ),
            title: Text(
              item['name'],
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (item['barcode'] != null &&
                    item['barcode'].toString().isNotEmpty)
                  Text('الباركود: ${item['barcode']}'),
                Text('الكمية الحالية: ${item['currentQty']}'),
                Text(
                    '${item['type'] == 'increase' ? 'زيادة' : 'نقص'}: ${item['adjustmentQty']}'),
                Text('الكمية الجديدة: ${item['newQty']}'),
                if (item['reason'] != null)
                  Text('السبب: ${item['reason']}',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12)),
                if (item['notes'] != null &&
                    item['notes'].toString().isNotEmpty)
                  Text('ملاحظات: ${item['notes']}',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12)),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'edit') {
                  _editItem(index);
                } else if (value == 'delete') {
                  _deleteItem(index);
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit_outlined),
                      SizedBox(width: 8),
                      Text('تعديل'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete_outlined, color: Colors.red),
                      SizedBox(width: 8),
                      Text('حذف', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSummary() {
    final totalItems = _adjustmentItems.length;
    final increaseItems =
        _adjustmentItems.where((item) => item['type'] == 'increase').length;
    final decreaseItems =
        _adjustmentItems.where((item) => item['type'] == 'decrease').length;

    return Container(
      padding: EdgeInsets.all(16.r),
      color: Colors.grey[100],
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildSummaryItem(
              'إجمالي الأصناف', totalItems.toString(), AppColors.primary),
          _buildSummaryItem(
              'زيادة', increaseItems.toString(), AppColors.success),
          _buildSummaryItem('نقص', decreaseItems.toString(), AppColors.error),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: AppTextStyles.titleLarge.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: EdgeInsets.all(16.r),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => context.pop(),
              child: const Text('إلغاء'),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: ElevatedButton(
              onPressed: _adjustmentItems.isEmpty ? null : _saveAdjustment,
              child: const Text('حفظ التسوية'),
            ),
          ),
        ],
      ),
    );
  }

  void _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _showAddItemDialog() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => StockAdjustmentItemDialog(
        warehouseId: _getWarehouseId(),
      ),
    );

    if (result != null) {
      setState(() {
        _adjustmentItems.add(result);
      });
    }
  }

  void _editItem(int index) async {
    final item = _adjustmentItems[index];
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => StockAdjustmentItemDialog(
        warehouseId: _getWarehouseId(),
        existingItem: item,
      ),
    );

    if (result != null) {
      setState(() {
        _adjustmentItems[index] = result;
      });
    }
  }

  String _getWarehouseId() {
    // For now, return a default warehouse ID
    // In a real app, this would be selected from the warehouse dropdown
    return 'warehouse_001';
  }

  void _deleteItem(int index) {
    setState(() {
      _adjustmentItems.removeAt(index);
    });
  }

  void _saveAdjustment() async {
    if (_formKey.currentState!.validate() && _adjustmentItems.isNotEmpty) {
      try {
        // Generate reference number if empty
        String referenceNumber = _referenceController.text.trim();
        if (referenceNumber.isEmpty) {
          referenceNumber = await ref
              .read(stockAdjustmentManagementProvider.notifier)
              .generateReferenceNumber();
        }

        final success = await ref
            .read(stockAdjustmentManagementProvider.notifier)
            .createAdjustment(
              referenceNumber: referenceNumber,
              warehouseId: _getWarehouseId(),
              reason: _selectedReason,
              notes: _notesController.text.trim(),
              adjustmentDate: _selectedDate,
              createdBy: 'current_user', // TODO: Get from auth service
              items: _adjustmentItems,
            );

        if (mounted) {
          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم حفظ تسوية المخزون بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
            context.pop();
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('فشل في حفظ تسوية المخزون'),
                backgroundColor: AppColors.error,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حفظ تسوية المخزون: ${e.toString()}'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }
}
