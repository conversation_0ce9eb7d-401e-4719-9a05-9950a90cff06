// import '../core/utils/app_utils.dart';
// import '../core/exceptions/app_exceptions.dart';
// import 'default_data_service.dart';
// import 'entity_relationship_service.dart';
// import 'integrated_business_service.dart';

// /// خدمة تهيئة النظام المتكامل
// /// تضمن تهيئة جميع الخدمات والبيانات الافتراضية عند بدء التطبيق
// class SystemInitializationService {
//   static final SystemInitializationService _instance = SystemInitializationService._internal();
//   factory SystemInitializationService() => _instance;
//   SystemInitializationService._internal();

//   final DefaultDataService _defaultDataService = DefaultDataService();
//   final EntityRelationshipService _relationshipService = EntityRelationshipService();
//   final IntegratedBusinessService _businessService = IntegratedBusinessService();

//   bool _isInitialized = false;

//   /// تهيئة النظام بالكامل
//   Future<Map<String, dynamic>> initializeSystem() async {
//     if (_isInitialized) {
//       return {
//         'success': true,
//         'message': 'النظام مهيأ مسبقاً',
//         'initialization_time': 0,
//       };
//     }

//     final startTime = DateTime.now();
    
//     try {
//       AppUtils.logInfo('=== بدء تهيئة النظام المتكامل ===');

//       final initializationResults = <String, dynamic>{
//         'success': true,
//         'steps': <Map<String, dynamic>>[],
//         'warnings': <String>[],
//         'errors': <String>[],
//       };

//       // الخطوة 1: تهيئة البيانات الافتراضية
//       await _initializeDefaultData(initializationResults);

//       // الخطوة 2: التحقق من العلاقات
//       await _validateSystemRelationships(initializationResults);

//       // الخطوة 3: التحقق من سلامة النظام
//       await _performSystemHealthCheck(initializationResults);

//       // الخطوة 4: إعداد الخدمات المتكاملة
//       await _setupIntegratedServices(initializationResults);

//       final endTime = DateTime.now();
//       final initializationTime = endTime.difference(startTime).inMilliseconds;

//       _isInitialized = true;

//       AppUtils.logInfo('=== تم تهيئة النظام بنجاح في ${initializationTime}ms ===');

//       return {
//         'success': true,
//         'message': 'تم تهيئة النظام بنجاح',
//         'initialization_time': initializationTime,
//         'details': initializationResults,
//       };
//     } catch (e) {
//       AppUtils.logError('خطأ في تهيئة النظام', e);
//       return {
//         'success': false,
//         'message': 'فشل في تهيئة النظام: ${e.toString()}',
//         'initialization_time': DateTime.now().difference(startTime).inMilliseconds,
//       };
//     }
//   }

//   /// تهيئة البيانات الافتراضية
//   Future<void> _initializeDefaultData(Map<String, dynamic> results) async {
//     try {
//       AppUtils.logInfo('الخطوة 1: تهيئة البيانات الافتراضية...');
      
//       await _defaultDataService.ensureDefaultDataExists();
      
//       results['steps'].add({
//         'step': 1,
//         'name': 'تهيئة البيانات الافتراضية',
//         'status': 'completed',
//         'message': 'تم إنشاء/التحقق من جميع البيانات الافتراضية',
//       });

//       AppUtils.logInfo('✅ تم تهيئة البيانات الافتراضية بنجاح');
//     } catch (e) {
//       results['errors'].add('فشل في تهيئة البيانات الافتراضية: ${e.toString()}');
//       results['steps'].add({
//         'step': 1,
//         'name': 'تهيئة البيانات الافتراضية',
//         'status': 'failed',
//         'message': 'فشل في التهيئة: ${e.toString()}',
//       });
//       rethrow;
//     }
//   }

//   /// التحقق من العلاقات في النظام
//   Future<void> _validateSystemRelationships(Map<String, dynamic> results) async {
//     try {
//       AppUtils.logInfo('الخطوة 2: التحقق من العلاقات...');

//       // التحقق من الكيانات الافتراضية
//       final defaultEntities = await _relationshipService.getDefaultEntities();
      
//       if (defaultEntities.isEmpty) {
//         throw AppException('لم يتم العثور على الكيانات الافتراضية');
//       }

//       // التحقق من العلاقات الأساسية
//       final relationshipValidation = await _relationshipService.validateEntityRelationships(
//         productIds: [], // فحص أساسي بدون منتجات
//       );

//       if (!relationshipValidation['valid']) {
//         final errors = relationshipValidation['errors'] as List<String>;
//         throw AppException('فشل في التحقق من العلاقات: ${errors.join(', ')}');
//       }

//       results['steps'].add({
//         'step': 2,
//         'name': 'التحقق من العلاقات',
//         'status': 'completed',
//         'message': 'تم التحقق من جميع العلاقات بنجاح',
//         'default_entities_count': defaultEntities.length,
//       });

//       // إضافة التحذيرات إن وجدت
//       final warnings = relationshipValidation['warnings'] as List<String>?;
//       if (warnings != null && warnings.isNotEmpty) {
//         results['warnings'].addAll(warnings);
//       }

//       AppUtils.logInfo('✅ تم التحقق من العلاقات بنجاح');
//     } catch (e) {
//       results['errors'].add('فشل في التحقق من العلاقات: ${e.toString()}');
//       results['steps'].add({
//         'step': 2,
//         'name': 'التحقق من العلاقات',
//         'status': 'failed',
//         'message': 'فشل في التحقق: ${e.toString()}',
//       });
//       rethrow;
//     }
//   }

//   /// فحص سلامة النظام
//   Future<void> _performSystemHealthCheck(Map<String, dynamic> results) async {
//     try {
//       AppUtils.logInfo('الخطوة 3: فحص سلامة النظام...');

//       final healthCheck = <String, dynamic>{
//         'database_connection': false,
//         'default_entities': false,
//         'services_ready': false,
//       };

//       // فحص الاتصال بقاعدة البيانات
//       try {
//         final defaultEntities = await _relationshipService.getDefaultEntities();
//         healthCheck['database_connection'] = true;
//         healthCheck['default_entities'] = defaultEntities.isNotEmpty;
//       } catch (e) {
//         AppUtils.logError('فشل في فحص قاعدة البيانات', e);
//       }

//       // فحص جاهزية الخدمات
//       try {
//         final businessServiceSummary = await _businessService.getDefaultEntitiesSummary();
//         healthCheck['services_ready'] = businessServiceSummary.isNotEmpty;
//       } catch (e) {
//         AppUtils.logError('فشل في فحص الخدمات', e);
//       }

//       // تقييم النتائج
//       final allHealthy = healthCheck.values.every((status) => status == true);
      
//       results['steps'].add({
//         'step': 3,
//         'name': 'فحص سلامة النظام',
//         'status': allHealthy ? 'completed' : 'warning',
//         'message': allHealthy ? 'النظام سليم وجاهز' : 'توجد مشاكل في بعض المكونات',
//         'health_check': healthCheck,
//       });

//       if (!allHealthy) {
//         results['warnings'].add('بعض مكونات النظام قد لا تعمل بشكل صحيح');
//       }

//       AppUtils.logInfo('✅ تم فحص سلامة النظام');
//     } catch (e) {
//       results['errors'].add('فشل في فحص سلامة النظام: ${e.toString()}');
//       results['steps'].add({
//         'step': 3,
//         'name': 'فحص سلامة النظام',
//         'status': 'failed',
//         'message': 'فشل في الفحص: ${e.toString()}',
//       });
//       // لا نرمي الخطأ هنا لأن فحص السلامة ليس حرجاً
//     }
//   }

//   /// إعداد الخدمات المتكاملة
//   Future<void> _setupIntegratedServices(Map<String, dynamic> results) async {
//     try {
//       AppUtils.logInfo('الخطوة 4: إعداد الخدمات المتكاملة...');

//       // التأكد من جاهزية خدمة العمليات التجارية
//       final defaultEntitiesSummary = await _businessService.getDefaultEntitiesSummary();
      
//       if (defaultEntitiesSummary.isEmpty) {
//         throw AppException('خدمة العمليات التجارية غير جاهزة');
//       }

//       results['steps'].add({
//         'step': 4,
//         'name': 'إعداد الخدمات المتكاملة',
//         'status': 'completed',
//         'message': 'تم إعداد جميع الخدمات المتكاملة بنجاح',
//         'available_services': [
//           'خدمة البيانات الافتراضية',
//           'خدمة إدارة العلاقات',
//           'خدمة العمليات التجارية المتكاملة',
//         ],
//       });

//       AppUtils.logInfo('✅ تم إعداد الخدمات المتكاملة بنجاح');
//     } catch (e) {
//       results['errors'].add('فشل في إعداد الخدمات المتكاملة: ${e.toString()}');
//       results['steps'].add({
//         'step': 4,
//         'name': 'إعداد الخدمات المتكاملة',
//         'status': 'failed',
//         'message': 'فشل في الإعداد: ${e.toString()}',
//       });
//       rethrow;
//     }
//   }

//   /// الحصول على ملخص حالة النظام
//   Future<Map<String, dynamic>> getSystemStatus() async {
//     try {
//       final status = <String, dynamic>{
//         'initialized': _isInitialized,
//         'timestamp': DateTime.now().toIso8601String(),
//       };

//       if (_isInitialized) {
//         // الحصول على معلومات الكيانات الافتراضية
//         final defaultEntities = await _relationshipService.getDefaultEntities();
//         status['default_entities'] = defaultEntities;
//         status['default_entities_count'] = defaultEntities.length;

//         // الحصول على ملخص الخدمات
//         final businessServiceSummary = await _businessService.getDefaultEntitiesSummary();
//         status['business_service_ready'] = businessServiceSummary.isNotEmpty;
//       }

//       return status;
//     } catch (e) {
//       AppUtils.logError('خطأ في الحصول على حالة النظام', e);
//       return {
//         'initialized': _isInitialized,
//         'error': e.toString(),
//         'timestamp': DateTime.now().toIso8601String(),
//       };
//     }
//   }

//   /// إعادة تهيئة النظام
//   Future<Map<String, dynamic>> reinitializeSystem() async {
//     AppUtils.logInfo('إعادة تهيئة النظام...');
//     _isInitialized = false;
//     return await initializeSystem();
//   }

//   /// التحقق من جاهزية النظام للعمليات التجارية
//   Future<bool> isSystemReadyForBusiness() async {
//     try {
//       if (!_isInitialized) {
//         return false;
//       }

//       // التحقق من وجود الكيانات الافتراضية
//       final defaultEntities = await _relationshipService.getDefaultEntities();
//       if (defaultEntities.isEmpty) {
//         return false;
//       }

//       // التحقق من جاهزية خدمة العمليات التجارية
//       final businessServiceSummary = await _businessService.getDefaultEntitiesSummary();
//       if (businessServiceSummary.isEmpty) {
//         return false;
//       }

//       return true;
//     } catch (e) {
//       AppUtils.logError('خطأ في التحقق من جاهزية النظام', e);
//       return false;
//     }
//   }
// }
