// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'warehouse.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Warehouse _$WarehouseFromJson(Map<String, dynamic> json) => Warehouse(
      id: json['id'] as String,
      name: json['name'] as String,
      location: json['location'] as String?,
      address: json['address'] as String?,
      managerId: json['managerId'] as String?,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      isDefault: json['isDefault'] as bool? ?? false,
      description: json['description'] as String?,
      notes: json['notes'] as String?,
      capacity: (json['capacity'] as num?)?.toDouble(),
      warehouseType: json['warehouseType'] as String? ?? 'main',
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      branchId: json['branchId'] as String?,
      isSynced: json['isSynced'] as bool? ?? false,
      managerName: json['managerName'] as String?,
      branchName: json['branchName'] as String?,
      productCount: (json['productCount'] as num?)?.toInt(),
      totalValue: (json['totalValue'] as num?)?.toDouble(),
      usedCapacity: (json['usedCapacity'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$WarehouseToJson(Warehouse instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'location': instance.location,
      'address': instance.address,
      'managerId': instance.managerId,
      'phone': instance.phone,
      'email': instance.email,
      'isActive': instance.isActive,
      'isDefault': instance.isDefault,
      'description': instance.description,
      'notes': instance.notes,
      'capacity': instance.capacity,
      'warehouseType': instance.warehouseType,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'branchId': instance.branchId,
      'isSynced': instance.isSynced,
      'managerName': instance.managerName,
      'branchName': instance.branchName,
      'productCount': instance.productCount,
      'totalValue': instance.totalValue,
      'usedCapacity': instance.usedCapacity,
    };
