import 'dart:async';
import 'dart:convert';
import '../storage/secure_storage_service.dart';

/// بيانات المستخدم المسجَّل
class AuthUser {
  final String id;
  final String name;
  final String email;
  final String role;
  final String token;
  final DateTime expiresAt;

  const AuthUser({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    required this.token,
    required this.expiresAt,
  });

  factory AuthUser.fromJson(Map<String, dynamic> json) => AuthUser(
        id: json['id'],
        name: json['name'],
        email: json['email'],
        role: json['role'],
        token: json['token'],
        expiresAt: DateTime.parse(json['expiresAt']),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'email': email,
        'role': role,
        'token': token,
        'expiresAt': expiresAt.toIso8601String(),
      };
}

/// خدمة مصادقة مركزية
class AuthService {
  AuthService._();
  static final AuthService _instance = AuthService._();
  factory AuthService() => _instance;

  static final _storage = SecureStorageService();
  static const _userKey = 'auth_user';
  static const _refreshTokenKey = 'refresh_token';

  AuthUser? _currentUser;

  /// دفق يُنبِّه المستمعين بتغيّر حالة المصادقة
  final _controller = StreamController<AuthUser?>.broadcast();
  Stream<AuthUser?> get authStateChanges => _controller.stream;

  /// تسجيل الدخول (dummy implementation – استبدله بـ API call)
  Future<void> login({
    required String email,
    required String password,
  }) async {
    // TODO: استبدل بالـ API الحقيقي
    final mockResponse = {
      'id': '1',
      'name': 'Test User',
      'email': email,
      'role': 'admin',
      'token': _generateMockJwt(),
      'expiresAt':
          DateTime.now().add(const Duration(hours: 2)).toIso8601String(),
      'refreshToken': 'dummy_refresh_token',
    };

    final user = AuthUser.fromJson({
      ...mockResponse,
      'token': mockResponse['token'],
      'expiresAt': DateTime.parse(mockResponse['expiresAt']!),
    });

    await _saveUser(user, mockResponse['refreshToken']!);
    _currentUser = user;
    _controller.add(user);
  }

  /// تسجيل الخروج
  static Future<void> logout() async {
    await _storage.delete(key: _userKey);
    await _storage.delete(key: _refreshTokenKey);
    _instance._currentUser = null;
    _instance._controller.add(null);
  }

  /// هل المستخدم مسجَّل دخول؟
  static bool isLoggedIn() => _instance._currentUser != null;

  /// هل الجلسة منتهية؟
  static bool isSessionExpired() {
    final user = _instance._currentUser;
    return user == null || user.expiresAt.isBefore(DateTime.now());
  }

  /// تجديد التوكن
  static Future<bool> refreshSession() async {
    final refreshToken = await _storage.read(key: _refreshTokenKey);
    if (refreshToken == null) return false;

    // TODO: استدعاء API تجديد التوكن
    final newToken = _generateMockJwt();
    final newExpiry = DateTime.now().add(const Duration(hours: 2));

    final newUser = AuthUser(
      id: _instance._currentUser!.id,
      name: _instance._currentUser!.name,
      email: _instance._currentUser!.email,
      role: _instance._currentUser!.role,
      token: newToken,
      expiresAt: newExpiry,
    );

    await _saveUser(newUser, refreshToken);
    _instance._currentUser = newUser;
    _instance._controller.add(newUser);
    return true;
  }

  /// جلب المستخدم الحالي (قد يكون null)
  static AuthUser? get currentUser => _instance._currentUser;

  /* -------------------------------------------------- */
  /*  Private helpers                                   */
  /* -------------------------------------------------- */

  static Future<void> _saveUser(AuthUser user, String refreshToken) async {
    await _storage.write(key: _userKey, value: jsonEncode(user.toJson()));
    await _storage.write(key: _refreshTokenKey, value: refreshToken);
  }

  /// استعادة الجلسة عند بدء التطبيق
  static Future<void> init() async {
    final userJson = await _storage.read(key: _userKey);
    if (userJson == null) return;

    try {
      final user = AuthUser.fromJson(jsonDecode(userJson));
      if (user.expiresAt.isAfter(DateTime.now())) {
        _instance._currentUser = user;
        _instance._controller.add(user);
      } else {
        await logout();
      }
    } catch (_) {
      await logout();
    }
  }

  /// توليد JWT وهمي للاختبار فقط
  static String _generateMockJwt() {
    final payload = {
      "sub": "1",
      "exp": (DateTime.now()
                  .add(const Duration(hours: 2))
                  .millisecondsSinceEpoch ~/
              1000)
          .toString(),
    };
    return base64Url.encode(utf8.encode(jsonEncode(payload)));
  }
}
