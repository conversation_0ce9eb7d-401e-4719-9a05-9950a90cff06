import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../router/routes.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/responsive_wrapper.dart';

class CashPage extends ConsumerWidget {
  const CashPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MainLayout(
      title: 'الصناديق والمعاملات',
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Cash Summary
            _buildCashSummary(context),
            SizedBox(height: 24.h),
            
            // Quick Actions
            _buildQuickActions(context),
            SizedBox(height: 24.h),
            
            // Recent Transactions
            _buildRecentTransactions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildCashSummary(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ملخص الصناديق',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 4,
          desktopColumns: 4,
          children: [
            _buildSummaryCard(
              context,
              title: 'إجمالي النقد',
              value: '125,750 ر.س',
              icon: Icons.account_balance_wallet_outlined,
              color: AppColors.success,
            ),
            _buildSummaryCard(
              context,
              title: 'الصندوق الرئيسي',
              value: '85,200 ر.س',
              icon: Icons.savings_outlined,
              color: AppColors.primary,
            ),
            _buildSummaryCard(
              context,
              title: 'معاملات اليوم',
              value: '47',
              icon: Icons.receipt_long_outlined,
              color: AppColors.info,
            ),
            _buildSummaryCard(
              context,
              title: 'صافي التدفق',
              value: '+12,500 ر.س',
              icon: Icons.trending_up_outlined,
              color: AppColors.success,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32.r,
              color: color,
            ),
            SizedBox(height: 8.h),
            Text(
              value,
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              title,
              style: AppTextStyles.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 3,
          desktopColumns: 4,
          children: [
            _buildActionCard(
              context,
              title: 'إيصال قبض',
              subtitle: 'تسجيل إيصال قبض جديد',
              icon: Icons.receipt_outlined,
              color: AppColors.success,
              onTap: () => context.go(AppRoutes.receipt),
            ),
            _buildActionCard(
              context,
              title: 'إيصال صرف',
              subtitle: 'تسجيل إيصال صرف جديد',
              icon: Icons.payment_outlined,
              color: AppColors.error,
              onTap: () => context.go(AppRoutes.payment),
            ),
            _buildActionCard(
              context,
              title: 'إدارة الصناديق',
              subtitle: 'عرض وإدارة الصناديق',
              icon: Icons.account_balance_outlined,
              color: AppColors.primary,
              onTap: () => context.go(AppRoutes.cashBoxes),
            ),
            _buildActionCard(
              context,
              title: 'سجل المعاملات',
              subtitle: 'عرض جميع المعاملات',
              icon: Icons.history_outlined,
              color: AppColors.info,
              onTap: () => context.go(AppRoutes.transactions),
            ),
            _buildActionCard(
              context,
              title: 'تحويل بين الصناديق',
              subtitle: 'تحويل مبلغ بين الصناديق',
              icon: Icons.swap_horiz_outlined,
              color: AppColors.warning,
              onTap: () {
                // TODO: Navigate to transfer
              },
            ),
            _buildActionCard(
              context,
              title: 'تقرير التدفق النقدي',
              subtitle: 'تقرير مفصل للتدفق النقدي',
              icon: Icons.analytics_outlined,
              color: AppColors.accent,
              onTap: () => context.go(AppRoutes.cashFlowReport),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32.r,
                color: color,
              ),
              SizedBox(height: 8.h),
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 4.h),
              Text(
                subtitle,
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentTransactions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'المعاملات الأخيرة',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => context.go(AppRoutes.transactions),
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Card(
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 6,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final transactions = [
                {
                  'type': 'receipt',
                  'title': 'إيصال قبض',
                  'description': 'من العميل: أحمد محمد',
                  'amount': '+2,500',
                  'time': '10:30 ص',
                  'icon': Icons.receipt_outlined,
                  'color': AppColors.success,
                },
                {
                  'type': 'payment',
                  'title': 'إيصال صرف',
                  'description': 'مصروفات إدارية',
                  'amount': '-850',
                  'time': '09:15 ص',
                  'icon': Icons.payment_outlined,
                  'color': AppColors.error,
                },
                {
                  'type': 'sale',
                  'title': 'فاتورة مبيعات',
                  'description': 'فاتورة #INV001234',
                  'amount': '+1,750',
                  'time': '08:45 ص',
                  'icon': Icons.point_of_sale_outlined,
                  'color': AppColors.primary,
                },
              ];
              
              final transaction = transactions[index % transactions.length];
              
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: (transaction['color'] as Color).withValues(alpha: 0.1),
                  child: Icon(
                    transaction['icon'] as IconData,
                    color: transaction['color'] as Color,
                    size: 20.r,
                  ),
                ),
                title: Text(
                  transaction['title'] as String,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                subtitle: Text(
                  transaction['description'] as String,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${transaction['amount']} ر.س',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: (transaction['amount'] as String).startsWith('+')
                            ? AppColors.success
                            : AppColors.error,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      transaction['time'] as String,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
                onTap: () {
                  // TODO: Navigate to transaction details
                },
              );
            },
          ),
        ),
      ],
    );
  }
}
