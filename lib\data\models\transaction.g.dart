// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Transactions _$TransactionsFromJson(Map<String, dynamic> json) => Transactions(
      id: json['id'] as String,
      type: json['type'] as String,
      subType: json['subType'] as String,
      referenceNumber: json['referenceNumber'] as String,
      amount: (json['amount'] as num).toDouble(),
      description: json['description'] as String?,
      notes: json['notes'] as String?,
      transactionDate: DateTime.parse(json['transactionDate'] as String),
      customerId: json['customerId'] as String?,
      supplierId: json['supplierId'] as String?,
      saleId: json['saleId'] as String?,
      purchaseId: json['purchaseId'] as String?,
      cashBoxId: json['cashBoxId'] as String?,
      bankAccountId: json['bankAccountId'] as String?,
      paymentMethod: json['paymentMethod'] as String,
      status: json['status'] as String? ?? 'completed',
      createdBy: json['createdBy'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      branchId: json['branchId'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$TransactionsToJson(Transactions instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'subType': instance.subType,
      'referenceNumber': instance.referenceNumber,
      'amount': instance.amount,
      'description': instance.description,
      'notes': instance.notes,
      'transactionDate': instance.transactionDate.toIso8601String(),
      'customerId': instance.customerId,
      'supplierId': instance.supplierId,
      'saleId': instance.saleId,
      'purchaseId': instance.purchaseId,
      'cashBoxId': instance.cashBoxId,
      'bankAccountId': instance.bankAccountId,
      'paymentMethod': instance.paymentMethod,
      'status': instance.status,
      'createdBy': instance.createdBy,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'branchId': instance.branchId,
      'metadata': instance.metadata,
    };
