import '../constants/app_constants.dart';

class AppValidators {
  // Required Field Validator
  static String? required(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return fieldName != null 
          ? '$fieldName مطلوب'
          : AppConstants.validationErrorMessage;
    }
    return null;
  }
  
  // Email Validator
  static String? email(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Allow empty for optional fields
    }
    
    final emailRegex = RegExp(AppConstants.emailRegex);
    if (!emailRegex.hasMatch(value.trim())) {
      return 'بريد إلكتروني غير صحيح';
    }
    return null;
  }
  
  // Phone Number Validator
  static String? phone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Allow empty for optional fields
    }
    
    // Remove all non-digit characters
    String cleaned = value.replaceAll(RegExp(r'[^\d]'), '');
    
    // Check Saudi phone number patterns
    if (cleaned.length == 9 && cleaned.startsWith('5')) {
      return null; // Valid 9-digit mobile
    } else if (cleaned.length == 10 && cleaned.startsWith('05')) {
      return null; // Valid 10-digit mobile with 0
    } else if (cleaned.length == 12 && cleaned.startsWith('966')) {
      return null; // Valid with country code
    }
    
    return 'رقم هاتف غير صحيح';
  }
  
  // Password Validator
  static String? password(String? value) {
    if (value == null || value.isEmpty) {
      return 'كلمة المرور مطلوبة';
    }
    
    if (value.length < AppConstants.minPasswordLength) {
      return 'كلمة المرور يجب أن تكون ${AppConstants.minPasswordLength} أحرف على الأقل';
    }
    
    return null;
  }
  
  // Confirm Password Validator
  static String? confirmPassword(String? value, String? originalPassword) {
    if (value == null || value.isEmpty) {
      return 'تأكيد كلمة المرور مطلوب';
    }
    
    if (value != originalPassword) {
      return 'كلمات المرور غير متطابقة';
    }
    
    return null;
  }
  
  // Number Validator
  static String? number(String? value, {String? fieldName, double? min, double? max}) {
    if (value == null || value.trim().isEmpty) {
      return fieldName != null 
          ? '$fieldName مطلوب'
          : 'هذا الحقل مطلوب';
    }
    
    final number = double.tryParse(value.trim());
    if (number == null) {
      return 'يجب أن يكون رقماً صحيحاً';
    }
    
    if (min != null && number < min) {
      return 'يجب أن يكون أكبر من أو يساوي $min';
    }
    
    if (max != null && number > max) {
      return 'يجب أن يكون أقل من أو يساوي $max';
    }
    
    return null;
  }
  
  // Positive Number Validator
  static String? positiveNumber(String? value, {String? fieldName}) {
    final result = number(value, fieldName: fieldName, min: 0);
    if (result != null) return result;
    
    final num = double.parse(value!.trim());
    if (num <= 0) {
      return 'يجب أن يكون رقماً موجباً';
    }
    
    return null;
  }
  
  // Price Validator
  static String? price(String? value) {
    return positiveNumber(value, fieldName: 'السعر');
  }
  
  // Quantity Validator
  static String? quantity(String? value) {
    return positiveNumber(value, fieldName: 'الكمية');
  }
  
  // Barcode Validator
  static String? barcode(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Allow empty for optional fields
    }
    
    // Remove all non-digit characters
    String cleaned = value.replaceAll(RegExp(r'[^\d]'), '');
    
    if (cleaned.length < 8 || cleaned.length > 13) {
      return 'الباركود يجب أن يكون بين 8 و 13 رقماً';
    }
    
    return null;
  }
  
  // Name Length Validator
  static String? nameLength(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return fieldName != null 
          ? '$fieldName مطلوب'
          : 'هذا الحقل مطلوب';
    }
    
    if (value.trim().length > AppConstants.maxNameLength) {
      return 'يجب أن يكون أقل من ${AppConstants.maxNameLength} حرف';
    }
    
    return null;
  }
  
  // Description Length Validator
  static String? descriptionLength(String? value) {
    if (value != null && value.trim().length > AppConstants.maxDescriptionLength) {
      return 'يجب أن يكون أقل من ${AppConstants.maxDescriptionLength} حرف';
    }
    return null;
  }
  
  // Notes Length Validator
  static String? notesLength(String? value) {
    if (value != null && value.trim().length > AppConstants.maxNotesLength) {
      return 'يجب أن يكون أقل من ${AppConstants.maxNotesLength} حرف';
    }
    return null;
  }
  
  // Tax Number Validator (Saudi)
  static String? taxNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Allow empty for optional fields
    }
    
    // Remove all non-digit characters
    String cleaned = value.replaceAll(RegExp(r'[^\d]'), '');
    
    if (cleaned.length != 15) {
      return 'الرقم الضريبي يجب أن يكون 15 رقماً';
    }
    
    return null;
  }
  
  // Commercial Register Validator (Saudi)
  static String? commercialRegister(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Allow empty for optional fields
    }
    
    // Remove all non-digit characters
    String cleaned = value.replaceAll(RegExp(r'[^\d]'), '');
    
    if (cleaned.length != 10) {
      return 'السجل التجاري يجب أن يكون 10 أرقام';
    }
    
    return null;
  }
  
  // Percentage Validator
  static String? percentage(String? value, {String? fieldName}) {
    final result = number(value, fieldName: fieldName, min: 0, max: 100);
    return result;
  }
  
  // Date Validator
  static String? date(DateTime? value, {String? fieldName}) {
    if (value == null) {
      return fieldName != null 
          ? '$fieldName مطلوب'
          : 'التاريخ مطلوب';
    }
    return null;
  }
  
  // Future Date Validator
  static String? futureDate(DateTime? value, {String? fieldName}) {
    final result = date(value, fieldName: fieldName);
    if (result != null) return result;
    
    if (value!.isBefore(DateTime.now())) {
      return 'يجب أن يكون التاريخ في المستقبل';
    }
    
    return null;
  }
  
  // Past Date Validator
  static String? pastDate(DateTime? value, {String? fieldName}) {
    final result = date(value, fieldName: fieldName);
    if (result != null) return result;
    
    if (value!.isAfter(DateTime.now())) {
      return 'يجب أن يكون التاريخ في الماضي';
    }
    
    return null;
  }
  
  // Username Validator
  static String? username(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'اسم المستخدم مطلوب';
    }
    
    if (value.trim().length < 3) {
      return 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
    }
    
    if (value.trim().length > 20) {
      return 'اسم المستخدم يجب أن يكون أقل من 20 حرف';
    }
    
    // Only allow letters, numbers, and underscores
    if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(value.trim())) {
      return 'اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط';
    }
    
    return null;
  }
  
  // Combine multiple validators
  static String? combine(String? value, List<String? Function(String?)> validators) {
    for (final validator in validators) {
      final result = validator(value);
      if (result != null) return result;
    }
    return null;
  }
  
  // Custom validator function type
  static String? Function(String?) custom(bool Function(String?) test, String errorMessage) {
    return (String? value) {
      if (value != null && !test(value)) {
        return errorMessage;
      }
      return null;
    };
  }
}
