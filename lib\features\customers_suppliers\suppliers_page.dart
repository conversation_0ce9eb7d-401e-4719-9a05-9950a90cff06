// suppliers_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:tijari_tech/data/models/supplier.dart';
import 'package:tijari_tech/providers/supplier_provider.dart';
import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../shared/widgets/loading_widget.dart';
import '../../shared/widgets/error_display_widget.dart';
import '../../shared/widgets/empty_state_widget.dart';
import '../../widgets/main_layout.dart';

/// صفحة الموردين
/// Suppliers Page
class SuppliersPage extends ConsumerStatefulWidget {
  const SuppliersPage({super.key});

  @override
  ConsumerState<SuppliersPage> createState() => _SuppliersPageState();
}

class _SuppliersPageState extends ConsumerState<SuppliersPage> {
  final _searchController = TextEditingController();
  final _scrollController = ScrollController();
  SupplierBalanceStatus? _selectedBalanceFilter;

  @override
  void initState() {
    super.initState();
    // تحديث البيانات عند بدء الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(supplierProvider.notifier).refresh();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final supplierState = ref.watch(supplierProvider);
    final supplierNotifier = ref.read(supplierProvider.notifier);

    return MainLayout(
      title: 'الموردين',
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToAddSupplier(),
        backgroundColor: AppColors.secondary,
        child: const Icon(Icons.business, color: Colors.white),
      ),
      child: Column(
        children: [
          // شريط البحث والفلاتر
          _buildSearchAndFilters(supplierNotifier),
          // الإحصائيات السريعة
          _buildQuickStats(supplierState),
          // قائمة الموردين
          Expanded(
            child: _buildSuppliersList(supplierState, supplierNotifier),
          ),
        ],
      ),
    );
  }

  /// بناء شريط البحث والفلاتر
  Widget _buildSearchAndFilters(SupplierNotifier notifier) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في الموردين...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        notifier.setSearchQuery('');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: const BorderSide(color: AppColors.secondary),
              ),
            ),
            onChanged: (value) {
              notifier.setSearchQuery(value);
            },
          ),
          SizedBox(height: 12.h),
          // فلاتر حالة الرصيد
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip(
                  label: 'الكل',
                  isSelected: _selectedBalanceFilter == null,
                  onTap: () {
                    setState(() => _selectedBalanceFilter = null);
                    notifier.setBalanceFilter(null);
                  },
                ),
                SizedBox(width: 8.w),
                _buildFilterChip(
                  label: 'مدين',
                  isSelected:
                      _selectedBalanceFilter == SupplierBalanceStatus.theyOwe,
                  onTap: () {
                    setState(() =>
                        _selectedBalanceFilter = SupplierBalanceStatus.theyOwe);
                    notifier.setBalanceFilter(SupplierBalanceStatus.theyOwe);
                  },
                ),
                SizedBox(width: 8.w),
                _buildFilterChip(
                  label: 'نحن ندين لهم',
                  isSelected:
                      _selectedBalanceFilter == SupplierBalanceStatus.weOwe,
                  onTap: () {
                    setState(() =>
                        _selectedBalanceFilter = SupplierBalanceStatus.weOwe);
                    notifier.setBalanceFilter(SupplierBalanceStatus.weOwe);
                  },
                ),
                SizedBox(width: 8.w),
                _buildFilterChip(
                  label: 'متوازن',
                  isSelected:
                      _selectedBalanceFilter == SupplierBalanceStatus.balanced,
                  onTap: () {
                    setState(() => _selectedBalanceFilter =
                        SupplierBalanceStatus.balanced);
                    notifier.setBalanceFilter(SupplierBalanceStatus.balanced);
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رقاقة الفلتر
  Widget _buildFilterChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.secondary : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(
            color: isSelected ? AppColors.secondary : Colors.grey.shade300,
          ),
        ),
        child: Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: isSelected ? Colors.white : Colors.grey.shade700,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  /// بناء الإحصائيات السريعة
  Widget _buildQuickStats(SupplierState state) {
    // استخدام الإحصائيات المتاحة أو حسابها يدوياً في حالة عدم توفرها
    final activeCount =
        state.statistics?['active_count'] ?? state.activeSuppliersCount;
    final debtCount =
        state.statistics?['debt_count'] ?? state.weOweSuppliersCount;
    final creditCount =
        state.statistics?['credit_count'] ?? state.theyOweSuppliersCount;

    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              title: 'العدد الكلي',
              value: activeCount.toString(),
              icon: Icons.business,
              color: AppColors.secondary,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildStatCard(
              title: 'مدين',
              value: debtCount.toString(),
              icon: Icons.trending_up,
              color: Colors.red,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildStatCard(
              title: 'دائن',
              value: creditCount.toString(),
              icon: Icons.trending_down,
              color: Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء كارت الإحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.r),
          SizedBox(height: 8.h),
          Text(
            value,
            style: AppTextStyles.titleMedium.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة الموردين
  Widget _buildSuppliersList(SupplierState state, SupplierNotifier notifier) {
    if (state.isLoading) {
      return const LoadingWidget();
    }

    if (state.error != null) {
      return ErrorDisplayWidget(
        error: state.error!,
        onRetry: () => notifier.refresh(),
      );
    }

    final filteredSuppliers = state.filteredSuppliers;

    if (filteredSuppliers.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.business_outlined,
        title: 'لا توجد موردين',
        subtitle: state.searchQuery.isNotEmpty
            ? 'لم يتم العثور على نتائج للبحث "${state.searchQuery}"'
            : 'ابدأ بإضافة مورد جديد',
        actionText: 'إضافة مورد',
        onActionPressed: () => _navigateToAddSupplier(),
      );
    }

    return RefreshIndicator(
      onRefresh: () => notifier.refresh(),
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.all(16.w),
        itemCount: filteredSuppliers.length,
        itemBuilder: (context, index) {
          final supplier = filteredSuppliers[index];
          return _buildSupplierCard(supplier, notifier);
        },
      ),
    );
  }

  /// بناء كارت المورد
  Widget _buildSupplierCard(Supplier supplier, SupplierNotifier notifier) {
    final currencyFormat = NumberFormat.currency(
      locale: 'ar_SA',
      symbol: 'ر.س',
      decimalDigits: 2,
    );

    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: () => _navigateToEditSupplier(supplier),
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف الأول: الاسم والرصيد
              Row(
                children: [
                  Expanded(
                    child: Text(
                      supplier.name,
                      style: AppTextStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: _getBalanceColor(supplier.balanceStatus)
                          .withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      currencyFormat.format(supplier.absoluteBalance),
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: _getBalanceColor(supplier.balanceStatus),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.h),
              // الصف الثاني: معلومات الاتصال
              if (supplier.hasPhone || supplier.hasEmail) ...[
                Row(
                  children: [
                    if (supplier.hasPhone) ...[
                      Icon(Icons.phone,
                          size: 16.r, color: Colors.grey.shade600),
                      SizedBox(width: 4.w),
                      Text(
                        supplier.phone!,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                    if (supplier.hasPhone && supplier.hasEmail)
                      SizedBox(width: 16.w),
                    if (supplier.hasEmail) ...[
                      Icon(Icons.email,
                          size: 16.r, color: Colors.grey.shade600),
                      SizedBox(width: 4.w),
                      Expanded(
                        child: Text(
                          supplier.email!,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: Colors.grey.shade600,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ],
                ),
                SizedBox(height: 8.h),
              ],
              // الصف الثالث: حالة الرصيد والإجراءات
              Row(
                children: [
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: _getBalanceColor(supplier.balanceStatus)
                          .withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6.r),
                    ),
                    child: Text(
                      supplier.balanceStatus.displayName,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: _getBalanceColor(supplier.balanceStatus),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const Spacer(),
                  // أزرار الإجراءات
                  PopupMenuButton<String>(
                    onSelected: (value) =>
                        _handleSupplierAction(value, supplier, notifier),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 18),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'balance',
                        child: Row(
                          children: [
                            Icon(Icons.account_balance_wallet, size: 18),
                            SizedBox(width: 8),
                            Text('تعديل الرصيد'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 18, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                    child: Icon(
                      Icons.more_vert,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// الحصول على لون حالة الرصيد
  Color _getBalanceColor(SupplierBalanceStatus status) {
    switch (status) {
      case SupplierBalanceStatus.weOwe:
        return Colors.red;
      case SupplierBalanceStatus.theyOwe:
        return Colors.green;
      case SupplierBalanceStatus.balanced:
        return Colors.blue;
    }
  }

  /// التعامل مع إجراءات المورد
  void _handleSupplierAction(
      String action, Supplier supplier, SupplierNotifier notifier) {
    switch (action) {
      case 'edit':
        _navigateToEditSupplier(supplier);
        break;
      case 'balance':
        _showBalanceDialog(supplier, notifier);
        break;
      case 'delete':
        _showDeleteConfirmation(supplier, notifier);
        break;
    }
  }

  /// عرض حوار تعديل الرصيد
  void _showBalanceDialog(Supplier supplier, SupplierNotifier notifier) {
    final balanceController = TextEditingController(
      text: supplier.balance.toString(),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تعديل رصيد ${supplier.name}'),
        content: TextField(
          controller: balanceController,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'الرصيد الجديد',
            suffixText: 'ر.س',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final newBalance = double.tryParse(balanceController.text) ?? 0.0;
              notifier.updateSupplierBalance(supplier.id, newBalance);
              Navigator.pop(context);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  /// عرض تأكيد الحذف
  void _showDeleteConfirmation(Supplier supplier, SupplierNotifier notifier) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف ${supplier.name}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              notifier.deleteSupplier(supplier.id);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  /// الانتقال لصفحة إضافة مورد
  void _navigateToAddSupplier() {
    context.go('/accounts/suppliers/add');
  }

  /// الانتقال لصفحة تعديل مورد
  void _navigateToEditSupplier(Supplier supplier) {
    context.go('/accounts/suppliers/edit/${supplier.id}');
  }
}
