import 'package:json_annotation/json_annotation.dart';

import '../../core/constants/database_constants.dart';
part 'stock_adjustment.g.dart';

@JsonSerializable()
class StockAdjustment {
  final String id;
  final String referenceNumber;
  final DateTime adjustmentDate;
  final String warehouseId;
  final String reason;
  final String notes;
  final String status;
  final String createdBy;
  final String? approvedBy;
  final DateTime? approvedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final bool isSynced;

  const StockAdjustment({
    required this.id,
    required this.referenceNumber,
    required this.adjustmentDate,
    required this.warehouseId,
    required this.reason,
    required this.notes,
    required this.status,
    required this.createdBy,
    this.approvedBy,
    this.approvedAt,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.isSynced,
  });

  factory StockAdjustment.create({
    required String id,
    required String referenceNumber,
    required String warehouseId,
    required String reason,
    String notes = '',
    DateTime? adjustmentDate,
    String status = 'draft',
    required String createdBy,
  }) {
    final now = DateTime.now();
    return StockAdjustment(
      id: id,
      referenceNumber: referenceNumber,
      warehouseId: warehouseId,
      reason: reason,
      notes: notes,
      adjustmentDate: adjustmentDate ?? now,
      status: status,
      createdBy: createdBy,
      createdAt: now,
      updatedAt: now,
      isSynced: false,
    );
  }

  factory StockAdjustment.fromJson(Map<String, dynamic> json) =>
      _$StockAdjustmentFromJson(json);
  Map<String, dynamic> toJson() => _$StockAdjustmentToJson(this);

  factory StockAdjustment.fromMap(Map<String, dynamic> map) {
    return StockAdjustment(
      id: map[DatabaseConstants.columnStockAdjustmentId] as String,
      referenceNumber:
          map[DatabaseConstants.columnStockAdjustmentReferenceNumber] as String,
      adjustmentDate: DateTime.parse(
          map[DatabaseConstants.columnStockAdjustmentDate] as String),
      warehouseId:
          map[DatabaseConstants.columnStockAdjustmentWarehouseId] as String,
      reason: map[DatabaseConstants.columnStockAdjustmentReason] as String,
      notes: map[DatabaseConstants.columnStockAdjustmentNotes] as String? ?? '',
      status: map[DatabaseConstants.columnStockAdjustmentStatus] as String,
      createdBy:
          map[DatabaseConstants.columnStockAdjustmentCreatedBy] as String,
      approvedBy:
          map[DatabaseConstants.columnStockAdjustmentApprovedBy] as String?,
      approvedAt: map[DatabaseConstants.columnStockAdjustmentApprovedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnStockAdjustmentApprovedAt] as String)
          : null,
      createdAt: DateTime.parse(
          map[DatabaseConstants.columnStockAdjustmentCreatedAt] as String),
      updatedAt: DateTime.parse(
          map[DatabaseConstants.columnStockAdjustmentUpdatedAt] as String),
      deletedAt: map[DatabaseConstants.columnStockAdjustmentDeletedAt] != null
          ? DateTime.parse(
              map[DatabaseConstants.columnStockAdjustmentDeletedAt] as String)
          : null,
      isSynced:
          (map[DatabaseConstants.columnStockAdjustmentIsSynced] as int) == 1,
    );
  }

  // Convert to map
  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnStockAdjustmentId: id,
      DatabaseConstants.columnStockAdjustmentReferenceNumber: referenceNumber,
      DatabaseConstants.columnStockAdjustmentDate:
          adjustmentDate.toIso8601String(),
      DatabaseConstants.columnStockAdjustmentWarehouseId: warehouseId,
      DatabaseConstants.columnStockAdjustmentReason: reason,
      DatabaseConstants.columnStockAdjustmentNotes: notes,
      DatabaseConstants.columnStockAdjustmentStatus: status,
      DatabaseConstants.columnStockAdjustmentCreatedBy: createdBy,
      DatabaseConstants.columnStockAdjustmentApprovedBy: approvedBy,
      DatabaseConstants.columnStockAdjustmentApprovedAt:
          approvedAt?.toIso8601String(),
      DatabaseConstants.columnStockAdjustmentCreatedAt:
          createdAt.toIso8601String(),
      DatabaseConstants.columnStockAdjustmentUpdatedAt:
          updatedAt.toIso8601String(),
      DatabaseConstants.columnStockAdjustmentDeletedAt:
          deletedAt?.toIso8601String(),
      DatabaseConstants.columnStockAdjustmentIsSynced: isSynced ? 1 : 0,
    };
  }

  // Copy with method
  StockAdjustment copyWith({
    String? id,
    String? referenceNumber,
    DateTime? adjustmentDate,
    String? warehouseId,
    String? reason,
    String? notes,
    String? status,
    String? createdBy,
    String? approvedBy,
    DateTime? approvedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return StockAdjustment(
      id: id ?? this.id,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      adjustmentDate: adjustmentDate ?? this.adjustmentDate,
      warehouseId: warehouseId ?? this.warehouseId,
      reason: reason ?? this.reason,
      notes: notes ?? this.notes,
      status: status ?? this.status,
      createdBy: createdBy ?? this.createdBy,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedAt: approvedAt ?? this.approvedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  // Update method
  StockAdjustment update({
    String? referenceNumber,
    DateTime? adjustmentDate,
    String? warehouseId,
    String? reason,
    String? notes,
    String? status,
    String? approvedBy,
    DateTime? approvedAt,
  }) {
    return copyWith(
      referenceNumber: referenceNumber,
      adjustmentDate: adjustmentDate,
      warehouseId: warehouseId,
      reason: reason,
      notes: notes,
      status: status,
      approvedBy: approvedBy,
      approvedAt: approvedAt,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as deleted
  StockAdjustment markAsDeleted() {
    return copyWith(
      deletedAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as synced
  StockAdjustment markAsSynced() {
    return copyWith(
      isSynced: true,
      updatedAt: DateTime.now(),
    );
  }

  // Approve adjustment
  StockAdjustment approve(String approvedBy) {
    return copyWith(
      status: 'approved',
      approvedBy: approvedBy,
      approvedAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Cancel adjustment
  StockAdjustment cancel() {
    return copyWith(
      status: 'cancelled',
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Getters
  bool get isDeleted => deletedAt != null;
  bool get isDraft => status == 'draft';
  bool get isApproved => status == 'approved';
  bool get isCancelled => status == 'cancelled';
  bool get canEdit => isDraft && !isDeleted;
  bool get canApprove => isDraft && !isDeleted;
  bool get canCancel => (isDraft || isApproved) && !isDeleted;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StockAdjustment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'StockAdjustment(id: $id, referenceNumber: $referenceNumber, status: $status)';
  }
}

@JsonSerializable()
class StockAdjustmentItem {
  final String id;
  final String adjustmentId;
  final String productId;
  final double currentQuantity;
  final double adjustmentQuantity;
  final double newQuantity;
  final String adjustmentType;
  final String reason;
  final String notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final bool isSynced;

  const StockAdjustmentItem({
    required this.id,
    required this.adjustmentId,
    required this.productId,
    required this.currentQuantity,
    required this.adjustmentQuantity,
    required this.newQuantity,
    required this.adjustmentType,
    required this.reason,
    required this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.isSynced,
  });

  factory StockAdjustmentItem.create({
    required String id,
    required String adjustmentId,
    required String productId,
    required double currentQuantity,
    required double adjustmentQuantity,
    required String adjustmentType,
    required String reason,
    String notes = '',
  }) {
    final now = DateTime.now();
    final newQuantity = adjustmentType == 'increase'
        ? currentQuantity + adjustmentQuantity
        : currentQuantity - adjustmentQuantity;

    return StockAdjustmentItem(
      id: id,
      adjustmentId: adjustmentId,
      productId: productId,
      currentQuantity: currentQuantity,
      adjustmentQuantity: adjustmentQuantity,
      newQuantity: newQuantity,
      adjustmentType: adjustmentType,
      reason: reason,
      notes: notes,
      createdAt: now,
      updatedAt: now,
      isSynced: false,
    );
  }

  factory StockAdjustmentItem.fromJson(Map<String, dynamic> json) =>
      _$StockAdjustmentItemFromJson(json);
  Map<String, dynamic> toJson() => _$StockAdjustmentItemToJson(this);

  // Factory constructor from map
  factory StockAdjustmentItem.fromMap(Map<String, dynamic> map) {
    return StockAdjustmentItem(
      id: map[DatabaseConstants.columnStockAdjustmentItemId] as String,
      adjustmentId: map[DatabaseConstants.columnStockAdjustmentItemAdjustmentId]
          as String,
      productId:
          map[DatabaseConstants.columnStockAdjustmentItemProductId] as String,
      currentQuantity:
          (map[DatabaseConstants.columnStockAdjustmentItemCurrentQuantity]
                  as num)
              .toDouble(),
      adjustmentQuantity:
          (map[DatabaseConstants.columnStockAdjustmentItemAdjustmentQuantity]
                  as num)
              .toDouble(),
      newQuantity:
          (map[DatabaseConstants.columnStockAdjustmentItemNewQuantity] as num)
              .toDouble(),
      adjustmentType:
          map[DatabaseConstants.columnStockAdjustmentItemType] as String,
      reason: map[DatabaseConstants.columnStockAdjustmentItemReason] as String,
      notes: map[DatabaseConstants.columnStockAdjustmentItemNotes] as String? ??
          '',
      createdAt: DateTime.parse(
          map[DatabaseConstants.columnStockAdjustmentItemCreatedAt] as String),
      updatedAt: DateTime.parse(
          map[DatabaseConstants.columnStockAdjustmentItemUpdatedAt] as String),
      deletedAt:
          map[DatabaseConstants.columnStockAdjustmentItemDeletedAt] != null
              ? DateTime.parse(
                  map[DatabaseConstants.columnStockAdjustmentItemDeletedAt]
                      as String)
              : null,
      isSynced:
          (map[DatabaseConstants.columnStockAdjustmentItemIsSynced] as int) ==
              1,
    );
  }

  // Convert to map
  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnStockAdjustmentItemId: id,
      DatabaseConstants.columnStockAdjustmentItemAdjustmentId: adjustmentId,
      DatabaseConstants.columnStockAdjustmentItemProductId: productId,
      DatabaseConstants.columnStockAdjustmentItemCurrentQuantity:
          currentQuantity,
      DatabaseConstants.columnStockAdjustmentItemAdjustmentQuantity:
          adjustmentQuantity,
      DatabaseConstants.columnStockAdjustmentItemNewQuantity: newQuantity,
      DatabaseConstants.columnStockAdjustmentItemType: adjustmentType,
      DatabaseConstants.columnStockAdjustmentItemReason: reason,
      DatabaseConstants.columnStockAdjustmentItemNotes: notes,
      DatabaseConstants.columnStockAdjustmentItemCreatedAt:
          createdAt.toIso8601String(),
      DatabaseConstants.columnStockAdjustmentItemUpdatedAt:
          updatedAt.toIso8601String(),
      DatabaseConstants.columnStockAdjustmentItemDeletedAt:
          deletedAt?.toIso8601String(),
      DatabaseConstants.columnStockAdjustmentItemIsSynced: isSynced ? 1 : 0,
    };
  }

  // Copy with method
  StockAdjustmentItem copyWith({
    String? id,
    String? adjustmentId,
    String? productId,
    double? currentQuantity,
    double? adjustmentQuantity,
    double? newQuantity,
    String? adjustmentType,
    String? reason,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return StockAdjustmentItem(
      id: id ?? this.id,
      adjustmentId: adjustmentId ?? this.adjustmentId,
      productId: productId ?? this.productId,
      currentQuantity: currentQuantity ?? this.currentQuantity,
      adjustmentQuantity: adjustmentQuantity ?? this.adjustmentQuantity,
      newQuantity: newQuantity ?? this.newQuantity,
      adjustmentType: adjustmentType ?? this.adjustmentType,
      reason: reason ?? this.reason,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  // Getters
  bool get isDeleted => deletedAt != null;
  bool get isIncrease => adjustmentType == 'increase';
  bool get isDecrease => adjustmentType == 'decrease';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StockAdjustmentItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'StockAdjustmentItem(id: $id, productId: $productId, adjustmentType: $adjustmentType, quantity: $adjustmentQuantity)';
  }
}
