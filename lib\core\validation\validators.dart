import '../constants/app_constants.dart';

// Base validator class
abstract class Validator<T> {
  String? validate(T? value);
}

// Required validator
class RequiredValidator extends Validator<String> {
  final String message;

  RequiredValidator({this.message = 'هذا الحقل مطلوب'});

  @override
  String? validate(String? value) {
    if (value == null || value.trim().isEmpty) {
      return message;
    }
    return null;
  }
}

// Minimum length validator
class MinLengthValidator extends Validator<String> {
  final int minLength;
  final String message;

  MinLengthValidator(
    this.minLength, {
    String? message,
  }) : message = message ?? 'يجب أن يكون النص $minLength أحرف على الأقل';

  @override
  String? validate(String? value) {
    if (value == null || value.length < minLength) {
      return message;
    }
    return null;
  }
}

// Maximum length validator
class MaxLengthValidator extends Validator<String> {
  final int maxLength;
  final String message;

  MaxLengthValidator(
    this.maxLength, {
    String? message,
  }) : message = message ?? 'يجب أن يكون النص أقل من $maxLength حرف';

  @override
  String? validate(String? value) {
    if (value != null && value.length > maxLength) {
      return message;
    }
    return null;
  }
}

// Email validator
class EmailValidator extends Validator<String> {
  final String message;

  EmailValidator({this.message = 'البريد الإلكتروني غير صحيح'});

  @override
  String? validate(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Allow empty for optional fields
    }
    
    if (!RegExp(AppConstants.emailRegex).hasMatch(value)) {
      return message;
    }
    return null;
  }
}

// Phone validator
class PhoneValidator extends Validator<String> {
  final String message;

  PhoneValidator({this.message = 'رقم الهاتف غير صحيح'});

  @override
  String? validate(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Allow empty for optional fields
    }
    
    if (!RegExp(AppConstants.phoneRegex).hasMatch(value)) {
      return message;
    }
    return null;
  }
}

// Numeric validator
class NumericValidator extends Validator<String> {
  final String message;

  NumericValidator({this.message = 'يجب أن يكون رقماً'});

  @override
  String? validate(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Allow empty for optional fields
    }
    
    if (double.tryParse(value) == null) {
      return message;
    }
    return null;
  }
}

// Minimum value validator
class MinValueValidator extends Validator<String> {
  final double minValue;
  final String message;

  MinValueValidator(
    this.minValue, {
    String? message,
  }) : message = message ?? 'يجب أن تكون القيمة $minValue أو أكثر';

  @override
  String? validate(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Allow empty for optional fields
    }
    
    final numValue = double.tryParse(value);
    if (numValue == null || numValue < minValue) {
      return message;
    }
    return null;
  }
}

// Maximum value validator
class MaxValueValidator extends Validator<String> {
  final double maxValue;
  final String message;

  MaxValueValidator(
    this.maxValue, {
    String? message,
  }) : message = message ?? 'يجب أن تكون القيمة $maxValue أو أقل';

  @override
  String? validate(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Allow empty for optional fields
    }
    
    final numValue = double.tryParse(value);
    if (numValue == null || numValue > maxValue) {
      return message;
    }
    return null;
  }
}

// Barcode validator
class BarcodeValidator extends Validator<String> {
  final String message;

  BarcodeValidator({this.message = 'الباركود غير صحيح'});

  @override
  String? validate(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Allow empty for optional fields
    }
    
    if (!RegExp(AppConstants.barcodeRegex).hasMatch(value)) {
      return message;
    }
    return null;
  }
}

// Pattern validator
class PatternValidator extends Validator<String> {
  final String pattern;
  final String message;

  PatternValidator(
    this.pattern, {
    required this.message,
  });

  @override
  String? validate(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Allow empty for optional fields
    }
    
    if (!RegExp(pattern).hasMatch(value)) {
      return message;
    }
    return null;
  }
}

// Match validator (for password confirmation)
class MatchValidator extends Validator<String> {
  final String matchValue;
  final String message;

  MatchValidator(
    this.matchValue, {
    this.message = 'القيم غير متطابقة',
  });

  @override
  String? validate(String? value) {
    if (value != matchValue) {
      return message;
    }
    return null;
  }
}

// Composite validator (combines multiple validators)
class CompositeValidator extends Validator<String> {
  final List<Validator<String>> validators;

  CompositeValidator(this.validators);

  @override
  String? validate(String? value) {
    for (final validator in validators) {
      final error = validator.validate(value);
      if (error != null) {
        return error;
      }
    }
    return null;
  }
}

// Validation helper class
class ValidationHelper {
  // Common validation combinations
  static Validator<String> required({String? message}) {
    return RequiredValidator(message: message ?? 'هذا الحقل مطلوب');
  }

  static Validator<String> requiredText({
    int? minLength,
    int? maxLength,
    String? requiredMessage,
    String? minLengthMessage,
    String? maxLengthMessage,
  }) {
    final validators = <Validator<String>>[
      RequiredValidator(message: requiredMessage ?? 'هذا الحقل مطلوب'),
    ];

    if (minLength != null) {
      validators.add(MinLengthValidator(
        minLength,
        message: minLengthMessage,
      ));
    }

    if (maxLength != null) {
      validators.add(MaxLengthValidator(
        maxLength,
        message: maxLengthMessage,
      ));
    }

    return CompositeValidator(validators);
  }

  static Validator<String> optionalText({
    int? minLength,
    int? maxLength,
    String? minLengthMessage,
    String? maxLengthMessage,
  }) {
    final validators = <Validator<String>>[];

    if (minLength != null) {
      validators.add(MinLengthValidator(
        minLength,
        message: minLengthMessage,
      ));
    }

    if (maxLength != null) {
      validators.add(MaxLengthValidator(
        maxLength,
        message: maxLengthMessage,
      ));
    }

    return CompositeValidator(validators);
  }

  static Validator<String> requiredEmail({String? message}) {
    return CompositeValidator([
      RequiredValidator(message: 'البريد الإلكتروني مطلوب'),
      EmailValidator(message: message ?? 'البريد الإلكتروني غير صحيح'),
    ]);
  }

  static Validator<String> optionalEmail({String? message}) {
    return EmailValidator(message: message ?? 'البريد الإلكتروني غير صحيح');
  }

  static Validator<String> requiredPhone({String? message}) {
    return CompositeValidator([
      RequiredValidator(message: 'رقم الهاتف مطلوب'),
      PhoneValidator(message: message ?? 'رقم الهاتف غير صحيح'),
    ]);
  }

  static Validator<String> optionalPhone({String? message}) {
    return PhoneValidator(message: message ?? 'رقم الهاتف غير صحيح');
  }

  static Validator<String> requiredNumber({
    double? minValue,
    double? maxValue,
    String? requiredMessage,
    String? numericMessage,
    String? minValueMessage,
    String? maxValueMessage,
  }) {
    final validators = <Validator<String>>[
      RequiredValidator(message: requiredMessage ?? 'هذا الحقل مطلوب'),
      NumericValidator(message: numericMessage ?? 'يجب أن يكون رقماً'),
    ];

    if (minValue != null) {
      validators.add(MinValueValidator(
        minValue,
        message: minValueMessage,
      ));
    }

    if (maxValue != null) {
      validators.add(MaxValueValidator(
        maxValue,
        message: maxValueMessage,
      ));
    }

    return CompositeValidator(validators);
  }

  static Validator<String> optionalNumber({
    double? minValue,
    double? maxValue,
    String? numericMessage,
    String? minValueMessage,
    String? maxValueMessage,
  }) {
    final validators = <Validator<String>>[
      NumericValidator(message: numericMessage ?? 'يجب أن يكون رقماً'),
    ];

    if (minValue != null) {
      validators.add(MinValueValidator(
        minValue,
        message: minValueMessage,
      ));
    }

    if (maxValue != null) {
      validators.add(MaxValueValidator(
        maxValue,
        message: maxValueMessage,
      ));
    }

    return CompositeValidator(validators);
  }

  static Validator<String> barcode({String? message}) {
    return BarcodeValidator(message: message ?? 'الباركود غير صحيح');
  }

  static Validator<String> pattern(String pattern, String message) {
    return PatternValidator(pattern, message: message);
  }

  static Validator<String> match(String matchValue, {String? message}) {
    return MatchValidator(matchValue, message: message ?? 'القيم غير متطابقة');
  }
}
