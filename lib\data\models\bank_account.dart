import 'package:json_annotation/json_annotation.dart';
import '../../core/constants/database_constants.dart';

part 'bank_account.g.dart';

/// نموذج الحساب البنكي - Bank Account Model
/// يمثل الحسابات البنكية في النظام
@JsonSerializable()
class BankAccount {
  final String id;
  final String accountName;    // اسم الحساب
  final String accountNumber;  // رقم الحساب
  final String bankName;       // اسم البنك
  final String? branchName;    // اسم الفرع
  final String? iban;          // رقم الآيبان
  final String? swiftCode;     // رمز السويفت
  final String accountType;    // نوع الحساب (current, savings, etc.)
  final String currency;       // العملة
  final double balance;        // الرصيد الحالي
  final double? creditLimit;   // حد الائتمان
  final bool isActive;         // هل الحساب نشط
  final String? notes;         // ملاحظات
  final String? branchId;      // معرف الفرع
  final String createdBy;      // من أنشأ السجل
  final DateTime createdAt;    // تاريخ الإنشاء
  final DateTime? updatedAt;   // تاريخ التحديث
  final DateTime? deletedAt;   // تاريخ الحذف (soft delete)
  final bool isSynced;         // هل تم المزامنة

  const BankAccount({
    required this.id,
    required this.accountName,
    required this.accountNumber,
    required this.bankName,
    this.branchName,
    this.iban,
    this.swiftCode,
    this.accountType = 'current',
    this.currency = 'SAR',
    this.balance = 0.0,
    this.creditLimit,
    this.isActive = true,
    this.notes,
    this.branchId,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  /// إنشاء حساب بنكي من JSON
  factory BankAccount.fromJson(Map<String, dynamic> json) =>
      _$BankAccountFromJson(json);

  /// تحويل الحساب البنكي إلى JSON
  Map<String, dynamic> toJson() => _$BankAccountToJson(this);

  /// إنشاء حساب بنكي من Map قاعدة البيانات
  factory BankAccount.fromMap(Map<String, dynamic> map) {
    return BankAccount(
      id: map[DatabaseConstants.columnBankAccountId] as String,
      accountName: map[DatabaseConstants.columnBankAccountBankName] as String,
      accountNumber: map[DatabaseConstants.columnBankAccountAccountNumber] as String,
      bankName: map[DatabaseConstants.columnBankAccountBankName] as String,
      branchName: map[DatabaseConstants.columnBankAccountBranchId] as String?,
      iban: map[DatabaseConstants.columnBankAccountIban] as String?,
      swiftCode: map[DatabaseConstants.columnBankAccountSwiftCode] as String?,
      accountType: map[DatabaseConstants.columnBankAccountType] as String? ?? 'current',
      currency: map[DatabaseConstants.columnBankAccountCurrency] as String? ?? 'SAR',
      balance: (map[DatabaseConstants.columnBankAccountBalance] as num?)?.toDouble() ?? 0.0,
      creditLimit: (map[DatabaseConstants.columnBankAccountCreditLimit] as num?)?.toDouble(),
      isActive: (map[DatabaseConstants.columnBankAccountIsActive] as int?) == 1,
      notes: map[DatabaseConstants.columnBankAccountNotes] as String?,
      branchId: map[DatabaseConstants.columnBankAccountBranchId] as String?,
      createdBy: map[DatabaseConstants.columnBankAccountCreatedBy] as String,
      createdAt: DateTime.parse(map[DatabaseConstants.columnBankAccountCreatedAt] as String),
      updatedAt: map[DatabaseConstants.columnBankAccountUpdatedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnBankAccountUpdatedAt] as String)
          : null,
      deletedAt: map[DatabaseConstants.columnBankAccountDeletedAt] != null
          ? DateTime.parse(map[DatabaseConstants.columnBankAccountDeletedAt] as String)
          : null,
      isSynced: (map[DatabaseConstants.columnBankAccountIsSynced] as int) == 1,
    );
  }

  /// تحويل الحساب البنكي إلى Map لقاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnBankAccountId: id,
      DatabaseConstants.columnBankAccountName: accountName,
      DatabaseConstants.columnBankAccountNumber: accountNumber,
      DatabaseConstants.columnBankAccountBankName: bankName,
      DatabaseConstants.columnBankAccountBranchName: branchName,
      DatabaseConstants.columnBankAccountIban: iban,
      DatabaseConstants.columnBankAccountSwiftCode: swiftCode,
      DatabaseConstants.columnBankAccountType: accountType,
      DatabaseConstants.columnBankAccountCurrency: currency,
      DatabaseConstants.columnBankAccountBalance: balance,
      DatabaseConstants.columnBankAccountCreditLimit: creditLimit,
      DatabaseConstants.columnBankAccountIsActive: isActive ? 1 : 0,
      DatabaseConstants.columnBankAccountNotes: notes,
      DatabaseConstants.columnBankAccountBranchId: branchId,
      DatabaseConstants.columnBankAccountCreatedBy: createdBy,
      DatabaseConstants.columnBankAccountCreatedAt: createdAt.toIso8601String(),
      DatabaseConstants.columnBankAccountUpdatedAt: updatedAt?.toIso8601String(),
      DatabaseConstants.columnBankAccountDeletedAt: deletedAt?.toIso8601String(),
      DatabaseConstants.columnBankAccountIsSynced: isSynced ? 1 : 0,
    };
  }

  /// نسخ الحساب البنكي مع تعديل بعض الخصائص
  BankAccount copyWith({
    String? id,
    String? accountName,
    String? accountNumber,
    String? bankName,
    String? branchName,
    String? iban,
    String? swiftCode,
    String? accountType,
    String? currency,
    double? balance,
    double? creditLimit,
    bool? isActive,
    String? notes,
    String? branchId,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return BankAccount(
      id: id ?? this.id,
      accountName: accountName ?? this.accountName,
      accountNumber: accountNumber ?? this.accountNumber,
      bankName: bankName ?? this.bankName,
      branchName: branchName ?? this.branchName,
      iban: iban ?? this.iban,
      swiftCode: swiftCode ?? this.swiftCode,
      accountType: accountType ?? this.accountType,
      currency: currency ?? this.currency,
      balance: balance ?? this.balance,
      creditLimit: creditLimit ?? this.creditLimit,
      isActive: isActive ?? this.isActive,
      notes: notes ?? this.notes,
      branchId: branchId ?? this.branchId,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  /// إنشاء حساب بنكي جديد
  factory BankAccount.create({
    required String id,
    required String accountName,
    required String accountNumber,
    required String bankName,
    String? branchName,
    String? iban,
    String? swiftCode,
    String accountType = 'current',
    String currency = 'SAR',
    double balance = 0.0,
    double? creditLimit,
    String? notes,
    String? branchId,
    required String createdBy,
  }) {
    final now = DateTime.now();
    return BankAccount(
      id: id,
      accountName: accountName,
      accountNumber: accountNumber,
      bankName: bankName,
      branchName: branchName,
      iban: iban,
      swiftCode: swiftCode,
      accountType: accountType,
      currency: currency,
      balance: balance,
      creditLimit: creditLimit,
      notes: notes,
      branchId: branchId,
      createdBy: createdBy,
      createdAt: now,
      isSynced: false,
    );
  }

  /// التحقق من صحة بيانات الحساب البنكي
  bool get isValid {
    return id.isNotEmpty &&
        accountName.isNotEmpty &&
        accountNumber.isNotEmpty &&
        bankName.isNotEmpty &&
        currency.isNotEmpty &&
        createdBy.isNotEmpty;
  }

  /// هل الحساب محذوف؟
  bool get isDeleted => deletedAt != null;

  /// هل الحساب مدين؟
  bool get isOverdrawn => balance < 0;

  /// هل الحساب يتجاوز حد الائتمان؟
  bool get isOverCreditLimit {
    if (creditLimit == null) return false;
    return balance < -creditLimit!;
  }

  /// الرصيد المتاح (مع مراعاة حد الائتمان)
  double get availableBalance {
    if (creditLimit == null) return balance;
    return balance + creditLimit!;
  }

  /// تحديث الرصيد
  BankAccount updateBalance(double newBalance) {
    return copyWith(
      balance: newBalance,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  /// إضافة مبلغ للرصيد
  BankAccount addToBalance(double amount) {
    return updateBalance(balance + amount);
  }

  /// خصم مبلغ من الرصيد
  BankAccount subtractFromBalance(double amount) {
    return updateBalance(balance - amount);
  }

  /// تفعيل الحساب
  BankAccount activate() {
    return copyWith(
      isActive: true,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  /// إلغاء تفعيل الحساب
  BankAccount deactivate() {
    return copyWith(
      isActive: false,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  /// اسم الحساب المختصر للعرض
  String get displayName {
    return '$accountName ($bankName)';
  }

  /// رقم الحساب المقنع (لإخفاء الأرقام الحساسة)
  String get maskedAccountNumber {
    if (accountNumber.length <= 4) return accountNumber;
    final visiblePart = accountNumber.substring(accountNumber.length - 4);
    final maskedPart = '*' * (accountNumber.length - 4);
    return '$maskedPart$visiblePart';
  }

  @override
  String toString() {
    return 'BankAccount(id: $id, accountName: $accountName, bankName: $bankName, balance: $balance)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BankAccount && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
