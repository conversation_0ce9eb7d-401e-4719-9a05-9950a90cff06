import 'package:flutter/foundation.dart';
import 'development_config.dart';
import 'dev_initializer.dart';
import 'dev_auth_service.dart';
import 'dev_dao_wrapper.dart';
import '../utils/app_utils.dart';

/// Examples of how to use the development configuration system
/// This file demonstrates various scenarios for testing without production constraints
class DevUsageExamples {
  
  /// Example 1: Initialize development environment at app startup
  static Future<void> initializeDevEnvironment() async {
    if (!kDebugMode) return;
    
    print('🚀 Setting up development environment...');
    
    // Initialize development configuration
    await DevInitializer.initialize();
    
    // Configure for specific testing scenario
    DevInitializer.configureForTesting(scenario: 'stock_operations');
    
    // Create test data
    await DevInitializer.createTestData();
    
    print('✅ Development environment ready!');
  }
  
  /// Example 2: Test stock operations without validation
  static Future<void> testStockOperationsWithoutValidation() async {
    if (!DevelopmentConfig.isEnabled) return;
    
    print('📦 Testing stock operations...');
    
    // Configure for full bypass
    DevelopmentConfig.configure(
      bypassAuth: true,
      bypassPermissions: true,
      bypassValidation: true,
      useMockData: true,
    );
    
    // Mock user context
    DevUserContext.setMockContext({
      'userId': 'test-user',
      'userName': 'Test Stock Manager',
      'branchId': 'test-branch',
      'warehouseId': 'test-warehouse',
      'canTransferStock': true,
      'canAdjustStock': true,
    });
    
    // Now your stock operations will bypass all validations
    // and use mock data automatically
    
    print('✅ Stock operations configured for testing');
  }
  
  /// Example 3: Test database insertions with safe defaults
  static Future<void> testDatabaseInsertions() async {
    if (!DevelopmentConfig.isEnabled) return;
    
    print('💾 Testing database insertions...');
    
    // Example stock movement data (incomplete)
    final incompleteData = {
      'product_id': 'test-product',
      'quantity': 10.0,
      'movement_type': 'in',
      // Missing: created_by, warehouse_id, unit_id, etc.
    };
    
    // Use development wrapper to add safe defaults
    final safeData = DevDaoWrapper.prepareStockMovementData(incompleteData);
    
    print('Original data: $incompleteData');
    print('Safe data: $safeData');
    
    // The safe data will now have all required fields with valid defaults
    assert(safeData['created_by'] != null);
    assert(safeData['warehouse_id'] != null);
    assert(safeData['unit_id'] != null);
    
    print('✅ Database insertion data prepared safely');
  }
  
  /// Example 4: Test authentication bypass
  static Future<void> testAuthenticationBypass() async {
    if (!DevelopmentConfig.isEnabled) return;
    
    print('🔐 Testing authentication bypass...');
    
    // Check if user is logged in (will return true in dev mode)
    final isLoggedIn = DevAuthService.isLoggedIn();
    print('Is logged in: $isLoggedIn'); // Should be true
    
    // Get current user (will return mock user)
    final currentUser = DevAuthService.getCurrentUser();
    print('Current user: $currentUser');
    
    // Check permissions (will return true for all)
    final hasInventoryAccess = DevPermissionService.hasInventoryAccess();
    final canEdit = DevPermissionService.hasInventoryEditAccess();
    print('Has inventory access: $hasInventoryAccess'); // Should be true
    print('Can edit inventory: $canEdit'); // Should be true
    
    print('✅ Authentication bypass working');
  }
  
  /// Example 5: Test business rule validation bypass
  static Future<void> testValidationBypass() async {
    if (!DevelopmentConfig.isEnabled) return;
    
    print('✅ Testing validation bypass...');
    
    // Test stock level validation (normally would fail)
    final canSubtract = DevBusinessRuleValidator.validateStockLevel(
      5.0, // current stock
      10.0, // requested quantity (more than available)
      'subtract', // operation
    );
    print('Can subtract more than available: $canSubtract'); // Should be true in dev mode
    
    // Test user permission validation
    final hasPermission = DevBusinessRuleValidator.validateUserPermission(
      'invalid-user',
      'delete',
      'inventory',
    );
    print('Invalid user has permission: $hasPermission'); // Should be true in dev mode
    
    print('✅ Validation bypass working');
  }
  
  /// Example 6: Configure for different testing scenarios
  static void demonstrateScenarios() {
    if (!DevelopmentConfig.isEnabled) return;
    
    print('🎭 Demonstrating different testing scenarios...');
    
    // Scenario 1: Full bypass for quick testing
    print('\n1. Full Bypass Mode:');
    DevInitializer.configureForTesting(scenario: 'full_bypass');
    _printCurrentConfig();
    
    // Scenario 2: Stock operations testing
    print('\n2. Stock Operations Mode:');
    DevInitializer.configureForTesting(scenario: 'stock_operations');
    _printCurrentConfig();
    
    // Scenario 3: Sales testing
    print('\n3. Sales Testing Mode:');
    DevInitializer.configureForTesting(scenario: 'sales_testing');
    _printCurrentConfig();
    
    // Scenario 4: Production-like testing
    print('\n4. Production-like Mode:');
    DevInitializer.configureForTesting(scenario: 'production_like');
    _printCurrentConfig();
    
    // Scenario 5: Custom configuration
    print('\n5. Custom Configuration:');
    DevInitializer.configureForTesting(
      customConfig: {
        'bypassAuth': true,
        'bypassPermissions': false, // Keep permission checks
        'bypassValidation': true,
        'useMockData': true,
        'skipEncryption': true,
        'logAllOperations': true,
        'mockContext': {
          'userId': 'custom-user',
          'role': 'custom_role',
        },
      },
    );
    _printCurrentConfig();
  }
  
  /// Example 7: Test encryption bypass
  static void testEncryptionBypass() {
    if (!DevelopmentConfig.isEnabled) return;
    
    print('🔒 Testing encryption bypass...');
    
    final sensitiveData = 'sensitive-password-123';
    
    // In development mode, this will return the plain text
    final processedData = DevDaoWrapper.handleEncryption(sensitiveData);
    
    print('Original: $sensitiveData');
    print('Processed: $processedData');
    
    if (DevelopmentConfig.skipEncryption) {
      assert(sensitiveData == processedData);
      print('✅ Encryption bypassed successfully');
    } else {
      print('🔒 Encryption applied (production mode)');
    }
  }
  
  /// Example 8: Test transaction context creation
  static void testTransactionContext() {
    if (!DevelopmentConfig.isEnabled) return;
    
    print('📋 Testing transaction context creation...');
    
    // Create transaction context with minimal data
    final context = DevDaoWrapper.createTransactionContext(
      userId: 'test-user', // Only provide user ID
      // branchId and warehouseId will be filled from mock context
    );
    
    print('Transaction context: $context');
    
    // Verify all required fields are present
    assert(context['userId'] != null);
    assert(context['branchId'] != null);
    assert(context['warehouseId'] != null);
    assert(context['timestamp'] != null);
    
    print('✅ Transaction context created with safe defaults');
  }
  
  /// Example 9: Complete workflow test
  static Future<void> testCompleteWorkflow() async {
    if (!DevelopmentConfig.isEnabled) return;
    
    print('🔄 Testing complete workflow...');
    
    try {
      // 1. Initialize development environment
      await DevInitializer.initialize();
      
      // 2. Configure for stock operations
      DevInitializer.configureForTesting(scenario: 'stock_operations');
      
      // 3. Set custom user context
      DevUserContext.setMockContext({
        'userId': 'workflow-test-user',
        'userName': 'Workflow Tester',
        'branchId': 'workflow-branch',
        'warehouseId': 'workflow-warehouse',
      });
      
      // 4. Test authentication
      assert(DevAuthService.isLoggedIn());
      assert(DevPermissionService.hasStockManagementAccess());
      
      // 5. Prepare database operation
      final stockData = DevDaoWrapper.prepareStockMovementData({
        'product_id': 'workflow-product',
        'quantity': 25.0,
        'movement_type': 'adjustment',
      });
      
      // 6. Validate the prepared data
      assert(stockData['created_by'] != null);
      assert(stockData['warehouse_id'] != null);
      assert(stockData['unit_id'] != null);
      
      print('✅ Complete workflow test passed');
      
    } catch (e) {
      print('❌ Workflow test failed: $e');
    }
  }
  
  /// Helper method to print current configuration
  static void _printCurrentConfig() {
    final status = DevelopmentConfig.getStatus();
    status.forEach((key, value) {
      print('  $key: $value');
    });
  }
  
  /// Example 10: Reset and cleanup
  static void demonstrateResetAndCleanup() {
    if (!DevelopmentConfig.isEnabled) return;
    
    print('🧹 Demonstrating reset and cleanup...');
    
    // Show current state
    print('Before reset:');
    _printCurrentConfig();
    
    // Reset to defaults
    DevInitializer.reset();
    
    print('\nAfter reset:');
    _printCurrentConfig();
    
    // Disable development mode
    DevInitializer.disable();
    
    print('\nAfter disable:');
    _printCurrentConfig();
  }
}

/// Usage in your main.dart or test files:
/// 
/// ```dart
/// void main() async {
///   WidgetsFlutterBinding.ensureInitialized();
///   
///   // Initialize development environment
///   if (kDebugMode) {
///     await DevUsageExamples.initializeDevEnvironment();
///   }
///   
///   runApp(MyApp());
/// }
/// ```
/// 
/// Or in your test files:
/// 
/// ```dart
/// void main() {
///   group('Stock Management Tests', () {
///     setUpAll(() async {
///       await DevUsageExamples.testStockOperationsWithoutValidation();
///     });
///     
///     test('should create stock movement without validation', () async {
///       // Your test code here - validations will be bypassed
///     });
///   });
/// }
/// ```
