// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'audit_log.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AuditLog _$AuditLogFromJson(Map<String, dynamic> json) => AuditLog(
      id: json['id'] as String,
      tableName: json['tableName'] as String,
      recordId: json['recordId'] as String,
      action: json['action'] as String,
      oldValues: json['oldValues'] as String?,
      newValues: json['newValues'] as String?,
      changedFields: json['changedFields'] as String?,
      userId: json['userId'] as String,
      userAgent: json['userAgent'] as String?,
      ipAddress: json['ipAddress'] as String?,
      sessionId: json['sessionId'] as String?,
      description: json['description'] as String?,
      category: json['category'] as String?,
      severity: json['severity'] as String? ?? 'medium',
      isSuccessful: json['isSuccessful'] as bool? ?? true,
      errorMessage: json['errorMessage'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      branchId: json['branchId'] as String?,
      isSynced: json['isSynced'] as bool? ?? false,
    );

Map<String, dynamic> _$AuditLogToJson(AuditLog instance) => <String, dynamic>{
      'id': instance.id,
      'tableName': instance.tableName,
      'recordId': instance.recordId,
      'action': instance.action,
      'oldValues': instance.oldValues,
      'newValues': instance.newValues,
      'changedFields': instance.changedFields,
      'userId': instance.userId,
      'userAgent': instance.userAgent,
      'ipAddress': instance.ipAddress,
      'sessionId': instance.sessionId,
      'description': instance.description,
      'category': instance.category,
      'severity': instance.severity,
      'isSuccessful': instance.isSuccessful,
      'errorMessage': instance.errorMessage,
      'metadata': instance.metadata,
      'timestamp': instance.timestamp.toIso8601String(),
      'branchId': instance.branchId,
      'isSynced': instance.isSynced,
    };
