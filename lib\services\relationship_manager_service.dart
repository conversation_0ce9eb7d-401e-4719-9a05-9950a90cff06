// import 'package:tijari_tech/core/utils/app_utils.dart';
// import 'package:tijari_tech/core/exceptions/business_exceptions.dart';
// import 'package:tijari_tech/data/local/dao/stock_movement_dao.dart';
// import 'package:tijari_tech/data/local/dao/customer_dao.dart';
// import 'package:tijari_tech/data/local/dao/supplier_dao.dart';
// import 'package:tijari_tech/data/local/dao/warehouse_dao.dart';
// import 'package:tijari_tech/data/local/dao/user_dao.dart';
// import 'package:tijari_tech/data/local/dao/unit_dao.dart';
// import 'package:tijari_tech/data/local/dao/product_dao.dart';
// import 'package:tijari_tech/data/models/stock_movement.dart';
// import 'package:tijari_tech/services/default_data_service.dart';

// /// خدمة إدارة العلاقات بين الجداول
// /// تضمن سلامة الروابط والعلاقات في قاعدة البيانات
// class RelationshipManagerService {
//   static final RelationshipManagerService _instance = RelationshipManagerService._internal();
//   factory RelationshipManagerService() => _instance;
//   RelationshipManagerService._internal();

//   final StockMovementDao _stockMovementDao = StockMovementDao();
//   final CustomerDao _customerDao = CustomerDao();
//   final SupplierDao _supplierDao = SupplierDao();
//   final WarehouseDao _warehouseDao = WarehouseDao();
//   final UserDao _userDao = UserDao();
//   final UnitDao _unitDao = UnitDao();
//   final ProductDao _productDao = ProductDao();
//   final DefaultDataService _defaultDataService = DefaultDataService();

//   /// التحقق من صحة العلاقات قبل إنشاء حركة مخزون
//   Future<Map<String, dynamic>> validateStockMovementRelationships({
//     required String productId,
//     String? warehouseId,
//     String? unitId,
//     String? createdBy,
//     String? branchId,
//     String? fromWarehouseId,
//     String? toWarehouseId,
//   }) async {
//     try {
//       AppUtils.logInfo('التحقق من صحة العلاقات لحركة المخزون');

//       final validatedData = <String, dynamic>{};

//       // التحقق من المنتج (مطلوب)
//       final product = await _productDao.getProductById(productId);
//       if (product == null) {
//         throw ValidationException('المنتج غير موجود: $productId');
//       }
//       validatedData['product'] = product;

//       // التحقق من المخزن أو استخدام الافتراضي
//       String finalWarehouseId = warehouseId ?? DefaultDataService.defaultWarehouseId;
//       var warehouse = await _warehouseDao.getWarehouseById(finalWarehouseId);
//       if (warehouse == null) {
//         AppUtils.logInfo('المخزن غير موجود، سيتم استخدام المخزن الافتراضي');
//         warehouse = await _defaultDataService.getDefaultWarehouse();
//         finalWarehouseId = warehouse.id;
//       }
//       validatedData['warehouse'] = warehouse;
//       validatedData['warehouseId'] = finalWarehouseId;

//       // التحقق من الوحدة أو استخدام الافتراضية
//       String finalUnitId = unitId ?? DefaultDataService.defaultUnitId;
//       var unit = await _unitDao.getUnitById(finalUnitId);
//       if (unit == null) {
//         AppUtils.logInfo('الوحدة غير موجودة، سيتم استخدام الوحدة الافتراضية');
//         unit = await _defaultDataService.getDefaultUnit();
//         finalUnitId = unit.id;
//       }
//       validatedData['unit'] = unit;
//       validatedData['unitId'] = finalUnitId;

//       // التحقق من المستخدم أو استخدام الافتراضي
//       String finalCreatedBy = createdBy ?? DefaultDataService.defaultUserId;
//       var user = await _userDao.getUserById(finalCreatedBy);
//       if (user == null) {
//         AppUtils.logInfo('المستخدم غير موجود، سيتم استخدام المستخدم الافتراضي');
//         user = await _defaultDataService.getDefaultUser();
//         finalCreatedBy = user.id;
//       }
//       validatedData['user'] = user;
//       validatedData['createdBy'] = finalCreatedBy;

//       // التحقق من الفرع أو استخدام الافتراضي
//       String finalBranchId = branchId ?? DefaultDataService.defaultBranchId;
//       validatedData['branchId'] = finalBranchId;

//       // التحقق من مخزن المصدر (للتحويلات)
//       if (fromWarehouseId != null) {
//         final fromWarehouse = await _warehouseDao.getWarehouseById(fromWarehouseId);
//         if (fromWarehouse == null) {
//           throw ValidationException('مخزن المصدر غير موجود: $fromWarehouseId');
//         }
//         validatedData['fromWarehouse'] = fromWarehouse;
//       }

//       // التحقق من مخزن الوجهة (للتحويلات)
//       if (toWarehouseId != null) {
//         final toWarehouse = await _warehouseDao.getWarehouseById(toWarehouseId);
//         if (toWarehouse == null) {
//           throw ValidationException('مخزن الوجهة غير موجود: $toWarehouseId');
//         }
//         validatedData['toWarehouse'] = toWarehouse;
//       }

//       AppUtils.logInfo('تم التحقق من جميع العلاقات بنجاح');
//       return validatedData;
//     } catch (e) {
//       AppUtils.logError('خطأ في التحقق من العلاقات', e);
//       rethrow;
//     }
//   }

//   /// إنشاء حركة مخزون مع التحقق من العلاقات
//   Future<String> createValidatedStockMovement({
//     required String productId,
//     required String movementType,
//     required double quantity,
//     required double unitCost,
//     String? warehouseId,
//     String? unitId,
//     String? createdBy,
//     String? branchId,
//     String? referenceType,
//     String? referenceId,
//     String? referenceNumber,
//     String? fromWarehouseId,
//     String? toWarehouseId,
//     String? batchNumber,
//     DateTime? expiryDate,
//     String? notes,
//     String? reason,
//   }) async {
//     try {
//       AppUtils.logInfo('إنشاء حركة مخزون مع التحقق من العلاقات');

//       // التحقق من صحة العلاقات
//       final validatedData = await validateStockMovementRelationships(
//         productId: productId,
//         warehouseId: warehouseId,
//         unitId: unitId,
//         createdBy: createdBy,
//         branchId: branchId,
//         fromWarehouseId: fromWarehouseId,
//         toWarehouseId: toWarehouseId,
//       );

//       // إنشاء حركة المخزون باستخدام البيانات المتحقق منها
//       final movementId = await _stockMovementDao.createMovement(
//         productId: productId,
//         warehouseId: validatedData['warehouseId'],
//         movementType: movementType,
//         quantity: quantity,
//         unitCost: unitCost,
//         referenceType: referenceType,
//         referenceId: referenceId,
//         referenceNumber: referenceNumber,
//         fromWarehouseId: fromWarehouseId,
//         toWarehouseId: toWarehouseId,
//         batchNumber: batchNumber,
//         expiryDate: expiryDate,
//         notes: notes,
//         reason: reason,
//         createdBy: validatedData['createdBy'],
//         branchId: validatedData['branchId'],
//       );

//       AppUtils.logInfo('تم إنشاء حركة المخزون بنجاح - ID: $movementId');
//       return movementId;
//     } catch (e) {
//       AppUtils.logError('خطأ في إنشاء حركة المخزون المتحقق منها', e);
//       rethrow;
//     }
//   }

//   /// التحقق من العلاقات للعمليات التجارية
//   Future<Map<String, dynamic>> validateBusinessOperationRelationships({
//     String? customerId,
//     String? supplierId,
//     String? warehouseId,
//     String? userId,
//     String? branchId,
//     required String operationType, // 'sale' or 'purchase'
//   }) async {
//     try {
//       AppUtils.logInfo('التحقق من العلاقات للعملية التجارية: $operationType');

//       final validatedData = <String, dynamic>{};

//       // التأكد من وجود البيانات الافتراضية
//       await _defaultDataService.ensureDefaultDataExists();

//       if (operationType == 'sale') {
//         // التحقق من العميل أو استخدام الافتراضي
//         String finalCustomerId = customerId ?? DefaultDataService.defaultCustomerId;
//         var customer = await _customerDao.getCustomerById(finalCustomerId);
//         if (customer == null) {
//           AppUtils.logInfo('العميل غير موجود، سيتم استخدام العميل الافتراضي');
//           customer = await _defaultDataService.getDefaultCustomer();
//           finalCustomerId = customer.id;
//         }
//         validatedData['customer'] = customer;
//         validatedData['customerId'] = finalCustomerId;
//       } else if (operationType == 'purchase') {
//         // التحقق من المورد أو استخدام الافتراضي
//         String finalSupplierId = supplierId ?? DefaultDataService.defaultSupplierId;
//         var supplier = await _supplierDao.getSupplierById(finalSupplierId);
//         if (supplier == null) {
//           AppUtils.logInfo('المورد غير موجود، سيتم استخدام المورد الافتراضي');
//           supplier = await _defaultDataService.getDefaultSupplier();
//           finalSupplierId = supplier.id;
//         }
//         validatedData['supplier'] = supplier;
//         validatedData['supplierId'] = finalSupplierId;
//       }

//       // التحقق من المخزن أو استخدام الافتراضي
//       String finalWarehouseId = warehouseId ?? DefaultDataService.defaultWarehouseId;
//       var warehouse = await _warehouseDao.getWarehouseById(finalWarehouseId);
//       if (warehouse == null) {
//         AppUtils.logInfo('المخزن غير موجود، سيتم استخدام المخزن الافتراضي');
//         warehouse = await _defaultDataService.getDefaultWarehouse();
//         finalWarehouseId = warehouse.id;
//       }
//       validatedData['warehouse'] = warehouse;
//       validatedData['warehouseId'] = finalWarehouseId;

//       // التحقق من المستخدم أو استخدام الافتراضي
//       String finalUserId = userId ?? DefaultDataService.defaultUserId;
//       var user = await _userDao.getUserById(finalUserId);
//       if (user == null) {
//         AppUtils.logInfo('المستخدم غير موجود، سيتم استخدام المستخدم الافتراضي');
//         user = await _defaultDataService.getDefaultUser();
//         finalUserId = user.id;
//       }
//       validatedData['user'] = user;
//       validatedData['userId'] = finalUserId;

//       // التحقق من الفرع أو استخدام الافتراضي
//       String finalBranchId = branchId ?? DefaultDataService.defaultBranchId;
//       validatedData['branchId'] = finalBranchId;

//       AppUtils.logInfo('تم التحقق من جميع العلاقات للعملية التجارية بنجاح');
//       return validatedData;
//     } catch (e) {
//       AppUtils.logError('خطأ في التحقق من العلاقات للعملية التجارية', e);
//       rethrow;
//     }
//   }

//   /// الحصول على تقرير حالة العلاقات
//   Future<Map<String, dynamic>> getRelationshipStatusReport() async {
//     try {
//       AppUtils.logInfo('إنشاء تقرير حالة العلاقات');

//       final report = <String, dynamic>{};

//       // عدد العملاء
//       final customersCount = await _customerDao.getCustomersCount();
//       report['customersCount'] = customersCount;

//       // عدد الموردين
//       final suppliersCount = await _supplierDao.getSuppliersCount();
//       report['suppliersCount'] = suppliersCount;

//       // عدد المخازن
//       final warehousesCount = await _warehouseDao.getWarehousesCount();
//       report['warehousesCount'] = warehousesCount;

//       // عدد المستخدمين
//       final usersCount = await _userDao.getUsersCount();
//       report['usersCount'] = usersCount;

//       // عدد الوحدات
//       final unitsCount = await _unitDao.getUnitsCount();
//       report['unitsCount'] = unitsCount;

//       // عدد المنتجات
//       final productsCount = await _productDao.getProductsCount();
//       report['productsCount'] = productsCount;

//       // عدد حركات المخزون
//       final stockMovementsCount = await _stockMovementDao.getMovementsCount();
//       report['stockMovementsCount'] = stockMovementsCount;

//       // حالة البيانات الافتراضية
//       report['defaultDataStatus'] = {
//         'hasDefaultCustomer': await _customerDao.getCustomerById(DefaultDataService.defaultCustomerId) != null,
//         'hasDefaultSupplier': await _supplierDao.getSupplierById(DefaultDataService.defaultSupplierId) != null,
//         'hasDefaultWarehouse': await _warehouseDao.getWarehouseById(DefaultDataService.defaultWarehouseId) != null,
//         'hasDefaultUser': await _userDao.getUserById(DefaultDataService.defaultUserId) != null,
//         'hasDefaultUnit': await _unitDao.getUnitById(DefaultDataService.defaultUnitId) != null,
//       };

//       AppUtils.logInfo('تم إنشاء تقرير حالة العلاقات بنجاح');
//       return report;
//     } catch (e) {
//       AppUtils.logError('خطأ في إنشاء تقرير حالة العلاقات', e);
//       rethrow;
//     }
//   }
// }
