// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stock.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Stock _$StockFromJson(Map<String, dynamic> json) => Stock(
      id: json['id'] as String,
      productId: json['productId'] as String,
      warehouseId: json['warehouseId'] as String,
      quantity: (json['quantity'] as num).toDouble(),
      reservedQuantity: (json['reservedQuantity'] as num?)?.toDouble() ?? 0,
      availableQuantity: (json['availableQuantity'] as num?)?.toDouble() ?? 0,
      minStock: (json['minStock'] as num?)?.toDouble() ?? 0,
      maxStock: (json['maxStock'] as num?)?.toDouble() ?? 0,
      reorderPoint: (json['reorderPoint'] as num?)?.toDouble() ?? 0,
      averageCost: (json['averageCost'] as num?)?.toDouble() ?? 0,
      lastMovementDate: json['lastMovementDate'] == null
          ? null
          : DateTime.parse(json['lastMovementDate'] as String),
      lastCountDate: json['lastCountDate'] == null
          ? null
          : DateTime.parse(json['lastCountDate'] as String),
      location: json['location'] as String?,
      batchNumber: json['batchNumber'] as String?,
      expiryDate: json['expiryDate'] == null
          ? null
          : DateTime.parse(json['expiryDate'] as String),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      isSynced: json['isSynced'] as bool? ?? false,
      productName: json['productName'] as String?,
      productBarcode: json['productBarcode'] as String?,
      warehouseName: json['warehouseName'] as String?,
      categoryName: json['categoryName'] as String?,
      unitName: json['unitName'] as String?,
    );

Map<String, dynamic> _$StockToJson(Stock instance) => <String, dynamic>{
      'id': instance.id,
      'productId': instance.productId,
      'warehouseId': instance.warehouseId,
      'quantity': instance.quantity,
      'reservedQuantity': instance.reservedQuantity,
      'availableQuantity': instance.availableQuantity,
      'minStock': instance.minStock,
      'maxStock': instance.maxStock,
      'reorderPoint': instance.reorderPoint,
      'averageCost': instance.averageCost,
      'lastMovementDate': instance.lastMovementDate?.toIso8601String(),
      'lastCountDate': instance.lastCountDate?.toIso8601String(),
      'location': instance.location,
      'batchNumber': instance.batchNumber,
      'expiryDate': instance.expiryDate?.toIso8601String(),
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'isSynced': instance.isSynced,
      'productName': instance.productName,
      'productBarcode': instance.productBarcode,
      'warehouseName': instance.warehouseName,
      'categoryName': instance.categoryName,
      'unitName': instance.unitName,
    };
