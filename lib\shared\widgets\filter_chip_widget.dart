import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';

class FilterChipWidget extends StatelessWidget {
  final String label;
  final VoidCallback? onDeleted;
  final VoidCallback? onTap;
  final bool isSelected;
  final Color? backgroundColor;
  final Color? selectedColor;
  final Color? textColor;
  final IconData? icon;
  final bool showDeleteIcon;

  const FilterChipWidget({
    super.key,
    required this.label,
    this.onDeleted,
    this.onTap,
    this.isSelected = true,
    this.backgroundColor,
    this.selectedColor,
    this.textColor,
    this.icon,
    this.showDeleteIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    final bgColor = isSelected 
        ? (selectedColor ?? AppColors.primary.withOpacity(0.1))
        : (backgroundColor ?? Colors.grey[200]);
    
    final labelColor = isSelected 
        ? (textColor ?? AppColors.primary)
        : (textColor ?? Colors.grey[700]);

    return Container(
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(20.r),
        border: isSelected 
            ? Border.all(color: AppColors.primary.withOpacity(0.3))
            : null,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20.r),
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: 12.w,
            vertical: 6.h,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) ...[
                Icon(
                  icon,
                  size: 14.sp,
                  color: labelColor,
                ),
                SizedBox(width: 4.w),
              ],
              Text(
                label,
                style: AppTextStyles.bodySmall.copyWith(
                  color: labelColor,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
              if (showDeleteIcon && onDeleted != null) ...[
                SizedBox(width: 4.w),
                GestureDetector(
                  onTap: onDeleted,
                  child: Icon(
                    Icons.close,
                    size: 14.sp,
                    color: labelColor,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class FilterChipGroup extends StatelessWidget {
  final List<FilterChipData> chips;
  final VoidCallback? onClearAll;
  final String? clearAllText;
  final Axis direction;
  final WrapAlignment alignment;
  final double spacing;
  final double runSpacing;

  const FilterChipGroup({
    super.key,
    required this.chips,
    this.onClearAll,
    this.clearAllText = 'مسح الكل',
    this.direction = Axis.horizontal,
    this.alignment = WrapAlignment.start,
    this.spacing = 8.0,
    this.runSpacing = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    if (chips.isEmpty) return const SizedBox.shrink();

    final chipWidgets = chips.map((chip) => FilterChipWidget(
      label: chip.label,
      onDeleted: chip.onDeleted,
      onTap: chip.onTap,
      isSelected: chip.isSelected,
      backgroundColor: chip.backgroundColor,
      selectedColor: chip.selectedColor,
      textColor: chip.textColor,
      icon: chip.icon,
      showDeleteIcon: chip.showDeleteIcon,
    )).toList();

    if (onClearAll != null && chips.any((chip) => chip.isSelected)) {
      chipWidgets.add(
        FilterChipWidget(
          label: clearAllText!,
          onTap: onClearAll,
          isSelected: false,
          backgroundColor: Colors.red[50],
          textColor: Colors.red[700],
          icon: Icons.clear_all,
          showDeleteIcon: false,
        ),
      );
    }

    return Wrap(
      direction: direction,
      alignment: alignment,
      spacing: spacing.w,
      runSpacing: runSpacing.h,
      children: chipWidgets,
    );
  }
}

class FilterChipData {
  final String label;
  final VoidCallback? onDeleted;
  final VoidCallback? onTap;
  final bool isSelected;
  final Color? backgroundColor;
  final Color? selectedColor;
  final Color? textColor;
  final IconData? icon;
  final bool showDeleteIcon;

  const FilterChipData({
    required this.label,
    this.onDeleted,
    this.onTap,
    this.isSelected = true,
    this.backgroundColor,
    this.selectedColor,
    this.textColor,
    this.icon,
    this.showDeleteIcon = true,
  });
}

class SelectableFilterChip extends StatelessWidget {
  final String label;
  final bool isSelected;
  final ValueChanged<bool>? onChanged;
  final IconData? icon;
  final Color? selectedColor;
  final Color? unselectedColor;

  const SelectableFilterChip({
    super.key,
    required this.label,
    required this.isSelected,
    this.onChanged,
    this.icon,
    this.selectedColor,
    this.unselectedColor,
  });

  @override
  Widget build(BuildContext context) {
    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: 14.sp,
              color: isSelected 
                  ? Colors.white 
                  : (unselectedColor ?? Colors.grey[600]),
            ),
            SizedBox(width: 4.w),
          ],
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              color: isSelected 
                  ? Colors.white 
                  : (unselectedColor ?? Colors.grey[600]),
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
      selected: isSelected,
      onSelected: onChanged,
      selectedColor: selectedColor ?? AppColors.primary,
      backgroundColor: Colors.grey[200],
      checkmarkColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.r),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: 8.w,
        vertical: 4.h,
      ),
    );
  }
}

class CountFilterChip extends StatelessWidget {
  final String label;
  final int count;
  final VoidCallback? onTap;
  final VoidCallback? onDeleted;
  final bool isSelected;
  final Color? color;

  const CountFilterChip({
    super.key,
    required this.label,
    required this.count,
    this.onTap,
    this.onDeleted,
    this.isSelected = true,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return FilterChipWidget(
      label: '$label ($count)',
      onTap: onTap,
      onDeleted: onDeleted,
      isSelected: isSelected,
      selectedColor: color?.withOpacity(0.1),
      textColor: color,
    );
  }
}
