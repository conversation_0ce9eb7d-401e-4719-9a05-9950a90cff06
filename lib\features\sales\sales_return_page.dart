import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../widgets/main_layout.dart';

class SalesReturnPage extends ConsumerStatefulWidget {
  const SalesReturnPage({super.key});

  @override
  ConsumerState<SalesReturnPage> createState() => _SalesReturnPageState();
}

class _SalesReturnPageState extends ConsumerState<SalesReturnPage> {
  final _invoiceNumberController = TextEditingController();
  final _notesController = TextEditingController();
  DateTime _date = DateTime.now();
  Map<String, dynamic>? _selectedInvoice;
  final List<Map<String, dynamic>> _returnItems = [];

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'مرتجع مبيعات',
      child: Column(
        children: [
          _buildHeader(),
          if (_selectedInvoice == null) _buildInvoiceSearch(),
          if (_selectedInvoice != null) Expanded(child: _buildItems()),
          if (_selectedInvoice != null) _buildFooter(),
        ],
      ),
    );
  }

  /* ---------- Header ---------- */
  Widget _buildHeader() => Container(
        padding: EdgeInsets.all(16.r),
        child: Row(
          children: [
            Expanded(child: _buildTextField(_invoiceNumberController, 'رقم الفاتورة')),
            SizedBox(width: 16.w),
            ElevatedButton.icon(
              onPressed: () async => _date = (await showDatePicker(
                    context: context,
                    initialDate: _date,
                    firstDate: DateTime(2020),
                    lastDate: DateTime.now(),
                  )) ??
                  _date,
              icon: const Icon(Icons.calendar_today),
              label: Text('${_date.day}/${_date.month}/${_date.year}'),
            ),
          ],
        ),
      );

  /* ---------- Search ---------- */
  Widget _buildInvoiceSearch() => Expanded(
        child: ListView(
          padding: EdgeInsets.all(16.r),
          children: [
            _buildTextField(_invoiceNumberController, 'ابحث برقم الفاتورة'),
            SizedBox(height: 16.h),
            ..._mockInvoices.map((inv) => _invoiceCard(inv)),
          ],
        ),
      );

  Widget _invoiceCard(Map inv) => Card(
        child: ListTile(
          title: Text('فاتورة #${inv['number']}'),
          subtitle: Text('${inv['customer']}  |  ${inv['total']} ر.س'),
          trailing: const Icon(Icons.arrow_forward_ios),
          onTap: () => _selectInvoice(inv),
        ),
      );

  /* ---------- Items ---------- */
  Widget _buildItems() {
    final items = _selectedInvoice!['items'] as List<Map>;
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('الصنف')),
          DataColumn(label: Text('الكمية')),
          DataColumn(label: Text('المرتجع')),
        ],
        rows: items.map((item) {
          final qty = int.tryParse(_returnItems.firstWhere(
                (r) => r['itemId'] == item['id'],
                orElse: () => {'returnQty': '0'},
              )['returnQty']) ??
              0;
          return DataRow(cells: [
            DataCell(Text(item['name'])),
            DataCell(Text(item['qty'].toString())),
            DataCell(
              SizedBox(
                width: 60.w,
                child: TextFormField(
                  initialValue: qty.toString(),
                  keyboardType: TextInputType.number,
                  onChanged: (v) => _updateReturnQty(item['id'], v),
                ),
              ),
            ),
          ]);
        }).toList(),
      ),
    );
  }

  /* ---------- Footer ---------- */
  Widget _buildFooter() => Container(
        padding: EdgeInsets.all(16.r),
        color: Colors.grey[900],
        child: Column(
          children: [
            _buildTextField(_notesController, 'ملاحظات'),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: _processReturn,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: Colors.white,
              ),
              child: const Text('تنفيذ المرتجع'),
            ),
          ],
        ),
      );

  /* ---------- Actions ---------- */
  void _selectInvoice(Map inv) {
    setState(() {
      _selectedInvoice = inv.cast<String, Map<String, dynamic>>();
      _returnItems.clear();
      for (final item in inv['items']) {
        _returnItems.add({
          'itemId': item['id'],
          'returnQty': 0,
          'maxQty': item['qty'],
        });
      }
    });
  }

  void _updateReturnQty(String id, String value) {
    final qty = int.tryParse(value) ?? 0;
    final index = _returnItems.indexWhere((r) => r['itemId'] == id);
    if (index != -1) {
      setState(() =>
          _returnItems[index]['returnQty'] = qty > _returnItems[index]['maxQty']
              ? _returnItems[index]['maxQty']
              : qty);
    }
  }

  void _processReturn() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تنفيذ المرتجع بنجاح')),
    );
  }

  Widget _buildTextField(TextEditingController c, String label) => TextField(
        controller: c,
        decoration: InputDecoration(
          labelText: label,
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
        ),
      );

  /* ---------- Mock data ---------- */
  List<Map<String, dynamic>> get _mockInvoices => [
        {
          'number': 'INV-001',
          'customer': 'أحمد محمد',
          'total': 1250,
          'items': [
            {'id': '1', 'name': 'منتج أ', 'qty': 2, 'price': 50},
            {'id': '2', 'name': 'منتج ب', 'qty': 1, 'price': 150},
          ],
        },
        {
          'number': 'INV-002',
          'customer': 'فاطمة أحمد',
          'total': 850,
          'items': [
            {'id': '3', 'name': 'منتج ج', 'qty': 3, 'price': 75},
          ],
        },
      ];
} 