import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../shared/widgets/loading_widget.dart';
import '../../shared/widgets/error_display_widget.dart';
import '../../shared/widgets/empty_state_widget.dart';
import '../../data/models/stock_transaction.dart';
import '../../data/models/product.dart';
import '../../providers/stock_management_provider.dart';
import '../../providers/enhanced_product_provider.dart';

/// صفحة تقارير معاملات المخزون
///
/// توفر تقارير شاملة لجميع معاملات المخزون مع:
/// - عرض المعاملات مرتبة حسب التاريخ
/// - فلترة حسب نوع المعاملة والمنتج والفترة الزمنية
/// - إحصائيات سريعة للمعاملات
/// - إمكانية التصدير والطباعة
///
/// تم تطوير هذه الصفحة لتوفير رؤية شاملة لحركة المخزون

class StockTransactionsReportPage extends ConsumerStatefulWidget {
  const StockTransactionsReportPage({super.key});

  @override
  ConsumerState<StockTransactionsReportPage> createState() =>
      _StockTransactionsReportPageState();
}

class _StockTransactionsReportPageState
    extends ConsumerState<StockTransactionsReportPage> {
  // فلاتر التقرير
  String _selectedTransactionType =
      'all'; // all, adjustment, purchase, sale, return
  String _selectedOperation = 'all'; // all, add, subtract, set
  DateTime? _startDate;
  DateTime? _endDate;
  String? _selectedProductId;

  final DateFormat _dateFormat = DateFormat('yyyy/MM/dd HH:mm');
  final DateFormat _shortDateFormat = DateFormat('MM/dd');

  @override
  void initState() {
    super.initState();
    // تحديد الفترة الافتراضية (آخر 30 يوم)
    _endDate = DateTime.now();
    _startDate = _endDate!.subtract(const Duration(days: 30));
  }

  @override
  Widget build(BuildContext context) {
    final stockState = ref.watch(stockManagementProvider);
    final productState = ref.watch(enhancedProductManagementProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'تقارير معاملات المخزون',
          style: AppTextStyles.titleLarge.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _showFiltersDialog,
            icon: const Icon(Icons.filter_list, color: Colors.white),
            tooltip: 'فلترة التقرير',
          ),
          IconButton(
            onPressed: _exportReport,
            icon: const Icon(Icons.download, color: Colors.white),
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // إحصائيات سريعة
          _buildQuickStats(),

          // فلاتر سريعة
          _buildQuickFilters(),

          // قائمة المعاملات
          Expanded(
            child: _buildTransactionsList(),
          ),
        ],
      ),
    );
  }

  /// بناء الإحصائيات السريعة
  Widget _buildQuickStats() {
    final summary = ref.watch(stockSummaryProvider);

    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات المعاملات',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي المعاملات',
                  summary['totalTransactions'].toString(),
                  Icons.receipt_long,
                  AppColors.primary,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildStatCard(
                  'تعديلات المخزون',
                  summary['totalAdjustments'].toString(),
                  Icons.edit,
                  AppColors.warning,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildStatCard(
                  'المشتريات',
                  summary['totalPurchases'].toString(),
                  Icons.shopping_cart,
                  AppColors.success,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildStatCard(
                  'المبيعات',
                  summary['totalSales'].toString(),
                  Icons.sell,
                  AppColors.error,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء كارت إحصائية
  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.r),
          SizedBox(height: 4.h),
          Text(
            value,
            style: AppTextStyles.titleLarge.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: color,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// بناء الفلاتر السريعة
  Widget _buildQuickFilters() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            _buildFilterChip(
              'الكل',
              _selectedTransactionType == 'all',
              () => setState(() => _selectedTransactionType = 'all'),
            ),
            SizedBox(width: 8.w),
            _buildFilterChip(
              'تعديلات',
              _selectedTransactionType == 'adjustment',
              () => setState(() => _selectedTransactionType = 'adjustment'),
            ),
            SizedBox(width: 8.w),
            _buildFilterChip(
              'مشتريات',
              _selectedTransactionType == 'purchase',
              () => setState(() => _selectedTransactionType = 'purchase'),
            ),
            SizedBox(width: 8.w),
            _buildFilterChip(
              'مبيعات',
              _selectedTransactionType == 'sale',
              () => setState(() => _selectedTransactionType = 'sale'),
            ),
            SizedBox(width: 8.w),
            _buildFilterChip(
              'مرتجعات',
              _selectedTransactionType == 'return',
              () => setState(() => _selectedTransactionType = 'return'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء رقاقة فلتر
  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.grey[200],
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: isSelected ? Colors.white : Colors.grey[700],
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  /// بناء قائمة المعاملات
  Widget _buildTransactionsList() {
    final stockState = ref.watch(stockManagementProvider);

    if (stockState.isLoading) {
      return const LoadingWidget();
    }

    if (stockState.error != null) {
      return ErrorDisplayWidget(
        error: stockState.error!,
        onRetry: () {
          // إعادة تحميل البيانات
        },
      );
    }

    final filteredTransactions =
        _getFilteredTransactions(stockState.transactions);

    if (filteredTransactions.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.receipt_long,
        title: 'لا توجد معاملات',
        subtitle: 'لم يتم العثور على معاملات تطابق الفلاتر المحددة',
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: filteredTransactions.length,
      itemBuilder: (context, index) {
        final transaction = filteredTransactions[index];
        return _buildTransactionCard(transaction);
      },
    );
  }

  /// بناء كارت معاملة
  Widget _buildTransactionCard(StockTransaction transaction) {
    final productState = ref.watch(enhancedProductManagementProvider);
    Product? product;
    try {
      product = productState.products.firstWhere(
        (p) => p.id == transaction.productId,
      );
    } catch (e) {
      product = null;
    }

    Color typeColor = _getTransactionTypeColor(transaction.type);
    IconData typeIcon = _getTransactionTypeIcon(transaction.type);

    return Card(
      margin: EdgeInsets.only(bottom: 8.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(12.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: typeColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(typeIcon, color: typeColor, size: 20.r),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product?.nameAr ?? 'منتج غير محدد',
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        _getTransactionTypeText(transaction.type),
                        style: AppTextStyles.bodySmall.copyWith(
                          color: typeColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${transaction.quantity} ${_getOperationSign(transaction.operation)}',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _getOperationColor(transaction.operation),
                      ),
                    ),
                    Text(
                      _dateFormat.format(transaction.createdAt),
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            if (transaction.reason.isNotEmpty) ...[
              SizedBox(height: 8.h),
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(6.r),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline,
                        size: 16.r, color: Colors.grey[600]),
                    SizedBox(width: 6.w),
                    Expanded(
                      child: Text(
                        transaction.reason,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// فلترة المعاملات حسب المعايير المحددة
  List<StockTransaction> _getFilteredTransactions(
      List<StockTransaction> transactions) {
    return transactions.where((transaction) {
      // فلتر نوع المعاملة
      if (_selectedTransactionType != 'all' &&
          transaction.type != _selectedTransactionType) {
        return false;
      }

      // فلتر نوع العملية
      if (_selectedOperation != 'all' &&
          transaction.operation != _selectedOperation) {
        return false;
      }

      // فلتر المنتج
      if (_selectedProductId != null &&
          transaction.productId != _selectedProductId) {
        return false;
      }

      // فلتر الفترة الزمنية
      if (_startDate != null && transaction.createdAt.isBefore(_startDate!)) {
        return false;
      }
      if (_endDate != null && transaction.createdAt.isAfter(_endDate!)) {
        return false;
      }

      return true;
    }).toList()
      ..sort((a, b) => b.createdAt
          .compareTo(a.createdAt)); // ترتيب حسب التاريخ (الأحدث أولاً)
  }

  /// الحصول على لون نوع المعاملة
  Color _getTransactionTypeColor(String type) {
    switch (type) {
      case 'adjustment':
        return AppColors.warning;
      case 'purchase':
        return AppColors.success;
      case 'sale':
        return AppColors.error;
      case 'return':
        return AppColors.info;
      default:
        return AppColors.primary;
    }
  }

  /// الحصول على أيقونة نوع المعاملة
  IconData _getTransactionTypeIcon(String type) {
    switch (type) {
      case 'adjustment':
        return Icons.edit;
      case 'purchase':
        return Icons.shopping_cart;
      case 'sale':
        return Icons.sell;
      case 'return':
        return Icons.keyboard_return;
      default:
        return Icons.receipt;
    }
  }

  /// الحصول على نص نوع المعاملة
  String _getTransactionTypeText(String type) {
    switch (type) {
      case 'adjustment':
        return 'تعديل مخزون';
      case 'purchase':
        return 'مشتريات';
      case 'sale':
        return 'مبيعات';
      case 'return':
        return 'مرتجعات';
      default:
        return 'معاملة';
    }
  }

  /// الحصول على رمز العملية
  String _getOperationSign(String operation) {
    switch (operation) {
      case 'add':
        return '+';
      case 'subtract':
        return '-';
      case 'set':
        return '=';
      default:
        return '';
    }
  }

  /// الحصول على لون العملية
  Color _getOperationColor(String operation) {
    switch (operation) {
      case 'add':
        return AppColors.success;
      case 'subtract':
        return AppColors.error;
      case 'set':
        return AppColors.warning;
      default:
        return Colors.grey;
    }
  }

  /// عرض حوار الفلاتر
  void _showFiltersDialog() {
    // TODO: تنفيذ حوار الفلاتر المتقدمة
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلاتر التقرير'),
        content: const Text('سيتم تطوير فلاتر متقدمة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// تصدير التقرير
  void _exportReport() {
    // TODO: تنفيذ تصدير التقرير
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة ميزة التصدير قريباً'),
        backgroundColor: AppColors.info,
      ),
    );
  }
}
