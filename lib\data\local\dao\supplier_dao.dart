import 'package:sqflite/sqflite.dart';
import '../../../core/constants/database_constants.dart';
import '../../../core/utils/app_utils.dart';
import '../../../core/utils/error_handler.dart';
import '../database.dart';
import '../../models/supplier.dart';

class SupplierDao {
  final DatabaseHelper _databaseHelper;

  SupplierDao(this._databaseHelper);

  Future<Database> get database async {
    try {
      final db = await _databaseHelper.database;
      AppUtils.logInfo('Database access successful in SupplierDao');
      return db;
    } catch (e) {
      AppUtils.logError('Database access failed in SupplierDao', e);
      
      if (e.toString().contains('read-only')) {
        AppUtils.logInfo('Attempting to reset database...');
        try {
          await _databaseHelper.resetDatabase();
          final db = await _databaseHelper.database;
          AppUtils.logInfo('Database reset successful');
          return db;
        } catch (resetError) {
          AppUtils.logError('Failed to reset database', resetError);
          rethrow;
        }
      }
      rethrow;
    }
  }

  Map<String, dynamic> _supplierToMap(Supplier supplier) {
    return {
      DatabaseConstants.columnSupplierId: supplier.id,
      DatabaseConstants.columnSupplierName: supplier.name,
      DatabaseConstants.columnSupplierPhone: supplier.phone,
      DatabaseConstants.columnSupplierEmail: supplier.email,
      DatabaseConstants.columnSupplierAddress: supplier.address,
      DatabaseConstants.columnSupplierBalance: supplier.balance,
      DatabaseConstants.columnSupplierNotes: supplier.notes,
      DatabaseConstants.columnSupplierCreatedAt: supplier.createdAt?.toIso8601String(),
      DatabaseConstants.columnSupplierUpdatedAt: supplier.updatedAt?.toIso8601String(),
      DatabaseConstants.columnSupplierDeletedAt: supplier.deletedAt?.toIso8601String(),
      DatabaseConstants.columnSupplierIsSynced: supplier.isSynced ? 1 : 0,
    };
  }

  Supplier _mapToSupplier(Map<String, dynamic> map) {
    return Supplier.fromMap(map);
  }

  Future<String> insert(Supplier supplier) async {
    try {
      AppUtils.logInfo('Inserting supplier: ${supplier.name}');
      final db = await database;
      final map = _supplierToMap(supplier);

      final now = DateTime.now().toIso8601String();
      map[DatabaseConstants.columnSupplierCreatedAt] = now;
      map[DatabaseConstants.columnSupplierUpdatedAt] = now;
      map[DatabaseConstants.columnSupplierIsSynced] = 0;

      if (map[DatabaseConstants.columnSupplierId] == null || 
          map[DatabaseConstants.columnSupplierId].toString().isEmpty) {
        map[DatabaseConstants.columnSupplierId] = AppUtils.generateId();
      }

      await db.insert(DatabaseConstants.tableSuppliers, map);
      AppUtils.logInfo('Supplier inserted successfully - ID: ${map[DatabaseConstants.columnSupplierId]}');
      return map[DatabaseConstants.columnSupplierId];
    } catch (e) {
      AppUtils.logError('Error inserting supplier', e);
      rethrow;
    }
  }

  Future<void> update(Supplier supplier) async {
    try {
      final db = await database;
      final map = _supplierToMap(supplier);
      map[DatabaseConstants.columnSupplierUpdatedAt] = DateTime.now().toIso8601String();
      map[DatabaseConstants.columnSupplierIsSynced] = 0;

      final rowsAffected = await db.update(
        DatabaseConstants.tableSuppliers,
        map,
        where: '${DatabaseConstants.columnSupplierId} = ?',
        whereArgs: [supplier.id],
      );

      if (rowsAffected == 0) {
        throw Exception('Supplier not found for update');
      }

      AppUtils.logInfo('Supplier updated successfully - ID: ${supplier.id}');
    } catch (e) {
      AppUtils.logError('Error updating supplier', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  Future<void> delete(String id) async {
    try {
      final db = await database;
      final rowsAffected = await db.update(
        DatabaseConstants.tableSuppliers,
        {
          DatabaseConstants.columnSupplierDeletedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnSupplierUpdatedAt: DateTime.now().toIso8601String(),
          DatabaseConstants.columnSupplierIsSynced: 0,
        },
        where: '${DatabaseConstants.columnSupplierId} = ?',
        whereArgs: [id],
      );

      if (rowsAffected == 0) {
        throw Exception('Supplier not found for deletion');
      }

      AppUtils.logInfo('Supplier deleted successfully - ID: $id');
    } catch (e) {
      AppUtils.logError('Error deleting supplier', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  Future<Supplier?> findById(String id) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.tableSuppliers,
        where: '${DatabaseConstants.columnSupplierId} = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isEmpty) return null;
      return _mapToSupplier(maps.first);
    } catch (e) {
      AppUtils.logError('Error finding supplier', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  Future<List<Supplier>> findAll({bool includeDeleted = false}) async {
    try {
      final db = await database;
      String? where;
      if (!includeDeleted) {
        where = '${DatabaseConstants.columnSupplierDeletedAt} IS NULL';
      }

      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.tableSuppliers,
        where: where,
        orderBy: DatabaseConstants.columnSupplierName,
      );

      return maps.map(_mapToSupplier).toList();
    } catch (e) {
      AppUtils.logError('Error retrieving suppliers', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  Future<List<Supplier>> search(String query, {bool includeDeleted = false}) async {
    try {
      final db = await database;
      String where = '(${DatabaseConstants.columnSupplierName} LIKE ? OR '
          '${DatabaseConstants.columnSupplierPhone} LIKE ? OR '
          '${DatabaseConstants.columnSupplierEmail} LIKE ?)';
      List<dynamic> whereArgs = ['%$query%', '%$query%', '%$query%'];

      if (!includeDeleted) {
        where += ' AND ${DatabaseConstants.columnSupplierDeletedAt} IS NULL';
      }

      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.tableSuppliers,
        where: where,
        whereArgs: whereArgs,
        orderBy: DatabaseConstants.columnSupplierName,
      );

      return maps.map(_mapToSupplier).toList();
    } catch (e) {
      AppUtils.logError('Error searching suppliers', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  Future<List<Supplier>> findByBalanceStatus(
    SupplierBalanceStatus status, {
    bool includeDeleted = false,
  }) async {
    try {
      final db = await database;
      String balanceCondition;
      switch (status) {
        case SupplierBalanceStatus.weOwe:
          balanceCondition = '${DatabaseConstants.columnSupplierBalance} > 0';
          break;
        case SupplierBalanceStatus.theyOwe:
          balanceCondition = '${DatabaseConstants.columnSupplierBalance} < 0';
          break;
        case SupplierBalanceStatus.balanced:
          balanceCondition = '${DatabaseConstants.columnSupplierBalance} = 0';
          break;
      }

      String where = balanceCondition;
      if (!includeDeleted) {
        where += ' AND ${DatabaseConstants.columnSupplierDeletedAt} IS NULL';
      }

      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.tableSuppliers,
        where: where,
        orderBy: DatabaseConstants.columnSupplierName,
      );

      return maps.map(_mapToSupplier).toList();
    } catch (e) {
      AppUtils.logError('Error retrieving suppliers by balance status', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  Future<Map<String, dynamic>> getSupplierStats() async {
    try {
      final db = await database;
      final result = await db.rawQuery('''
        SELECT 
          COUNT(*) as total_count,
          COUNT(CASE WHEN ${DatabaseConstants.columnSupplierDeletedAt} IS NULL THEN 1 END) as active_count,
          COUNT(CASE WHEN ${DatabaseConstants.columnSupplierBalance} > 0 AND ${DatabaseConstants.columnSupplierDeletedAt} IS NULL THEN 1 END) as we_owe_count,
          COUNT(CASE WHEN ${DatabaseConstants.columnSupplierBalance} < 0 AND ${DatabaseConstants.columnSupplierDeletedAt} IS NULL THEN 1 END) as they_owe_count,
          COUNT(CASE WHEN ${DatabaseConstants.columnSupplierBalance} = 0 AND ${DatabaseConstants.columnSupplierDeletedAt} IS NULL THEN 1 END) as balanced_count,
          COALESCE(SUM(CASE WHEN ${DatabaseConstants.columnSupplierBalance} > 0 AND ${DatabaseConstants.columnSupplierDeletedAt} IS NULL THEN ${DatabaseConstants.columnSupplierBalance} END), 0) as total_we_owe,
          COALESCE(SUM(CASE WHEN ${DatabaseConstants.columnSupplierBalance} < 0 AND ${DatabaseConstants.columnSupplierDeletedAt} IS NULL THEN ABS(${DatabaseConstants.columnSupplierBalance}) END), 0) as total_they_owe
        FROM ${DatabaseConstants.tableSuppliers}
      ''');

      return result.first;
    } catch (e) {
      AppUtils.logError('Error retrieving supplier statistics', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  Future<List<Supplier>> getUnsyncedSuppliers() async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.tableSuppliers,
        where: '${DatabaseConstants.columnSupplierIsSynced} = 0',
        orderBy: DatabaseConstants.columnSupplierUpdatedAt,
      );

      return maps.map(_mapToSupplier).toList();
    } catch (e) {
      AppUtils.logError('Error retrieving unsynced suppliers', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }

  Future<void> markAsSynced(String id) async {
    try {
      final db = await database;
      await db.update(
        DatabaseConstants.tableSuppliers,
        {DatabaseConstants.columnSupplierIsSynced: 1},
        where: '${DatabaseConstants.columnSupplierId} = ?',
        whereArgs: [id],
      );

      AppUtils.logInfo('Supplier marked as synced - ID: $id');
    } catch (e) {
      AppUtils.logError('Error marking supplier as synced', e);
      throw Exception(ErrorHandler.handleDatabaseError(e));
    }
  }
}