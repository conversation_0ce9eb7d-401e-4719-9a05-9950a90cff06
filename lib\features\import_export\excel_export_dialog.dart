import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tijari_tech/core/theme/colors.dart';
import 'package:tijari_tech/core/theme/text_styles.dart';
import 'package:tijari_tech/providers/enhanced_product_provider.dart';
import 'package:tijari_tech/services/excel_service.dart';

/// حوار تصدير البيانات إلى Excel
class ExcelExportDialog extends ConsumerStatefulWidget {
  const ExcelExportDialog({super.key});

  @override
  ConsumerState<ExcelExportDialog> createState() => _ExcelExportDialogState();
}

class _ExcelExportDialogState extends ConsumerState<ExcelExportDialog> {
  String _selectedTemplate = 'Default';
  String _selectedColor = 'الافتراضي';
  bool _useTemplates = false;
  bool _showWidth = false;
  bool _showExcel = true;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500.w,
        height: 600.h,
        padding: EdgeInsets.all(24.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            _buildHeader(),
            SizedBox(height: 20.h),

            // Export Options
            Expanded(
              child: _buildExportOptions(),
            ),

            SizedBox(height: 20.h),

            // Action Buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// بناء رأس الحوار
  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(12.r),
          decoration: BoxDecoration(
            color: AppColors.info.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Icon(
            Icons.file_download,
            color: AppColors.info,
            size: 28.r,
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.close,
                    color: AppColors.error,
                    size: 20.r,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'مشاركة الملف',
                    style: AppTextStyles.titleLarge.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.error,
                    ),
                  ),
                ],
              ),
              Text(
                'تصدير تقرير المنتجات إلى اكسل',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  /// بناء خيارات التصدير
  Widget _buildExportOptions() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس الصفحة
          _buildSectionTitle('رأس الصفحة'),
          _buildDropdownField(
            label: 'القالب',
            value: _selectedTemplate,
            items: ['Default'],
            onChanged: (value) {
              setState(() {
                _selectedTemplate = value!;
              });
            },
          ),

          SizedBox(height: 20.h),

          // لون الطباعة
          _buildSectionTitle('لون الطباعة'),
          _buildDropdownField(
            label: 'اللون',
            value: _selectedColor,
            items: ['الافتراضي'],
            onChanged: (value) {
              setState(() {
                _selectedColor = value!;
              });
            },
          ),

          SizedBox(height: 20.h),

          // خيارات إضافية
          _buildSectionTitle('خيارات إضافية'),

          CheckboxListTile(
            title: const Text('استخدام قوالب الطباعة'),
            value: _useTemplates,
            onChanged: (value) {
              setState(() {
                _useTemplates = value ?? false;
              });
            },
            controlAffinity: ListTileControlAffinity.leading,
            contentPadding: EdgeInsets.zero,
          ),

          CheckboxListTile(
            title: const Text('طباعة عرضي'),
            value: _showWidth,
            onChanged: (value) {
              setState(() {
                _showWidth = value ?? false;
              });
            },
            controlAffinity: ListTileControlAffinity.leading,
            contentPadding: EdgeInsets.zero,
          ),

          CheckboxListTile(
            title: const Text('طباعة اكسل'),
            value: _showExcel,
            onChanged: (value) {
              setState(() {
                _showExcel = value ?? true;
              });
            },
            controlAffinity: ListTileControlAffinity.leading,
            contentPadding: EdgeInsets.zero,
          ),

          SizedBox(height: 20.h),

          // معلومات إضافية
          Container(
            padding: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(
                color: AppColors.info.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: AppColors.info,
                      size: 20.r,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      'معلومات التصدير',
                      style: AppTextStyles.bodyLarge.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.info,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12.h),
                Text(
                  'سيتم تصدير جميع المنتجات المعروضة حالياً مع تطبيق المرشحات المحددة.',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[700],
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'الملف سيحتوي على: اسم المنتج، الباركود، السعر، الكمية، والفئة.',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنوان القسم
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Text(
        title,
        style: AppTextStyles.bodyLarge.copyWith(
          fontWeight: FontWeight.bold,
          color: AppColors.primary,
        ),
      ),
    );
  }

  /// بناء حقل القائمة المنسدلة
  Widget _buildDropdownField({
    required String label,
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          DropdownButtonFormField<String>(
            value: value,
            items: items.map((item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(item),
              );
            }).toList(),
            onChanged: onChanged,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: 12.w,
                vertical: 8.h,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close),
            label: const Text('إلغاء'),
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          flex: 2,
          child: ElevatedButton.icon(
            onPressed: _exportToExcel,
            icon: const Icon(Icons.print),
            label: const Text('طباعة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  /// تصدير إلى Excel
  Future<void> _exportToExcel() async {
    try {
      // الحصول على المنتجات
      final products = ref.read(enhancedProductManagementProvider).products;

      if (products.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا توجد منتجات للتصدير'),
            backgroundColor: Colors.orange,
            behavior: SnackBarBehavior.floating,
          ),
        );
        return;
      }

      // تصدير البيانات
      await ExcelService.exportProductsToExcel(products);

      if (mounted) {
        Navigator.of(context).pop();

        // إظهار رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تصدير البيانات بنجاح'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في التصدير: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}
