// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ai_prediction.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AIPrediction _$AIPredictionFromJson(Map<String, dynamic> json) => AIPrediction(
      id: json['id'] as String,
      type: json['type'] as String,
      period: json['period'] as String,
      predictionDate: DateTime.parse(json['predictionDate'] as String),
      targetDate: DateTime.parse(json['targetDate'] as String),
      inputData: json['inputData'] as Map<String, dynamic>,
      predictions: json['predictions'] as Map<String, dynamic>,
      confidence: (json['confidence'] as num).toDouble(),
      algorithm: json['algorithm'] as String,
      metadata: json['metadata'] as Map<String, dynamic>,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$AIPredictionToJson(AIPrediction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'period': instance.period,
      'predictionDate': instance.predictionDate.toIso8601String(),
      'targetDate': instance.targetDate.toIso8601String(),
      'inputData': instance.inputData,
      'predictions': instance.predictions,
      'confidence': instance.confidence,
      'algorithm': instance.algorithm,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt.toIso8601String(),
    };

AIInsight _$AIInsightFromJson(Map<String, dynamic> json) => AIInsight(
      id: json['id'] as String,
      category: json['category'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      recommendation: json['recommendation'] as String,
      impact: (json['impact'] as num).toDouble(),
      priority: json['priority'] as String,
      data: json['data'] as Map<String, dynamic>,
      createdAt: DateTime.parse(json['createdAt'] as String),
      actionDate: json['actionDate'] == null
          ? null
          : DateTime.parse(json['actionDate'] as String),
      isActionable: json['isActionable'] as bool? ?? true,
    );

Map<String, dynamic> _$AIInsightToJson(AIInsight instance) => <String, dynamic>{
      'id': instance.id,
      'category': instance.category,
      'title': instance.title,
      'description': instance.description,
      'recommendation': instance.recommendation,
      'impact': instance.impact,
      'priority': instance.priority,
      'data': instance.data,
      'createdAt': instance.createdAt.toIso8601String(),
      'actionDate': instance.actionDate?.toIso8601String(),
      'isActionable': instance.isActionable,
    };
