// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sale.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Sale _$SaleFromJson(Map<String, dynamic> json) => Sale(
      id: json['id'] as String,
      customerId: json['customerId'] as String?,
      invoiceNo: json['invoiceNo'] as String,
      branchId: json['branchId'] as String?,
      date: DateTime.parse(json['date'] as String),
      total: (json['total'] as num).toDouble(),
      paid: (json['paid'] as num).toDouble(),
      due: (json['due'] as num).toDouble(),
      notes: json['notes'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      isSynced: json['isSynced'] as bool? ?? false,
      tax: (json['tax'] as num?)?.toDouble() ?? 0.0,
      discount: (json['discount'] as num?)?.toDouble() ?? 0.0,
      status: json['status'] as String?,
      paymentMethod: json['paymentMethod'] as String?,
      shippingCost: (json['shippingCost'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$SaleToJson(Sale instance) => <String, dynamic>{
      'id': instance.id,
      'customerId': instance.customerId,
      'invoiceNo': instance.invoiceNo,
      'branchId': instance.branchId,
      'date': instance.date.toIso8601String(),
      'total': instance.total,
      'paid': instance.paid,
      'due': instance.due,
      'notes': instance.notes,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'isSynced': instance.isSynced,
      'tax': instance.tax,
      'discount': instance.discount,
      'status': instance.status,
      'paymentMethod': instance.paymentMethod,
      'shippingCost': instance.shippingCost,
    };
