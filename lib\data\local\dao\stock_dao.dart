import 'package:tijari_tech/core/constants/database_constants.dart';
import 'base_dao.dart';
import '../../models/stock.dart';
import '../../../core/utils/app_utils.dart';

class StockDao extends BaseDao<Stock> {
  @override
  String get tableName => DatabaseConstants.tableStocks;

  @override
  Stock fromMap(Map<String, dynamic> map) => Stock.fromMap(map);

  @override
  Map<String, dynamic> toMap(Stock entity) => entity.toMap();

  // ------------------------------------------------------------------
  // Stock-specific queries
  // ------------------------------------------------------------------

  /// استرجاع المخزون حسب المنتج والمخزن
  Future<Stock?> getStockByProductAndWarehouse(
      String productId, String warehouseId) async {
    final stocks = await findWhere(
      where: '''
        ${DatabaseConstants.columnStockProductId} = ? AND
        ${DatabaseConstants.columnStockWarehouseId} = ? AND
        ${DatabaseConstants.columnStockDeletedAt} IS NULL
      ''',
      whereArgs: [productId, warehouseId],
      limit: 1,
    );
    return stocks.isNotEmpty ? stocks.first : null;
  }

  /// استرجاع المخزون حسب المنتج
  Future<List<Stock>> getStocksByProduct(String productId) async {
    return await findWhere(
      where: '''
        ${DatabaseConstants.columnStockProductId} = ? AND
        ${DatabaseConstants.columnStockDeletedAt} IS NULL
      ''',
      whereArgs: [productId],
      orderBy: DatabaseConstants.columnStockWarehouseId,
    );
  }

  /// استرجاع المخزون حسب المخزن
  Future<List<Stock>> getStocksByWarehouse(String warehouseId) async {
    return await findWhere(
      where: '''
        ${DatabaseConstants.columnStockWarehouseId} = ? AND
        ${DatabaseConstants.columnStockDeletedAt} IS NULL
      ''',
      whereArgs: [warehouseId],
      orderBy: DatabaseConstants.columnStockProductId,
    );
  }

  /// استرجاع المخزون المنخفض
  Future<List<Stock>> getLowStockItems() async {
    final sqlQuery = '''
      SELECT s.*, p.name_ar as product_name, p.barcode as product_barcode,
             w.name as warehouse_name, c.name_ar as category_name, u.name as unit_name
      FROM ${DatabaseConstants.tableStocks} s
      LEFT JOIN ${DatabaseConstants.tableProducts} p 
        ON s.${DatabaseConstants.columnStockProductId} = p.${DatabaseConstants.columnProductId}
      LEFT JOIN ${DatabaseConstants.tableWarehouses} w 
        ON s.${DatabaseConstants.columnStockWarehouseId} = w.${DatabaseConstants.columnWarehouseId}
      LEFT JOIN ${DatabaseConstants.tableCategories} c 
        ON p.${DatabaseConstants.columnProductCategoryId} = c.${DatabaseConstants.columnCategoryId}
      LEFT JOIN ${DatabaseConstants.tableUnits} u 
        ON p.${DatabaseConstants.columnProductBaseUnitId} = u.${DatabaseConstants.columnUnitId}
      WHERE s.${DatabaseConstants.columnStockDeletedAt} IS NULL
        AND s.${DatabaseConstants.columnStockQuantity} <= s.reorder_point
        AND s.reorder_point > 0
      ORDER BY s.${DatabaseConstants.columnStockQuantity} ASC
    ''';

    final result = await rawQuery(sqlQuery);
    return result.map((map) => Stock.fromMap(map)).toList();
  }

  /// استرجاع المخزون المنتهي الصلاحية
  Future<List<Stock>> getExpiredStockItems() async {
    final now = DateTime.now().toIso8601String();
    final sqlQuery = '''
      SELECT s.*, p.name_ar as product_name, p.barcode as product_barcode,
             w.name as warehouse_name, c.name_ar as category_name, u.name as unit_name
      FROM ${DatabaseConstants.tableStocks} s
      LEFT JOIN ${DatabaseConstants.tableProducts} p 
        ON s.${DatabaseConstants.columnStockProductId} = p.${DatabaseConstants.columnProductId}
      LEFT JOIN ${DatabaseConstants.tableWarehouses} w 
        ON s.${DatabaseConstants.columnStockWarehouseId} = w.${DatabaseConstants.columnWarehouseId}
      LEFT JOIN ${DatabaseConstants.tableCategories} c 
        ON p.${DatabaseConstants.columnProductCategoryId} = c.${DatabaseConstants.columnCategoryId}
      LEFT JOIN ${DatabaseConstants.tableUnits} u 
        ON p.${DatabaseConstants.columnProductBaseUnitId} = u.${DatabaseConstants.columnUnitId}
      WHERE s.${DatabaseConstants.columnStockDeletedAt} IS NULL
        AND s.expiry_date IS NOT NULL
        AND s.expiry_date <= ?
      ORDER BY s.expiry_date ASC
    ''';

    final result = await rawQuery(sqlQuery, [now]);
    return result.map((map) => Stock.fromMap(map)).toList();
  }

  /// استرجاع المخزون قريب الانتهاء
  Future<List<Stock>> getNearExpiryStockItems({int daysAhead = 30}) async {
    final futureDate =
        DateTime.now().add(Duration(days: daysAhead)).toIso8601String();
    final now = DateTime.now().toIso8601String();

    final sqlQuery = '''
      SELECT s.*, p.name_ar as product_name, p.barcode as product_barcode,
             w.name as warehouse_name, c.name_ar as category_name, u.name as unit_name
      FROM ${DatabaseConstants.tableStocks} s
      LEFT JOIN ${DatabaseConstants.tableProducts} p
        ON s.${DatabaseConstants.columnStockProductId} = p.${DatabaseConstants.columnProductId}
      LEFT JOIN ${DatabaseConstants.tableWarehouses} w
        ON s.${DatabaseConstants.columnStockWarehouseId} = w.${DatabaseConstants.columnWarehouseId}
      LEFT JOIN ${DatabaseConstants.tableCategories} c
        ON p.${DatabaseConstants.columnProductCategoryId} = c.${DatabaseConstants.columnCategoryId}
      LEFT JOIN ${DatabaseConstants.tableUnits} u
        ON p.${DatabaseConstants.columnProductBaseUnitId} = u.${DatabaseConstants.columnUnitId}
      WHERE s.${DatabaseConstants.columnStockDeletedAt} IS NULL
        AND s.expiry_date IS NOT NULL
        AND s.expiry_date > ?
        AND s.expiry_date <= ?
      ORDER BY s.expiry_date ASC
    ''';

    final result = await rawQuery(sqlQuery, [now, futureDate]);
    return result.map((map) => Stock.fromMap(map)).toList();
  }

  /// استرجاع المخزون النافد (الكمية = 0)
  Future<List<Stock>> getOutOfStockItems() async {
    final sqlQuery = '''
      SELECT s.*, p.name_ar as product_name, p.barcode as product_barcode,
             w.name as warehouse_name, c.name_ar as category_name, u.name as unit_name
      FROM ${DatabaseConstants.tableStocks} s
      LEFT JOIN ${DatabaseConstants.tableProducts} p
        ON s.${DatabaseConstants.columnStockProductId} = p.${DatabaseConstants.columnProductId}
      LEFT JOIN ${DatabaseConstants.tableWarehouses} w
        ON s.${DatabaseConstants.columnStockWarehouseId} = w.${DatabaseConstants.columnWarehouseId}
      LEFT JOIN ${DatabaseConstants.tableCategories} c
        ON p.${DatabaseConstants.columnProductCategoryId} = c.${DatabaseConstants.columnCategoryId}
      LEFT JOIN ${DatabaseConstants.tableUnits} u
        ON p.${DatabaseConstants.columnProductBaseUnitId} = u.${DatabaseConstants.columnUnitId}
      WHERE s.${DatabaseConstants.columnStockDeletedAt} IS NULL
        AND s.${DatabaseConstants.columnStockQuantity} <= 0
      ORDER BY s.${DatabaseConstants.columnStockProductId}
    ''';

    final result = await rawQuery(sqlQuery);
    return result.map((map) => Stock.fromMap(map)).toList();
  }

  /// استرجاع المخزون مع التفاصيل الكاملة
  Future<List<Stock>> getStocksWithDetails({
    String? warehouseId,
    String? productId,
    String? categoryId,
    String? orderBy,
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    final conditions = <String>[];
    final args = <dynamic>[];

    // Always exclude deleted stocks
    conditions.add('s.${DatabaseConstants.columnStockDeletedAt} IS NULL');

    if (warehouseId != null) {
      conditions.add('s.${DatabaseConstants.columnStockWarehouseId} = ?');
      args.add(warehouseId);
    }

    if (productId != null) {
      conditions.add('s.${DatabaseConstants.columnStockProductId} = ?');
      args.add(productId);
    }

    if (categoryId != null) {
      conditions.add('p.${DatabaseConstants.columnProductCategoryId} = ?');
      args.add(categoryId);
    }

    final whereClause = conditions.join(' AND ');

    final sqlQuery = '''
      SELECT s.*, p.name_ar as product_name, p.barcode as product_barcode,
             w.name as warehouse_name, c.name_ar as category_name, u.name as unit_name
      FROM ${DatabaseConstants.tableStocks} s
      LEFT JOIN ${DatabaseConstants.tableProducts} p 
        ON s.${DatabaseConstants.columnStockProductId} = p.${DatabaseConstants.columnProductId}
      LEFT JOIN ${DatabaseConstants.tableWarehouses} w 
        ON s.${DatabaseConstants.columnStockWarehouseId} = w.${DatabaseConstants.columnWarehouseId}
      LEFT JOIN ${DatabaseConstants.tableCategories} c 
        ON p.${DatabaseConstants.columnProductCategoryId} = c.${DatabaseConstants.columnCategoryId}
      LEFT JOIN ${DatabaseConstants.tableUnits} u 
        ON p.${DatabaseConstants.columnProductBaseUnitId} = u.${DatabaseConstants.columnUnitId}
      WHERE $whereClause
      ORDER BY ${orderBy ?? 's.${DatabaseConstants.columnStockQuantity}'} ${ascending ? 'ASC' : 'DESC'}
      ${limit != null ? 'LIMIT $limit' : ''}
      ${offset != null ? 'OFFSET $offset' : ''}
    ''';

    final result = await rawQuery(sqlQuery, args);
    return result.map((map) => Stock.fromMap(map)).toList();
  }

  /// تحديث كمية المخزون
  Future<bool> updateStockQuantity({
    required String productId,
    required String warehouseId,
    required double newQuantity,
    double? newReservedQuantity,
    double? newAverageCost,
  }) async {
    try {
      final stock = await getStockByProductAndWarehouse(productId, warehouseId);
      if (stock == null) return false;

      final updatedStock = stock.updateQuantity(
        newQuantity: newQuantity,
        newReservedQuantity: newReservedQuantity,
        newAverageCost: newAverageCost,
      );

      return await update(stock.id, updatedStock);
    } catch (e) {
      AppUtils.logError('Error updating stock quantity', e);
      return false;
    }
  }

  /// إنشاء أو تحديث المخزون
  Future<String> createOrUpdateStock({
    required String productId,
    required String warehouseId,
    required double quantity,
    double reservedQuantity = 0,
    double minStock = 0,
    double maxStock = 0,
    double reorderPoint = 0,
    double averageCost = 0,
    String? location,
    String? batchNumber,
    DateTime? expiryDate,
  }) async {
    try {
      final existingStock =
          await getStockByProductAndWarehouse(productId, warehouseId);

      if (existingStock != null) {
        // Update existing stock
        final updatedStock = existingStock.updateQuantity(
          newQuantity: quantity,
          newReservedQuantity: reservedQuantity,
          newAverageCost: averageCost,
        );

        await update(existingStock.id, updatedStock);
        return existingStock.id;
      } else {
        // Create new stock
        final newStock = Stock.create(
          id: AppUtils.generateId(),
          productId: productId,
          warehouseId: warehouseId,
          quantity: quantity,
          reservedQuantity: reservedQuantity,
          minStock: minStock,
          maxStock: maxStock,
          reorderPoint: reorderPoint,
          averageCost: averageCost,
          location: location,
          batchNumber: batchNumber,
          expiryDate: expiryDate,
        );

        return await insert(newStock);
      }
    } catch (e) {
      AppUtils.logError('Error creating or updating stock', e);
      rethrow;
    }
  }

  /// استرجاع إحصائيات المخزون
  Future<Map<String, dynamic>> getStockStatistics() async {
    final sqlQuery = '''
      SELECT 
        COUNT(*) as total_stock_items,
        COUNT(CASE WHEN ${DatabaseConstants.columnStockQuantity} <= reorder_point AND reorder_point > 0 THEN 1 END) as low_stock_items,
        COUNT(CASE WHEN ${DatabaseConstants.columnStockQuantity} <= 0 THEN 1 END) as out_of_stock_items,
        COUNT(CASE WHEN expiry_date IS NOT NULL AND expiry_date <= datetime('now') THEN 1 END) as expired_items,
        COUNT(CASE WHEN expiry_date IS NOT NULL AND expiry_date <= datetime('now', '+30 days') AND expiry_date > datetime('now') THEN 1 END) as near_expiry_items,
        SUM(${DatabaseConstants.columnStockQuantity} * average_cost) as total_stock_value,
        AVG(${DatabaseConstants.columnStockQuantity}) as avg_stock_quantity
      FROM ${DatabaseConstants.tableStocks}
      WHERE ${DatabaseConstants.columnStockDeletedAt} IS NULL
    ''';

    final result = await rawQuery(sqlQuery);
    return result.isNotEmpty ? result.first : {};
  }
}
